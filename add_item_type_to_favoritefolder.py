import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

print("Starting script to add item_type column to directory_favoritefolder table...")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

# SQL to add the item_type column to the directory_favoritefolder table
add_column_sql = """
ALTER TABLE directory_favoritefolder 
ADD COLUMN IF NOT EXISTS item_type varchar(20) NOT NULL DEFAULT 'assistant';
"""

# SQL to add the unique constraint
add_constraint_sql = """
ALTER TABLE directory_favoritefolder 
DROP CONSTRAINT IF EXISTS directory_favoritefolder_user_id_name_item_type_unique;

ALTER TABLE directory_favoritefolder 
ADD CONSTRAINT directory_favoritefolder_user_id_name_item_type_unique 
UNIQUE (user_id, name, item_type);
"""

# Connect to the database
try:
    print("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(**db_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    
    # Add the item_type column
    print("Adding item_type column to directory_favoritefolder table...")
    cursor.execute(add_column_sql)
    print("Column added successfully!")
    
    # Add the unique constraint
    print("Adding unique constraint...")
    cursor.execute(add_constraint_sql)
    print("Constraint added successfully!")
    
    # Close the connection
    cursor.close()
    conn.close()
    print("Database connection closed.")
    print("Table modification completed successfully!")
    
except Exception as e:
    print(f"Error: {e}")
