import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

print("Starting script to create missing tables...")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

# SQL to create the impersonate_impersonationlog table
impersonationlog_sql = """
CREATE TABLE IF NOT EXISTS "impersonate_impersonationlog" (
    "id" serial NOT NULL PRIMARY KEY,
    "session_key" varchar(40) NOT NULL,
    "session_started_at" timestamp with time zone NULL,
    "session_ended_at" timestamp with time zone NULL,
    "impersonator_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
    "impersonating_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);

CREATE INDEX IF NOT EXISTS "impersonate_impersonationlog_impersonator_id_idx" ON "impersonate_impersonationlog" ("impersonator_id");
CREATE INDEX IF NOT EXISTS "impersonate_impersonationlog_impersonating_id_idx" ON "impersonate_impersonationlog" ("impersonating_id");
CREATE INDEX IF NOT EXISTS "impersonate_impersonationlog_session_started_at_idx" ON "impersonate_impersonationlog" ("session_started_at");
"""

# SQL to create the content_contentimage table
contentimage_sql = """
CREATE TABLE IF NOT EXISTS "content_contentimage" (
    "id" serial NOT NULL PRIMARY KEY,
    "image" varchar(100) NOT NULL,
    "alt_text" varchar(200) NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "content_id" integer NOT NULL REFERENCES "content_content" ("id") DEFERRABLE INITIALLY DEFERRED
);

CREATE INDEX IF NOT EXISTS "content_contentimage_content_id_idx" ON "content_contentimage" ("content_id");
"""

# SQL to create the content_content table
content_sql = """
CREATE TABLE IF NOT EXISTS "content_content" (
    "id" serial NOT NULL PRIMARY KEY,
    "title" varchar(200) NOT NULL,
    "slug" varchar(200) NOT NULL,
    "content_type" varchar(20) NOT NULL,
    "body" text NOT NULL,
    "summary" text NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "updated_at" timestamp with time zone NOT NULL,
    "is_public" boolean NOT NULL,
    "is_archived" boolean NOT NULL,
    "assistant_id" integer NULL,
    "author_id" integer NULL,
    "company_id" integer NOT NULL
);

CREATE INDEX IF NOT EXISTS "content_content_company_id_content_type_idx" ON "content_content" ("company_id", "content_type");
CREATE INDEX IF NOT EXISTS "content_content_company_id_is_public_idx" ON "content_content" ("company_id", "is_public");
CREATE INDEX IF NOT EXISTS "content_content_company_id_is_archived_idx" ON "content_content" ("company_id", "is_archived");
"""

# SQL to create the assistants_interaction table
interaction_sql = """
CREATE TABLE IF NOT EXISTS "assistants_interaction" (
    "id" serial NOT NULL PRIMARY KEY,
    "prompt" text NOT NULL,
    "response" text NOT NULL,
    "context" text NOT NULL,
    "rating" integer NULL,
    "duration" double precision NOT NULL,
    "token_count" integer NOT NULL,
    "use_community_context" boolean NOT NULL DEFAULT false,
    "created_at" timestamp with time zone NOT NULL,
    "assistant_id" integer NOT NULL,
    "user_id" integer NULL
);

CREATE INDEX IF NOT EXISTS "assistants_interaction_assistant_id_created_at_idx" ON "assistants_interaction" ("assistant_id", "created_at");
CREATE INDEX IF NOT EXISTS "assistants_interaction_user_id_created_at_idx" ON "assistants_interaction" ("user_id", "created_at");
"""

# Connect to the database
try:
    print("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(**db_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    
    # Create the content_content table
    print("Creating content_content table...")
    cursor.execute(content_sql)
    print("Content table created successfully!")
    
    # Create the content_contentimage table
    print("Creating content_contentimage table...")
    cursor.execute(contentimage_sql)
    print("ContentImage table created successfully!")
    
    # Create the impersonate_impersonationlog table
    print("Creating impersonate_impersonationlog table...")
    cursor.execute(impersonationlog_sql)
    print("ImpersonationLog table created successfully!")
    
    # Create the assistants_interaction table
    print("Creating assistants_interaction table...")
    cursor.execute(interaction_sql)
    print("Interaction table created successfully!")
    
    # Close the connection
    cursor.close()
    conn.close()
    print("Database connection closed.")
    print("All missing tables created successfully!")
    
except Exception as e:
    print(f"Error: {e}")
