/**
 * Unified Cards Responsive CSS
 * Applies consistent styling to all directory cards (assistants, companies, community)
 * with proper mobile and tablet responsiveness and dark mode support
 */

/* Base improvements for all directory cards */
.directory-card {
  transition: all 0.3s ease !important;
  overflow: hidden !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
}

.directory-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
}

.logo-container {
  transition: all 0.3s ease !important;
  overflow: hidden !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Dark mode styling for all directory cards */
[data-theme="dark"] .directory-card {
  background: linear-gradient(145deg, #1e1e1e, #252525) !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  color: #ffffff !important;
}

[data-theme="dark"] .directory-card:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
}

[data-theme="dark"] .logo-container {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

[data-theme="dark"] .logo-placeholder {
  color: #0d6efd !important;
  background-color: rgba(13, 110, 253, 0.1) !important;
}

[data-theme="dark"] .assistant-name a,
[data-theme="dark"] .directory-card h5 a,
[data-theme="dark"] .directory-card h6 a {
  color: #ffffff !important;
  text-decoration: none !important;
}

[data-theme="dark"] .assistant-name a:hover,
[data-theme="dark"] .directory-card h5 a:hover,
[data-theme="dark"] .directory-card h6 a:hover {
  color: #0088ff !important;
}

[data-theme="dark"] .assistant-meta,
[data-theme="dark"] .directory-card .text-muted {
  color: #cccccc !important;
}

/* Mobile optimizations (up to 768px) */
@media (max-width: 768px) {
  /* Optimize directory cards */
  .directory-card {
    padding: 1rem !important;
    margin-bottom: 1rem !important;
    border-radius: 0.75rem !important;
  }

  /* Optimize logo container */
  .logo-container {
    height: 100px !important;
    width: 100px !important;
    min-height: 100px !important;
    min-width: 100px !important;
    max-height: 100px !important;
    max-width: 100px !important;
    margin: 0 auto 1rem auto !important;
  }

  /* Optimize directory card layout */
  .directory-card .row {
    flex-direction: column !important;
  }

  /* Optimize logo column */
  .directory-item-link-wrapper .col-md-2,
  .directory-item-link-wrapper .col-md-3 {
    width: 100% !important;
    display: flex !important;
    justify-content: center !important;
    margin-bottom: 1rem !important;
    padding: 0 !important;
  }

  /* Optimize name and company column */
  .directory-item-link-wrapper .col-md-3,
  .directory-item-link-wrapper .col-md-4 {
    width: 100% !important;
    text-align: center !important;
    padding: 0 !important;
    margin-bottom: 0.5rem !important;
  }

  /* Optimize description column */
  .directory-item-link-wrapper .col-md-7 {
    width: 100% !important;
    text-align: center !important;
    padding: 0 !important;
  }

  /* Optimize action buttons column */
  .directory-card .col-md-2.text-end,
  .directory-card .col-md-2.d-flex {
    width: 100% !important;
    text-align: center !important;
    margin-top: 1rem !important;
    padding: 0 !important;
    justify-content: center !important;
  }

  /* Optimize tier badge */
  .tier-badge {
    position: absolute !important;
    top: 0.5rem !important;
    left: 0.5rem !important;
    font-size: 0.7rem !important;
    padding: 0.25rem 0.5rem !important;
    z-index: 10 !important;
  }

  /* Optimize filter form */
  .filter-form {
    padding: 1rem !important;
    margin-bottom: 1.5rem !important;
    border-radius: 0.75rem !important;
  }

  /* Dark mode optimizations for mobile */
  [data-theme="dark"] .filter-form {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
  }

  [data-theme="dark"] .tier-section {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
  }

  [data-theme="dark"] .directory-card {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
  }
}

/* Very small screens (up to 576px) */
@media (max-width: 576px) {
  /* Further optimize logo container */
  .logo-container {
    height: 80px !important;
    width: 80px !important;
    min-height: 80px !important;
    min-width: 80px !important;
    max-height: 80px !important;
    max-width: 80px !important;
  }

  /* Further optimize logo placeholder */
  .logo-container .logo-placeholder i {
    font-size: 50px !important;
  }

  /* Further optimize directory card */
  .directory-card {
    padding: 0.75rem !important;
  }

  /* Further optimize filter form */
  .filter-form {
    padding: 0.75rem !important;
  }
}

/* Tablet optimizations (between 768px and 992px) */
@media (min-width: 769px) and (max-width: 991.98px) {
  /* Optimize directory cards for tablets */
  .directory-card {
    padding: 1.25rem !important;
    margin-bottom: 1.5rem !important;
    border-radius: 0.75rem !important;
    min-height: 180px !important;
  }

  /* Optimize logo container for tablets */
  .logo-container {
    height: 90px !important;
    width: 90px !important;
    min-height: 90px !important;
    min-width: 90px !important;
    max-height: 90px !important;
    max-width: 90px !important;
    margin-bottom: 0 !important;
  }

  /* Adjust row layout for tablets - not fully stacked but optimized */
  .directory-card .row {
    flex-wrap: nowrap !important;
    height: 100% !important;
  }

  /* Optimize link wrapper for tablets */
  .directory-item-link-wrapper {
    display: flex !important;
    flex-wrap: nowrap !important;
  }

  /* Optimize logo column for tablets */
  .directory-item-link-wrapper .col-md-3 {
    width: 120px !important;
    min-width: 120px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 0 0.5rem !important;
  }

  /* Optimize name and company column for tablets */
  .directory-item-link-wrapper .col-md-4 {
    width: 30% !important;
    padding: 0 0.5rem !important;
    margin-bottom: 0 !important;
  }

  /* Optimize description column for tablets */
  .directory-item-link-wrapper .col-md-5 {
    width: 40% !important;
    padding: 0 0.5rem !important;
  }

  /* Optimize contact info column for tablets */
  .directory-card .col-md-2.d-flex.flex-column.justify-content-center {
    width: 20% !important;
    padding: 0 0.5rem !important;
  }

  /* Optimize action buttons column for tablets */
  .directory-card .col-md-2.d-flex.flex-column.align-items-end {
    width: 20% !important;
    text-align: right !important;
    padding: 0 0.5rem !important;
  }

  /* Ensure proper spacing between elements */
  .directory-card h5,
  .directory-card h6 {
    margin-bottom: 0.5rem !important;
    font-size: 1rem !important;
  }

  .directory-card p {
    margin-bottom: 0.5rem !important;
    font-size: 0.85rem !important;
  }

  /* Optimize contact info text */
  .directory-card .contact-info {
    font-size: 0.75rem !important;
  }

  /* Optimize buttons */
  .directory-card .btn-sm {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.75rem !important;
  }

  /* Optimize tier sections for tablets */
  .tier-section {
    padding: 1.25rem !important;
    margin-bottom: 1.5rem !important;
    border-radius: 0.75rem !important;
  }

  /* Optimize tier section headings for tablets */
  .tier-section h3 {
    font-size: 1.35rem !important;
    margin-bottom: 1.25rem !important;
  }

  /* Dark mode optimizations for tablets */
  [data-theme="dark"] .filter-form {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
  }

  [data-theme="dark"] .tier-section {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
  }

  [data-theme="dark"] .directory-card {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
  }
}
