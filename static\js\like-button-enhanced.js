/**
 * Enhanced Like Button Functionality
 * Provides improved animations and effects for like buttons
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Enhanced Like Button: Initializing');
    
    // Initialize enhanced like buttons
    initEnhancedLikeButtons();
    
    // Set up a MutationObserver to watch for dynamically added like buttons
    const observer = new MutationObserver(function(mutations) {
        initEnhancedLikeButtons();
    });
    
    // Start observing the document
    observer.observe(document.body, { 
        childList: true, 
        subtree: true 
    });
});

/**
 * Initialize enhanced like buttons
 */
function initEnhancedLikeButtons() {
    // Find all like buttons that haven't been enhanced yet
    const likeButtons = document.querySelectorAll('.like-button:not([data-enhanced="true"])');
    
    likeButtons.forEach(button => {
        // Mark as enhanced to avoid duplicate initialization
        button.setAttribute('data-enhanced', 'true');
        
        // Add click event for instant visual feedback
        button.addEventListener('click', function(event) {
            // The actual like functionality is handled by favorites-functionality.js
            // We're just adding visual enhancements here
            
            // Get the heart icon
            const icon = button.querySelector('i.bi-heart, i.bi-heart-fill');
            if (!icon) return;
            
            // Add ripple effect
            button.classList.add('ripple');
            setTimeout(() => {
                button.classList.remove('ripple');
            }, 500);
            
            // Toggle heart animation instantly for better UX
            // The actual state will be updated by the main functionality
            if (icon.classList.contains('bi-heart')) {
                // Optimistically show as liked
                icon.classList.remove('bi-heart');
                icon.classList.add('bi-heart-fill', 'pulse-heart');
                button.classList.add('text-danger');
                button.classList.remove('text-secondary');
                
                // Create heart particles for immediate feedback
                createEnhancedHeartParticles(button);
            } else {
                // Optimistically show as unliked
                icon.classList.remove('bi-heart-fill', 'pulse-heart');
                icon.classList.add('bi-heart');
                button.classList.remove('text-danger');
                button.classList.add('text-secondary');
            }
        });
    });
}

/**
 * Create enhanced heart particle effects
 * @param {HTMLElement} button - The button to create particles around
 */
function createEnhancedHeartParticles(button) {
    if (!button) return;
    
    // Get button position
    const rect = button.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    // Create particle container to improve performance
    const particleContainer = document.createElement('div');
    particleContainer.style.position = 'fixed';
    particleContainer.style.left = '0';
    particleContainer.style.top = '0';
    particleContainer.style.width = '100%';
    particleContainer.style.height = '100%';
    particleContainer.style.pointerEvents = 'none';
    particleContainer.style.zIndex = '9999';
    particleContainer.style.overflow = 'hidden';
    document.body.appendChild(particleContainer);
    
    // Create particles
    const particleCount = 8; // Increased for better effect
    const colors = ['#ff3366', '#ff6b98', '#ff4d79', '#ff1a53'];
    const sizes = [10, 12, 14, 16];
    
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        
        // Randomly choose heart or star for variety
        const isHeart = Math.random() > 0.3;
        particle.innerHTML = isHeart 
            ? '<i class="bi bi-heart-fill"></i>' 
            : '<i class="bi bi-star-fill"></i>';
        
        // Position at center of button
        particle.style.position = 'absolute';
        particle.style.left = `${centerX}px`;
        particle.style.top = `${centerY}px`;
        
        // Random styling
        const colorIndex = Math.floor(Math.random() * colors.length);
        const sizeIndex = Math.floor(Math.random() * sizes.length);
        particle.style.color = colors[colorIndex];
        particle.style.fontSize = `${sizes[sizeIndex]}px`;
        particle.style.opacity = '1';
        particle.style.transform = 'translate(-50%, -50%)';
        particle.style.transition = 'all 0.8s cubic-bezier(0.165, 0.84, 0.44, 1)';
        
        // Add to container
        particleContainer.appendChild(particle);
        
        // Random direction and distance
        const angle = Math.random() * Math.PI * 2;
        const distance = 30 + Math.random() * 60;
        const destX = centerX + Math.cos(angle) * distance;
        const destY = centerY + Math.sin(angle) * distance;
        
        // Random rotation
        const rotation = -30 + Math.random() * 60;
        
        // Animate with slight delay for each particle
        setTimeout(() => {
            particle.style.transform = `translate(${destX - centerX}px, ${destY - centerY}px) rotate(${rotation}deg) scale(${Math.random() * 0.5 + 0.5})`;
            particle.style.opacity = '0';
        }, i * 50);
    }
    
    // Remove particle container after animation completes
    setTimeout(() => {
        particleContainer.remove();
    }, 1000);
}
