/**
 * White Hearts Override
 * This CSS ensures all heart icons are white by default in dark mode
 * with the highest specificity and !important flags
 */

/* FORCE ALL HEART ICONS TO BE WHITE IN DARK MODE */
html[data-theme="dark"] .like-button i,
html[data-theme="dark"] .btn-like i,
html[data-theme="dark"] .btn-favorite i,
html[data-theme="dark"] .favorite-button i,
html[data-theme="dark"] .like-button svg,
html[data-theme="dark"] .btn-like svg,
html[data-theme="dark"] .btn-favorite svg,
html[data-theme="dark"] .favorite-button svg,
html[data-theme="dark"] .like-button .bi-heart,
html[data-theme="dark"] .btn-like .bi-heart,
html[data-theme="dark"] .btn-favorite .bi-heart,
html[data-theme="dark"] .favorite-button .bi-heart,
html[data-theme="dark"] .like-button .bi-heart-fill:not(.text-danger),
html[data-theme="dark"] .btn-like .bi-heart-fill:not(.text-danger),
html[data-theme="dark"] .btn-favorite .bi-heart-fill:not(.text-danger),
html[data-theme="dark"] .favorite-button .bi-heart-fill:not(.text-danger),
html[data-theme="dark"] body .like-button i,
html[data-theme="dark"] body .btn-like i,
html[data-theme="dark"] body .btn-favorite i,
html[data-theme="dark"] body .favorite-button i,
html[data-theme="dark"] body .like-button svg,
html[data-theme="dark"] body .btn-like svg,
html[data-theme="dark"] body .btn-favorite svg,
html[data-theme="dark"] body .favorite-button svg,
html[data-theme="dark"] body .like-button .bi-heart,
html[data-theme="dark"] body .btn-like .bi-heart,
html[data-theme="dark"] body .btn-favorite .bi-heart,
html[data-theme="dark"] body .favorite-button .bi-heart,
html[data-theme="dark"] body .like-button .bi-heart-fill:not(.text-danger),
html[data-theme="dark"] body .btn-like .bi-heart-fill:not(.text-danger),
html[data-theme="dark"] body .btn-favorite .bi-heart-fill:not(.text-danger),
html[data-theme="dark"] body .favorite-button .bi-heart-fill:not(.text-danger) {
  color: #ffffff !important;
  fill: #ffffff !important;
  stroke: #ffffff !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5)) !important;
}

/* FORCE ALL SVG PATHS TO BE WHITE IN DARK MODE */
html[data-theme="dark"] .like-button:not(.text-danger) svg path,
html[data-theme="dark"] .btn-like:not(.text-danger) svg path,
html[data-theme="dark"] .btn-favorite:not(.text-danger) svg path,
html[data-theme="dark"] .favorite-button:not(.text-danger) svg path,
html[data-theme="dark"] body .like-button:not(.text-danger) svg path,
html[data-theme="dark"] body .btn-like:not(.text-danger) svg path,
html[data-theme="dark"] body .btn-favorite:not(.text-danger) svg path,
html[data-theme="dark"] body .favorite-button:not(.text-danger) svg path {
  fill: #ffffff !important;
  stroke: #ffffff !important;
}

/* ONLY LIKED HEARTS SHOULD BE PINK/RED */
html[data-theme="dark"] .like-button.text-danger i,
html[data-theme="dark"] .btn-like.text-danger i,
html[data-theme="dark"] .btn-favorite.text-danger i,
html[data-theme="dark"] .favorite-button.text-danger i,
html[data-theme="dark"] .like-button.text-danger svg,
html[data-theme="dark"] .btn-like.text-danger svg,
html[data-theme="dark"] .btn-favorite.text-danger svg,
html[data-theme="dark"] .favorite-button.text-danger svg,
html[data-theme="dark"] .like-button.text-danger .bi-heart,
html[data-theme="dark"] .btn-like.text-danger .bi-heart,
html[data-theme="dark"] .btn-favorite.text-danger .bi-heart,
html[data-theme="dark"] .favorite-button.text-danger .bi-heart,
html[data-theme="dark"] .like-button.text-danger .bi-heart-fill,
html[data-theme="dark"] .btn-like.text-danger .bi-heart-fill,
html[data-theme="dark"] .btn-favorite.text-danger .bi-heart-fill,
html[data-theme="dark"] .favorite-button.text-danger .bi-heart-fill,
html[data-theme="dark"] body .like-button.text-danger i,
html[data-theme="dark"] body .btn-like.text-danger i,
html[data-theme="dark"] body .btn-favorite.text-danger i,
html[data-theme="dark"] body .favorite-button.text-danger i,
html[data-theme="dark"] body .like-button.text-danger svg,
html[data-theme="dark"] body .btn-like.text-danger svg,
html[data-theme="dark"] body .btn-favorite.text-danger svg,
html[data-theme="dark"] body .favorite-button.text-danger svg,
html[data-theme="dark"] body .like-button.text-danger .bi-heart,
html[data-theme="dark"] body .btn-like.text-danger .bi-heart,
html[data-theme="dark"] body .btn-favorite.text-danger .bi-heart,
html[data-theme="dark"] body .favorite-button.text-danger .bi-heart,
html[data-theme="dark"] body .like-button.text-danger .bi-heart-fill,
html[data-theme="dark"] body .btn-like.text-danger .bi-heart-fill,
html[data-theme="dark"] body .btn-favorite.text-danger .bi-heart-fill,
html[data-theme="dark"] body .favorite-button.text-danger .bi-heart-fill {
  color: #ff3366 !important;
  fill: #ff3366 !important;
  stroke: #ff3366 !important;
  filter: drop-shadow(0 0 3px rgba(255, 51, 102, 0.5)) !important;
}

/* FORCE ALL SVG PATHS FOR LIKED BUTTONS TO BE PINK/RED */
html[data-theme="dark"] .like-button.text-danger svg path,
html[data-theme="dark"] .btn-like.text-danger svg path,
html[data-theme="dark"] .btn-favorite.text-danger svg path,
html[data-theme="dark"] .favorite-button.text-danger svg path,
html[data-theme="dark"] body .like-button.text-danger svg path,
html[data-theme="dark"] body .btn-like.text-danger svg path,
html[data-theme="dark"] body .btn-favorite.text-danger svg path,
html[data-theme="dark"] body .favorite-button.text-danger svg path {
  fill: #ff3366 !important;
  stroke: #ff3366 !important;
}
