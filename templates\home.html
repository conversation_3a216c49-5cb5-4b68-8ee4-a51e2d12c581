{% extends 'base/layout.html' %}
{% load static %}

{% block title %}24seven - AI-Powered Virtual Assistant Platform{% endblock %}

{% block meta_description %}
Enhance productivity and streamline workflows with 24<PERSON>ven's intelligent virtual assistants. Automate tasks, manage information, and work smarter.
{% endblock %}

{% block body_class %}bg-light{% endblock %}

{% block content %}
{% if user.is_authenticated %}
    {% load account_tags %}
    {% get_pending_companies user as pending_companies %}
    {% get_pending_communities user as pending_communities %}
    {% get_pending_assistants user as pending_assistants %}

    <!-- Show a single consolidated notification for all pending items -->
    <div id="pending-notifications-container">
        {% include 'accounts/tags/consolidated_simple_notification.html' with pending_companies=pending_companies pending_communities=pending_communities pending_assistants=pending_assistants %}
    </div>
{% endif %}

<!-- Hero Section -->
<section class="py-5 bg-primary text-white position-relative overflow-hidden vibrant-hero hero-section">
    <div class="container py-5">
        <div class="row align-items-center">
            <div class="col-lg-6 mx-auto mx-lg-0">
                <h1 class="display-4 fw-bold mb-4">
                    Empower Your Workflow<br>
                    with Intelligent AI
                </h1>
                <p class="lead mb-4">
                    24seven delivers powerful virtual assistants that learn, adapt, and help you accomplish more with less effort.
                </p>
                <div class="d-grid gap-3 d-sm-flex mb-4">
                    <a href="{% url 'accounts:register' %}" class="btn btn-light btn-lg px-4">
                        Get Started Free
                    </a>
                    <a href="{{ site_config.learn_ai_url|default:'#' }}" class="btn btn-outline-light btn-lg px-4">
                        Learn AI
                    </a>
                </div>

                <!-- Search Form -->
                <div class="card bg-white text-dark p-3 shadow-sm search-form-card">
                    <form action="{% url 'search' %}" method="get" class="mb-0">
                        <div class="row g-2">
                            <div class="col-12 mb-2">
                                <label for="searchQuery" class="form-label fw-bold mb-1">Search Directory</label>
                                <input type="text" class="form-control" id="searchQuery" name="q" placeholder="Search for companies, assistants, or community assistants..." required>
                            </div>
                            <div class="col-12 d-flex flex-wrap">
                                <div class="form-check form-check-inline me-3">
                                    <input class="form-check-input" type="radio" name="type" id="searchAssistants" value="assistant" checked>
                                    <label class="form-check-label" for="searchAssistants">Assistants</label>
                                </div>
                                <div class="form-check form-check-inline me-3">
                                    <input class="form-check-input" type="radio" name="type" id="searchCommunityAssistants" value="community">
                                    <label class="form-check-label" for="searchCommunityAssistants">Community Assistants</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="type" id="searchCompanies" value="company">
                                    <label class="form-check-label" for="searchCompanies">Companies</label>
                                </div>
                                <div class="ms-auto mt-2 mt-sm-0">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-search"></i> Search
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="col-lg-6 d-none d-lg-block">
                <img src="{% static 'img/blue1.png' %}"
                     alt="24seven Platform"
                     class="img-fluid">
            </div>
        </div>
    </div>
    <!-- Background Pattern -->
    <img src="{% static 'img/grid-pattern.svg' %}"
         class="position-absolute top-0 end-0 opacity-10"
         style="z-index: 0;"
         alt="">
</section>

<!-- Features Section -->
<section class="py-5 features-section">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold">Why Choose 24seven?</h2>
            <p class="lead text-muted">
                Intelligent virtual assistants designed for your unique needs
            </p>
        </div>

        <div class="row g-4">
            <!-- AI Assistants -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="feature-icon bg-primary bg-gradient text-white rounded-3 mb-3">
                            <i class="bi bi-robot"></i>
                        </div>
                        <h3 class="h5 mb-2">AI Assistants</h3>
                        <p class="text-muted mb-0">
                            Custom AI assistants trained on your company's data to help with various tasks.
                        </p>
                        <div class="mt-3">
                            <a href="{% url 'directory:assistant_list' %}" class="btn btn-sm btn-outline-primary">Browse Assistant Directory</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Smart Documentation -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="feature-icon bg-success bg-gradient text-white rounded-3 mb-3">
                            <i class="bi bi-files"></i>
                        </div>
                        <h3 class="h5 mb-2">Smart Documentation</h3>
                        <p class="text-muted mb-0">
                            Intelligent document management with automatic categorization and search.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Team Collaboration -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="feature-icon bg-info bg-gradient text-white rounded-3 mb-3">
                            <i class="bi bi-people"></i>
                        </div>
                        <h3 class="h5 mb-2">Team Collaboration</h3>
                        <p class="text-muted mb-0">
                            Easy team management, role-based access, and real-time collaboration.
                        </p>
                        <div class="mt-3">
                            <a href="{% url 'directory:company_list' %}" class="btn btn-sm btn-outline-info">Browse Company Directory</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Social Proof -->
<section class="py-5 bg-white">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="h3">Trusted by Companies Worldwide</h2>
        </div>

        <!-- Featured Companies Carousel -->
        <div class="company-logo-carousel-container">
            <div class="company-logo-carousel">
                {% if featured_companies %}
                    <!-- Display featured companies -->
                    {% for company in featured_companies %}
                        <div class="company-logo-item">
                            <a href="{% url 'accounts:public_company_detail' slug=company.slug %}" title="{{ company.name }}" class="text-center">
                                {% if company.info.logo %}
                                    <div class="logo-container home-carousel-logo">
                                        <img src="{{ company.info.logo.url }}" alt="{{ company.name }} Logo" class="company-logo">
                                    </div>
                                {% else %}
                                    <div class="company-logo-placeholder home-carousel-placeholder">
                                        <i class="bi bi-building"></i>
                                        <span>{{ company.name }}</span>
                                    </div>
                                {% endif %}
                                <div class="company-info">
                                    <h5 class="company-name">{{ company.name }}</h5>
                                    <p class="assistant-count">
                                        <i class="bi bi-robot"></i>
                                        {{ company.public_assistants_count }} Public Assistant{{ company.public_assistants_count|pluralize }}
                                    </p>
                                </div>
                            </a>
                        </div>
                    {% endfor %}

                    <!-- Only duplicate logos if we have at least 3 companies for a good scrolling effect -->
                    {% if featured_companies|length >= 3 %}
                        {% for company in featured_companies %}
                            <div class="company-logo-item">
                                <a href="{% url 'accounts:public_company_detail' slug=company.slug %}" title="{{ company.name }}" class="text-center">
                                    {% if company.info.logo %}
                                        <div class="logo-container home-carousel-logo">
                                            <img src="{{ company.info.logo.url }}" alt="{{ company.name }} Logo" class="company-logo">
                                        </div>
                                    {% else %}
                                        <div class="company-logo-placeholder home-carousel-placeholder">
                                            <i class="bi bi-building"></i>
                                            <span>{{ company.name }}</span>
                                        </div>
                                    {% endif %}
                                    <div class="company-info">
                                        <h5 class="company-name">{{ company.name }}</h5>
                                        <p class="assistant-count">
                                            <i class="bi bi-robot"></i>
                                            {{ company.public_assistants_count }} Public Assistant{{ company.public_assistants_count|pluralize }}
                                        </p>
                                    </div>
                                </a>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% else %}
                    <!-- Fallback static logos if no featured companies -->
                    {% for i in "12345" %}
                        <div class="company-logo-item">
                            <a href="{% url 'directory:company_list' %}" title="Browse Companies" class="text-center">
                                <div class="logo-container home-carousel-logo">
                                    <img src="{% static 'img/logos/company-'|add:forloop.counter|add:'.svg' %}" alt="Company Logo" class="company-logo">
                                </div>
                                <div class="company-info">
                                    <h5 class="company-name">Example Company {{ forloop.counter }}</h5>
                                    <p class="assistant-count">
                                        <i class="bi bi-robot"></i>
                                        {{ forloop.counter }} Public Assistant{{ forloop.counter|pluralize }}
                                    </p>
                                </div>
                            </a>
                        </div>
                    {% endfor %}
                    <!-- Only duplicate if we have enough for a good scrolling effect -->
                    {% for i in "12345" %}
                        <div class="company-logo-item">
                            <a href="{% url 'directory:company_list' %}" title="Browse Companies" class="text-center">
                                <div class="logo-container home-carousel-logo">
                                    <img src="{% static 'img/logos/company-'|add:forloop.counter|add:'.svg' %}" alt="Company Logo" class="company-logo">
                                </div>
                                <div class="company-info">
                                    <h5 class="company-name">Example Company {{ forloop.counter }}</h5>
                                    <p class="assistant-count">
                                        <i class="bi bi-robot"></i>
                                        {{ forloop.counter }} Public Assistant{{ forloop.counter|pluralize }}
                                    </p>
                                </div>
                            </a>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-gradient position-relative cta-section">
    <div class="container">
        <div class="p-5 text-center bg-white rounded-3 shadow-sm">
            <h2 class="display-6 fw-bold mb-4">Ready to Transform Your Workflow?</h2>
            <p class="lead mb-4">
                Join the growing number of companies using 24seven to enhance productivity and streamline operations.
            </p>
            <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
                <a href="{% url 'accounts:register' %}" class="btn btn-primary btn-lg px-4 me-sm-3">
                    Start Free Trial
                </a>
                <a href="{{ site_config.contact_url|default:'/contact/' }}" class="btn btn-outline-secondary btn-lg px-4 me-sm-3">
                    Contact Sales
                </a>
                <a href="{% url 'search' %}" class="btn btn-outline-primary btn-lg px-4">
                    <i class="bi bi-search"></i> Search Directory
                </a>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/home-carousel-mobile-fix.css' %}">
<link rel="stylesheet" href="{% static 'css/hero-mobile-center.css' %}">
<style>
.feature-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}
.opacity-10 {
    opacity: 0.1;
}
/* Search form styles */
.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.search-form-card {
    width: 100%;
    max-width: 600px;
}

@media (max-width: 767.98px) {
    .search-form-card {
        max-width: 100%;
    }
}

/* Company Logo Carousel Styles */
/* Company Logo Carousel base styles - mobile styles in home-carousel-mobile-fix.css */
.company-logo-carousel-container {
    width: 100%;
    overflow: hidden;
    position: relative;
    padding: 30px 0 70px 0; /* More padding at bottom for company info */
    margin-bottom: 30px;
}

.company-logo-carousel {
    display: flex;
    animation: scroll 60s linear infinite; /* Slower animation for larger logos */
    width: max-content;
}

.company-logo-item {
    flex: 0 0 auto;
    margin: 0 30px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
}

.company-logo-item a {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    text-decoration: none;
    width: 360px; /* Match logo width */
    color: #333;
    transition: all 0.3s ease;
}

.company-logo-item a:hover {
    color: #0d6efd;
    text-decoration: none;
}

/* Desktop logo container styles - mobile styles in home-carousel-mobile-fix.css */
@media (min-width: 769px) {
    .logo-container {
        height: 180px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
    }

    .company-info {
        text-align: center;
        width: 100%;
    }
}

.company-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.assistant-count {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0;
}

.assistant-count i {
    color: #0d6efd;
    margin-right: 5px;
}

/* Desktop logo styles - mobile styles in home-carousel-mobile-fix.css */
@media (min-width: 769px) {
    .company-logo {
        height: 180px; /* 3x larger */
        width: auto;
        max-width: 360px; /* 3x larger */
        object-fit: contain;
        opacity: 0.8;
        transition: all 0.3s ease;
        margin-bottom: 10px;
    }

    .company-logo:hover {
        opacity: 1;
        transform: scale(1.05);
    }

    .company-logo-placeholder {
        height: 180px; /* 3x larger */
        width: 360px; /* 3x larger */
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        border-radius: 8px;
        opacity: 0.8;
        transition: all 0.3s ease;
        margin-bottom: 10px;
    }

    .company-logo-placeholder:hover {
        opacity: 1;
    }

    .company-logo-placeholder i {
        font-size: 48px; /* Larger icon */
        color: #6c757d;
        margin-bottom: 10px;
        display: block;
    }

    .company-logo-placeholder span {
        font-size: 16px; /* Larger text */
        color: #6c757d;
        text-align: center;
        max-width: 300px; /* Wider to accommodate larger text */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

@keyframes scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add scroll reveal animation
    const animateOnScroll = () => {
        document.querySelectorAll('.card').forEach(card => {
            const rect = card.getBoundingClientRect();
            if (rect.top < window.innerHeight - 100) {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }
        });
    };

    // Initial styles
    document.querySelectorAll('.card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.6s ease-out';
    });

    // Listen for scroll
    window.addEventListener('scroll', animateOnScroll);
    animateOnScroll(); // Initial check

    // Company logo carousel hover effect
    const carousel = document.querySelector('.company-logo-carousel');
    if (carousel) {
        const carouselContainer = document.querySelector('.company-logo-carousel-container');

        // Pause animation on hover
        carouselContainer.addEventListener('mouseenter', () => {
            carousel.style.animationPlayState = 'paused';
        });

        // Resume animation when mouse leaves
        carouselContainer.addEventListener('mouseleave', () => {
            carousel.style.animationPlayState = 'running';
        });
    }
});
</script>
{% endblock %}
