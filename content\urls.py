from django.urls import path
from . import views

app_name = 'content'

urlpatterns = [
    # Content Management
    path('company/<int:company_id>/content/', views.content_list, name='list'),
    path('company/<int:company_id>/content/upload/', views.content_upload, name='upload'),
    path('company/<int:company_id>/content/<int:content_id>/', views.content_detail, name='detail'),
    path('company/<int:company_id>/content/<int:content_id>/edit/', views.content_edit, name='edit'),
    path('company/<int:company_id>/content/<int:content_id>/delete/', views.content_delete, name='delete'),
    
    # Content Categories
    path('company/<int:company_id>/content/categories/', views.category_list, name='category_list'),
    path('company/<int:company_id>/content/categories/create/', views.category_create, name='category_create'),
    path('company/<int:company_id>/content/categories/<int:category_id>/edit/', 
         views.category_edit, name='category_edit'),
    path('company/<int:company_id>/content/categories/<int:category_id>/delete/', 
         views.category_delete, name='category_delete'),
    
    # Content Processing
    path('company/<int:company_id>/content/<int:content_id>/process/', views.process_content, name='process'),
    path('company/<int:company_id>/content/<int:content_id>/extract/', views.extract_knowledge, name='extract'),
    
    # API Endpoints
    path('api/company/<int:company_id>/content/upload/', views.api_upload, name='api_upload'),
    path('api/company/<int:company_id>/content/search/', views.api_search, name='api_search'),
]
