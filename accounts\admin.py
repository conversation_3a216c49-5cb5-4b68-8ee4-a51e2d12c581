from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
# Import guardian admin helpers
from guardian.admin import GuardedModelAdmin
from .models import (
    Company, CompanyInformation, CompanyInvitation as TeamInvitation,
    Membership, RegistrationLink # Removed Role import
)

class CompanyInformationInline(admin.StackedInline):
    model = CompanyInformation
    can_delete = False
    verbose_name_plural = 'Company Information'

class MembershipInline(admin.TabularInline): # Renamed for clarity
    model = Membership # Use the Membership model directly
    fk_name = 'user' # Explicitly state the foreign key to User
    extra = 1
    verbose_name = 'Company Membership'
    verbose_name_plural = 'Company Memberships'
    # Remove role from fields and autocomplete
    fields = ('company', 'date_joined')
    readonly_fields = ('date_joined',)
    autocomplete_fields = ['company']

class CompanyOwnershipInline(admin.TabularInline):
    model = Company
    fk_name = 'owner'
    extra = 1
    verbose_name = 'Owned Company'
    verbose_name_plural = 'Owned Companies'

# Extend User admin
class UserAdmin(BaseUserAdmin):
    inlines = tuple(list(BaseUserAdmin.inlines) + [MembershipInline, CompanyOwnershipInline]) # Use MembershipInline

# Re-register UserAdmin
admin.site.unregister(User)
admin.site.register(User, UserAdmin)

@admin.register(Company)
class CompanyAdmin(GuardedModelAdmin): # Inherit from GuardedModelAdmin
    list_display = ('name', 'owner', 'tier', 'is_featured', 'is_active', 'list_in_directory_status', 'created_at')
    list_filter = ('created_at', 'tier', 'is_featured', 'is_active', 'info__list_in_directory')
    search_fields = ('name', 'owner__username', 'owner__email')
    # Add Membership inline to Company admin as well? Optional.
    # inlines = [CompanyInformationInline, MembershipInline] # If you want to see/add members from Company page
    inlines = [CompanyInformationInline] # Keep it simple for now
    date_hierarchy = 'created_at'
    prepopulated_fields = {'slug': ('name',)} # Add slug prepopulation

    def list_in_directory_status(self, obj):
        """Display whether the company is listed in the directory."""
        if hasattr(obj, 'info') and obj.info:
            return obj.info.list_in_directory
        return False
    list_in_directory_status.short_description = 'Public'
    list_in_directory_status.boolean = True

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('owner')

@admin.register(TeamInvitation)
class TeamInvitationAdmin(admin.ModelAdmin):
    # Remove role from list_display, list_filter, autocomplete_fields
    list_display = ('email', 'company', 'invited_by', 'status', 'invited_at', 'expires_at')
    list_filter = ('status', 'invited_at', 'company')
    search_fields = ('email', 'company__name', 'invited_by__username')
    readonly_fields = ('token', 'invited_at', 'accepted_at')
    autocomplete_fields = ['company', 'invited_by']
    date_hierarchy = 'invited_at'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('company', 'invited_by')

    actions = ['resend_invitations']

    def resend_invitations(self, request, queryset):
        for invitation in queryset:
            # invitation.send_invitation() # Actual email sending logic needed here
            pass # Placeholder
        self.message_user(request, f"Placeholder: Would resend {queryset.count()} invitation emails")
    resend_invitations.short_description = "Resend selected invitations"

# Removed RoleAdmin registration

@admin.register(Membership)
class MembershipAdmin(admin.ModelAdmin):
    # Remove role from list_display, list_filter, search_fields, autocomplete_fields
    list_display = ('user', 'company', 'date_joined')
    list_filter = ('company', 'date_joined')
    search_fields = ('user__username', 'user__email', 'company__name')
    autocomplete_fields = ['user', 'company']
    date_hierarchy = 'date_joined'


@admin.register(RegistrationLink)
class RegistrationLinkAdmin(admin.ModelAdmin):
    # Remove role from list_display, list_filter, search_fields, autocomplete_fields
    list_display = ('company', 'token', 'created_by', 'is_active', 'expires_at', 'uses_count', 'max_uses')
    list_filter = ('company', 'is_active', 'created_at', 'expires_at')
    search_fields = ('company__name', 'created_by__username', 'token')
    readonly_fields = ('token', 'uses_count', 'created_at')
    autocomplete_fields = ['company', 'created_by']
    date_hierarchy = 'created_at'
    actions = ['activate_links', 'deactivate_links']

    def activate_links(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f"Activated {updated} registration links.")
    activate_links.short_description = "Activate selected links"

    def deactivate_links(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f"Deactivated {updated} registration links.")
    deactivate_links.short_description = "Deactivate selected links"
