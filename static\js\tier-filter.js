/**
 * Tier Filter JavaScript
 * Handles filtering of standard tier assistants and companies in the directory
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Tier Filter: Initializing');

    // Apply tier filtering on page load
    setTimeout(applyTierFiltering, 100); // Small delay to ensure DOM is fully loaded

    // Set up a MutationObserver to watch for changes to the DOM
    const observer = new MutationObserver(function(mutations) {
        // Check if any of the mutations involve adding or removing elements
        const shouldReapplyFilter = mutations.some(mutation =>
            mutation.type === 'childList' ||
            (mutation.type === 'attributes' && mutation.attributeName === 'class')
        );

        if (shouldReapplyFilter) {
            applyTierFiltering();
        }
    });

    // Start observing the document body
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class']
    });

    // Also add a direct event listener for URL changes (for when sort_by parameter changes)
    window.addEventListener('popstate', function() {
        console.log('URL changed, reapplying tier filtering');
        setTimeout(applyTierFiltering, 100);
    });

    console.log('Tier Filter: Initialized');
});

/**
 * Apply tier filtering based on the container's data attributes
 */
function applyTierFiltering() {
    console.log('Applying tier filtering');

    // Find the container with the tier filtering settings
    const container = document.querySelector('.company-directory-container');
    if (!container) {
        console.log('Directory container not found');
        return;
    }

    // Get the tier filtering settings and log the raw data attribute values
    console.log('Raw data attributes:', {
        hideStandardTierAssistants: container.dataset.hideStandardTierAssistants,
        hideStandardTierCompanies: container.dataset.hideStandardTierCompanies
    });

    const hideStandardTierAssistants = container.dataset.hideStandardTierAssistants === 'True';
    const hideStandardTierCompanies = container.dataset.hideStandardTierCompanies === 'True';

    console.log(`Tier Filter Settings: hideStandardTierAssistants=${hideStandardTierAssistants}, hideStandardTierCompanies=${hideStandardTierCompanies}`);

    // Apply filtering for assistants
    if (hideStandardTierAssistants) {
        console.log('Hiding standard tier assistants');

        // Check if we're in sort by tier mode
        const isSortByTier = document.querySelector('.tier-section') !== null;
        console.log('Is sort by tier mode:', isSortByTier);

        if (isSortByTier) {
            // Find the standard tier section for assistants
            const standardTierSection = document.querySelector('.tier-section.standard');
            console.log('Standard tier section found:', standardTierSection);

            // Debug all tier sections
            const allTierSections = document.querySelectorAll('.tier-section');
            console.log(`Found ${allTierSections.length} tier sections:`);
            allTierSections.forEach((section, index) => {
                console.log(`Section ${index + 1} classes:`, section.className);
                console.log(`Section ${index + 1} HTML:`, section.outerHTML.substring(0, 100) + '...');
            });

            if (standardTierSection) {
                // Check if there are any featured assistants in this section
                const featuredAssistants = standardTierSection.querySelectorAll('.list-group-item[data-featured="True"]');
                console.log(`Found ${featuredAssistants.length} featured assistants in standard tier`);

                if (featuredAssistants.length > 0) {
                    console.log(`Found ${featuredAssistants.length} featured assistants in standard tier, keeping section visible`);

                    // Hide only non-featured assistants
                    const allAssistants = standardTierSection.querySelectorAll('.list-group-item');
                    allAssistants.forEach(item => {
                        const isFeatured = item.dataset.featured === 'True';
                        if (!isFeatured) {
                            item.style.display = 'none';
                            console.log(`Hiding standard tier assistant: ${item.dataset.name}`);
                        } else {
                            item.style.display = '';
                            console.log(`Keeping featured standard tier assistant: ${item.dataset.name}`);
                        }
                    });
                } else {
                    // No featured assistants, hide the entire section
                    standardTierSection.style.display = 'none';
                    console.log('No featured assistants in standard tier, hiding entire section');
                }
            } else {
                console.log('Standard tier section not found for assistants');
            }
        } else {
            // Not in sort by tier mode, we need to hide individual standard tier cards
            console.log('Not in sort by tier mode, hiding individual standard tier cards');

            const allAssistantCards = document.querySelectorAll('.directory-card[data-tier="standard"]');
            console.log(`Found ${allAssistantCards.length} standard tier assistant cards`);

            allAssistantCards.forEach(card => {
                const isFeatured = card.dataset.featured === 'True';
                if (!isFeatured) {
                    card.style.display = 'none';
                    console.log(`Hiding standard tier assistant card: ${card.dataset.name}`);
                } else {
                    console.log(`Keeping featured standard tier assistant card: ${card.dataset.name}`);
                }
            });
        }
    } else {
        // Show the standard tier section if in tier mode
        const standardTierSection = document.querySelector('.tier-section.standard');
        if (standardTierSection) {
            standardTierSection.style.display = '';

            // Show all assistant items
            const assistantItems = standardTierSection.querySelectorAll('.list-group-item');
            assistantItems.forEach(item => {
                item.style.display = '';
            });
        }

        // Show all standard tier cards if not in tier mode
        const allAssistantCards = document.querySelectorAll('.directory-card[data-tier="standard"]');
        allAssistantCards.forEach(card => {
            card.style.display = '';
        });
    }

    // Apply filtering for companies
    if (hideStandardTierCompanies) {
        console.log('Hiding standard tier companies');

        // Check if we're in sort by tier mode for companies
        const isSortByTier = document.querySelector('.company-tier-section') !== null;
        console.log('Is sort by tier mode for companies:', isSortByTier);

        if (isSortByTier) {
            // Find the standard tier section for companies
            const standardTierSection = document.querySelector('.company-tier-section.standard');
            console.log('Standard tier section found for companies:', standardTierSection);

            if (standardTierSection) {
                // Check if there are any featured companies in this section
                const featuredCompanies = standardTierSection.querySelectorAll('.list-group-item[data-featured="True"]');
                console.log(`Found ${featuredCompanies.length} featured companies in standard tier`);

                if (featuredCompanies.length > 0) {
                    console.log(`Found ${featuredCompanies.length} featured companies in standard tier, keeping section visible`);

                    // Hide only non-featured companies
                    const allCompanies = standardTierSection.querySelectorAll('.list-group-item');
                    allCompanies.forEach(item => {
                        const isFeatured = item.dataset.featured === 'True';
                        if (!isFeatured) {
                            item.style.display = 'none';
                            console.log(`Hiding standard tier company: ${item.dataset.name}`);
                        } else {
                            item.style.display = '';
                            console.log(`Keeping featured standard tier company: ${item.dataset.name}`);
                        }
                    });
                } else {
                    // No featured companies, hide the entire section
                    standardTierSection.style.display = 'none';
                    console.log('No featured companies in standard tier, hiding entire section');
                }
            } else {
                console.log('Standard tier section not found for companies');
            }
        } else {
            // Not in sort by tier mode, we need to hide individual standard tier cards
            console.log('Not in sort by tier mode, hiding individual standard tier company cards');

            const allCompanyCards = document.querySelectorAll('.directory-card[data-tier="standard"]');
            console.log(`Found ${allCompanyCards.length} standard tier company cards`);

            allCompanyCards.forEach(card => {
                const isFeatured = card.dataset.featured === 'True';
                if (!isFeatured) {
                    card.style.display = 'none';
                    console.log(`Hiding standard tier company card: ${card.dataset.name}`);
                } else {
                    console.log(`Keeping featured standard tier company card: ${card.dataset.name}`);
                }
            });
        }
    } else {
        // Show the standard tier section if in tier mode
        const standardTierSection = document.querySelector('.company-tier-section.standard');
        if (standardTierSection) {
            standardTierSection.style.display = '';

            // Show all company items
            const companyItems = standardTierSection.querySelectorAll('.list-group-item');
            companyItems.forEach(item => {
                item.style.display = '';
            });
        }

        // Show all standard tier cards if not in tier mode
        const allCompanyCards = document.querySelectorAll('.directory-card[data-tier="standard"]');
        allCompanyCards.forEach(card => {
            card.style.display = '';
        });
    }
}
