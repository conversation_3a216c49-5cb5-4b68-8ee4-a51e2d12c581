# Deployment Fix Guide

This guide addresses two issues:
1. Migration errors during deployment
2. CSS not loading properly on cPanel

## Part 1: Fixing Migration Errors

You're encountering a migration error where <PERSON><PERSON><PERSON> is trying to add a column that already exists in your database:

```
django.db.utils.ProgrammingError: column "categories" of relation "accounts_companyinformation" already exists
```

### Step 1: Mark the Problematic Migration as Applied

Run the following command to mark the migration as applied without actually running it:

```bash
python mark_migration_applied.py
```

This script will mark the `accounts.0004_companyinformation_categories_and_more` migration as applied in the database without trying to run it.

### Step 2: Continue with Migrations

Run the migrate command again to apply the remaining migrations:

```bash
python manage.py migrate
```

If you encounter more migration errors of the same type, you may need to mark additional migrations as applied by editing the `mark_migration_applied.py` script.

### Step 3: Verify Database State

Check that your database tables are in the expected state:

```bash
python manage.py dbshell
```

Then in the database shell:
```sql
\d accounts_companyinformation
```

This will show the structure of the table, including the `categories` column.

## Part 2: Fixing CSS Loading on cPanel

### Step 1: Update Configuration Files

1. **Create Production Settings File**:
   - We've created `company_assistant/production_settings.py` with optimized settings for cPanel
   - This file includes proper static files configuration

2. **Update WSGI Configuration**:
   - We've modified `passenger_wsgi.py` to use production settings instead of development settings

### Step 2: Upload Files to cPanel

Upload these files to your cPanel environment:
- `company_assistant/production_settings.py` (new file)
- `passenger_wsgi.py` (updated file)

### Step 3: Fix File Permissions

Run the permission fixing script on your cPanel server:
```bash
python fix_permissions.py
```

This will set the correct permissions for all files and directories.

### Step 4: Collect Static Files

Run the static file collection script on your cPanel server:
```bash
python collect_static.py
```

This will collect all static files into the `staticfiles` directory.

### Step 5: Restart the Application

Restart your application in cPanel:
- Go to "Setup Python App" in cPanel and click "Restart App", or
- Use SSH to touch the WSGI file: `touch passenger_wsgi.py`

### Step 6: Clear Browser Cache

Clear your browser cache completely before testing.

## Troubleshooting

### If Migrations Continue to Fail

1. **Check Migration History**:
   ```bash
   python manage.py showmigrations accounts
   ```

2. **Fake Migrations**:
   If needed, you can fake all migrations for an app:
   ```bash
   python manage.py migrate accounts --fake
   ```

### If CSS Still Doesn't Load

1. **Check Static Files Location**:
   ```bash
   ls -la staticfiles/css/
   ```

2. **Check Error Logs**:
   ```bash
   tail -f logs/error.log
   ```

3. **Manual Static Files Fix**:
   ```bash
   mkdir -p ~/public_html/static
   cp -r staticfiles/* ~/public_html/static/
   ```

4. **Check .htaccess Configuration**:
   Make sure your .htaccess file contains the correct rules for static files.

## Additional Resources

- `STATIC_FILES_FIX_GUIDE.md` - More detailed information about static files configuration
- `CPANEL_DEPLOYMENT_GUIDE.md` - General deployment guide for cPanel
- `CPANEL_CSS_FIX_GUIDE.md` - Detailed guide for fixing CSS issues on cPanel
