"""
<PERSON><PERSON><PERSON> to test the context links functionality.
"""

import os
import sys
import django
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'virtualo4.settings')
django.setup()

from django.test import Client
from django.urls import reverse
from assistants.models import Assistant, CommunityContext
from accounts.models import Company
from django.contrib.auth import get_user_model

User = get_user_model()

def test_context_links():
    """Test that context links are included in the assistant response."""
    # Get a community assistant
    assistant = Assistant.objects.filter(assistant_type='community').first()
    
    if not assistant:
        print("No community assistant found. Please create one first.")
        return
    
    # Get the company
    company = assistant.company
    
    # Get or create a test user
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'is_active': True
        }
    )
    
    if created:
        user.set_password('testpassword')
        user.save()
    
    # Create a test client
    client = Client()
    
    # Log in the user
    client.login(username='testuser', password='testpassword')
    
    # Get the URL for the assistant_interact view
    url = reverse('assistants:interact', kwargs={
        'company_id': company.id,
        'assistant_id': assistant.id
    })
    
    # Create a test message
    message = "Tell me about the community context"
    
    # Create a request with community context enabled
    request_data = {
        'message': message,
        'history': [],
        'use_community_context': True
    }
    
    # Send the request
    response = client.post(
        url,
        data=json.dumps(request_data),
        content_type='application/json'
    )
    
    # Check that the response is successful
    if response.status_code == 200:
        print("Response successful!")
        response_data = json.loads(response.content)
        print(f"Response content: {response_data.get('content')}")
        
        # Check if used_contexts is in the response
        used_contexts = response_data.get('used_contexts', [])
        if used_contexts:
            print(f"Used contexts: {used_contexts}")
        else:
            print("No contexts were used in the response.")
            
        # Test the context content endpoint
        if used_contexts:
            context_id = used_contexts[0]['id']
            context_url = reverse('assistants:get_context_content', kwargs={
                'company_id': company.id,
                'assistant_id': assistant.id,
                'context_id': context_id
            })
            
            context_response = client.get(context_url)
            if context_response.status_code == 200:
                context_data = json.loads(context_response.content)
                print(f"Context content: {context_data.get('content')}")
            else:
                print(f"Context content request failed with status code {context_response.status_code}")
                print(f"Response content: {context_response.content}")
    else:
        print(f"Response failed with status code {response.status_code}")
        print(f"Response content: {response.content}")

if __name__ == '__main__':
    test_context_links()
