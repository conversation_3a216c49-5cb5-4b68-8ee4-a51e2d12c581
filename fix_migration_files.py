"""
<PERSON><PERSON>t to fix migration files by modifying them directly.
"""
import os
import sys
import re
import django
from django.core.management import call_command

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

def check_migration_status():
    """Check the status of migrations for the assistants app."""
    print("Checking migration status for assistants app...")
    try:
        call_command('showmigrations', 'assistants')
        return True
    except Exception as e:
        print(f"Error checking migration status: {e}")
        return False

def fix_migration_file(file_path):
    """Fix a migration file by modifying it directly."""
    print(f"Fixing migration file: {file_path}")
    try:
        # Read the file
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check if the file contains references to NavigationItem
        if 'NavigationItem' in content:
            # Add a do_nothing function if it doesn't exist
            if 'def do_nothing(' not in content:
                do_nothing_func = '''
def do_nothing(apps, schema_editor):
    """
    This function does nothing.
    We're using it because the model doesn't exist in the state.
    """
    pass
'''
                # Add the function after the imports
                content = re.sub(r'(from django.db import migrations.*?\n)',
                                r'\1\n' + do_nothing_func,
                                content,
                                flags=re.DOTALL)
            
            # Replace operations that reference NavigationItem with RunPython
            content = re.sub(r'(migrations\.AddField\([^)]*?model_name=[\'"]navigationitem[\'"][^)]*?\),)',
                            r'# \1\n        migrations.RunPython(do_nothing, do_nothing),',
                            content)
            
            content = re.sub(r'(migrations\.AlterField\([^)]*?model_name=[\'"]navigationitem[\'"][^)]*?\),)',
                            r'# \1\n        migrations.RunPython(do_nothing, do_nothing),',
                            content)
            
            content = re.sub(r'(migrations\.RemoveField\([^)]*?model_name=[\'"]navigationitem[\'"][^)]*?\),)',
                            r'# \1\n        migrations.RunPython(do_nothing, do_nothing),',
                            content)
            
            content = re.sub(r'(migrations\.CreateModel\(\s*name=[\'"]NavigationItem[\'"][^)]*?\),)',
                            r'# \1\n        migrations.RunPython(do_nothing, do_nothing),',
                            content,
                            flags=re.DOTALL)
            
            # Write the modified content back to the file
            with open(file_path, 'w') as f:
                f.write(content)
            
            print(f"Fixed migration file: {file_path}")
            return True
        else:
            print(f"No references to NavigationItem found in {file_path}")
            return True
    except Exception as e:
        print(f"Error fixing migration file {file_path}: {e}")
        return False

def find_migration_files():
    """Find all migration files in the assistants app."""
    print("Finding migration files...")
    try:
        migration_dir = os.path.join('assistants', 'migrations')
        migration_files = []
        
        for filename in os.listdir(migration_dir):
            if filename.endswith('.py') and not filename.startswith('__'):
                migration_files.append(os.path.join(migration_dir, filename))
        
        return migration_files
    except Exception as e:
        print(f"Error finding migration files: {e}")
        return []

def main():
    """Main function to fix migration files."""
    print("Starting migration file fix...")
    
    # Check migration status
    check_migration_status()
    
    # Find migration files
    migration_files = find_migration_files()
    
    # Fix each migration file
    for file_path in migration_files:
        fix_migration_file(file_path)
    
    # Check migration status again
    check_migration_status()
    
    print("\nNext steps:")
    print("1. Run 'python manage.py migrate --fake-initial' to apply the migrations")
    print("2. If you still encounter issues, try 'python manage.py migrate --fake'")

if __name__ == "__main__":
    main()
