import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

print("Starting script to check content tables...")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

# Connect to the database
try:
    print("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(**db_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    
    # Check if the content_content table exists
    print("Checking if content_content table exists...")
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = 'content_content'
        );
    """)
    content_table_exists = cursor.fetchone()[0]
    
    if content_table_exists:
        print("The content_content table exists!")
        
        # Check the table structure
        cursor.execute("""
            SELECT column_name, data_type, character_maximum_length
            FROM information_schema.columns
            WHERE table_name = 'content_content'
            ORDER BY ordinal_position;
        """)
        columns = cursor.fetchall()
        
        print("\nTable structure:")
        print("Column Name | Data Type | Max Length")
        print("-" * 50)
        for column in columns:
            print(f"{column[0]} | {column[1]} | {column[2]}")
    else:
        print("WARNING: The content_content table does NOT exist!")
    
    # Check if the content_contentimage table exists
    print("\nChecking if content_contentimage table exists...")
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = 'content_contentimage'
        );
    """)
    contentimage_table_exists = cursor.fetchone()[0]
    
    if contentimage_table_exists:
        print("The content_contentimage table exists!")
        
        # Check the table structure
        cursor.execute("""
            SELECT column_name, data_type, character_maximum_length
            FROM information_schema.columns
            WHERE table_name = 'content_contentimage'
            ORDER BY ordinal_position;
        """)
        columns = cursor.fetchall()
        
        print("\nTable structure:")
        print("Column Name | Data Type | Max Length")
        print("-" * 50)
        for column in columns:
            print(f"{column[0]} | {column[1]} | {column[2]}")
    else:
        print("WARNING: The content_contentimage table does NOT exist!")
    
    # Close the connection
    cursor.close()
    conn.close()
    print("\nDatabase connection closed.")
    
except Exception as e:
    print(f"Error: {e}")
