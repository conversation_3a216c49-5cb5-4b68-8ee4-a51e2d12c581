/**
 * Featured Carousel JavaScript
 * Handles continuous scrolling carousel functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('[DEBUG] Featured carousel script loaded');

    // Get all featured carousel containers
    const carousels = document.querySelectorAll('.featured-carousel-container');

    console.log('[DEBUG] Found', carousels.length, 'featured carousels');

    // Log details about each carousel
    carousels.forEach((carousel, index) => {
        console.log(`[DEBUG] Carousel ${index} details:`, {
            id: carousel.id,
            hasItems: carousel.dataset.hasItems,
            visibleCount: carousel.dataset.visibleCount,
            animationDelay: carousel.dataset.animationDelay,
            childrenCount: carousel.children.length
        });

        // Check if the carousel has items
        const itemsContainer = carousel.querySelector('.featured-carousel-items');
        if (itemsContainer) {
            const items = itemsContainer.querySelectorAll('.featured-carousel-item');
            console.log(`[DEBUG] Carousel ${index} has ${items.length} items`);

            // Log the first few items
            items.forEach((item, i) => {
                if (i < 3) { // Only log the first 3 items to avoid console spam
                    const itemName = item.querySelector('.item-info h5')?.textContent || 'No name';
                    const itemLogo = item.querySelector('.logo-container img') ? 'Has logo' : 'No logo';
                    console.log(`[DEBUG] Item ${i}: ${itemName} (${itemLogo})`);
                }
            });
        } else {
            console.log(`[DEBUG] Carousel ${index} has no items container`);
        }
    });

    // If no carousels found, check if the featured section exists
    if (carousels.length === 0) {
        console.warn('[DEBUG] No carousel containers found. Checking for featured sections...');
        const featuredSections = document.querySelectorAll('.featured-section');
        console.log('[DEBUG] Found', featuredSections.length, 'featured sections');

        // Check if there's an alert message indicating no featured assistants
        const noFeaturedAlert = document.querySelector('.featured-section .alert-info');
        if (noFeaturedAlert) {
            console.log('[DEBUG] Found alert message:', noFeaturedAlert.textContent.trim());
        }

        return; // Exit early if no carousels found
    }

    carousels.forEach((carouselContainer, index) => {
        // Check if this carousel has items (using data attribute)
        const hasItems = carouselContainer.dataset.hasItems === 'true';
        if (!hasItems) {
            console.log('[DEBUG] Carousel', index, 'has no items, skipping initialization');
            return; // Skip initialization for carousels with no items
        }

        console.log('[DEBUG] Initializing carousel', index);
        const carousel = carouselContainer.querySelector('.featured-carousel-items');

        if (!carousel) {
            console.error('[DEBUG] No carousel items container found for carousel', index);
            return;
        }

        // Count the carousel items
        const allItems = carousel.querySelectorAll('.featured-carousel-item');
        console.log('[DEBUG] Carousel items count:', allItems.length);

        // Log the HTML content of the carousel for debugging
        console.log('[DEBUG] Carousel HTML content:', carousel.innerHTML);

        if (allItems.length === 0) {
            console.warn('[DEBUG] No items found in carousel', index);

            // Check if there are any direct child elements that might not have the correct class
            const directChildren = carousel.children;
            console.log('[DEBUG] Direct children count:', directChildren.length);

            if (directChildren.length > 0) {
                console.log('[DEBUG] First child HTML:', directChildren[0].outerHTML);
                console.log('[DEBUG] First child classList:', Array.from(directChildren[0].classList));

                // If there are children but they don't have the correct class, add it
                if (directChildren.length > 0 && !directChildren[0].classList.contains('featured-carousel-item')) {
                    console.log('[DEBUG] Adding featured-carousel-item class to direct children');
                    Array.from(directChildren).forEach(child => {
                        child.classList.add('featured-carousel-item');
                    });

                    // Re-count after adding the class
                    const updatedItems = carousel.querySelectorAll('.featured-carousel-item');
                    console.log('[DEBUG] Updated carousel items count:', updatedItems.length);

                    // If we still have no items, return
                    if (updatedItems.length === 0) {
                        return;
                    }
                } else {
                    return; // Skip initialization if no items
                }
            } else {
                return; // Skip initialization if no items
            }
        }

        // Check for empty or invalid items and fix them if possible
        allItems.forEach((item, i) => {
            // Check if the item has a wrapper
            const wrapper = item.querySelector('.featured-item-wrapper');
            if (!wrapper) {
                console.warn(`[DEBUG] Item ${i} has no wrapper, attempting to fix`);

                // Create a wrapper if it doesn't exist
                const newWrapper = document.createElement('div');
                newWrapper.className = 'featured-item-wrapper';

                // Move all child elements into the wrapper
                while (item.firstChild) {
                    newWrapper.appendChild(item.firstChild);
                }

                // Add the wrapper back to the item
                item.appendChild(newWrapper);
            }
        });

        // Log details about each item for debugging
        allItems.forEach((item, i) => {
            const wrapper = item.querySelector('.featured-item-wrapper');
            const link = item.querySelector('a');
            const logo = item.querySelector('.logo-container img');
            const logoPlaceholder = item.querySelector('.logo-placeholder');
            const title = item.querySelector('.item-info h5');

            console.log(`[DEBUG] Item ${i}:`, {
                hasWrapper: !!wrapper,
                linkHref: link ? link.getAttribute('href') : 'no link',
                hasLogo: !!logo,
                hasPlaceholder: !!logoPlaceholder,
                title: title ? title.textContent : 'no title'
            });
        });

        // Check if animation is applied
        const computedStyle = window.getComputedStyle(carousel);
        console.log('[DEBUG] Carousel computed style animation:', computedStyle.animation);

        // Check if there's only one item in the carousel
        if (allItems.length === 1) {
            console.log('[DEBUG] Only one item in carousel, centering it and disabling animation');

            // Center the single item
            carousel.style.animation = 'none';
            carousel.style.display = 'flex';
            carousel.style.justifyContent = 'center';

            // Make the container wider to accommodate the single item
            const item = allItems[0];
            if (item) {
                const wrapper = item.querySelector('.featured-item-wrapper');
                if (wrapper) {
                    wrapper.style.margin = '0 auto';
                }
            }
        }
        // Ensure animation is applied for multiple items
        else if (!computedStyle.animation || computedStyle.animation === 'none') {
            console.log('[DEBUG] Applying animation to carousel', index);

            // Check if browser supports calc() in CSS
            const testElem = document.createElement('div');
            testElem.style.width = 'calc(10px + 10px)';
            const supportsCalc = testElem.style.width === '' ? false : true;

            if (supportsCalc) {
                carousel.style.animation = 'scroll 60s linear infinite';
            } else {
                carousel.style.animation = 'scroll-fallback 60s linear infinite';
            }

            console.log('[DEBUG] Browser supports calc():', supportsCalc);
        }

        // Force remove any !important inline styles that might be interfering
        // Use a more aggressive approach to remove inline styles
        try {
            // First try to remove the property directly
            carousel.style.removeProperty('animation-play-state');

            // Then try to override with empty string
            carousel.style.animationPlayState = '';

            // For assistants list page, use a more aggressive approach
            if (window.location.href.includes('/directory/assistants/')) {
                console.log('[DEBUG] Detected assistants list page, using aggressive style override');

                // Remove all inline styles and reapply only what we need
                const originalAnimation = carousel.style.animation;
                const originalWidth = carousel.style.width;
                const originalMinHeight = carousel.style.minHeight;

                // Clear all inline styles
                carousel.setAttribute('style', '');

                // Reapply only the essential styles
                carousel.style.display = 'flex';
                carousel.style.animation = originalAnimation || 'scroll 60s linear infinite';
                carousel.style.width = originalWidth || 'max-content';
                carousel.style.minHeight = originalMinHeight || '300px';

                console.log('[DEBUG] Aggressively reset styles for assistants list carousel');
            }

            console.log('[DEBUG] Removed animation-play-state inline style');
        } catch (e) {
            console.error('[DEBUG] Error removing animation-play-state:', e);
        }

        // Pause animation on hover over the container or any item
        carouselContainer.addEventListener('mouseenter', () => {
            // Special handling for assistants list page
            if (window.location.href.includes('/directory/assistants/')) {
                // Use direct style manipulation for assistants list
                carousel.style.animationPlayState = 'paused';
                // Also try with !important as a fallback
                carousel.setAttribute('style', carousel.getAttribute('style') + '; animation-play-state: paused !important;');
                console.log('[DEBUG] Assistants list carousel animation paused (container hover)');
            } else {
                // Use setAttribute to override any !important styles
                carousel.setAttribute('style', carousel.getAttribute('style') + '; animation-play-state: paused !important;');
                console.log('[DEBUG] Carousel animation paused (container hover)');
            }
        });

        // Resume animation when mouse leaves the container
        carouselContainer.addEventListener('mouseleave', () => {
            // Special handling for assistants list page
            if (window.location.href.includes('/directory/assistants/')) {
                // Use direct style manipulation for assistants list
                carousel.style.animationPlayState = 'running';
                // Also try with !important as a fallback
                carousel.setAttribute('style', carousel.getAttribute('style') + '; animation-play-state: running !important;');
                console.log('[DEBUG] Assistants list carousel animation resumed (container leave)');
            } else {
                // Use setAttribute to override any !important styles
                carousel.setAttribute('style', carousel.getAttribute('style') + '; animation-play-state: running !important;');
                console.log('[DEBUG] Carousel animation resumed (container leave)');
            }
        });

        // Add hover events to individual carousel items
        allItems.forEach((item) => {
            item.addEventListener('mouseenter', () => {
                // Special handling for assistants list page
                if (window.location.href.includes('/directory/assistants/')) {
                    // Use direct style manipulation for assistants list
                    carousel.style.animationPlayState = 'paused';
                    // Also try with !important as a fallback
                    carousel.setAttribute('style', carousel.getAttribute('style') + '; animation-play-state: paused !important;');
                    console.log('[DEBUG] Assistants list carousel animation paused (item hover)');
                } else {
                    // Use setAttribute to override any !important styles
                    carousel.setAttribute('style', carousel.getAttribute('style') + '; animation-play-state: paused !important;');
                    console.log('[DEBUG] Carousel animation paused (item hover)');
                }
            });

            item.addEventListener('mouseleave', () => {
                // Only resume if not hovering over the container
                if (!carouselContainer.matches(':hover')) {
                    // Special handling for assistants list page
                    if (window.location.href.includes('/directory/assistants/')) {
                        // Use direct style manipulation for assistants list
                        carousel.style.animationPlayState = 'running';
                        // Also try with !important as a fallback
                        carousel.setAttribute('style', carousel.getAttribute('style') + '; animation-play-state: running !important;');
                        console.log('[DEBUG] Assistants list carousel animation resumed (item leave)');
                    } else {
                        // Use setAttribute to override any !important styles
                        carousel.setAttribute('style', carousel.getAttribute('style') + '; animation-play-state: running !important;');
                        console.log('[DEBUG] Carousel animation resumed (item leave)');
                    }
                }
            });
        });

        // Get the number of items to display from data attribute
        const visibleCount = parseInt(carouselContainer.dataset.visibleCount || 3, 10);
        console.log('[DEBUG] Visible count:', visibleCount);

        // Adjust the width of the carousel items based on visible count
        if (visibleCount > 0) {
            const items = carousel.querySelectorAll('.featured-carousel-item');
            const containerWidth = carouselContainer.offsetWidth;
            const itemWidth = Math.floor(containerWidth / visibleCount) - 60; // 60px for margins

            // Ensure minimum width for items
            const finalWidth = Math.max(itemWidth, 240); // Minimum width of 240px

            console.log('[DEBUG] Container width:', containerWidth, 'Calculated width:', itemWidth, 'Final width:', finalWidth);

            items.forEach((item, i) => {
                const wrapper = item.querySelector('.featured-item-wrapper');
                if (wrapper) {
                    wrapper.style.width = `${finalWidth}px`;
                    console.log(`[DEBUG] Set item ${i} width to ${finalWidth}px`);

                    // Ensure the item has proper styling
                    item.style.margin = '0 30px';
                    item.style.display = 'flex';
                    item.style.alignItems = 'flex-start';
                    item.style.justifyContent = 'center';
                } else {
                    console.warn(`[DEBUG] No wrapper found for item ${i}`);
                }
            });
        }

        // Add event listener for like buttons in the carousel
        carousel.addEventListener('click', (event) => {
            const likeButton = event.target.closest('.like-button');
            if (!likeButton) return; // Not a like button click

            // Prevent the click from bubbling up to the carousel item link
            event.preventDefault();
            event.stopPropagation();

            console.log('[DEBUG] Like button clicked:', {
                itemId: likeButton.dataset.itemId,
                itemType: likeButton.dataset.itemType
            });

            // The rest of the like button handling is in the main script
        });
    });
});
