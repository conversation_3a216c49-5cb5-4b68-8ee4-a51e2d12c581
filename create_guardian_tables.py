import os
import django
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

print("Starting script to create guardian tables...")

# SQL to create the guardian_userobjectpermission table
userobjectpermission_sql = """
CREATE TABLE IF NOT EXISTS "guardian_userobjectpermission" (
    "id" serial NOT NULL PRIMARY KEY,
    "object_pk" varchar(255) NOT NULL,
    "content_type_id" integer NOT NULL REFERENCES "django_content_type" ("id") DEFERRABLE INITIALLY DEFERRED,
    "permission_id" integer NOT NULL REFERENCES "auth_permission" ("id") DEFERRABLE INITIALLY DEFERRED,
    "user_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT "guardian_userobjectpermission_user_id_permission_id_object_pk_unique" UNIQUE ("user_id", "permission_id", "object_pk")
);
"""

# SQL to create the guardian_groupobjectpermission table
groupobjectpermission_sql = """
CREATE TABLE IF NOT EXISTS "guardian_groupobjectpermission" (
    "id" serial NOT NULL PRIMARY KEY,
    "object_pk" varchar(255) NOT NULL,
    "content_type_id" integer NOT NULL REFERENCES "django_content_type" ("id") DEFERRABLE INITIALLY DEFERRED,
    "permission_id" integer NOT NULL REFERENCES "auth_permission" ("id") DEFERRABLE INITIALLY DEFERRED,
    "group_id" integer NOT NULL REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT "guardian_groupobjectpermission_group_id_permission_id_object_pk_unique" UNIQUE ("group_id", "permission_id", "object_pk")
);
"""

# SQL to create indexes
indexes_sql = """
CREATE INDEX IF NOT EXISTS "guardian_userobjectpermission_content_type_id_object_pk_idx" ON "guardian_userobjectpermission" ("content_type_id", "object_pk");
CREATE INDEX IF NOT EXISTS "guardian_userobjectpermission_user_id_idx" ON "guardian_userobjectpermission" ("user_id");
CREATE INDEX IF NOT EXISTS "guardian_userobjectpermission_permission_id_idx" ON "guardian_userobjectpermission" ("permission_id");

CREATE INDEX IF NOT EXISTS "guardian_groupobjectpermission_content_type_id_object_pk_idx" ON "guardian_groupobjectpermission" ("content_type_id", "object_pk");
CREATE INDEX IF NOT EXISTS "guardian_groupobjectpermission_group_id_idx" ON "guardian_groupobjectpermission" ("group_id");
CREATE INDEX IF NOT EXISTS "guardian_groupobjectpermission_permission_id_idx" ON "guardian_groupobjectpermission" ("permission_id");
"""

# Execute the SQL
try:
    with connection.cursor() as cursor:
        print("Creating guardian_userobjectpermission table...")
        cursor.execute(userobjectpermission_sql)
        print("guardian_userobjectpermission table created successfully!")
        
        print("Creating guardian_groupobjectpermission table...")
        cursor.execute(groupobjectpermission_sql)
        print("guardian_groupobjectpermission table created successfully!")
        
        print("Creating indexes...")
        cursor.execute(indexes_sql)
        print("Indexes created successfully!")
    
    print("All guardian tables created successfully!")
except Exception as e:
    print(f"Error creating tables: {e}")
    sys.exit(1)
