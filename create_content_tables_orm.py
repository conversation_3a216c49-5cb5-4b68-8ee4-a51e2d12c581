import os
import django
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection and ContentType model
from django.db import connection
from django.contrib.contenttypes.models import ContentType
from django.apps import apps

print("Starting script to create content tables using Django ORM...")

# Get the Content and ContentImage models
try:
    Content = apps.get_model('content', 'Content')
    ContentImage = apps.get_model('content', 'ContentImage')
    ContentVersion = apps.get_model('content', 'ContentVersion')
    
    print(f"Content model: {Content}")
    print(f"ContentImage model: {ContentImage}")
    print(f"ContentVersion model: {ContentVersion}")
    
    # Create the tables using Django's ORM
    with connection.schema_editor() as schema_editor:
        print("Creating content_content table...")
        schema_editor.create_model(Content)
        print("Content table created successfully!")
        
        print("Creating content_contentimage table...")
        schema_editor.create_model(ContentImage)
        print("ContentImage table created successfully!")
        
        print("Creating content_contentversion table...")
        schema_editor.create_model(ContentVersion)
        print("ContentVersion table created successfully!")
    
    print("All content tables created successfully!")
except Exception as e:
    print(f"Error creating tables: {e}")
    sys.exit(1)
