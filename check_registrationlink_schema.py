import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

print("Starting script to check accounts_registrationlink table structure...")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

# SQL to check the table structure
check_table_sql = """
SELECT column_name, data_type, character_maximum_length
FROM information_schema.columns
WHERE table_name = 'accounts_registrationlink'
ORDER BY ordinal_position;
"""

# Connect to the database
try:
    print("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(**db_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    
    # Check if the table exists
    print("Checking if accounts_registrationlink table exists...")
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = 'accounts_registrationlink'
        );
    """)
    table_exists = cursor.fetchone()[0]
    
    if table_exists:
        print("The accounts_registrationlink table exists!")
        
        # Check the table structure
        print("Checking accounts_registrationlink table structure...")
        cursor.execute(check_table_sql)
        columns = cursor.fetchall()
        
        print("\nTable structure:")
        print("Column Name | Data Type | Max Length")
        print("-" * 50)
        for column in columns:
            print(f"{column[0]} | {column[1]} | {column[2]}")
    else:
        print("WARNING: The accounts_registrationlink table does NOT exist!")
    
    # Check if the many-to-many table exists
    print("\nChecking if accounts_registrationlink_accessible_folders table exists...")
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = 'accounts_registrationlink_accessible_folders'
        );
    """)
    m2m_table_exists = cursor.fetchone()[0]
    
    if m2m_table_exists:
        print("The accounts_registrationlink_accessible_folders table exists!")
        
        # Check the table structure
        print("Checking accounts_registrationlink_accessible_folders table structure...")
        cursor.execute("""
            SELECT column_name, data_type, character_maximum_length
            FROM information_schema.columns
            WHERE table_name = 'accounts_registrationlink_accessible_folders'
            ORDER BY ordinal_position;
        """)
        columns = cursor.fetchall()
        
        print("\nTable structure:")
        print("Column Name | Data Type | Max Length")
        print("-" * 50)
        for column in columns:
            print(f"{column[0]} | {column[1]} | {column[2]}")
    else:
        print("WARNING: The accounts_registrationlink_accessible_folders table does NOT exist!")
    
    # Close the connection
    cursor.close()
    conn.close()
    print("\nDatabase connection closed.")
    
except Exception as e:
    print(f"Error: {e}")
