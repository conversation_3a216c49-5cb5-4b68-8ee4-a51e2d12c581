"""
WSGI configuration for local testing with the same settings as cPanel
"""
import os
import sys

# Add the project directory to the Python path
sys.path.insert(0, os.path.dirname(__file__))

# Set the Django settings module to use the standard settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')

# Set environment variable to indicate we're in a cPanel-like environment
# This will enable file-based cache and sessions locally
os.environ['CPANEL_ENV'] = 'True'

# Set DEBUG to True for local testing
os.environ['DEBUG'] = 'True'

# Create necessary directories
BASE_DIR = os.path.dirname(__file__)
for directory in ['logs', 'cache', 'token_storage', 'media', 'session']:
    os.makedirs(os.path.join(BASE_DIR, directory), exist_ok=True)

# Set permissions for session directory
session_dir = os.path.join(BASE_DIR, 'session')
try:
    import stat
    os.chmod(session_dir, stat.S_IRWXU | stat.S_IRWXG | stat.S_IRWXO)  # 0777 permissions
except Exception as e:
    print(f"Warning: Could not set permissions for session directory: {e}")

# Print confirmation message
print("Running in local test mode with cPanel-like settings")
print(f"Session directory: {session_dir}")
print(f"Cache directory: {os.path.join(BASE_DIR, 'cache')}")
print(f"Token storage directory: {os.path.join(BASE_DIR, 'token_storage')}")

# Import the WSGI application
from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()