{% load static rating_tags %}

<div class="company-header-actions d-flex align-items-center">
    <!-- Rating Display -->
    <div class="me-3">
        <div id="rating-display-{{ company.id }}">
            {% if company_listing %}
                {% render_stars company_listing.avg_rating company_listing.total_ratings %}
            {% else %}
                {% render_stars 0 0 %}
            {% endif %}
        </div>
        {% if user.is_authenticated %}
            <button type="button" class="btn btn-sm btn-link text-decoration-none p-0 mt-1"
                    data-bs-toggle="modal" data-bs-target="#ratingModal"
                    data-company-id="{{ company.id }}"
                    data-company-name="{{ company.name }}">
                <i class="bi bi-star me-1"></i>Rate
            </button>
        {% endif %}
    </div>
    
    <!-- Like Button -->
    {% if user.is_authenticated %}
        <button
            class="like-button btn btn-sm p-1 {% if is_favorited %}text-danger{% else %}text-secondary{% endif %}"
            data-item-id="{{ company.id }}"
            data-item-type="company"
            title="{% if is_favorited %}Remove from Favorites{% else %}Add to Favorites{% endif %}"
            style="background: none; border: none;">
            <i class="bi bi-heart{% if is_favorited %}-fill{% endif %} me-1"></i>
            {% if is_favorited %}Favorited{% else %}Favorite{% endif %}
        </button>
    {% endif %}
    
    <!-- Share Button -->
    <button class="btn btn-sm btn-outline-secondary ms-2" id="shareButton">
        <i class="bi bi-share me-1"></i> Share
    </button>
    
    <!-- QR Code Button -->
    <button class="btn btn-sm btn-outline-secondary ms-2" data-bs-toggle="modal" data-bs-target="#qrCodeModal">
        <i class="bi bi-qr-code me-1"></i> QR
    </button>
</div>

<!-- JavaScript for handling the share button -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const shareButton = document.getElementById('shareButton');
    if (shareButton) {
        shareButton.addEventListener('click', function() {
            if (navigator.share) {
                navigator.share({
                    title: '{{ company.name }}',
                    text: '{{ company.info.description|default:"Check out this company!" }}',
                    url: window.location.href
                })
                .then(() => console.log('Share successful'))
                .catch((error) => console.log('Error sharing:', error));
            } else {
                // Fallback for browsers that don't support the Web Share API
                const tempInput = document.createElement('input');
                tempInput.value = window.location.href;
                document.body.appendChild(tempInput);
                tempInput.select();
                document.execCommand('copy');
                document.body.removeChild(tempInput);
                alert('URL copied to clipboard!');
            }
        });
    }
});
</script>
