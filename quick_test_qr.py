#!/usr/bin/env python
"""
Quick test to generate a single QR code with the improved bold and centered letter A.
Enhanced with additional validation and testing features.
"""

import os
import django
import time

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from utils.qr_generator import generate_qr_with_a, QR_LETTER_VERTICAL_OFFSET

def test_qr_generation():
    """Generate and validate a QR code"""
    print("🔧 Quick QR Code Test - FINAL FIX: Completely Straight Lines")
    print("=" * 65)

    # Generate test QR code
    test_url = "https://24seven.site/final-straight-fix"
    print(f"Generating QR code for: {test_url}")
    print(f"Using vertical offset: {QR_LETTER_VERTICAL_OFFSET} pixels")

    try:
        start_time = time.time()
        qr_img = generate_qr_with_a(test_url, letter="A")
        generation_time = time.time() - start_time

        # Save the QR code
        output_file = "quick_test_qr_final.png"
        qr_img.save(output_file)

        # Get file information
        file_size = os.path.getsize(output_file)
        img_width, img_height = qr_img.size

        print(f"✅ QR code saved as: {output_file}")
        print(f"📏 Dimensions: {img_width}x{img_height} pixels")
        print(f"📁 File size: {file_size} bytes")
        print(f"⏱️  Generation time: {generation_time:.3f} seconds")
        print(f"🎨 Image mode: {qr_img.mode}")

        # Validate dimensions
        expected_size = 290  # Version 1 QR with box_size=10 and border=4
        if abs(img_width - expected_size) <= 10 and abs(img_height - expected_size) <= 10:
            print(f"✅ Dimensions are correct (expected ~{expected_size}x{expected_size})")
        else:
            print(f"⚠️  Dimensions may be unexpected (expected ~{expected_size}x{expected_size})")

        print()
        print("🎯 What to look for:")
        print("   ✓ BLACK LETTER A: NO V-dip at top (completely straight)")
        print("   ✓ BLACK LETTER A: Straight bottom legs (no angles)")
        print("   ✓ BLACK LETTER A: All lines are perfectly straight rectangles")
        print("   ✓ BLACK LETTER A: Bold and thick appearance")
        print("   ✓ WHITE BACKGROUND: Clean letter A (10% larger)")
        print("   ✓ WHITE BACKGROUND: Straight lines, no messy areas")
        print("   ✓ POSITIONING: Perfect centering, no interference")
        print()
        print("🔧 Technical improvements:")
        print("   - BLACK LETTER: Built with straight rectangles only")
        print("   - NO V-SHAPES: Top is a straight horizontal line")
        print("   - STRAIGHT LEGS: Bottom legs are straight horizontal lines")
        print("   - CLEAN GEOMETRY: All components are rectangles")
        print("   - WHITE BACKGROUND: Proper letter A shape, larger size")
        print()
        print("📱 Test by scanning the QR code to make sure it still works!")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_letters():
    """Test with different letters for comparison"""
    print("\n" + "=" * 65)
    print("🔤 Testing Multiple Letters")
    print("=" * 65)

    letters = ["A", "B", "X"]
    test_url = "https://24seven.site/test-letter"

    for letter in letters:
        try:
            print(f"Testing letter: {letter}")
            qr_img = generate_qr_with_a(f"{test_url}-{letter.lower()}", letter=letter)

            output_file = f"quick_test_letter_{letter}.png"
            qr_img.save(output_file)

            file_size = os.path.getsize(output_file)
            print(f"   ✅ Letter '{letter}': {output_file} ({file_size} bytes)")

        except Exception as e:
            print(f"   ❌ Letter '{letter}': Error - {e}")

def main():
    """Run the quick QR code test"""
    success = test_qr_generation()

    if success:
        # Ask if user wants to test multiple letters
        try:
            test_more = input("\nTest additional letters? (y/n): ").lower().strip()
            if test_more in ['y', 'yes']:
                test_multiple_letters()
        except KeyboardInterrupt:
            print("\nTest completed.")

    print("\n🏁 Quick test completed!")

if __name__ == "__main__":
    main()
