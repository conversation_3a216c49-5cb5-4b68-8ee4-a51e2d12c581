/**
 * Company List Tablet Mode Fixes
 * Specific fixes for company list cards in tablet mode
 */

/* Tablet optimizations (between 768px and 992px) */
@media (min-width: 769px) and (max-width: 991.98px) {
  /* Fix for company list cards in tablet mode */
  .company-directory-page .directory-card {
    display: flex !important;
    flex-direction: column !important;
    min-height: 180px !important;
    height: auto !important;
  }

  /* Fix for list-group-item height */
  .company-directory-page .list-group-item {
    height: auto !important;
    min-height: 180px !important;
  }

  .company-directory-page .directory-card .row {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    width: 100% !important;
    height: 100% !important;
  }

  /* Fix for company list card link wrapper */
  .company-directory-page .directory-item-link-wrapper {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    width: 70% !important;
  }

  /* Fix for company list card logo column */
  .company-directory-page .directory-item-link-wrapper .col-md-3 {
    width: 120px !important;
    min-width: 120px !important;
    max-width: 120px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }

  /* Fix for company list card name column */
  .company-directory-page .directory-item-link-wrapper .col-md-4 {
    width: 30% !important;
    min-width: 30% !important;
    padding-left: 1rem !important;
  }

  /* Fix for company list card description column */
  .company-directory-page .directory-item-link-wrapper .col-md-5 {
    width: 50% !important;
    padding-left: 1rem !important;
  }

  /* Fix for company list card contact info column */
  .company-directory-page .directory-card .col-md-2.d-flex.flex-column.justify-content-center {
    width: 15% !important;
    min-width: 15% !important;
    /* No color or font styling here - that's handled in tablet-mobile-contact-fix.css */
  }

  /* Fix for company list card rating column */
  .company-directory-page .directory-card .col-md-2.d-flex.flex-column.align-items-end {
    width: 15% !important;
    min-width: 15% !important;
  }

  /* Fix for company list card text */
  .company-directory-page .directory-card h6 {
    font-size: 1rem !important;
    margin-bottom: 0.5rem !important;
    white-space: normal !important;
  }

  .company-directory-page .directory-card p {
    font-size: 0.85rem !important;
    margin-bottom: 0.5rem !important;
    white-space: normal !important;
  }

  .company-directory-page .directory-card .item-description {
    max-height: 80px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 4 !important;
    -webkit-box-orient: vertical !important;
  }

  /* Dark mode fixes for company list cards in tablet mode */
  [data-theme="dark"] .company-directory-page .directory-card {
    background: linear-gradient(145deg, #1e1e1e, #252525) !important;
    border: 1px solid #333333 !important;
  }

  [data-theme="dark"] .company-directory-page .directory-card h6 {
    color: #ffffff !important;
  }

  [data-theme="dark"] .company-directory-page .directory-card .text-muted {
    color: #cccccc !important;
  }

  [data-theme="dark"] .company-directory-page .directory-card .item-description {
    color: #cccccc !important;
  }

  /* Contact info text color is handled in tablet-mobile-contact-fix.css */
}
