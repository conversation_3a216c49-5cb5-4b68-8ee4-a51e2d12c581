from django.contrib import admin
from .models import SiteConfiguration

@admin.register(SiteConfiguration)
class SiteConfigurationAdmin(admin.ModelAdmin):
    """
    Admin interface for the singleton SiteConfiguration model.
    Ensures only the existing instance can be edited.
    """
    list_display = ('site_name', 'default_assistant_logo', 'learn_ai_url', 'contact_url')
    fieldsets = (
        (None, {
            'fields': ('site_name', 'default_assistant_logo'),
        }),
        ('URL Configuration', {
            'fields': ('learn_ai_url', 'contact_url'),
            'description': 'Configure URLs for various links throughout the site.',
        }),
    )

    def has_add_permission(self, request):
        # Prevent adding new instances if one already exists
        return not SiteConfiguration.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Prevent deleting the singleton instance
        return False
