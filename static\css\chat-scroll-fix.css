/**
 * Chat <PERSON>roll Fix CSS
 * Ensures chat container and chat box scroll properly
 */

/* Ensure chat container has proper overflow handling */
.chat-container,
.general-chat-container,
div.chat-container,
div.general-chat-container {
  overflow: hidden !important;
  position: relative !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Ensure chat box has proper overflow and scrolling */
.chat-box,
.general-chat-box,
#chat-box,
div.chat-box,
div.general-chat-box {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  -webkit-overflow-scrolling: touch !important;
  position: relative !important;
  min-height: 400px !important;
  max-height: calc(100vh - 250px) !important;
  display: flex !important;
  flex-direction: column !important;
  padding-bottom: 80px !important; /* Space for the fixed input form */
}

/* Ensure messages are properly displayed in the flex container */
.message {
  width: 100% !important;
  max-width: 100% !important;
  display: flex !important;
  margin: 0.5rem 0 !important;
  box-sizing: border-box !important;
  padding: 0 !important;
}

/* Ensure user messages are aligned to the right */
.user-message {
  justify-content: flex-end !important;
  align-self: flex-end !important;
  max-width: 100% !important;
  width: 100% !important;
}

/* Ensure assistant messages are aligned to the left */
.assistant-message {
  justify-content: flex-start !important;
  align-self: flex-start !important;
  max-width: 100% !important;
  width: 100% !important;
}

/* Fix for chat form positioning */
#chat-form {
  position: sticky !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  z-index: 1000 !important;
  background-color: rgba(255, 255, 255, 0.95) !important;
  padding: 0.75rem 1rem !important;
  border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
  margin-top: auto !important;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .chat-box,
  .general-chat-box,
  #chat-box,
  div.chat-box,
  div.general-chat-box {
    max-height: calc(100vh - 200px) !important;
    padding-bottom: 70px !important;
  }

  #chat-form {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    padding: 0.5rem !important;
    border-radius: 0 !important;
  }

  /* Ensure chat box has enough bottom padding for fixed input */
  .chat-box {
    padding-bottom: 70px !important;
  }

  /* User messages should be 80% width on mobile */
  .user-message .message-content {
    width: 80% !important;
    max-width: 80% !important;
    min-width: 80% !important;
  }

  /* Assistant messages should be 95% width on mobile */
  .assistant-message .message-content {
    width: 95% !important;
    max-width: 95% !important;
    min-width: 95% !important;
  }
}

/* Fix for initial display area */
#initial-display-area {
  width: 100% !important;
  max-width: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  margin-bottom: 1rem !important;
}

/* Fix for scrollbar styling */
.chat-box::-webkit-scrollbar {
  width: 8px !important;
}

.chat-box::-webkit-scrollbar-track {
  background: #f1f1f1 !important;
  border-radius: 10px !important;
}

.chat-box::-webkit-scrollbar-thumb {
  background: #c1c1c1 !important;
  border-radius: 10px !important;
}

.chat-box::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8 !important;
}

/* Dark mode scrollbar */
[data-theme="dark"] .chat-box::-webkit-scrollbar-track {
  background: #2a2a2a !important;
}

[data-theme="dark"] .chat-box::-webkit-scrollbar-thumb {
  background: #555555 !important;
}

[data-theme="dark"] .chat-box::-webkit-scrollbar-thumb:hover {
  background: #666666 !important;
}
