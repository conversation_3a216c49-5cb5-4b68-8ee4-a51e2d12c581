from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.http import JsonResponse
from django.urls import reverse
from django.views.decorators.http import require_POST
from django.views.decorators.http import require_GET
from django.contrib.contenttypes.models import ContentType

from accounts.models import Company
from .models import (
    Assistant, CommunityContext, ContextImage,
    FlaggedQuestion, ContextNotification, ContextUpvote, ReportedContent, Comment
)
from .forms import CommunityContextForm, FlagQuestionForm, SimpleCommunityContextForm


@login_required
def add_context(request, company_id, assistant_id):
    """Add context to a community assistant."""
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )

    # Check if this is related to a flagged question
    flagged_id = request.GET.get('flagged')
    flagged_question = None
    if flagged_id:
        flagged_question = get_object_or_404(
            FlaggedQuestion,
            pk=flagged_id,
            assistant=assistant
        )

    if request.method == 'POST':
        # Check if it's the simple form from the dashboard or the full form
        if 'keywords' in request.POST or 'image' in request.FILES:
            form = CommunityContextForm(request.POST, request.FILES)
        else:
            form = SimpleCommunityContextForm(request.POST)

        if form.is_valid():
            # Create the context
            context = CommunityContext.objects.create(
                assistant=assistant,
                created_by=request.user,
                title=form.cleaned_data['title'],
                text_content=form.cleaned_data['text_content'],
                keywords=form.cleaned_data.get('keywords', [])
            )

            # Handle image if provided (only in full form)
            if hasattr(form, 'cleaned_data') and form.cleaned_data.get('image'):
                ContextImage.objects.create(
                    context=context,
                    image=form.cleaned_data['image']
                )

            # If this is related to a flagged question, create a notification
            if flagged_question:
                ContextNotification.objects.create(
                    user=flagged_question.user,
                    flagged_question=flagged_question,
                    context=context
                )

                # Check if we should mark the question as resolved
                if request.POST.get('mark_resolved') and (
                    request.user == flagged_question.user or
                    request.user == assistant.company.owner
                ):
                    flagged_question.resolve()

            messages.success(request, "Context added successfully!")

            # Redirect to the appropriate page
            if flagged_question:
                return redirect('assistants:flagged_question_detail',
                               company_id=company_id,
                               assistant_id=assistant_id,
                               flagged_id=flagged_question.id)
            else:
                return redirect('assistants:contexts',
                               company_id=company_id,
                               assistant_id=assistant_id)
    else:
        form = CommunityContextForm()

    return render(request, 'assistants/community_context_form.html', {
        'form': form,
        'assistant': assistant,
        'company': company,
        'flagged_question': flagged_question
    })


@login_required
def edit_context(request, company_id, assistant_id, context_id):
    """Edit an existing context."""
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )
    context = get_object_or_404(
        CommunityContext,
        pk=context_id,
        assistant=assistant,
        created_by=request.user  # Only allow editing own contexts
    )

    if request.method == 'POST':
        form = CommunityContextForm(request.POST, request.FILES)
        if form.is_valid():
            # Update the context
            context.title = form.cleaned_data['title']
            context.text_content = form.cleaned_data['text_content']
            context.keywords = form.cleaned_data['keywords']
            context.updated_at = timezone.now()
            context.save()

            # Handle image if provided
            if form.cleaned_data.get('image'):
                ContextImage.objects.create(
                    context=context,
                    image=form.cleaned_data['image']
                )

            messages.success(request, "Context updated successfully!")
            return redirect('assistants:contexts',
                           company_id=company_id,
                           assistant_id=assistant_id)
    else:
        # Pre-populate the form
        initial_data = {
            'title': context.title,
            'text_content': context.text_content,
            'keywords': ', '.join(context.keywords) if context.keywords else ''
        }
        form = CommunityContextForm(initial=initial_data)

    return render(request, 'assistants/community_context_form.html', {
        'form': form,
        'assistant': assistant,
        'company': company,
        'context': context,
        'is_edit': True
    })


@login_required
def list_contexts(request, company_id, assistant_id):
    """List all contexts for a community assistant."""
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )

    # Apply filters
    contexts = CommunityContext.objects.filter(assistant=assistant, is_active=True)

    # Add upvote count and usage stats
    from django.db.models import Count, Subquery, OuterRef
    from .models import AnswerUpvote

    # Count direct upvotes
    contexts = contexts.annotate(upvote_count=Count('upvotes'))

    # Count answer upvotes (upvotes from answers that used this context)
    answer_upvotes = AnswerUpvote.objects.filter(
        interaction__used_contexts=OuterRef('pk')
    ).values('interaction__used_contexts').annotate(
        count=Count('id')
    ).values('count')

    # Use Coalesce to handle NULL values (when there are no answer upvotes)
    from django.db.models.functions import Coalesce
    contexts = contexts.annotate(answer_upvote_count=Coalesce(Subquery(answer_upvotes), 0))

    # Sort by upvotes if requested
    sort_by = request.GET.get('sort')
    if sort_by == 'upvotes':
        # Sort by total upvotes (direct + answer upvotes)
        from django.db.models import F
        contexts = contexts.annotate(
            total_upvotes=F('upvote_count') + F('answer_upvote_count')
        ).order_by('-total_upvotes', '-created_at')
    elif sort_by == 'usage':
        contexts = contexts.order_by('-times_used', '-created_at')
    else:
        contexts = contexts.order_by('-created_at')

    keyword = request.GET.get('keyword')
    if keyword:
        # Filter by keyword (case insensitive)
        contexts = contexts.filter(keywords__contains=[keyword.lower()])

    user_id = request.GET.get('user')
    if user_id:
        contexts = contexts.filter(created_by_id=user_id)

    # Check which contexts the current user has upvoted
    if request.user.is_authenticated:
        upvoted_contexts = ContextUpvote.objects.filter(
            user=request.user,
            context__in=contexts
        ).values_list('context_id', flat=True)
    else:
        upvoted_contexts = []

    # Paginate results
    paginator = Paginator(contexts, 10)  # 10 contexts per page
    page = request.GET.get('page', 1)
    contexts = paginator.get_page(page)

    # Get unique users who have contributed
    users = CommunityContext.objects.filter(
        assistant=assistant
    ).values_list('created_by', 'created_by__username').distinct()

    return render(request, 'assistants/community_contexts.html', {
        'assistant': assistant,
        'company': company,
        'contexts': contexts,
        'users': users,
        'upvoted_contexts': upvoted_contexts,
        'sort_by': sort_by
    })


def flag_question(request, company_id, assistant_id):
    """Flag a question that received an unsatisfactory answer.
    This view supports both logged-in and anonymous users.
    """
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )

    if request.method == 'POST':
        form = FlagQuestionForm(request.POST)
        if form.is_valid():
            question = request.POST.get('question')
            answer = request.POST.get('answer')
            reason = form.cleaned_data.get('reason', '')
            email = form.cleaned_data.get('email', '')

            # Check if user is authenticated
            is_anonymous = not request.user.is_authenticated

            # Create the flagged question
            flagged = FlaggedQuestion.objects.create(
                assistant=assistant,
                user=None if is_anonymous else request.user,
                question=question,
                original_answer=answer,
                reason=reason,
                is_anonymous=is_anonymous,
                email=email if is_anonymous else ''
            )

            success_message = "Question flagged successfully. The community can now help improve the answer."

            if is_anonymous and email:
                success_message += " You'll receive an email notification when community members add context."

            messages.success(request, success_message)

            # For anonymous users, redirect to the detail page with the tracking ID
            if is_anonymous:
                return redirect('assistants:flagged_question_detail',
                              company_id=company_id,
                              assistant_id=assistant_id,
                              flagged_id=flagged.id)
            else:
                return redirect('assistants:flagged_questions',
                              company_id=company_id,
                              assistant_id=assistant_id)
    else:
        form = FlagQuestionForm()
        question = request.GET.get('question', '')
        answer = request.GET.get('answer', '')

    return render(request, 'assistants/flag_question_form.html', {
        'form': form,
        'assistant': assistant,
        'company': company,
        'question': question,
        'answer': answer,
        'is_anonymous': not request.user.is_authenticated
    })


@login_required
def list_flagged_questions(request, company_id, assistant_id):
    """List all flagged questions for a community assistant."""
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )

    # Apply filters
    flagged = FlaggedQuestion.objects.filter(assistant=assistant)

    status = request.GET.get('status')
    if status == 'resolved':
        flagged = flagged.filter(is_resolved=True)
    elif status == 'unresolved':
        flagged = flagged.filter(is_resolved=False)

    search = request.GET.get('search')
    if search:
        flagged = flagged.filter(
            Q(question__icontains=search) |
            Q(original_answer__icontains=search) |
            Q(reason__icontains=search)
        )

    # Paginate results
    paginator = Paginator(flagged, 10)  # 10 questions per page
    page = request.GET.get('page', 1)
    flagged_questions = paginator.get_page(page)

    return render(request, 'assistants/flagged_questions.html', {
        'assistant': assistant,
        'company': company,
        'flagged_questions': flagged_questions
    })


def flagged_question_detail(request, company_id, assistant_id, flagged_id):
    """View details of a flagged question.
    Supports both logged-in and anonymous users.
    """
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )
    flagged = get_object_or_404(
        FlaggedQuestion,
        pk=flagged_id,
        assistant=assistant
    )

    # Check permissions for viewing this flagged question
    is_anonymous = not request.user.is_authenticated
    can_view = False

    # Company owner or admin can always view
    if not is_anonymous and (request.user == assistant.company.owner or
                           request.user.is_staff or
                           request.user.is_superuser):
        can_view = True
    # User who flagged the question can view
    elif not is_anonymous and request.user == flagged.user:
        can_view = True
    # Anonymous user with matching tracking ID in session can view
    elif is_anonymous and flagged.is_anonymous:
        # Allow viewing for anonymous users who just flagged the question
        tracking_id = request.GET.get('tracking_id')
        if tracking_id and str(flagged.tracking_id) == tracking_id:
            can_view = True
            # Store tracking ID in session for future visits
            request.session[f'flagged_tracking_{flagged_id}'] = str(flagged.tracking_id)
        # Check if tracking ID is in session
        elif f'flagged_tracking_{flagged_id}' in request.session:
            stored_tracking_id = request.session[f'flagged_tracking_{flagged_id}']
            if stored_tracking_id == str(flagged.tracking_id):
                can_view = True

    if not can_view:
        messages.error(request, "You don't have permission to view this flagged question.")
        return redirect('assistants:assistant_chat_by_id', company_id=company_id, assistant_id=assistant_id)

    # Get related contexts through notifications
    context_ids = ContextNotification.objects.filter(
        flagged_question=flagged
    ).values_list('context_id', flat=True)

    # Get the contexts using the IDs
    related_contexts = CommunityContext.objects.filter(
        id__in=context_ids
    ).order_by('-created_at')

    # Get notifications for this flagged question
    notifications = ContextNotification.objects.filter(
        flagged_question=flagged
    ).order_by('-created_at')

    # Mark notifications as read if the user is the one who flagged the question
    if not is_anonymous and request.user == flagged.user:
        notifications.filter(is_read=False).update(is_read=True)

    return render(request, 'assistants/flagged_question_detail.html', {
        'assistant': assistant,
        'company': company,
        'flagged': flagged,
        'related_contexts': related_contexts,
        'notifications': notifications,
        'is_anonymous': is_anonymous,
        'tracking_id': str(flagged.tracking_id)
    })


def add_context_to_flagged(request, company_id, assistant_id, flagged_id):
    """Add context to a flagged question.
    Supports both logged-in and anonymous users.
    """
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )
    flagged = get_object_or_404(
        FlaggedQuestion,
        pk=flagged_id,
        assistant=assistant
    )

    # Check if user is authenticated
    is_anonymous = not request.user.is_authenticated

    if request.method == 'POST':
        title = request.POST.get('title', '').strip()
        text_content = request.POST.get('text_content', '').strip()

        if not text_content:
            messages.error(request, "Please provide some content for your contribution.")
            return render(request, 'assistants/add_context_form.html', {
                'assistant': assistant,
                'company': company,
                'flagged': flagged,
                'is_anonymous': is_anonymous
            })

        # Check word count (strip HTML tags first)
        import re
        text_without_html = re.sub(r'<[^>]*>', ' ', text_content)
        words = text_without_html.split()
        word_count = len(words)

        if word_count > 500:
            messages.error(request, f"Content must be 500 words or less. You entered {word_count} words.")
            return render(request, 'assistants/add_context_form.html', {
                'assistant': assistant,
                'company': company,
                'flagged': flagged,
                'is_anonymous': is_anonymous,
                'title': title,
                'text_content': text_content
            })

        # Create the context entry
        context = CommunityContext.objects.create(
            assistant=assistant,
            title=title,
            text_content=text_content,
            created_by=None if is_anonymous else request.user
        )

        # Create notification for the user who flagged the question
        if flagged.user:
            ContextNotification.objects.create(
                flagged_question=flagged,
                context=context,
                user=flagged.user
            )

        messages.success(request, "Thank you for your contribution! Your knowledge has been added to the community assistant.")
        return redirect('assistants:flagged_question_detail',
                      company_id=company_id,
                      assistant_id=assistant_id,
                      flagged_id=flagged_id)

    return render(request, 'assistants/add_context_form.html', {
        'assistant': assistant,
        'company': company,
        'flagged': flagged,
        'is_anonymous': is_anonymous
    })


@require_GET
@login_required # Or adjust permissions as needed
def get_community_context_content(request, context_id):
    """Fetch the content of a specific CommunityContext item for AJAX display."""
    try:
        # Ensure the user has some level of access, e.g., belongs to the assistant's company
        # This is a basic check; refine based on your access control needs.
        context = get_object_or_404(CommunityContext, id=context_id)
        assistant = context.assistant
        company = assistant.company

        # Basic permission check: User must be part of the company
        # You might need more granular checks depending on context visibility rules
        from accounts.models import Membership
        if not Membership.objects.filter(user=request.user, company=company).exists() and not request.user.is_superuser:
             # Allow public access if the assistant is public? Decide based on requirements.
             if not assistant.is_public:
                 return JsonResponse({'error': 'Permission denied.'}, status=403)

        # Prepare content - potentially render markdown if stored as markdown
        # For now, returning raw text_content
        import markdown
        content_html = markdown.markdown(context.text_content, extensions=['fenced_code', 'tables', 'nl2br'])

        return JsonResponse({
            'status': 'success',
            'title': context.title or f"Context #{context.id}",
            'content': content_html # Return rendered HTML
        })
    except CommunityContext.DoesNotExist:
        return JsonResponse({'error': 'Context not found.'}, status=404)
    except Exception as e:
        # Log the error e
        print(f"Error fetching context {context_id}: {e}")
        return JsonResponse({'error': 'An unexpected error occurred.'}, status=500)


def resolve_flagged_question(request, company_id, assistant_id, flagged_id):
    """Mark a flagged question as resolved.
    Supports both logged-in and anonymous users.
    """
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )
    flagged = get_object_or_404(
        FlaggedQuestion,
        pk=flagged_id,
        assistant=assistant
    )

    # Check permissions for resolving this flagged question
    is_anonymous = not request.user.is_authenticated
    can_resolve = False

    # Company owner or admin can always resolve
    if not is_anonymous and (request.user == assistant.company.owner or
                           request.user.is_staff or
                           request.user.is_superuser):
        can_resolve = True
    # User who flagged the question can resolve
    elif not is_anonymous and request.user == flagged.user:
        can_resolve = True
    # Anonymous user with matching tracking ID in session can resolve
    elif is_anonymous and flagged.is_anonymous:
        tracking_id = request.GET.get('tracking_id')
        if tracking_id and str(flagged.tracking_id) == tracking_id:
            can_resolve = True
        # Check if tracking ID is in session
        elif f'flagged_tracking_{flagged_id}' in request.session:
            stored_tracking_id = request.session[f'flagged_tracking_{flagged_id}']
            if stored_tracking_id == str(flagged.tracking_id):
                can_resolve = True

    if can_resolve:
        flagged.is_resolved = True
        flagged.resolved_at = timezone.now()
        flagged.resolved_by = None if is_anonymous else request.user
        flagged.save(update_fields=['is_resolved', 'resolved_at', 'resolved_by'])
        messages.success(request, "Question marked as resolved.")
    else:
        messages.error(request, "You don't have permission to resolve this question.")

    # Include tracking_id in redirect for anonymous users
    redirect_kwargs = {
        'company_id': company_id,
        'assistant_id': assistant_id,
        'flagged_id': flagged_id
    }
    redirect_url = reverse('assistants:flagged_question_detail', kwargs=redirect_kwargs)

    if is_anonymous and can_resolve:
        redirect_url += f'?tracking_id={str(flagged.tracking_id)}'

    return redirect(redirect_url)


def flag_from_chat(request, company_id, assistant_id):
    """AJAX endpoint to flag a question from the chat interface.
    Supports both logged-in and anonymous users.
    """
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': 'Method not allowed'}, status=405)

    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )

    question = request.POST.get('question')
    answer = request.POST.get('answer')
    email = request.POST.get('email', '')
    reason = request.POST.get('reason', '')

    if not question or not answer:
        return JsonResponse({'status': 'error', 'message': 'Missing required fields'}, status=400)

    # Check if user is authenticated
    is_anonymous = not request.user.is_authenticated

    # Create the flagged question
    flagged = FlaggedQuestion.objects.create(
        assistant=assistant,
        user=None if is_anonymous else request.user,
        question=question,
        original_answer=answer,
        reason=reason,
        is_anonymous=is_anonymous,
        email=email if is_anonymous else ''
    )

    message = 'Question flagged successfully. The community can now help improve the answer.'

    return JsonResponse({
        'status': 'success',
        'message': message,
        'flagged_id': flagged.id,
        'tracking_id': str(flagged.tracking_id),
        'url': reverse('assistants:flagged_question_detail',
                      kwargs={
                          'company_id': company_id,
                          'assistant_id': assistant_id,
                          'flagged_id': flagged.id
                      })
    })


@login_required
def recent_contexts(request, company_id, assistant_id):
    """AJAX endpoint to get recent contexts for the community assistant."""
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )

    # Get recent contexts with upvote counts
    contexts = CommunityContext.objects.filter(
        assistant=assistant,
        is_active=True
    ).annotate(upvote_count=Count('upvotes')).order_by('-created_at')[:5]

    # Check which contexts the current user has upvoted
    if request.user.is_authenticated:
        upvoted_contexts = ContextUpvote.objects.filter(
            user=request.user,
            context__in=contexts
        ).values_list('context_id', flat=True)
    else:
        upvoted_contexts = []

    return render(request, 'assistants/partials/recent_contexts.html', {
        'contexts': contexts,
        'assistant': assistant,
        'company': company,
        'upvoted_contexts': upvoted_contexts
    })


@login_required
def recent_flagged_questions(request, company_id, assistant_id):
    """AJAX endpoint to get recent flagged questions for the community assistant."""
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )

    # Get unresolved flagged questions
    flagged_questions = FlaggedQuestion.objects.filter(
        assistant=assistant,
        is_resolved=False
    ).order_by('-created_at')[:5]

    return render(request, 'assistants/partials/recent_flagged_questions.html', {
        'flagged_questions': flagged_questions,
        'assistant': assistant,
        'company': company
    })


@login_required
@require_POST
def delete_context(request, company_id, assistant_id, context_id):
    """Delete a community context."""
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )
    context = get_object_or_404(
        CommunityContext,
        pk=context_id,
        assistant=assistant,
        created_by=request.user  # Only allow deleting own contexts
    )

    # Store the title for the success message
    title = context.title or "Untitled Context"

    # Delete the context
    context.delete()

    messages.success(request, f'"{title}" has been deleted successfully.')

    # Redirect back to the contexts list
    return redirect('assistants:contexts',
                   company_id=company_id,
                   assistant_id=assistant_id)


@login_required
@require_POST
def upvote_context(request, company_id, assistant_id, context_id):
    """Upvote a community context."""
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )
    context = get_object_or_404(
        CommunityContext,
        pk=context_id,
        assistant=assistant,
        is_active=True
    )

    # Check if user has already upvoted this context
    upvote, created = ContextUpvote.objects.get_or_create(
        user=request.user,
        context=context
    )

    if created:
        message = "Thank you for your upvote!"
        status = "added"
    else:
        # If upvote already exists, remove it (toggle behavior)
        upvote.delete()
        message = "Upvote removed."
        status = "removed"

    # Get the new direct upvote count
    direct_upvote_count = ContextUpvote.objects.filter(context=context).count()

    # Get answer upvote count
    from .models import AnswerUpvote
    answer_upvote_count = AnswerUpvote.objects.filter(
        interaction__used_contexts=context
    ).count()

    # Return JSON response for AJAX handling
    return JsonResponse({
        'status': 'success',
        'message': message,
        'upvote_status': status,
        'upvote_count': direct_upvote_count,
        'answer_upvote_count': answer_upvote_count,
        'total_upvote_count': direct_upvote_count + answer_upvote_count
    })


def report_content(request, company_id, assistant_id):
    """Report content for moderation.
    This view handles reporting various types of content (contexts, comments, etc.)
    """
    if request.method != 'POST':
        return redirect('assistants:community_dashboard', company_id=company_id, assistant_id=assistant_id)

    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )

    # Get form data
    content_type_id = request.POST.get('content_type')
    object_id = request.POST.get('object_id')
    report_type = request.POST.get('report_type')
    reason = request.POST.get('reason', '')

    if not all([content_type_id, object_id, report_type]):
        messages.error(request, "Missing required fields for reporting content.")
        return redirect('assistants:community_dashboard', company_id=company_id, assistant_id=assistant_id)

    try:
        # Get the content type
        content_type = ContentType.objects.get(id=content_type_id)

        # Create the report
        ReportedContent.objects.create(
            content_type=content_type,
            object_id=object_id,
            reported_by=request.user if request.user.is_authenticated else None,
            report_type=report_type,
            reason=reason,
            assistant=assistant,
            status='pending'
        )

        messages.success(request, "Thank you for reporting this content. A moderator will review it soon.")
    except Exception as e:
        messages.error(request, f"An error occurred while reporting content: {str(e)}")

    # Redirect back to the community dashboard
    return redirect('assistants:community_dashboard', company_id=company_id, assistant_id=assistant_id)


@login_required
@require_POST
def add_comment(request, company_id, assistant_id):
    """Add a comment to a content object (context, question, etc.)"""
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )

    # Get form data
    content_type_id = request.POST.get('content_type')
    object_id = request.POST.get('object_id')
    comment_text = request.POST.get('comment')

    if not all([content_type_id, object_id, comment_text]):
        messages.error(request, "Missing required fields for adding a comment.")
        return redirect('assistants:community_dashboard', company_id=company_id, assistant_id=assistant_id)

    try:
        # Get the content type
        content_type = ContentType.objects.get(id=content_type_id)

        # Create the comment
        Comment.objects.create(
            content_type=content_type,
            object_id=object_id,
            user=request.user,
            text=comment_text
        )

        messages.success(request, "Your comment has been added successfully.")
    except Exception as e:
        messages.error(request, f"An error occurred while adding your comment: {str(e)}")

    # Redirect back to the community dashboard
    return redirect('assistants:community_dashboard', company_id=company_id, assistant_id=assistant_id)
