/**
 * Responsive Message Bubbles
 * Makes user message bubbles adjust width based on content length
 */

(function() {
    // Function to adjust message bubble width based on content
    function adjustMessageBubbleWidth() {
        // Get all user message content elements
        const userMessages = document.querySelectorAll('.user-message .message-content');

        // Get all assistant message content elements
        const assistantMessages = document.querySelectorAll('.assistant-message .message-content');

        userMessages.forEach(message => {
            // Get the text content length
            const textLength = message.textContent.trim().length;
            const wordCount = message.textContent.trim().split(/\s+/).length;

            // Remove any existing length-based classes
            message.classList.remove('very-short', 'short', 'medium', 'long', 'multi-word');

            // Add class based on word count for proper wrapping
            if (wordCount > 1) {
                message.classList.add('multi-word');
            }

            // Check if the message contains complex content like images, tables, or code blocks
            const hasComplexContent =
                message.querySelector('img') ||
                message.querySelector('table') ||
                message.querySelector('pre') ||
                message.querySelector('code') ||
                message.querySelector('ul') ||
                message.querySelector('ol');

            // If it has complex content, use maximum width
            if (hasComplexContent) {
                // Use maximum width for complex content
                message.classList.add('long');
                if (window.innerWidth <= 768) {
                    // Mobile - 80% width
                    message.style.width = '80%';
                    message.style.maxWidth = '80%';
                } else {
                    // Desktop - 75% width
                    message.style.width = '75%';
                    message.style.maxWidth = '75%';
                }
            } else {
                // For text-only messages, adjust width based on content length
                if (window.innerWidth <= 768) {
                    // Mobile sizing
                    if (textLength === 1) {
                        // Single character messages (like "k", "?", "!")
                        message.classList.add('very-short', 'single-char');
                        message.style.width = 'fit-content';
                        message.style.maxWidth = 'fit-content';
                        message.style.minWidth = 'auto';
                        message.style.padding = '0.3rem 0.6rem';
                        message.style.display = 'inline-block';
                        message.style.whiteSpace = 'nowrap';
                    } else if (textLength <= 2) {
                        // Extremely short messages (2 characters like "hi", "ok")
                        message.classList.add('very-short');
                        message.style.width = 'fit-content';
                        message.style.maxWidth = 'fit-content';
                        message.style.minWidth = 'auto';
                        // Add inline style to ensure content-based width
                        message.style.display = 'inline-block';
                        message.style.whiteSpace = 'nowrap';
                    } else if (textLength <= 3) {
                        // Very short messages (3 characters)
                        message.classList.add('very-short');
                        message.style.width = 'auto';
                        message.style.maxWidth = '20%';
                    } else if (textLength <= 10) {
                        // Short messages (4-10 characters)
                        message.classList.add('short');
                        message.style.width = 'auto';
                        message.style.maxWidth = '40%';
                    } else if (textLength < 50) {
                        // Medium messages
                        message.classList.add('medium');
                        message.style.width = 'auto';
                        message.style.maxWidth = '60%';
                    } else {
                        // Longer messages
                        message.classList.add('long');
                        message.style.width = 'auto';
                        message.style.maxWidth = '80%';
                    }
                } else {
                    // Desktop sizing
                    if (textLength === 1) {
                        // Single character messages (like "k", "?", "!")
                        message.classList.add('very-short', 'single-char');
                        message.style.width = 'fit-content';
                        message.style.maxWidth = 'fit-content';
                        message.style.minWidth = 'auto';
                        message.style.padding = '0.3rem 0.6rem';
                        message.style.display = 'inline-block';
                        message.style.whiteSpace = 'nowrap';
                    } else if (textLength <= 2) {
                        // Extremely short messages (2 characters like "hi", "ok")
                        message.classList.add('very-short');
                        message.style.width = 'fit-content';
                        message.style.maxWidth = 'fit-content';
                        message.style.minWidth = 'auto';
                        // Add inline style to ensure content-based width
                        message.style.display = 'inline-block';
                        message.style.whiteSpace = 'nowrap';
                    } else if (textLength <= 3) {
                        // Very short messages (3 characters)
                        message.classList.add('very-short');
                        message.style.width = 'auto';
                        message.style.maxWidth = '15%';
                    } else if (textLength <= 10) {
                        // Short messages (4-10 characters)
                        message.classList.add('short');
                        message.style.width = 'auto';
                        message.style.maxWidth = '25%';
                    } else if (textLength < 30) {
                        // Medium-short messages
                        message.classList.add('medium-short');
                        message.style.width = 'auto';
                        message.style.maxWidth = '40%';
                    } else if (textLength < 100) {
                        // Medium messages
                        message.classList.add('medium');
                        message.style.width = 'auto';
                        message.style.maxWidth = '60%';
                    } else {
                        // Longer messages
                        message.classList.add('long');
                        message.style.width = 'auto';
                        message.style.maxWidth = '75%';
                    }
                }
            }

            // Ensure minimum width for multi-word messages
            if (wordCount > 1) {
                message.style.minWidth = '60px';
            } else {
                message.style.minWidth = 'auto';
            }
        });

        // Process assistant messages to ensure they're 80% width on mobile
        assistantMessages.forEach(message => {
            // Check if the message is in a thinking indicator (don't modify those)
            if (message.closest('.thinking') || message.closest('.loading')) {
                return;
            }

            // Apply mobile width for assistant messages
            if (window.innerWidth <= 768) {
                // Mobile - 80% width
                message.style.width = 'auto';
                message.style.maxWidth = '80%';
            } else {
                // Desktop - 85% width (keep original desktop width)
                message.style.width = 'auto';
                message.style.maxWidth = '85%';
            }
        });
    }

    // Function to observe DOM changes for new messages
    function observeNewMessages() {
        const chatBox = document.getElementById('chat-box');
        if (!chatBox) return;

        // Create a MutationObserver to watch for new messages
        const observer = new MutationObserver(function(mutations) {
            let shouldAdjust = false;

            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Check if any of the added nodes are messages
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        if (node.classList &&
                            (node.classList.contains('message') ||
                             node.classList.contains('user-message'))) {
                            shouldAdjust = true;
                            break;
                        }
                    }
                }
            });

            if (shouldAdjust) {
                // Adjust message bubble widths after a short delay to ensure content is rendered
                setTimeout(adjustMessageBubbleWidth, 100);
            }
        });

        // Start observing the chat box for changes
        observer.observe(chatBox, { childList: true, subtree: true });
    }

    // Initialize when the DOM is fully loaded
    function init() {
        // Adjust existing message bubbles
        adjustMessageBubbleWidth();

        // Set up observer for new messages
        observeNewMessages();

        // Also adjust on window resize
        window.addEventListener('resize', adjustMessageBubbleWidth);

        // Adjust when the chat form is submitted
        const chatForm = document.getElementById('chat-form');
        if (chatForm) {
            chatForm.addEventListener('submit', function() {
                // Adjust after a delay to allow the new message to be added
                setTimeout(adjustMessageBubbleWidth, 200);
            });
        }

        // Make the adjustMessageBubbleWidth function globally available
        // so it can be called directly from the addMessage function
        window.adjustMessageBubbleWidth = adjustMessageBubbleWidth;
    }

    // Run the initialization when the DOM is fully loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();
