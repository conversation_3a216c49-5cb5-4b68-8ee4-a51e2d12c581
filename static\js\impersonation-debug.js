/**
 * Impersonation Debug Script
 * This script helps diagnose impersonation issues by checking various indicators
 * and reporting them to the console.
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('=== IMPERSONATION DEBUG ===');

    // Check for meta tags
    const isImpersonatingMeta = document.querySelector('meta[name="is-impersonating"]');
    const impersonatedUserMeta = document.querySelector('meta[name="impersonated-user"]');
    const realUserMeta = document.querySelector('meta[name="real-user"]');

    console.log('Meta tags:');
    console.log('- is-impersonating:', isImpersonatingMeta ? isImpersonatingMeta.getAttribute('content') : 'not found');
    console.log('- impersonated-user:', impersonatedUserMeta ? impersonatedUserMeta.getAttribute('content') : 'not found');
    console.log('- real-user:', realUserMeta ? realUserMeta.getAttribute('content') : 'not found');

    // Check for debug elements
    const debugRealUser = document.getElementById('debug-real-user');
    const debugCurrentUser = document.getElementById('debug-current-user');

    console.log('Debug elements:');
    console.log('- debug-real-user:', debugRealUser ? debugRealUser.textContent : 'not found');
    console.log('- debug-current-user:', debugCurrentUser ? debugCurrentUser.textContent : 'not found');

    // Check for global variables
    console.log('Global variables:');
    console.log('- window.realUserUsername:', window.realUserUsername || 'not set');
    console.log('- window.currentUsername:', window.currentUsername || 'not set');
    console.log('- window.isImpersonating:', window.isImpersonating || 'not set');
    console.log('- window.debug_impersonation_info:', window.debug_impersonation_info || 'not set');

    // Check for body class
    console.log('Body class:');
    console.log('- is-impersonating class:', document.body.classList.contains('is-impersonating'));

    // Check for impersonation UI elements
    const impersonationAlert = document.getElementById('impersonation-alert');
    const impersonationFloatButton = document.getElementById('impersonation-float-button');
    const impersonationDropdown = document.getElementById('impersonation-dropdown');

    console.log('UI elements:');
    console.log('- impersonation-alert:', impersonationAlert ? 'found' : 'not found');
    console.log('- impersonation-float-button:', impersonationFloatButton ? 'found' : 'not found');
    console.log('- impersonation-dropdown:', impersonationDropdown ? 'found' : 'not found');

    // Check if we should force impersonation UI
    const shouldForceImpersonation =
        // Check global variables
        (window.realUserUsername && window.currentUsername &&
         window.realUserUsername !== window.currentUsername &&
         window.realUserUsername !== 'AnonymousUser' &&
         window.currentUsername !== 'AnonymousUser') ||
        // Check debug elements
        (debugRealUser && debugCurrentUser &&
         debugRealUser.textContent !== debugCurrentUser.textContent &&
         debugRealUser.textContent !== 'AnonymousUser' &&
         debugCurrentUser.textContent !== 'AnonymousUser');

    console.log('Should force impersonation UI:', shouldForceImpersonation);

    // Only force UI if we're sure this is a valid impersonation
    if (shouldForceImpersonation) {
        console.log('DETECTED IMPERSONATION CONDITION - Forcing UI');

        // Add body class
        document.body.classList.add('is-impersonating');
        document.body.style.paddingTop = '50px';

        // Force alert visibility
        if (impersonationAlert) {
            impersonationAlert.style.display = 'flex';
            impersonationAlert.style.visibility = 'visible';
            impersonationAlert.style.opacity = '1';
        }

        // Force float button visibility
        if (impersonationFloatButton) {
            impersonationFloatButton.style.display = 'flex';
            impersonationFloatButton.style.visibility = 'visible';
            impersonationFloatButton.style.opacity = '1';
        }
    } else {
        console.log('NOT forcing impersonation UI');

        // Remove impersonation UI if it shouldn't be shown
        document.body.classList.remove('is-impersonating');

        // Hide alert
        if (impersonationAlert) {
            impersonationAlert.style.display = 'none';
        }

        // Hide float button
        if (impersonationFloatButton) {
            impersonationFloatButton.style.display = 'none';
        }
    }

    console.log('=== END IMPERSONATION DEBUG ===');
});
