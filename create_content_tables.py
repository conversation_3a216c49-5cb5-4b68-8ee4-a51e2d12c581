import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

print("Starting script to create content tables...")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

# SQL to create the content_content table
content_sql = """
CREATE TABLE IF NOT EXISTS "content_content" (
    "id" serial NOT NULL PRIMARY KEY,
    "title" varchar(200) NOT NULL,
    "slug" varchar(200) NOT NULL,
    "content_type" varchar(20) NOT NULL,
    "body" text NOT NULL,
    "summary" text NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "updated_at" timestamp with time zone NOT NULL,
    "is_public" boolean NOT NULL,
    "is_archived" boolean NOT NULL,
    "assistant_id" integer NULL REFERENCES "assistants_assistant" ("id") <PERSON><PERSON><PERSON>RABLE INITIALLY DEFERRED,
    "author_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
    "company_id" integer NOT NULL REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# SQL to create the content_contentimage table
contentimage_sql = """
CREATE TABLE IF NOT EXISTS "content_contentimage" (
    "id" serial NOT NULL PRIMARY KEY,
    "image" varchar(100) NOT NULL,
    "alt_text" varchar(200) NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "content_id" integer NOT NULL REFERENCES "content_content" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# SQL to create the content_contentversion table
contentversion_sql = """
CREATE TABLE IF NOT EXISTS "content_contentversion" (
    "id" serial NOT NULL PRIMARY KEY,
    "body" text NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "change_summary" varchar(200) NOT NULL,
    "content_id" integer NOT NULL REFERENCES "content_content" ("id") DEFERRABLE INITIALLY DEFERRED,
    "edited_by_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# SQL to create indexes
indexes_sql = """
CREATE INDEX IF NOT EXISTS "content_content_company_id_content_type_idx" ON "content_content" ("company_id", "content_type");
CREATE INDEX IF NOT EXISTS "content_content_company_id_is_public_idx" ON "content_content" ("company_id", "is_public");
CREATE INDEX IF NOT EXISTS "content_content_company_id_is_archived_idx" ON "content_content" ("company_id", "is_archived");
CREATE INDEX IF NOT EXISTS "content_contentimage_content_id_idx" ON "content_contentimage" ("content_id");
CREATE INDEX IF NOT EXISTS "content_contentversion_content_id_idx" ON "content_contentversion" ("content_id");
"""

# Connect to the database
try:
    print("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(**db_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    
    # Create the content_content table
    print("Creating content_content table...")
    cursor.execute(content_sql)
    print("Content table created successfully!")
    
    # Create the content_contentimage table
    print("Creating content_contentimage table...")
    cursor.execute(contentimage_sql)
    print("ContentImage table created successfully!")
    
    # Create the content_contentversion table
    print("Creating content_contentversion table...")
    cursor.execute(contentversion_sql)
    print("ContentVersion table created successfully!")
    
    # Create indexes
    print("Creating indexes...")
    cursor.execute(indexes_sql)
    print("Indexes created successfully!")
    
    # Close the connection
    cursor.close()
    conn.close()
    print("Database connection closed.")
    print("All content tables created successfully!")
    
except Exception as e:
    print(f"Error: {e}")
