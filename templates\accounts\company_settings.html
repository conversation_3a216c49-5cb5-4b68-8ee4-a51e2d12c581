{% extends 'base/layout.html' %}
{% load static %}
{% load crispy_forms_tags %}
{# Remove widget_tweaks load tag #}

{% block title %}Company Settings - {{ company.name }} - 24seven{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/company-settings-enhanced.css' %}">
<link rel="stylesheet" href="{% static 'css/category-dropdowns.css' %}">
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row align-items-center mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Company Settings</h1>
            <p class="text-muted mb-0">
                Manage settings for {{ company.name }}
            </p>
        </div>
        <div class="col-auto">
            <a href="{% url 'accounts:company_detail' company.id %}" class="btn btn-primary btn-sm rounded-pill px-3">
                <i class="bi bi-arrow-left me-2"></i>
                Back to Company
            </a>
        </div>
    </div>

    <div class="row g-4">
        <!-- Settings Form -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg rounded-4 overflow-hidden">
                <div class="card-body p-4">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger rounded-3">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        <!-- Basic Information Card -->
                        <div class="card mb-4 border-0 shadow-sm rounded-3">
                            <div class="card-header bg-gradient-primary-subtle py-3">
                                <h5 class="card-title mb-0"><i class="bi bi-info-circle me-2"></i>Basic Information</h5>
                            </div>
                            <div class="card-body p-4">
                                <div class="row g-4">
                                    <!-- Mission -->
                                    <div class="col-12">
                                        <label for="{{ form.mission.id_for_label }}" class="form-label fw-medium">
                                            Company Mission
                                        </label>
                                        {# Render using standard Django syntax #}
                                        {{ form.mission }}
                                        {% if form.mission.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.mission.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">{{ form.mission.help_text }}</div>
                                    </div>

                                    <!-- Website -->
                                    <div class="col-md-6">
                                        <label for="{{ form.website.id_for_label }}" class="form-label fw-medium">
                                            <i class="bi bi-globe2 me-1 text-primary"></i> Website
                                        </label>
                                        {# Render using standard Django syntax #}
                                        {{ form.website }}
                                        {% if form.website.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.website.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                        {% if form.website.help_text %}
                                            <div class="form-text">{{ form.website.help_text }}</div>
                                        {% endif %}
                                    </div>

                                    <!-- Industry -->
                                    <div class="col-md-6">
                                        <label for="industry_input" class="form-label fw-medium">
                                            <i class="bi bi-building me-1 text-primary"></i> Industry
                                        </label>
                                        <div class="dropdown-container industry-dropdown-container">
                                            <input type="text" id="industry_input" name="industry" class="form-control"
                                                   placeholder="Select or type industry" value="{{ form.industry.value|default:form.initial.industry|default:'' }}">
                                            <input type="hidden" name="industry_value" id="industry_value" value="{{ form.industry.value|default:form.initial.industry|default:'' }}">
                                            <div class="dropdown-list"></div>
                                        </div>
                                        {% if form.industry.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.industry.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">
                                            Select your company's primary industry
                                        </div>
                                    </div>

                                    <!-- Categories - Moved up as per user preference -->
                                    <div class="col-12">
                                        <label for="categories_input" class="form-label fw-medium">
                                            <i class="bi bi-tags me-1 text-primary"></i> Categories
                                        </label>
                                        <div class="dropdown-container category-dropdown-container">
                                            <input type="text" id="categories_input" name="categories" class="form-control"
                                                   placeholder="Select or type categories" value="{{ form.categories.value|default:'' }}">
                                            <input type="hidden" name="categories_value" id="categories_value" value="{{ form.categories.value|default:'' }}">
                                            <div class="dropdown-list"></div>
                                        </div>
                                        {% if form.categories.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.categories.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">Select categories related to your company's industry</div>
                                    </div>

                                    <!-- Size - Moved down as per user preference -->
                                    <div class="col-md-6">
                                        <label for="{{ form.size.id_for_label }}" class="form-label fw-medium">
                                            <i class="bi bi-people me-1 text-primary"></i> Company Size
                                        </label>
                                        <select name="{{ form.size.name }}" id="{{ form.size.id_for_label }}" class="form-select">
                                            {% for value, text in form.fields.size.choices %}
                                                <option value="{{ value }}" {% if form.size.value|stringformat:"s" == value|stringformat:"s" %}selected{% endif %}>{{ text }}</option>
                                            {% endfor %}
                                        </select>
                                        {% if form.size.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.size.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">
                                            Number of employees in your company
                                        </div>
                                    </div>

                                    <!-- Founded Year -->
                                    <div class="col-md-6">
                                        <label for="{{ form.founded.id_for_label }}" class="form-label fw-medium">
                                            <i class="bi bi-calendar-event me-1 text-primary"></i> Founded Year
                                        </label>
                                        <input type="{{ form.founded.field.widget.input_type }}"
                                               name="{{ form.founded.name }}"
                                               {% if form.founded.value != None %}value="{{ form.founded.value }}"{% endif %}
                                               class="form-control {% if form.founded.errors %}is-invalid{% endif %}"
                                               id="{{ form.founded.id_for_label }}"
                                               placeholder="e.g., 2020"
                                               {% if form.founded.field.widget.attrs.min %}min="{{ form.founded.field.widget.attrs.min }}"{% endif %}
                                               {% if form.founded.field.widget.attrs.max %}max="{{ form.founded.field.widget.attrs.max }}"{% endif %}
                                               {% if form.founded.field.required %}required{% endif %}>
                                        {% if form.founded.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.founded.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">
                                            Year your company was established
                                        </div>
                                    </div>
                                </div> {# End .row g-4 #}
                            </div> {# End .card-body #}
                        </div> {# End Basic Information Card #}

                        <!-- Company Branding Card -->
                        <div class="card mb-4 border-0 shadow-sm rounded-3">
                            <div class="card-header bg-gradient-primary-subtle py-3">
                                <h5 class="card-title mb-0"><i class="bi bi-image me-2"></i>Company Branding</h5>
                            </div>
                            <div class="card-body p-4">
                                <div class="mb-3">
                                    <label for="{{ form.logo.id_for_label }}" class="form-label fw-medium">Company Logo</label>
                                    {% if form.instance.logo %}
                                        <div class="mb-3 text-center">
                                            <img src="{{ form.instance.logo.url }}" alt="Current Logo" style="max-height: 100px; max-width: 240px;" class="img-thumbnail rounded-3 shadow-sm">
                                        </div>
                                    {% endif %}
                                    <div class="input-group">
                                        {{ form.logo }}
                                    </div>
                                    <div class="form-text mt-2">
                                        <i class="bi bi-info-circle me-1"></i> Upload a new logo. Select 'Clear' to remove the current logo.
                                    </div>
                                    {% if form.logo.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.logo.errors|join:", " }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div> {# End .card-body #}
                        </div> {# End Company Branding Card #}


                        <!-- Contact Information Card -->
                        <div class="card mb-4 border-0 shadow-sm rounded-3">
                            <div class="card-header bg-gradient-primary-subtle py-3">
                                <h5 class="card-title mb-0"><i class="bi bi-envelope me-2"></i>Contact Information</h5>
                            </div>
                            <div class="card-body p-4">
                                <div class="row g-4">
                                    <!-- Contact Email -->
                                    <div class="col-md-6">
                                        <label for="{{ form.contact_email.id_for_label }}" class="form-label fw-medium">
                                            <i class="bi bi-envelope-at me-1 text-primary"></i> Contact Email
                                        </label>
                                        {{ form.contact_email }}
                                        {% if form.contact_email.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.contact_email.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <!-- Contact Phone -->
                                    <div class="col-md-6">
                                        <label for="{{ form.contact_phone.id_for_label }}" class="form-label fw-medium">
                                            <i class="bi bi-telephone me-1 text-primary"></i> Contact Phone <span class="text-danger">*</span>
                                        </label>
                                        {{ form.contact_phone }}
                                        {% if form.contact_phone.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.contact_phone.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">
                                            {{ form.contact_phone.help_text }}
                                        </div>
                                    </div>

                                    <!-- Address Section -->
                                    <div class="col-12">
                                        <div class="border-top pt-3 mt-2 mb-3">
                                            <h6 class="fw-medium mb-3"><i class="bi bi-geo-alt me-1 text-primary"></i> Address Information</h6>
                                        </div>
                                    </div>

                                    <!-- Address Line 1 -->
                                    <div class="col-12">
                                        <label for="{{ form.address_line1.id_for_label }}" class="form-label fw-medium">Address Line 1</label>
                                        {{ form.address_line1 }} {# Renders the input with widget attrs #}
                                        {% if form.address_line1.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.address_line1.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                        {% if form.address_line1.help_text %}
                                            <div class="form-text">{{ form.address_line1.help_text }}</div>
                                        {% endif %}
                                    </div>

                                    <!-- Address Line 2 -->
                                    <div class="col-12">
                                        <label for="{{ form.address_line2.id_for_label }}" class="form-label fw-medium">
                                            Address Line 2 <span class="text-muted">(Optional)</span>
                                        </label>
                                        {{ form.address_line2 }} {# Renders the input with widget attrs #}
                                        {% if form.address_line2.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.address_line2.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                        {% if form.address_line2.help_text %}
                                            <div class="form-text">{{ form.address_line2.help_text }}</div>
                                        {% endif %}
                                    </div>

                                    <!-- City -->
                                    <div class="col-md-6">
                                        <label for="{{ form.city.id_for_label }}" class="form-label fw-medium">City</label>
                                        {{ form.city }} {# Renders the input with widget attrs #}
                                        {% if form.city.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.city.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                        {% if form.city.help_text %}
                                            <div class="form-text">{{ form.city.help_text }}</div>
                                        {% endif %}
                                    </div>

                                    <!-- Postal Code -->
                                    <div class="col-md-3">
                                        <label for="{{ form.postal_code.id_for_label }}" class="form-label fw-medium">Postal Code</label>
                                        {{ form.postal_code }} {# Renders the input with widget attrs #}
                                        {% if form.postal_code.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.postal_code.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                        {% if form.postal_code.help_text %}
                                            <div class="form-text">{{ form.postal_code.help_text }}</div>
                                        {% endif %}
                                    </div>

                                    <!-- Country -->
                                    <div class="col-md-3">
                                        <label for="{{ form.country.id_for_label }}" class="form-label fw-medium">Country</label>
                                        {{ form.country }} {# Renders the input with widget attrs #}
                                        {% if form.country.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.country.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                        {% if form.country.help_text %}
                                            <div class="form-text">{{ form.country.help_text }}</div>
                                        {% endif %}
                                    </div>
                                </div> {# End .row g-4 #}
                            </div> {# End .card-body #}
                        </div> {# End Contact Information Card #}


                        <!-- Social Media Card -->
                        <div class="card mb-4 border-0 shadow-sm rounded-3">
                            <div class="card-header bg-gradient-primary-subtle py-3">
                                <h5 class="card-title mb-0"><i class="bi bi-share me-2"></i>Social Media</h5>
                            </div>
                            <div class="card-body p-4">
                                <div class="row g-4">
                                    <div class="col-md-4">
                                        <label for="{{ form.linkedin.id_for_label }}" class="form-label fw-medium">
                                            <i class="bi bi-linkedin text-primary"></i> LinkedIn
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light"><i class="bi bi-linkedin"></i></span>
                                            {{ form.linkedin }}
                                        </div>
                                        {% if form.linkedin.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.linkedin.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <div class="col-md-4">
                                        <label for="{{ form.twitter.id_for_label }}" class="form-label fw-medium">
                                            <i class="bi bi-twitter text-info"></i> Twitter
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light"><i class="bi bi-twitter"></i></span>
                                            {{ form.twitter }}
                                        </div>
                                        {% if form.twitter.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.twitter.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <div class="col-md-4">
                                        <label for="{{ form.facebook.id_for_label }}" class="form-label fw-medium">
                                            <i class="bi bi-facebook text-primary"></i> Facebook
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light"><i class="bi bi-facebook"></i></span>
                                            {{ form.facebook }}
                                        </div>
                                        {% if form.facebook.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.facebook.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div> {# End .row g-4 #}
                            </div> {# End .card-body #}
                        </div> {# End Social Media Card #}


                        <!-- Directory Settings Card -->
                        <div class="card mb-4 border-0 shadow-sm rounded-3">
                            <div class="card-header bg-gradient-primary-subtle py-3">
                                <h5 class="card-title mb-0"><i class="bi bi-globe me-2"></i>Directory Settings</h5>
                            </div>
                            <div class="card-body p-4">
                                <!-- Visibility Section -->
                                <div class="mb-4">
                                    <h6 class="fw-medium mb-3"><i class="bi bi-eye me-1 text-primary"></i> Visibility Settings</h6>

                                    <div class="form-check form-switch mb-3">
                                        {{ form.list_in_directory }}
                                        <label class="form-check-label fw-medium" for="{{ form.list_in_directory.id_for_label }}">
                                            List in Public Directory
                                        </label>
                                        <div class="form-text">
                                            Make your company visible in the public directory.
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ form.description.id_for_label }}" class="form-label fw-medium">
                                            Public Description
                                        </label>
                                        {{ form.description }}
                                        <div class="form-text">
                                            This description will be shown in the public directory.
                                        </div>
                                    </div>
                                </div>

                                <!-- Note: Categories field is already included in the Basic Information section -->

                                {# --- Company Tier & Featured Status/Request Section --- #}
                                <div class="border-top pt-4 mt-2 mb-3">
                                    <h6 class="fw-medium mb-3"><i class="bi bi-award me-1 text-primary"></i> Directory Tier & Featured Status</h6>
                                </div>

                                <div class="card border-0 bg-light-subtle rounded-3 shadow-sm mb-4">
                                    <div class="card-body p-4">
                                        {# Pending Tier Alert #}
                                        {% if company.tier_change_pending %}
                                        <div class="alert alert-info d-flex align-items-center rounded-3 mb-4" role="alert">
                                            <i class="bi bi-info-circle-fill me-2"></i>
                                            <div>
                                                A request to change the tier to <strong>{{ company.get_requested_tier_display }}</strong>
                                                {% if company.requested_tier_duration %}
                                                    ({{ company.get_requested_tier_duration_display }})
                                                {% endif %}
                                                is pending superadmin approval.
                                            </div>
                                        </div>
                                        {% endif %}

                                        <div class="row g-4">
                                            {# Current Tier Status #}
                                            <div class="col-md-6">
                                                <div class="card h-100 border-0 bg-opacity-50 bg-white rounded-3">
                                                    <div class="card-body">
                                                        <h6 class="card-subtitle mb-2 text-muted">Current Tier</h6>
                                                        <h5 class="card-title">
                                                            <span class="badge bg-primary rounded-pill px-3 py-2">{{ company.get_tier_display }}</span>
                                                        </h5>
                                                        {% if company.tier_expiry_date %}
                                                            <p class="card-text small mt-2">
                                                                <i class="bi bi-calendar-event me-1"></i> Expires: {{ company.tier_expiry_date|date:"Y-m-d" }}
                                                            </p>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>

                                            {# Tier Change Request Field (from dir_form) #}
                                            <div class="col-md-6">
                                                <div class="card h-100 border-0 bg-opacity-50 bg-white rounded-3">
                                                    <div class="card-body">
                                                        <h6 class="card-subtitle mb-2 text-muted">Request Tier Change</h6>
                                                        <div class="mt-2">
                                                            <label for="{{ dir_form.request_new_tier.id_for_label }}" class="form-label fw-medium">
                                                                Select Tier
                                                            </label>
                                                            <select name="{{ dir_form.request_new_tier.name }}" id="{{ dir_form.request_new_tier.id_for_label }}" class="form-select">
                                                                {% for value, text in dir_form.fields.request_new_tier.choices %}
                                                                    <option value="{{ value }}" {% if dir_form.request_new_tier.value|stringformat:"s" == value|stringformat:"s" %}selected{% endif %}>{{ text }}</option>
                                                                {% endfor %}
                                                            </select>
                                                            {% if dir_form.request_new_tier.help_text %}
                                                                <div class="form-text">{{ dir_form.request_new_tier.help_text }}</div>
                                                            {% endif %}
                                                        </div>

                                                        {# Tier Duration Request Field (Initially Hidden) #}
                                                        <div class="mt-3" id="company-tier-duration-wrapper" style="display: none;" data-duration-for="tier">
                                                            <label for="{{ dir_form.requested_tier_duration.id_for_label }}" class="form-label fw-medium">
                                                                Duration
                                                            </label>
                                                            {{ dir_form.requested_tier_duration }}
                                                            {% if dir_form.requested_tier_duration.help_text %}
                                                                <div class="form-text">{{ dir_form.requested_tier_duration.help_text }}</div>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <hr class="my-4">

                                        {# Pending Featured Alert #}
                                        {% if company.featured_request_pending %}
                                        <div class="alert alert-info d-flex align-items-center rounded-3 mb-4" role="alert">
                                            <i class="bi bi-info-circle-fill me-2"></i>
                                            <div>
                                                A request to feature this company
                                                {% if company.requested_featured_duration %}
                                                    ({{ company.get_requested_featured_duration_display }})
                                                {% endif %}
                                                is pending superadmin approval.
                                            </div>
                                        </div>
                                        {% endif %}

                                        <div class="row g-4">
                                            {# Current Featured Status #}
                                            <div class="col-md-6">
                                                <div class="card h-100 border-0 bg-opacity-50 bg-white rounded-3">
                                                    <div class="card-body">
                                                        <h6 class="card-subtitle mb-2 text-muted">Featured Status</h6>
                                                        <h5 class="card-title">
                                                            {% if company.is_featured %}
                                                                <span class="badge bg-success rounded-pill px-3 py-2">Featured</span>
                                                                {% if company.featured_expiry_date %}
                                                                    <p class="card-text small mt-2">
                                                                        <i class="bi bi-calendar-event me-1"></i> Expires: {{ company.featured_expiry_date|date:"Y-m-d" }}
                                                                    </p>
                                                                {% endif %}
                                                            {% else %}
                                                                <span class="badge bg-secondary rounded-pill px-3 py-2">Not Featured</span>
                                                            {% endif %}
                                                        </h5>
                                                    </div>
                                                </div>
                                            </div>

                                            {# Featured Status Request Field (from dir_form) #}
                                            <div class="col-md-6">
                                                <div class="card h-100 border-0 bg-opacity-50 bg-white rounded-3">
                                                    <div class="card-body">
                                                        <h6 class="card-subtitle mb-2 text-muted">Request Featured Status</h6>
                                                        <div class="mt-2">
                                                            <div class="form-check">
                                                                {{ dir_form.request_featured_status }}
                                                                <label for="{{ dir_form.request_featured_status.id_for_label }}" class="form-check-label fw-medium">
                                                                    Request to be featured
                                                                </label>
                                                            </div>
                                                            {% if dir_form.request_featured_status.help_text %}
                                                                <div class="form-text">{{ dir_form.request_featured_status.help_text }}</div>
                                                            {% endif %}
                                                        </div>

                                                        {# Featured Duration Request Field (Initially Hidden) #}
                                                        <div class="mt-3" id="company-featured-duration-wrapper" style="display: none;" data-duration-for="featured">
                                                            <label for="{{ dir_form.requested_featured_duration.id_for_label }}" class="form-label fw-medium">
                                                                Duration
                                                            </label>
                                                            {{ dir_form.requested_featured_duration }}
                                                            {% if dir_form.requested_featured_duration.help_text %}
                                                                <div class="form-text">{{ dir_form.requested_featured_duration.help_text }}</div>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {# --- End Company Tier & Featured Section --- #}

                            </div> {# End .card-body #}
                        </div> {# End Directory Settings Card #}


                        <!-- Advanced Settings Card -->
                        <div class="card mb-4 border-0 shadow-sm rounded-3">
                            <div class="card-header bg-gradient-primary-subtle py-3">
                                <h5 class="card-title mb-0"><i class="bi bi-gear me-2"></i>Advanced Settings</h5>
                            </div>
                            <div class="card-body p-4">
                                <div class="row g-4">
                                    <!-- Timezone -->
                                    <div class="col-md-6">
                                        <label for="{{ form.timezone.id_for_label }}" class="form-label fw-medium">
                                            <i class="bi bi-clock me-1 text-primary"></i> Timezone
                                        </label>
                                        {{ form.timezone }}
                                        <div class="form-text">
                                            Select your company's primary timezone.
                                        </div>
                                    </div>

                                    <!-- Language -->
                                    <div class="col-md-6">
                                        <label for="{{ form.language.id_for_label }}" class="form-label fw-medium">
                                            <i class="bi bi-translate me-1 text-primary"></i> Language
                                        </label>
                                        {{ form.language }}
                                        <div class="form-text">
                                            Select your company's primary language.
                                        </div>
                                    </div>

                                    <!-- Custom Domain -->
                                    <div class="col-12">
                                        <label for="{{ form.custom_domain.id_for_label }}" class="form-label fw-medium">
                                            <i class="bi bi-link-45deg me-1 text-primary"></i> Custom Domain
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text">https://</span>
                                            {{ form.custom_domain }}
                                        </div>
                                        <div class="form-text">
                                            Enter your domain to use a custom URL for your company workspace.
                                        </div>
                                    </div>
                                </div> {# End .row g-4 #}
                            </div> {# End .card-body #}
                        </div> {# End Advanced Settings Card #}


                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-3 mt-5 mb-3">
                            <button type="reset" class="btn btn-light btn-lg px-4">
                                <i class="bi bi-arrow-counterclockwise me-2"></i>Reset
                            </button>
                            <button type="submit" class="btn btn-primary btn-lg px-4">
                                <i class="bi bi-save me-2"></i>
                                Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Company QR Code -->
            <div class="card border-0 shadow-lg rounded-4 mb-4 overflow-hidden">
                <div class="card-header bg-gradient-primary-subtle py-3">
                    <h5 class="card-title mb-0"><i class="bi bi-qr-code me-2"></i>Company QR Code</h5>
                </div>
                <div class="card-body p-4 text-center">
                    <div id="qr-code-container" class="py-3">
                        {% if company.qr_code %}
                            <div class="mb-4 p-3 bg-light rounded-4 shadow-sm d-inline-block">
                                <img src="{{ company.qr_code.url }}" alt="{{ company.name }} QR Code" class="img-fluid" style="max-width: 180px;" id="company-qr-code-img">
                            </div>
                            <div class="d-flex justify-content-center gap-3 mt-2">
                                <a href="{{ company.qr_code.url }}" download class="btn btn-outline-primary rounded-pill px-4" id="qr-code-download-btn">
                                    <i class="bi bi-download me-2"></i> Download
                                </a>
                                <button type="button" class="btn btn-outline-secondary rounded-pill px-4" id="regenerate-qr-code-btn" data-company-id="{{ company.id }}">
                                    <i class="bi bi-arrow-clockwise me-2"></i> Regenerate
                                </button>
                            </div>
                        {% else %}
                            <div class="alert alert-info rounded-4 mb-4 p-4">
                                <i class="bi bi-info-circle-fill fs-4 mb-3 d-block"></i>
                                <p class="mb-0">QR code not available for your company.</p>
                                <p class="text-muted small mt-2">Generate a QR code to make it easier for users to find your company.</p>
                            </div>
                            <button type="button" class="btn btn-primary rounded-pill px-4 py-2" id="regenerate-qr-code-btn" data-company-id="{{ company.id }}">
                                <i class="bi bi-qr-code me-2"></i> Generate QR Code
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            <!-- End Company QR Code -->

            <!-- Danger Zone -->
            <div class="card border-0 rounded-4 shadow-lg overflow-hidden">
                <div class="card-header bg-danger bg-gradient text-white py-3">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        Danger Zone
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="alert alert-warning rounded-3 mb-4">
                        <p class="mb-0">
                            <i class="bi bi-exclamation-circle me-2"></i>
                            <strong>Warning:</strong> These actions cannot be undone. Please be certain before proceeding.
                        </p>
                    </div>

                    <div class="d-grid gap-3">
                        <button type="button"
                                class="btn btn-outline-danger py-3 rounded-3"
                                data-bs-toggle="modal"
                                data-bs-target="#transferModal">
                            <i class="bi bi-arrow-left-right me-2"></i>
                            Transfer Ownership
                        </button>
                        {# Button to trigger delete modal via JS #}
                        <button type="button"
                                class="btn btn-outline-danger py-3 rounded-3 js-trigger-delete-company-modal"
                                data-company-name="{{ company.name|escapejs }}"
                                data-delete-url="{% url 'accounts:company_delete' company.id %}">
                            <i class="bi bi-trash me-2"></i>
                            Delete Company
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Ownership Modal -->
<div class="modal fade" id="transferModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content rounded-4 shadow">
            {# Form action uncommented as URL pattern 'accounts:transfer_ownership' is confirmed to exist #}
            <form method="post" action="{% url 'accounts:transfer_ownership' company.id %}">
                {% csrf_token %}
                <div class="modal-header border-bottom-0 pb-0">
                    <h5 class="modal-title fs-4">
                        <i class="bi bi-arrow-left-right text-primary me-2"></i>
                        Transfer Company Ownership
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body pt-3">
                    <div class="alert alert-warning rounded-3 mb-4">
                        <p class="mb-0">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <strong>Warning:</strong> This action cannot be undone!
                        </p>
                    </div>

                    <p class="mb-4">
                        You will lose owner privileges and become an administrator.
                        The new owner will have full control over the company.
                    </p>

                    <div class="mb-4">
                        <label for="new_owner" class="form-label fw-medium">Select New Owner</label>
                        <select name="new_owner" id="new_owner" class="form-select form-select-lg" required>
                            <option value="">Choose a member...</option>
                            {% for member in company.members.all %}
                                {% if member != user %}
                                    <option value="{{ member.id }}">
                                        {{ member.get_full_name|default:member.username }}
                                    </option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>

                    <div class="form-check mb-3">
                        <input type="checkbox"
                               class="form-check-input"
                               id="confirm_transfer"
                               required>
                        <label class="form-check-label fw-medium" for="confirm_transfer">
                            I understand that I will lose ownership of this company
                        </label>
                    </div>
                </div>
                <div class="modal-footer border-top-0">
                    <button type="button" class="btn btn-light btn-lg px-4" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger btn-lg px-4" disabled>Transfer Ownership</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Company Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content rounded-4 shadow">
            <div class="modal-header border-bottom-0 pb-0">
                <h5 class="modal-title fs-4" id="deleteCompanyModalLabel">
                    <i class="bi bi-trash text-danger me-2"></i>
                    Confirm Company Deletion
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body pt-3">
                {# Hidden input to store the actual delete URL #}
                <input type="hidden" id="confirmDeleteCompanyUrlInput">

                <div class="alert alert-danger rounded-3 mb-4">
                    <p class="mb-0">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <strong>Warning:</strong> This action cannot be undone!
                    </p>
                </div>

                <p class="mb-3">
                    This will permanently delete the company "<strong id="deleteCompanyNameSpan" class="text-danger"></strong>" and all associated data:
                </p>

                <div class="card bg-light rounded-3 mb-4">
                    <div class="card-body">
                        <ul class="mb-0">
                            <li class="mb-2">All team members will lose access</li>
                            <li class="mb-2">All company content will be deleted</li>
                            <li class="mb-2">All AI assistants will be removed</li>
                            <li>All settings and configurations will be lost</li>
                        </ul>
                    </div>
                </div>

                <div class="mb-4">
                    <label for="company_name" class="form-label fw-medium">
                        Type your company name to confirm
                    </label>
                    <input type="text"
                           class="form-control form-control-lg"
                           id="company_name"
                           required
                           pattern="{{ company.name|escapejs }}"
                           placeholder="Type company name here">
                </div>

                {# Error message display area #}
                <div id="deleteCompanyErrorMsg" class="alert alert-danger rounded-3 mt-3" style="display: none;"></div>
            </div>
            <div class="modal-footer border-top-0">
                <button type="button" class="btn btn-light btn-lg px-4" data-bs-dismiss="modal">Cancel</button>
                <button type="button" id="confirmDeleteCompanyBtn" class="btn btn-danger btn-lg px-4" disabled>Delete Company</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="{% static 'js/qr-code-regeneration.js' %}"></script>
<script src="{% static 'js/category-dropdowns-multi.js' %}"></script>
<script>
// Helper function to get CSRF token (ensure it's defined if not already in base template)
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
const csrftoken = getCookie('csrftoken'); // Get token once

document.addEventListener('DOMContentLoaded', function() {
    // --- Form Submission Handler ---
    const mainForm = document.querySelector('.col-lg-8 .card form');
    if (mainForm) {
        mainForm.addEventListener('submit', function(event) {
            // Get the categories value from the input field
            const categoriesInput = document.getElementById('categories_input');
            const categoriesValueInput = document.getElementById('categories_value');

            if (categoriesInput && categoriesValueInput) {
                // Ensure the categories value is set in the form
                categoriesInput.value = categoriesInput.value || categoriesValueInput.value;
            }
        });
    }

    // --- Existing Transfer Ownership Modal Logic ---
    const transferForm = document.querySelector('#transferModal form');
    if (transferForm) {
        const transferCheckbox = transferForm.querySelector('#confirm_transfer');
        const transferButton = transferForm.querySelector('button[type="submit"]');

        if (transferCheckbox && transferButton) {
            transferCheckbox.addEventListener('change', function() {
                transferButton.disabled = !this.checked;
            });
            transferButton.disabled = true; // Initial state
        }
    }

    // --- Existing Form Dirty Check ---
    const mainForm = document.querySelector('.col-lg-8 .card form'); // More specific selector for the main settings form
    if (mainForm) {
        let formDirty = false;
        mainForm.addEventListener('change', function() {
            formDirty = true;
        });
        window.addEventListener('beforeunload', function(e) {
            if (formDirty) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
        mainForm.addEventListener('submit', function() {
            formDirty = false; // Reset dirty flag on submit
        });
    }

    // --- New Delete Company Modal Logic ---
    const deleteCompanyModalElement = document.getElementById('deleteModal'); // Target the adapted modal
    if (deleteCompanyModalElement) {
        const companyNameInput = deleteCompanyModalElement.querySelector('#company_name');
        const confirmDeleteCompanyBtn = deleteCompanyModalElement.querySelector('#confirmDeleteCompanyBtn');
        const deleteCompanyUrlInput = deleteCompanyModalElement.querySelector('#confirmDeleteCompanyUrlInput');
        const deleteCompanyNameSpan = deleteCompanyModalElement.querySelector('#deleteCompanyNameSpan');
        const deleteCompanyErrorMsg = deleteCompanyModalElement.querySelector('#deleteCompanyErrorMsg');
        const expectedCompanyName = '{{ company.name|escapejs }}'; // Store expected name

        // Listener to trigger the modal
        document.querySelectorAll('.js-trigger-delete-company-modal').forEach(button => {
            button.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();

                const companyName = this.getAttribute('data-company-name');
                const deleteUrl = this.getAttribute('data-delete-url');

                // Populate modal
                deleteCompanyNameSpan.textContent = companyName;
                deleteCompanyUrlInput.value = deleteUrl;
                companyNameInput.value = ''; // Clear input field
                companyNameInput.setAttribute('pattern', companyName); // Set pattern for validation (optional)
                deleteCompanyErrorMsg.style.display = 'none';
                confirmDeleteCompanyBtn.disabled = true; // Start disabled

                // Show modal
                const modalInstance = bootstrap.Modal.getOrCreateInstance(deleteCompanyModalElement);
                modalInstance.show();
            });
        });

        // Listener for the company name input field
        companyNameInput.addEventListener('input', function() {
            // Enable button only if typed name matches expected name
            confirmDeleteCompanyBtn.disabled = this.value !== expectedCompanyName;
        });

        // Listener for the final confirmation button
        confirmDeleteCompanyBtn.addEventListener('click', function() {
            const deleteUrl = deleteCompanyUrlInput.value;
            const typedName = companyNameInput.value;

            // Final check (redundant if button is disabled, but safe)
            if (typedName !== expectedCompanyName) {
                deleteCompanyErrorMsg.textContent = 'Company name does not match.';
                deleteCompanyErrorMsg.style.display = 'block';
                return;
            }
            if (!deleteUrl) {
                 deleteCompanyErrorMsg.textContent = 'Could not determine delete action.';
                 deleteCompanyErrorMsg.style.display = 'block';
                 console.error("Delete URL not found in modal.");
                 return;
            }
             if (!csrftoken) {
                 deleteCompanyErrorMsg.textContent = "CSRF token not found. Please refresh.";
                 deleteCompanyErrorMsg.style.display = 'block';
                 return;
            }

            // Disable button and show spinner (optional)
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Deleting...';
            deleteCompanyErrorMsg.style.display = 'none';

            // Create and submit hidden form
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = deleteUrl;
            form.style.display = 'none';

            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrftoken;
            form.appendChild(csrfInput);

            document.body.appendChild(form);
            form.submit();
        });
    }

    // --- Conditional Duration Fields for Company ---
    // Get the actual DOM elements by their IDs
    const companyRequestTierSelect = document.querySelector('select[name="{{ dir_form.request_new_tier.name }}"]');
    const companyRequestFeaturedCheckbox = document.querySelector('input[name="{{ dir_form.request_featured_status.name }}"]');
    const companyTierDurationWrapper = document.getElementById('company-tier-duration-wrapper');
    const companyFeaturedDurationWrapper = document.getElementById('company-featured-duration-wrapper');

    // Get the values from the Django template
    const companyStandardTierValue = "{{ Company.TIER_STANDARD }}";
    const currentCompanyTierValue = "{{ company.tier }}";
    const currentCompanyFeaturedStatusRaw = "{{ company.is_featured|yesno:'true,false' }}";
    const currentCompanyFeaturedStatus = currentCompanyFeaturedStatusRaw === 'true';

    // Log all element IDs and details for debugging
    console.log("DEBUG: Company Duration Fields - Elements:", {
        tierSelect: companyRequestTierSelect ? {
            id: companyRequestTierSelect.id,
            name: companyRequestTierSelect.name,
            value: companyRequestTierSelect.value
        } : 'Not found',
        featuredCheckbox: companyRequestFeaturedCheckbox ? {
            id: companyRequestFeaturedCheckbox.id,
            name: companyRequestFeaturedCheckbox.name,
            checked: companyRequestFeaturedCheckbox.checked
        } : 'Not found',
        tierDurationWrapper: companyTierDurationWrapper ? companyTierDurationWrapper.id : 'Not found',
        featuredDurationWrapper: companyFeaturedDurationWrapper ? companyFeaturedDurationWrapper.id : 'Not found'
    });

    console.log("DEBUG: Company Duration Fields - Elements found:", {
        companyRequestTierSelect: !!companyRequestTierSelect,
        companyRequestFeaturedCheckbox: !!companyRequestFeaturedCheckbox,
        companyTierDurationWrapper: !!companyTierDurationWrapper,
        companyFeaturedDurationWrapper: !!companyFeaturedDurationWrapper
    });
    console.log("DEBUG: Company Duration Fields - Values:", {
        companyStandardTierValue,
        currentCompanyTierValue,
        currentCompanyFeaturedStatus
    });

    function toggleCompanyDurationFields() {
        let showTierDuration = false;
        let showFeaturedDuration = false;

        // Check Tier Request Selection
        // Show duration if a tier is selected and it's not empty (not "--- No Change ---")
        if (companyRequestTierSelect) {
            console.log("DEBUG: Selected tier value:", companyRequestTierSelect.value);
            // Only show duration if a value is selected and it's not empty
            if (companyRequestTierSelect.value && companyRequestTierSelect.value !== "") {
                showTierDuration = true;
            }
        } else {
            console.error("Company tier select element not found - using direct DOM query");
            // Fallback to direct DOM query if the element wasn't found
            const tierSelect = document.querySelector('select[id$="request_new_tier"]');
            if (tierSelect && tierSelect.value && tierSelect.value !== "") {
                console.log("DEBUG: Found tier select via direct query:", tierSelect.value);
                showTierDuration = true;
            }
        }

        // Check Featured Request Checkbox
        // Show duration if checkbox is checked (regardless of current status)
        if (companyRequestFeaturedCheckbox) {
            console.log("DEBUG: Featured checkbox checked:", companyRequestFeaturedCheckbox.checked);
            if (companyRequestFeaturedCheckbox.checked) {
                showFeaturedDuration = true;
            }
        } else {
            console.error("Company featured checkbox not found - using direct DOM query");
            // Fallback to direct DOM query if the element wasn't found
            const featuredCheckbox = document.querySelector('input[id$="request_featured_status"]');
            if (featuredCheckbox && featuredCheckbox.checked) {
                console.log("DEBUG: Found featured checkbox via direct query:", featuredCheckbox.checked);
                showFeaturedDuration = true;
            }
        }

        console.log("DEBUG: toggleCompanyDurationFields - Conditions:", {
            showTierDuration,
            showFeaturedDuration,
            requestTierValue: companyRequestTierSelect ? companyRequestTierSelect.value : 'N/A',
            requestFeaturedChecked: companyRequestFeaturedCheckbox ? companyRequestFeaturedCheckbox.checked : 'N/A'
        });

        // Apply display styles
        // Try to get the duration wrappers by ID or query selector
        const tierDurationWrappers = [
            companyTierDurationWrapper,
            document.getElementById('company-tier-duration-wrapper'),
            document.querySelector('[id="company-tier-duration-wrapper"]'),
            document.querySelector('.mt-3[id="company-tier-duration-wrapper"]')
        ];

        const featuredDurationWrappers = [
            companyFeaturedDurationWrapper,
            document.getElementById('company-featured-duration-wrapper'),
            document.querySelector('[id="company-featured-duration-wrapper"]'),
            document.querySelector('.mt-3[id="company-featured-duration-wrapper"]')
        ];

        // Find the first non-null tier duration wrapper
        const tierDurationWrapper = tierDurationWrappers.find(el => el !== null);
        if (tierDurationWrapper) {
            tierDurationWrapper.style.display = showTierDuration ? 'block' : 'none';
            console.log(`Company Tier Duration Visible: ${showTierDuration} (Element ID: ${tierDurationWrapper.id})`);
        } else {
            console.error("Company tier duration wrapper not found with any selector");
        }

        // Find the first non-null featured duration wrapper
        const featuredDurationWrapper = featuredDurationWrappers.find(el => el !== null);
        if (featuredDurationWrapper) {
            featuredDurationWrapper.style.display = showFeaturedDuration ? 'block' : 'none';
            console.log(`Company Featured Duration Visible: ${showFeaturedDuration} (Element ID: ${featuredDurationWrapper.id})`);
        } else {
            console.error("Company featured duration wrapper not found with any selector");
        }
    }

    // Add event listeners to the form elements
    function setupEventListeners() {
        // Try to get elements by various selectors to ensure we find them
        const tierSelectors = [
            companyRequestTierSelect,
            document.querySelector('select[name="{{ dir_form.request_new_tier.name }}"]'),
            document.querySelector('select[id$="request_new_tier"]'),
            document.querySelector('select[id*="request_new_tier"]')
        ];

        const featuredSelectors = [
            companyRequestFeaturedCheckbox,
            document.querySelector('input[name="{{ dir_form.request_featured_status.name }}"]'),
            document.querySelector('input[id$="request_featured_status"]'),
            document.querySelector('input[id*="request_featured_status"]')
        ];

        // Find the first non-null tier select element
        const tierSelect = tierSelectors.find(el => el !== null);
        if (tierSelect) {
            tierSelect.addEventListener('change', toggleCompanyDurationFields);
            console.log("Added change listener to company request tier select:", tierSelect.id);
        } else {
            console.error("Company request tier select element not found with any selector.");
        }

        // Find the first non-null featured checkbox
        const featuredCheckbox = featuredSelectors.find(el => el !== null);
        if (featuredCheckbox) {
            featuredCheckbox.addEventListener('change', toggleCompanyDurationFields);
            console.log("Added change listener to company request featured checkbox:", featuredCheckbox.id);
        } else {
            console.error("Company request featured checkbox element not found with any selector.");
        }
    }

    // Setup the event listeners
    setupEventListeners();

    // Initial check on page load
    console.log("Running initial toggleCompanyDurationFields on page load.");
    toggleCompanyDurationFields();

    // Add a more aggressive approach to ensure the duration fields are shown
    function forceShowDurationFields() {
        // Force check the form elements again
        const tierSelect = document.querySelector('select[id$="request_new_tier"]');
        const featuredCheckbox = document.querySelector('input[id$="request_featured_status"]');

        // Get all possible duration wrappers
        const tierDurationWrapper = document.getElementById('company-tier-duration-wrapper');
        const featuredDurationWrapper = document.getElementById('company-featured-duration-wrapper');

        console.log("FORCE CHECK - Form Elements:", {
            tierSelect: tierSelect ? { value: tierSelect.value, id: tierSelect.id } : 'Not found',
            featuredCheckbox: featuredCheckbox ? { checked: featuredCheckbox.checked, id: featuredCheckbox.id } : 'Not found',
            tierDurationWrapper: tierDurationWrapper ? tierDurationWrapper.id : 'Not found',
            featuredDurationWrapper: featuredDurationWrapper ? featuredDurationWrapper.id : 'Not found'
        });

        // Show duration fields if appropriate
        if (tierSelect && tierSelect.value && tierSelect.value !== "" && tierDurationWrapper) {
            console.log("FORCE SHOWING tier duration wrapper");
            tierDurationWrapper.style.display = 'block';
        }

        if (featuredCheckbox && featuredCheckbox.checked && featuredDurationWrapper) {
            console.log("FORCE SHOWING featured duration wrapper");
            featuredDurationWrapper.style.display = 'block';
        }
    }

    // Run the force check after a short delay to ensure the DOM is fully loaded
    setTimeout(forceShowDurationFields, 500);

    // Add direct event listeners to the form elements
    document.addEventListener('change', function(event) {
        const target = event.target;

        // Check if the changed element is the tier select or featured checkbox
        if (target && (
            target.id.includes('request_new_tier') ||
            target.name.includes('request_new_tier') ||
            target.id.includes('request_featured_status') ||
            target.name.includes('request_featured_status')
        )) {
            console.log("Direct change event detected on:", target.id);
            toggleCompanyDurationFields();
            setTimeout(forceShowDurationFields, 100); // Run force check after a short delay
        }
    });

    // --- End Conditional Duration Fields ---

});
</script>
<!-- Direct script to ensure duration fields are shown -->
<script>
// This script runs directly in the page to ensure the duration fields are shown
document.addEventListener('DOMContentLoaded', function() {
    // Function to handle showing/hiding duration fields
    function handleDurationFields() {
        // Get form elements
        const tierSelect = document.querySelector('select[id$="request_new_tier"]');
        const featuredCheckbox = document.querySelector('input[id$="request_featured_status"]');

        // Get duration wrappers
        const tierDurationWrapper = document.getElementById('company-tier-duration-wrapper');
        const featuredDurationWrapper = document.getElementById('company-featured-duration-wrapper');

        console.log("Direct script - Form elements:", {
            tierSelect: tierSelect ? tierSelect.id : 'Not found',
            featuredCheckbox: featuredCheckbox ? featuredCheckbox.id : 'Not found',
            tierDurationWrapper: tierDurationWrapper ? tierDurationWrapper.id : 'Not found',
            featuredDurationWrapper: featuredDurationWrapper ? featuredDurationWrapper.id : 'Not found'
        });

        // Show/hide tier duration
        if (tierSelect && tierDurationWrapper) {
            tierSelect.addEventListener('change', function() {
                console.log("Direct tier select change:", this.value);
                tierDurationWrapper.style.display = (this.value && this.value !== "") ? 'block' : 'none';
            });

            // Initial check
            if (tierSelect.value && tierSelect.value !== "") {
                tierDurationWrapper.style.display = 'block';
            }
        }

        // Show/hide featured duration
        if (featuredCheckbox && featuredDurationWrapper) {
            featuredCheckbox.addEventListener('change', function() {
                console.log("Direct featured checkbox change:", this.checked);
                featuredDurationWrapper.style.display = this.checked ? 'block' : 'none';
            });

            // Initial check
            if (featuredCheckbox.checked) {
                featuredDurationWrapper.style.display = 'block';
            }
        }
    }

    // Run immediately
    handleDurationFields();

    // Also run after a short delay to ensure everything is loaded
    setTimeout(handleDurationFields, 500);
});
</script>
{% endblock %}