@echo off
echo PostgreSQL Database Setup Script
echo ===============================
echo.

set /p DB_NAME=Enter database name [virtualo]: 
if "%DB_NAME%"=="" set DB_NAME=virtualo

set /p DB_USER=Enter PostgreSQL username [postgres]: 
if "%DB_USER%"=="" set DB_USER=postgres

set /p DB_PASSWORD=Enter PostgreSQL password: 

echo.
echo Creating database "%DB_NAME%"...
echo.

:: Create a temporary SQL file with the database name
echo -- PostgreSQL setup script > temp_setup.sql
echo DROP DATABASE IF EXISTS %DB_NAME%; >> temp_setup.sql
echo CREATE DATABASE %DB_NAME%; >> temp_setup.sql
echo. >> temp_setup.sql
echo \c %DB_NAME% >> temp_setup.sql
echo. >> temp_setup.sql
echo CREATE EXTENSION IF NOT EXISTS pg_trgm; >> temp_setup.sql
echo CREATE EXTENSION IF NOT EXISTS unaccent; >> temp_setup.sql
echo. >> temp_setup.sql
echo \echo 'Database "%DB_NAME%" created successfully!' >> temp_setup.sql

:: Run the SQL script
set PGPASSWORD=%DB_PASSWORD%
psql -U %DB_USER% -f temp_setup.sql

:: Check if the command was successful
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Failed to create database. Please check your PostgreSQL installation and credentials.
    goto :cleanup
)

echo.
echo Updating Django settings...

:: Update local_settings.py
set SETTINGS_FILE=company_assistant\local_settings.py

:: Create a temporary Python script to update the settings
echo import re > update_settings.py
echo with open('%SETTINGS_FILE%', 'r') as f: >> update_settings.py
echo     content = f.read() >> update_settings.py
echo content = re.sub(r"'NAME': '.*?'", "'NAME': '%DB_NAME%'", content) >> update_settings.py
echo content = re.sub(r"'USER': '.*?'", "'USER': '%DB_USER%'", content) >> update_settings.py
echo content = re.sub(r"'PASSWORD': '.*?'", "'PASSWORD': '%DB_PASSWORD%'", content) >> update_settings.py
echo with open('%SETTINGS_FILE%', 'w') as f: >> update_settings.py
echo     f.write(content) >> update_settings.py
echo print("Updated %SETTINGS_FILE% with new database settings.") >> update_settings.py

:: Run the Python script
python update_settings.py

echo.
echo Next steps:
echo 1. Run migrations: python manage.py migrate
echo 2. Create a superuser: python manage.py createsuperuser
echo 3. Run the development server: python manage.py runserver

:cleanup
:: Clean up temporary files
del temp_setup.sql
del update_settings.py

pause
