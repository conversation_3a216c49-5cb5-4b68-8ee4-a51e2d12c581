/**
 * Category Dropdowns - Handles industry and category selection
 *
 * This script loads categories from the category.json file and creates
 * dynamic dropdowns for industry and category selection.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log("Category Dropdowns script loaded");

    // Global variables
    let categoriesData = {};
    let activeDropdown = null;

    // Load categories data
    fetch('/static/json/category.json')
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to load categories data');
            }
            return response.json();
        })
        .then(data => {
            categoriesData = data;
            console.log("Categories loaded:", Object.keys(categoriesData).length);
            initializeDropdowns();
        })
        .catch(error => {
            console.error("Error loading categories:", error);
            // Fallback data
            categoriesData = {
                "Technology": ["Software", "Hardware", "Cloud Computing"],
                "Healthcare": ["Hospitals", "Pharmaceuticals", "Medical Devices"],
                "Candy & Soda": ["Candy and other confectionery", "Bottled-canned soft drinks", "Flavoring syrup", "Potato chips", "Manufactured ice"]
            };
            console.log("Using fallback data");
            initializeDropdowns();
        });

    /**
     * Initialize all dropdown components
     */
    function initializeDropdowns() {
        setupIndustryDropdowns();

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.dropdown-container')) {
                closeAllDropdowns();
            }
        });

        // Close dropdowns when pressing escape
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeAllDropdowns();
            }
        });
    }

    /**
     * Set up industry dropdown components
     */
    function setupIndustryDropdowns() {
        const industryContainers = document.querySelectorAll('.industry-dropdown-container');

        industryContainers.forEach(container => {
            const input = container.querySelector('input[type="text"]');
            const hiddenInput = container.querySelector('input[type="hidden"]');
            const dropdownList = container.querySelector('.dropdown-list');

            if (!input || !dropdownList) return;

            // Create industry options
            populateIndustryDropdown(dropdownList);

            // Show dropdown on focus
            input.addEventListener('focus', function() {
                closeAllDropdowns();
                showDropdown(dropdownList);
                activeDropdown = dropdownList;
            });

            // Filter options on input
            input.addEventListener('input', function() {
                filterDropdownOptions(input.value, dropdownList);
                showDropdown(dropdownList);

                // Update hidden input value
                if (hiddenInput) {
                    hiddenInput.value = input.value;
                }
            });

            // Check if industry is already selected
            if (input.value) {
                updateCategoryDropdowns(input.value);
            }
        });
    }

    /**
     * Populate industry dropdown with options
     */
    function populateIndustryDropdown(dropdownList) {
        // Clear existing options
        dropdownList.innerHTML = '';

        // Add industry options
        Object.keys(categoriesData).forEach(industry => {
            const option = document.createElement('div');
            option.className = 'dropdown-item';
            option.textContent = industry;

            option.addEventListener('click', function(event) {
                event.stopPropagation();
                const container = dropdownList.closest('.dropdown-container');
                const input = container.querySelector('input[type="text"]');
                const hiddenInput = container.querySelector('input[type="hidden"]');

                if (input) {
                    input.value = industry;
                }

                if (hiddenInput) {
                    hiddenInput.value = industry;
                }

                hideDropdown(dropdownList);

                // Update category dropdowns
                updateCategoryDropdowns(industry);
            });

            dropdownList.appendChild(option);
        });
    }

    /**
     * Filter dropdown options based on input value
     */
    function filterDropdownOptions(value, dropdownList) {
        const searchText = value.toLowerCase();

        // Filter options
        const options = dropdownList.querySelectorAll('.dropdown-item');
        let hasVisibleOptions = false;

        options.forEach(option => {
            if (option.textContent.toLowerCase().includes(searchText)) {
                option.style.display = 'block';
                hasVisibleOptions = true;
            } else {
                option.style.display = 'none';
            }
        });

        // If no options match, hide dropdown
        if (!hasVisibleOptions && searchText) {
            hideDropdown(dropdownList);
        }
    }

    /**
     * Update category dropdowns based on selected industry
     */
    function updateCategoryDropdowns(industry) {
        console.log("Updating categories for industry:", industry);

        const categoryContainers = document.querySelectorAll('.category-dropdown-container');

        categoryContainers.forEach(container => {
            const input = container.querySelector('input[type="text"]');
            const hiddenInput = container.querySelector('input[type="hidden"]');
            const dropdownList = container.querySelector('.dropdown-list');

            if (!input || !dropdownList) return;

            // Remove existing event listeners
            const newInput = input.cloneNode(true);
            input.parentNode.replaceChild(newInput, input);

            // Clear existing options
            dropdownList.innerHTML = '';
            newInput.value = '';
            if (hiddenInput) {
                hiddenInput.value = '';
            }

            // Get categories for selected industry
            const categories = categoriesData[industry] || [];
            console.log("Found categories:", categories.length);

            // Add category options
            categories.forEach(category => {
                const option = document.createElement('div');
                option.className = 'dropdown-item';
                option.textContent = category;

                option.addEventListener('click', function(event) {
                    event.stopPropagation();
                    newInput.value = category;
                    if (hiddenInput) {
                        hiddenInput.value = category;
                    }
                    hideDropdown(dropdownList);
                });

                dropdownList.appendChild(option);
            });

            // Show dropdown on focus
            newInput.addEventListener('focus', function() {
                closeAllDropdowns();
                showDropdown(dropdownList);
                activeDropdown = dropdownList;
            });

            // Filter options on input
            newInput.addEventListener('input', function() {
                filterDropdownOptions(newInput.value, dropdownList);
                showDropdown(dropdownList);
                if (hiddenInput) {
                    hiddenInput.value = newInput.value;
                }
            });
        });
    }

    /**
     * Show dropdown list
     */
    function showDropdown(dropdownList) {
        dropdownList.style.display = 'block';
        dropdownList.classList.add('show');

        // Ensure dropdown is visible within viewport
        const rect = dropdownList.getBoundingClientRect();
        const viewportHeight = window.innerHeight;

        if (rect.bottom > viewportHeight) {
            dropdownList.style.maxHeight = (viewportHeight - rect.top - 20) + 'px';
        }
    }

    /**
     * Hide dropdown list
     */
    function hideDropdown(dropdownList) {
        dropdownList.style.display = 'none';
        dropdownList.classList.remove('show');
    }

    /**
     * Close all open dropdowns
     */
    function closeAllDropdowns() {
        document.querySelectorAll('.dropdown-list').forEach(list => {
            hideDropdown(list);
        });
        activeDropdown = null;
    }
});
