"""
Script to reset Django migration state in PostgreSQL database.
This is useful when migrating from SQLite to PostgreSQL.
"""
import os
import django
import psycopg2
from django.conf import settings

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Get database connection info from settings
db_settings = settings.DATABASES['default']

def reset_migrations():
    """Reset all migration records in the database."""
    print("Connecting to PostgreSQL database...")
    try:
        # Connect to PostgreSQL
        conn_params = {
            'dbname': db_settings['NAME'],
            'user': db_settings['USER'],
            'host': db_settings['HOST'],
            'port': db_settings['PORT']
        }

        # Only add password if it's not empty
        if db_settings['PASSWORD']:
            conn_params['password'] = db_settings['PASSWORD']

        conn = psycopg2.connect(**conn_params)

        # Create a cursor
        cursor = conn.cursor()

        # Check if django_migrations table exists
        cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_name = 'django_migrations'
        )
        """)

        table_exists = cursor.fetchone()[0]

        if table_exists:
            # Delete all migration records
            print("Deleting all migration records...")
            cursor.execute("DELETE FROM django_migrations")
            print("All migration records deleted.")
        else:
            print("The django_migrations table doesn't exist yet. No need to reset.")

        # Commit the changes
        conn.commit()
        print("Migration reset completed successfully.")

    except Exception as e:
        print(f"An error occurred: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        # Close the connection
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def mark_migrations_as_applied(app_name, migration_name):
    """Mark a specific migration as applied."""
    try:
        # Connect to PostgreSQL
        conn_params = {
            'dbname': db_settings['NAME'],
            'user': db_settings['USER'],
            'host': db_settings['HOST'],
            'port': db_settings['PORT']
        }

        # Only add password if it's not empty
        if db_settings['PASSWORD']:
            conn_params['password'] = db_settings['PASSWORD']

        conn = psycopg2.connect(**conn_params)

        # Create a cursor
        cursor = conn.cursor()

        # Check if the migration is already marked as applied
        cursor.execute("""
        SELECT EXISTS (
            SELECT FROM django_migrations
            WHERE app = %s AND name = %s
        )
        """, [app_name, migration_name])

        migration_exists = cursor.fetchone()[0]

        if migration_exists:
            print(f"Migration {app_name}.{migration_name} is already marked as applied.")
        else:
            # Mark the migration as applied
            cursor.execute("""
            INSERT INTO django_migrations (app, name, applied)
            VALUES (%s, %s, NOW())
            """, [app_name, migration_name])
            print(f"Migration {app_name}.{migration_name} marked as applied successfully.")

        # Commit the changes
        conn.commit()

    except Exception as e:
        print(f"An error occurred: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        # Close the connection
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    import sys

    if len(sys.argv) == 1:
        # No arguments, reset all migrations
        reset_migrations()
    elif len(sys.argv) == 3:
        # Mark specific migration as applied
        app_name = sys.argv[1]
        migration_name = sys.argv[2]
        mark_migrations_as_applied(app_name, migration_name)
    else:
        print("Usage:")
        print("  python reset_migrations.py                    # Reset all migrations")
        print("  python reset_migrations.py app_name migration_name  # Mark specific migration as applied")
