{% extends 'base/layout.html' %}
{% load static %}
{% load tinymce_tags %}

{% block title %}TinyMCE Example{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">TinyMCE Example</h1>
            
            <div class="card mb-5">
                <div class="card-header">
                    <h5 class="card-title mb-0">Method 1: Using the tinymce_textarea Template Tag</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="#">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="id_content1" class="form-label">Content</label>
                            {% tinymce_textarea name="content1" id="id_content1" placeholder="Enter your content here..." %}
                        </div>
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </form>
                </div>
            </div>
            
            <div class="card mb-5">
                <div class="card-header">
                    <h5 class="card-title mb-0">Method 2: Using the tinymce-editor Class</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="#">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="id_content2" class="form-label">Content</label>
                            <textarea id="id_content2" name="content2" class="form-control tinymce-editor" rows="10" placeholder="Enter your content here..."></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </form>
                </div>
            </div>
            
            <div class="card mb-5">
                <div class="card-header">
                    <h5 class="card-title mb-0">Method 3: Using the add_tinymce_class Filter with Django Forms</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">In your template:</p>
                    <pre><code>{{ '{% load tinymce_tags %}' }}
{{ '{{ form.content|add_tinymce_class }}' }}</code></pre>
                    
                    <p class="card-text mt-3">In your forms.py:</p>
                    <pre><code>class MyForm(forms.Form):
    content = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 10}),
        label="Content",
        help_text="Enter your content here"
    )</code></pre>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Method 4: Dynamically Adding TinyMCE to Textareas</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">You can dynamically add TinyMCE to any textarea using JavaScript:</p>
                    <pre><code>// Add the tinymce-editor class to the textarea
document.getElementById('my-textarea').classList.add('tinymce-editor');

// Then initialize TinyMCE on this element
if (typeof window.initializeDynamicTinyMCE === 'function') {
    window.initializeDynamicTinyMCE(document.getElementById('my-textarea'));
}</code></pre>
                    
                    <div class="mt-4">
                        <label for="dynamic-textarea" class="form-label">Try it out:</label>
                        <textarea id="dynamic-textarea" class="form-control" rows="5" placeholder="This is a regular textarea. Click the button below to convert it to TinyMCE."></textarea>
                        <button type="button" class="btn btn-primary mt-2" id="convert-btn">Convert to TinyMCE</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add event listener to the convert button
        document.getElementById('convert-btn').addEventListener('click', function() {
            const textarea = document.getElementById('dynamic-textarea');
            
            // Add the tinymce-editor class
            textarea.classList.add('tinymce-editor');
            
            // Initialize TinyMCE on this element
            if (typeof window.initializeDynamicTinyMCE === 'function') {
                window.initializeDynamicTinyMCE(textarea);
                
                // Disable the button
                this.disabled = true;
                this.textContent = 'Converted!';
            } else {
                alert('TinyMCE initialization function not found!');
            }
        });
    });
</script>
{% endblock %}
