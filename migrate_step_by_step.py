"""
<PERSON><PERSON>t to migrate Django project to PostgreSQL step by step.
"""
import os
import sys
import subprocess
import time

def run_command(command, description=None):
    """Run a command and return its output and success status."""
    if description:
        print(f"\n{description}...")
    
    print(f"Running: {command}")
    try:
        result = subprocess.run(
            command,
            check=True,
            capture_output=True,
            text=True,
            shell=True
        )
        print(result.stdout)
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        print(f"Command failed with exit code {e.returncode}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        return False, e.stderr

def migrate_app(app_name=None, fake=False, fake_initial=False):
    """Run migrations for a specific app or all apps."""
    command = "python manage.py migrate"
    
    if app_name:
        command += f" {app_name}"
    
    if fake:
        command += " --fake"
    elif fake_initial:
        command += " --fake-initial"
    
    description = f"Migrating {app_name if app_name else 'all apps'}"
    if fake:
        description += " (fake)"
    elif fake_initial:
        description += " (fake-initial)"
    
    return run_command(command, description)

def main():
    """Main function to migrate step by step."""
    print("Django PostgreSQL Migration - Step by Step")
    print("=========================================")
    
    # Step 1: Check if psycopg2 is installed
    success, _ = run_command("pip show psycopg2-binary", "Checking if psycopg2-binary is installed")
    if not success:
        print("Installing psycopg2-binary...")
        success, _ = run_command("pip install psycopg2-binary", "Installing psycopg2-binary")
        if not success:
            print("Failed to install psycopg2-binary. Aborting.")
            return
    
    # Step 2: Migrate the contenttypes app first (often needed for other apps)
    success, _ = migrate_app("contenttypes", fake_initial=True)
    if not success:
        print("Failed to migrate contenttypes. Trying without --fake-initial...")
        success, _ = migrate_app("contenttypes")
        if not success:
            print("Failed to migrate contenttypes. Aborting.")
            return
    
    # Step 3: Migrate the auth app
    success, _ = migrate_app("auth", fake_initial=True)
    if not success:
        print("Failed to migrate auth. Trying without --fake-initial...")
        success, _ = migrate_app("auth")
        if not success:
            print("Failed to migrate auth. Aborting.")
            return
    
    # Step 4: Migrate the admin app
    success, _ = migrate_app("admin", fake_initial=True)
    if not success:
        print("Failed to migrate admin. Trying without --fake-initial...")
        success, _ = migrate_app("admin")
    
    # Step 5: Migrate the sessions app
    success, _ = migrate_app("sessions", fake_initial=True)
    if not success:
        print("Failed to migrate sessions. Trying without --fake-initial...")
        success, _ = migrate_app("sessions")
    
    # Step 6: Migrate the sites app
    success, _ = migrate_app("sites", fake_initial=True)
    if not success:
        print("Failed to migrate sites. Trying without --fake-initial...")
        success, _ = migrate_app("sites")
    
    # Step 7: Migrate the remaining apps
    print("\nMigrating remaining apps...")
    
    # List of apps from your INSTALLED_APPS
    apps = [
        "debug_toolbar",
        "widget_tweaks",
        "crispy_forms",
        "crispy_bootstrap5",
        "guardian",
        "impersonate",
        "tinymce",
        "accounts",
        "assistants",
        "content",
        "directory",
        "site_settings",
        "superadmin",
    ]
    
    for app in apps:
        print(f"\nMigrating {app}...")
        success, _ = migrate_app(app, fake_initial=True)
        if not success:
            print(f"Failed to migrate {app} with --fake-initial. Trying without...")
            success, _ = migrate_app(app)
            if not success:
                print(f"Failed to migrate {app}. Continuing with next app...")
    
    # Step 8: Final migration to catch any dependencies
    print("\nRunning final migration to catch any dependencies...")
    success, _ = migrate_app(fake_initial=True)
    if not success:
        print("Failed to run final migration with --fake-initial. Trying without...")
        success, _ = migrate_app()
    
    print("\nMigration process completed.")
    print("Check the output above for any errors.")
    print("\nNext steps:")
    print("1. Create a superuser: python manage.py createsuperuser")
    print("2. Run the development server: python manage.py runserver")

if __name__ == "__main__":
    main()
