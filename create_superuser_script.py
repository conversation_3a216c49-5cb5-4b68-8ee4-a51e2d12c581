import os
import django
import getpass

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.auth.models import User
from django.db import IntegrityError

print("Creating a superuser account")
print("===========================")

# Get superuser details
while True:
    username = input("Username (default: admin): ").strip() or "admin"
    email = input("Email (default: <EMAIL>): ").strip() or "<EMAIL>"
    
    # Check if user already exists
    if User.objects.filter(username=username).exists():
        print(f"User '{username}' already exists.")
        use_existing = input("Do you want to update this user? (y/n): ").strip().lower()
        if use_existing != 'y':
            continue
    
    # Get password
    while True:
        password = getpass.getpass("Password (default: admin123): ") or "admin123"
        if len(password) < 8:
            print("Password must be at least 8 characters long.")
            continue
        
        password_confirm = getpass.getpass("Confirm password: ")
        if password != password_confirm:
            print("Passwords do not match. Please try again.")
            continue
        
        break
    
    break

try:
    # Create or update the superuser
    if User.objects.filter(username=username).exists():
        user = User.objects.get(username=username)
        user.email = email
        user.set_password(password)
        user.is_staff = True
        user.is_superuser = True
        user.save()
        print(f"\nSuperuser '{username}' updated successfully!")
    else:
        User.objects.create_superuser(username=username, email=email, password=password)
        print(f"\nSuperuser '{username}' created successfully!")
    
    print(f"Username: {username}")
    print(f"Email: {email}")
    print("\nYou can now log in to the admin interface at http://127.0.0.1:8000/admin/")
    
except IntegrityError as e:
    print(f"Error: {e}")
except Exception as e:
    print(f"An unexpected error occurred: {e}")
