import os
import shutil
from django.conf import settings

def check_media_directories():
    """
    Check and create necessary media directories for the application.
    Returns a dictionary with the status of each directory.
    """
    directories = [
        'assistant_logos',
        'assistant_avatars',
        'assistant_qrcodes',
        'uploads',
        'company_logos',
        'site_defaults',
    ]

    results = {}

    # Ensure MEDIA_ROOT exists
    if not os.path.exists(settings.MEDIA_ROOT):
        try:
            os.makedirs(settings.MEDIA_ROOT)
            results['media_root'] = f"Created: {settings.MEDIA_ROOT}"
        except Exception as e:
            results['media_root'] = f"Error creating: {str(e)}"
    else:
        results['media_root'] = f"Exists: {settings.MEDIA_ROOT}"

    # Check each subdirectory
    for directory in directories:
        dir_path = os.path.join(settings.MEDIA_ROOT, directory)
        if not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path)
                results[directory] = f"Created: {dir_path}"
            except Exception as e:
                results[directory] = f"Error creating: {str(e)}"
        else:
            results[directory] = f"Exists: {dir_path}"

    return results

def ensure_media_directories():
    """
    Ensure all necessary media directories exist.
    This is a simplified version of check_media_directories that doesn't return results.
    """
    directories = [
        'assistant_logos',
        'assistant_avatars',
        'assistant_qrcodes',
        'uploads',
        'company_logos',
        'site_defaults',
        'tinymce_uploads',
    ]

    # Ensure MEDIA_ROOT exists
    if not os.path.exists(settings.MEDIA_ROOT):
        os.makedirs(settings.MEDIA_ROOT)

    # Check each subdirectory
    for directory in directories:
        dir_path = os.path.join(settings.MEDIA_ROOT, directory)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)

    return True

def fix_logo_paths():
    """
    Scan the media directory for logo files and ensure they're in the correct location.
    Returns a dictionary with the results of the operation.
    """
    results = {
        'scanned': 0,
        'moved': 0,
        'errors': 0,
        'details': []
    }

    # First ensure directories exist
    check_media_directories()

    # Scan MEDIA_ROOT for image files
    for root, dirs, files in os.walk(settings.MEDIA_ROOT):
        for file in files:
            results['scanned'] += 1

            # Check if it's an image file
            if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.svg')):
                file_path = os.path.join(root, file)

                # Skip files already in the correct directories
                if any(d in file_path for d in ['assistant_logos/', 'company_logos/', 'assistant_avatars/']):
                    continue

                # Determine target directory based on filename or path
                target_dir = None
                if 'logo' in file.lower():
                    target_dir = 'assistant_logos'
                elif 'avatar' in file.lower():
                    target_dir = 'assistant_avatars'
                else:
                    # Default to assistant_logos for now
                    target_dir = 'assistant_logos'

                # Create target path
                target_path = os.path.join(settings.MEDIA_ROOT, target_dir, file)

                # Move the file
                try:
                    if not os.path.exists(target_path):
                        shutil.copy2(file_path, target_path)
                        results['moved'] += 1
                        results['details'].append({
                            'source': file_path,
                            'target': target_path,
                            'status': 'copied'
                        })
                except Exception as e:
                    results['errors'] += 1
                    results['details'].append({
                        'source': file_path,
                        'target': target_path,
                        'status': f'error: {str(e)}'
                    })

    return results
