# Simple Deployment Guide

This guide provides a straightforward approach to fix CSS loading issues on cPanel using Whitenoise.

## What We've Done

1. **Added Whitenoise to the main settings.py file**:
   - Added Whitenoise middleware
   - Configured Whitenoise for static file serving
   - Set DEBUG to be configurable via environment variable

2. **Updated passenger_wsgi.py**:
   - Set DEBUG to False for production
   - Uses the standard settings.py file

## Deployment Steps

### 1. Upload the Updated Files

Upload these files to your cPanel environment:
- `company_assistant/settings.py`
- `passenger_wsgi.py`

### 2. Install Whitenoise

Make sure Whitenoise is installed in your cPanel environment:

```bash
pip install whitenoise
```

### 3. Collect Static Files

Run the Django collectstatic command:

```bash
python manage.py collectstatic --noinput
```

### 4. Restart Your Application

Restart your application in cPanel:
- Go to "Setup Python App" in cPanel and click "Restart App", or
- Use SSH to touch the WSGI file: `touch passenger_wsgi.py`

### 5. Clear Browser Cache

Clear your browser cache completely before testing.

## Troubleshooting

If you encounter issues with the `CompressedManifestStaticFilesStorage`, try changing it to a simpler storage backend in settings.py:

```python
# Try this if you have issues with the default storage
STATICFILES_STORAGE = 'whitenoise.storage.CompressedStaticFilesStorage'

# Or this for the simplest option
STATICFILES_STORAGE = 'whitenoise.storage.StaticFilesStorage'
```

After changing the storage backend, run collectstatic again and restart your application.

## Fixing Migration Errors

If you're still encountering migration errors, you can mark problematic migrations as applied:

1. Run the script to mark the migration as applied:
   ```bash
   python mark_migration_applied.py
   ```

2. Continue with migrations:
   ```bash
   python manage.py migrate
   ```

## Additional Tips

### Checking Static Files

To verify that your static files have been collected properly:

```bash
ls -la staticfiles/css/
```

### Setting File Permissions

If needed, set proper file permissions:

```bash
find staticfiles/ -type d -exec chmod 755 {} \;
find staticfiles/ -type f -exec chmod 644 {} \;
```

### Checking Logs

If you encounter issues, check the Django logs:

```bash
tail -f logs/django.log
```
