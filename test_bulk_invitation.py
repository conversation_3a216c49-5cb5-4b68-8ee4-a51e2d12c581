"""
Test script for bulk invitation functionality.
"""

import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from accounts.models import Company, CompanyInvitation, Membership
from accounts.forms import TeamInvitationForm
from accounts.email_utils import send_team_invitation_email, send_bulk_invitation_emails
from site_settings.models import SiteConfiguration

User = get_user_model()

def test_email_config():
    """Test email configuration."""
    from django.conf import settings
    print(f"Email settings:")
    print(f"EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
    print(f"EMAIL_HOST: {settings.EMAIL_HOST}")
    print(f"EMAIL_PORT: {settings.EMAIL_PORT}")
    print(f"EMAIL_USE_SSL: {getattr(settings, 'EMAIL_USE_SSL', False)}")
    print(f"EMAIL_USE_TLS: {getattr(settings, 'EMAIL_USE_TLS', False)}")
    print(f"EMAIL_HOST_USER: {settings.EMAIL_HOST_USER}")
    print(f"DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}")

def test_bulk_invitation():
    """Test bulk invitation functionality."""
    try:
        # Get or create a test user and company
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'is_active': True,
            }
        )

        company, created = Company.objects.get_or_create(
            name='Test Company',
            defaults={
                'owner': user,
                'is_active': True,
            }
        )

        print(f"Testing bulk invitation for company: {company.name}")

        # Create a list of test email addresses with timestamps to ensure uniqueness
        import time
        timestamp = int(time.time())
        test_emails = [
            f'test1_{timestamp}@example.com',
            f'test2_{timestamp}@example.com',
            f'test3_{timestamp}@example.com'
        ]

        # Create form data
        form_data = {
            'emails': '\n'.join(test_emails),
            'message': 'This is a test invitation message for bulk invitations.'
        }

        # Create and validate the form
        form = TeamInvitationForm(data=form_data)

        if form.is_valid():
            print(f"Form is valid. Sending {len(form.cleaned_data['emails'])} invitations...")

            # Save the form (this creates the invitations and sends emails)
            invitations = form.save(company=company, invited_by=user)

            print(f"Created {len(invitations)} invitations:")
            for invitation in invitations:
                print(f"  - {invitation.email} (Token: {invitation.token[:8]}...)")

            # Test sending bulk invitation emails
            success_count, failed_emails = send_bulk_invitation_emails(invitations)
            print(f"Bulk email sending results: {success_count} successful, {len(failed_emails)} failed")
            if failed_emails:
                print(f"Failed emails: {failed_emails}")

            # Clean up test data
            print("Cleaning up test data...")
            for invitation in invitations:
                invitation.delete()
            print("Test data cleaned up.")
        else:
            print(f"Form is not valid: {form.errors}")
    except Exception as e:
        print(f"Error testing bulk invitation: {e}")

def test_team_invitation_form():
    """Test the TeamInvitationForm with various inputs."""
    try:
        # Get or create a test user and company
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'is_active': True,
            }
        )

        company, created = Company.objects.get_or_create(
            name='Test Company',
            defaults={
                'owner': user,
                'is_active': True,
            }
        )

        print(f"Testing TeamInvitationForm for company: {company.name}")

        # Test case 1: Valid single email
        form_data = {
            'emails': '<EMAIL>',
            'message': 'Test message for single email'
        }
        form = TeamInvitationForm(data=form_data)
        if form.is_valid():
            print("Test case 1 (Single email): Form is valid")
            invitations = form.save(company=company, invited_by=user)
            print(f"  Created {len(invitations)} invitation(s)")
            for invitation in invitations:
                invitation.delete()
        else:
            print(f"Test case 1 (Single email): Form is not valid: {form.errors}")

        # Test case 2: Multiple valid emails
        form_data = {
            'emails': '<EMAIL>\<EMAIL>\<EMAIL>',
            'message': 'Test message for multiple emails'
        }
        form = TeamInvitationForm(data=form_data)
        if form.is_valid():
            print("Test case 2 (Multiple emails): Form is valid")
            invitations = form.save(company=company, invited_by=user)
            print(f"  Created {len(invitations)} invitation(s)")
            for invitation in invitations:
                invitation.delete()
        else:
            print(f"Test case 2 (Multiple emails): Form is not valid: {form.errors}")

        # Test case 3: Invalid email
        form_data = {
            'emails': 'invalid-email',
            'message': 'Test message for invalid email'
        }
        form = TeamInvitationForm(data=form_data)
        if form.is_valid():
            print("Test case 3 (Invalid email): Form is valid (unexpected)")
        else:
            print(f"Test case 3 (Invalid email): Form is not valid (expected): {form.errors}")

        # Test case 4: Empty emails
        form_data = {
            'emails': '',
            'message': 'Test message for empty emails'
        }
        form = TeamInvitationForm(data=form_data)
        if form.is_valid():
            print("Test case 4 (Empty emails): Form is valid (unexpected)")
        else:
            print(f"Test case 4 (Empty emails): Form is not valid (expected): {form.errors}")

        # Test case 5: Existing member
        member, created = User.objects.get_or_create(
            username='member',
            defaults={
                'email': '<EMAIL>',
                'is_active': True,
            }
        )
        Membership.objects.get_or_create(user=member, company=company)

        form_data = {
            'emails': '<EMAIL>',
            'message': 'Test message for existing member'
        }
        form = TeamInvitationForm(data=form_data)
        if form.is_valid():
            print("Test case 5 (Existing member): Form is valid")
            invitations = form.save(company=company, invited_by=user)
            print(f"  Created {len(invitations)} invitation(s) (should be 0)")
            for invitation in invitations:
                invitation.delete()
        else:
            print(f"Test case 5 (Existing member): Form is not valid: {form.errors}")

    except Exception as e:
        print(f"Error testing TeamInvitationForm: {e}")

if __name__ == '__main__':
    print("Testing bulk invitation functionality...")
    test_email_config()

    print("\nTesting TeamInvitationForm...")
    test_team_invitation_form()

    print("\nTesting bulk invitation...")
    test_bulk_invitation()
