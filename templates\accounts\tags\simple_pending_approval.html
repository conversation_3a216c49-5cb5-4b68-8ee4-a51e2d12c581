{% comment %}
This template tag displays a simple warning alert for entities that are pending approval.
Usage: {% include 'accounts/tags/simple_pending_approval.html' with entity_type='company' entity_name=company.name %}
Parameters:
- entity_type: The type of entity (company, community, assistant)
- entity_name: The name of the entity
{% endcomment %}

<div class="glass-notification notification-warning d-flex align-items-center simple-pending-alert" role="alert">
    <i class="bi bi-hourglass-split notification-icon"></i>
    <div class="notification-text">
        Your {{ entity_type }} <strong>{{ entity_name }}</strong> is pending admin approval. You'll be notified once it's approved.
    </div>
</div>

<script>
    // Make the simple pending alert disappear after 20 seconds
    document.addEventListener('DOMContentLoaded', function() {
        const pendingAlerts = document.querySelectorAll('.simple-pending-alert');
        pendingAlerts.forEach(alert => {
            // Add animation class
            alert.classList.add('new-notification');

            setTimeout(() => {
                // Fade out effect
                alert.style.transition = 'all 1s ease';
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-10px)';

                // Remove from DOM after fade completes
                setTimeout(() => {
                    alert.remove();
                }, 1000);
            }, 20000); // 20 seconds
        });
    });
</script>
