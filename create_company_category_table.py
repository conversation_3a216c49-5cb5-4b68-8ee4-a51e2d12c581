import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

# SQL to create the directory_companycategory table
sql = """
CREATE TABLE IF NOT EXISTS "directory_companycategory" (
    "id" serial NOT NULL PRIMARY KEY,
    "name" varchar(100) NOT NULL UNIQUE,
    "slug" varchar(110) NOT NULL UNIQUE
);
"""

# Execute the SQL
with connection.cursor() as cursor:
    cursor.execute(sql)

print("Company category table created successfully!")
