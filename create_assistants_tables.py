import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

print("Starting Assistants tables creation script...")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

# SQL to create the assistants_assistant table
assistant_sql = """
CREATE TABLE IF NOT EXISTS "assistants_assistant" (
    "id" serial NOT NULL PRIMARY KEY,
    "name" varchar(100) NOT NULL,
    "slug" varchar(100) NOT NULL UNIQUE,
    "description" text NOT NULL,
    "avatar" varchar(100) NULL,
    "is_active" boolean NOT NULL,
    "is_public" boolean NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "updated_at" timestamp with time zone NOT NULL,
    "company_id" integer NOT NULL REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED,
    "folder_id" integer NULL REFERENCES "assistants_assistantfolder" ("id") DEFERRABLE INITIALLY DEFERRED,
    "llm_provider" varchar(20) NOT NULL,
    "llm_model" varchar(50) NOT NULL,
    "system_prompt" text NOT NULL,
    "temperature" numeric(3, 2) NOT NULL,
    "max_tokens" integer NOT NULL,
    "top_p" numeric(3, 2) NOT NULL,
    "frequency_penalty" numeric(3, 2) NOT NULL,
    "presence_penalty" numeric(3, 2) NOT NULL,
    "stop_sequences" jsonb NOT NULL,
    "tools_config" jsonb NOT NULL,
    "memory_config" jsonb NOT NULL,
    "ui_config" jsonb NOT NULL
);
"""

# SQL to create the assistants_assistantfolder table
assistantfolder_sql = """
CREATE TABLE IF NOT EXISTS "assistants_assistantfolder" (
    "id" serial NOT NULL PRIMARY KEY,
    "name" varchar(100) NOT NULL,
    "description" text NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "updated_at" timestamp with time zone NOT NULL,
    "company_id" integer NOT NULL REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED,
    "parent_id" integer NULL REFERENCES "assistants_assistantfolder" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# SQL to create the assistants_conversation table
conversation_sql = """
CREATE TABLE IF NOT EXISTS "assistants_conversation" (
    "id" serial NOT NULL PRIMARY KEY,
    "title" varchar(200) NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "updated_at" timestamp with time zone NOT NULL,
    "assistant_id" integer NOT NULL REFERENCES "assistants_assistant" ("id") DEFERRABLE INITIALLY DEFERRED,
    "user_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# SQL to create the assistants_message table
message_sql = """
CREATE TABLE IF NOT EXISTS "assistants_message" (
    "id" serial NOT NULL PRIMARY KEY,
    "role" varchar(20) NOT NULL,
    "content" text NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "conversation_id" integer NOT NULL REFERENCES "assistants_conversation" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# Connect to the database
try:
    print("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(**db_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    
    # Create the assistants_assistantfolder table first (since it's referenced by assistants_assistant)
    print("Creating assistants_assistantfolder table...")
    cursor.execute(assistantfolder_sql)
    print("AssistantFolder table created successfully!")
    
    # Create the assistants_assistant table
    print("Creating assistants_assistant table...")
    cursor.execute(assistant_sql)
    print("Assistant table created successfully!")
    
    # Create the assistants_conversation table
    print("Creating assistants_conversation table...")
    cursor.execute(conversation_sql)
    print("Conversation table created successfully!")
    
    # Create the assistants_message table
    print("Creating assistants_message table...")
    cursor.execute(message_sql)
    print("Message table created successfully!")
    
    # Close the connection
    cursor.close()
    conn.close()
    print("Database connection closed.")
    
except Exception as e:
    print(f"Error: {e}")
