/**
 * Styles for category dropdowns
 */

/* Base container styles */
.dropdown-container {
    position: relative;
    margin-bottom: 1rem;
    z-index: 1050; /* Base z-index for all dropdowns */
}

/* Dropdown list styling */
.dropdown-list {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 9999; /* Very high z-index to ensure it's above everything */
    display: none;
    max-height: 200px;
    overflow-y: auto;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    margin-top: 2px;
}

/* Ensure dropdown is visible */
.dropdown-list.show {
    display: block;
}

/* Dropdown items */
.dropdown-item {
    padding: 0.5rem 1rem;
    cursor: pointer;
    color: #212529;
    position: relative;
    z-index: 10000; /* Even higher to ensure clickability */
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

/* Selected item styling */
.dropdown-item.selected {
    background-color: #e9f5ff;
    color: #0d6efd;
    font-weight: 500;
}

.dropdown-item.selected:hover {
    background-color: #d8edff;
}

/* Dark mode selected item */
[data-theme="dark"] .dropdown-item.selected {
    background-color: #0d3a6b;
    color: #8bb9fe;
}

/* When dropdown is open, increase container z-index */
.dropdown-container:focus-within {
    z-index: 9000; /* Very high when focused/active */
}

/* Dark mode styles */
[data-theme="dark"] .dropdown-list {
    background-color: #121212;
    border-color: #333;
    color: #ffffff;
}

[data-theme="dark"] .dropdown-item {
    color: #ffffff;
}

[data-theme="dark"] .dropdown-item:hover {
    background-color: #2a2a2a;
}

/* Input field styling */
.dropdown-container input {
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    position: relative;
    z-index: 1051; /* Higher than container */
}

[data-theme="dark"] .dropdown-container input {
    color: #ffffff;
    background-color: #121212;
    border-color: #333;
}

/* Arrow indicator */
.dropdown-container::after {
    content: '';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #6c757d;
    pointer-events: none;
    z-index: 1052; /* Above input */
}

/* Category dropdown specific styles */
.category-dropdown-container {
    position: relative;
    z-index: 1060; /* Higher than industry dropdown */
}

/* Fix for wizard steps */
.step {
    position: relative;
}

/* Ensure dropdowns in wizard steps are visible */
.step .dropdown-list {
    position: absolute;
    z-index: 10000; /* Very high to ensure visibility in wizard steps */
}
