"""
WSGI config for company_assistant project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/howto/deployment/wsgi/
"""

# Load .env file as early as possible
from dotenv import load_dotenv
load_dotenv()

import os
from django.core.wsgi import get_wsgi_application

# load_dotenv() # Moved to top
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')

application = get_wsgi_application()
