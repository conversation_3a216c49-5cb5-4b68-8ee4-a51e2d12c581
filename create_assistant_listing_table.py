import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

# SQL to create the directory_assistantlisting table
sql = """
CREATE TABLE IF NOT EXISTS "directory_assistantlisting" (
    "id" serial NOT NULL PRIMARY KEY,
    "is_listed" boolean NOT NULL,
    "short_description" text NOT NULL,
    "long_description" text NOT NULL,
    "categories" jsonb NOT NULL,
    "tags" jsonb NOT NULL,
    "capabilities" jsonb NOT NULL,
    "avg_rating" numeric(3, 2) NOT NULL,
    "total_ratings" integer NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "updated_at" timestamp with time zone NOT NULL,
    "assistant_id" integer NOT NULL REFERENCES "assistants_assistant" ("id") DEFERRABLE INITIALLY DEFERRED
);

CREATE INDEX IF NOT EXISTS "directory_assistantlisting_assistant_id_idx" ON "directory_assistantlisting" ("assistant_id");
"""

# Execute the SQL
with connection.cursor() as cursor:
    cursor.execute(sql)

print("Assistant listing table created successfully!")
