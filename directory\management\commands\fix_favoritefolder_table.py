from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    help = 'Checks and fixes the directory_favoritefolder table structure'

    def handle(self, *args, **options):
        self.stdout.write('Checking directory_favoritefolder table structure...')
        
        # Check if the item_type column exists
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT column_name, data_type, character_maximum_length
                FROM information_schema.columns
                WHERE table_name = 'directory_favoritefolder'
                ORDER BY ordinal_position;
            """)
            columns = cursor.fetchall()
            
            self.stdout.write('\nTable structure:')
            self.stdout.write('Column Name | Data Type | Max Length')
            self.stdout.write('-' * 50)
            for column in columns:
                self.stdout.write(f"{column[0]} | {column[1]} | {column[2]}")
            
            # Check if item_type column exists
            item_type_exists = any(column[0] == 'item_type' for column in columns)
            if item_type_exists:
                self.stdout.write(self.style.SUCCESS('\nThe item_type column exists in the table!'))
            else:
                self.stdout.write(self.style.WARNING('\nThe item_type column does NOT exist in the table!'))
                
                # Add the item_type column
                self.stdout.write('Adding item_type column to directory_favoritefolder table...')
                cursor.execute("""
                    ALTER TABLE directory_favoritefolder 
                    ADD COLUMN IF NOT EXISTS item_type varchar(20) NOT NULL DEFAULT 'assistant';
                """)
                self.stdout.write(self.style.SUCCESS('Column added successfully!'))
                
                # Add the unique constraint
                self.stdout.write('Adding unique constraint...')
                cursor.execute("""
                    ALTER TABLE directory_favoritefolder 
                    DROP CONSTRAINT IF EXISTS directory_favoritefolder_user_id_name_item_type_unique;

                    ALTER TABLE directory_favoritefolder 
                    ADD CONSTRAINT directory_favoritefolder_user_id_name_item_type_unique 
                    UNIQUE (user_id, name, item_type);
                """)
                self.stdout.write(self.style.SUCCESS('Constraint added successfully!'))
                
        self.stdout.write(self.style.SUCCESS('Table check and fix completed successfully!'))
