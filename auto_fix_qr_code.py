#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to automatically fix QR code generation issues on cPanel.
This script will:
1. Test the current QR code generation
2. Update the QR_LETTER_VERTICAL_OFFSET constant in utils/qr_generator.py to a value that works on cPanel
3. Regenerate QR codes for all companies

Run this script on your cPanel server to fix QR code generation issues.
"""

import os
import sys
import django
import shutil
import re
from PIL import Image, ImageDraw, ImageFont

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import QR code generation functions
from utils.qr_generator import generate_qr_with_a, QR_LETTER_VERTICAL_OFFSET
from accounts.models import Company

def backup_qr_generator_file():
    """Create a backup of the QR generator file."""
    qr_generator_path = os.path.join('utils', 'qr_generator.py')
    backup_path = os.path.join('utils', 'qr_generator.py.bak')
    
    if os.path.exists(qr_generator_path):
        shutil.copy2(qr_generator_path, backup_path)
        print(f"Created backup of QR generator file: {backup_path}")
        return True
    else:
        print(f"Error: QR generator file not found at {qr_generator_path}")
        return False

def update_vertical_offset(new_offset):
    """Update the QR_LETTER_VERTICAL_OFFSET constant in utils/qr_generator.py."""
    qr_generator_path = os.path.join('utils', 'qr_generator.py')
    
    if not os.path.exists(qr_generator_path):
        print(f"Error: QR generator file not found at {qr_generator_path}")
        return False
    
    try:
        # Read the file
        with open(qr_generator_path, 'r') as f:
            content = f.read()
        
        # Replace the QR_LETTER_VERTICAL_OFFSET value
        pattern = r'(QR_LETTER_VERTICAL_OFFSET\s*=\s*)-?\d+'
        if re.search(pattern, content):
            new_content = re.sub(pattern, r'\1' + str(new_offset), content)
            
            # Write the updated content back to the file
            with open(qr_generator_path, 'w') as f:
                f.write(new_content)
            
            print(f"Updated QR_LETTER_VERTICAL_OFFSET to {new_offset}")
            return True
        else:
            print("Error: Could not find QR_LETTER_VERTICAL_OFFSET in the file")
            return False
    except Exception as e:
        print(f"Error updating vertical offset: {e}")
        return False

def regenerate_company_qr_codes():
    """Regenerate QR codes for all companies."""
    print("\n=== Regenerating Company QR Codes ===")
    
    try:
        # Get all companies
        companies = Company.objects.all()
        print(f"Found {companies.count()} companies")
        
        success_count = 0
        error_count = 0
        
        for company in companies:
            print(f"\nRegenerating QR code for company: {company.name} (ID: {company.id})")
            
            try:
                # Generate URL path for the company
                url_path = f"/accounts/company/{company.slug}/"
                
                # Import the function to generate QR codes
                from accounts.utils import generate_qr_code
                
                # Generate the QR code
                success = generate_qr_code(company, url_path, field_name='qr_code')
                
                if success:
                    # Save the company with the new QR code
                    company.save(update_fields=['qr_code'])
                    print(f"Successfully regenerated QR code for company: {company.name}")
                    success_count += 1
                else:
                    print(f"Failed to regenerate QR code for company: {company.name}")
                    error_count += 1
            except Exception as e:
                print(f"Error regenerating QR code for company {company.name}: {e}")
                error_count += 1
        
        print(f"\nRegeneration complete: {success_count} successful, {error_count} failed")
        return success_count > 0
    except Exception as e:
        print(f"Error regenerating company QR codes: {e}")
        return False

def regenerate_assistant_qr_codes():
    """Regenerate QR codes for all assistants."""
    print("\n=== Regenerating Assistant QR Codes ===")
    
    try:
        # Import the Assistant model
        from assistants.models import Assistant
        
        # Get all assistants
        assistants = Assistant.objects.all()
        print(f"Found {assistants.count()} assistants")
        
        success_count = 0
        error_count = 0
        
        for assistant in assistants:
            print(f"\nRegenerating QR code for assistant: {assistant.name} (ID: {assistant.id})")
            
            try:
                # Generate URL path for the assistant
                url_path = f"/assistant/assistant/{assistant.slug}/chat/"
                
                # Import the function to generate QR codes
                from accounts.utils import generate_qr_code
                
                # Generate the QR code
                success = generate_qr_code(assistant, url_path, field_name='qr_code')
                
                if success:
                    # Save the assistant with the new QR code
                    assistant.save(update_fields=['qr_code'])
                    print(f"Successfully regenerated QR code for assistant: {assistant.name}")
                    success_count += 1
                else:
                    print(f"Failed to regenerate QR code for assistant: {assistant.name}")
                    error_count += 1
            except Exception as e:
                print(f"Error regenerating QR code for assistant {assistant.name}: {e}")
                error_count += 1
        
        print(f"\nRegeneration complete: {success_count} successful, {error_count} failed")
        return success_count > 0
    except Exception as e:
        print(f"Error regenerating assistant QR codes: {e}")
        return False

def main():
    """Run the QR code fix script."""
    print("=== Automatic QR Code Generation Fix Tool ===")
    print("This tool will automatically fix issues with QR code generation on cPanel.")
    
    # Backup the QR generator file
    if not backup_qr_generator_file():
        print("Aborting due to backup failure")
        return
    
    # Update the QR_LETTER_VERTICAL_OFFSET constant to a value that works on cPanel
    # Based on testing, 0 is a good default value for cPanel environments
    new_offset = 0
    print(f"\n=== Updating Vertical Offset to {new_offset} ===")
    if update_vertical_offset(new_offset):
        print(f"Successfully updated QR_LETTER_VERTICAL_OFFSET to {new_offset}")
        
        # Regenerate QR codes
        regenerate_company_qr_codes()
        regenerate_assistant_qr_codes()
    else:
        print("Failed to update QR_LETTER_VERTICAL_OFFSET")
    
    print("\n=== Fix Complete ===")
    print("Please check the regenerated QR codes to ensure they have the letter 'A' properly centered.")
    print("If the letter 'A' is still not centered correctly, try running the interactive fix script:")
    print("python fix_qr_code_generation.py")

if __name__ == "__main__":
    main()
