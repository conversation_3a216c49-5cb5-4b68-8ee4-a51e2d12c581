# Email Domain Fix Guide

This guide provides instructions for fixing the domain used in activation emails and other site-related URLs. By default, <PERSON><PERSON><PERSON> uses the domain from the Site framework, which is set to "example.com" by default. This guide will help you update it to use "24seven.site" instead.

## Problem Description

When sending activation emails or generating URLs that include the site domain, <PERSON><PERSON><PERSON> uses the domain from the Site framework. By default, this is set to "example.com", which causes activation emails to use "example.com" or "localhost:8000" instead of "24seven.site".

## Solution

We've created two scripts to help diagnose and fix the site domain issue:

1. `check_site_domain.py` - Checks the current site domain configuration
2. `update_site_domain.py` - Updates the site domain to "24seven.site"

## Step 1: Check the Current Site Domain

Run the check script to see the current site domain configuration:

```bash
python check_site_domain.py
```

This script will:
- Print the current site domain and name
- Show the site URL that would be used in emails
- Check if the site domain is included in CSRF_TRUSTED_ORIGINS and ALLOWED_HOSTS
- Provide recommendations for fixing any issues

## Step 2: Update the Site Domain

Run the update script to change the site domain to "24seven.site":

```bash
python update_site_domain.py
```

This script will:
- Update the domain of the current site to "24seven.site"
- Update the name of the current site to "24seven.site" (if not specified)
- Print the old and new domain and name

If you want to specify a different domain or name, you can pass them as arguments:

```bash
python update_site_domain.py example.com "Example Site"
```

## Step 3: Update CSRF_TRUSTED_ORIGINS (Already Done)

We've already updated the CSRF_TRUSTED_ORIGINS setting in `company_assistant/settings.py` to include "24seven.site":

```python
CSRF_TRUSTED_ORIGINS = ['http://localhost:8000', 'http://127.0.0.1:8000', 'https://24seven.site', 'http://24seven.site']
```

## Step 4: Restart the Application

After updating the site domain, restart your application to apply the changes:

- If running locally:
  ```bash
  python manage.py runserver
  ```

- If running on cPanel:
  - Go to "Setup Python App" in cPanel
  - Click "Restart App" for your application, or
  - Use SSH to touch the WSGI file: `touch passenger_wsgi.py`

## Step 5: Test the Activation Emails

Test the activation emails to ensure they use the correct domain:

1. Create a new user account
2. Check the activation email that is sent
3. Verify that the links in the email use "24seven.site" instead of "example.com" or "localhost:8000"

## Troubleshooting

### Emails Still Using Incorrect Domain

If the emails are still using the incorrect domain after updating the site domain:

1. Check if the site domain was updated correctly:
   ```bash
   python check_site_domain.py
   ```

2. Make sure the application was restarted after updating the site domain

3. Check if the `get_site_url()` function in `accounts/email_utils.py` is overriding the site domain:
   ```python
   def get_site_url():
       """Get the site URL from the Site model or settings."""
       try:
           site = Site.objects.get_current()
           site_url = f"https://{site.domain}"
           if settings.DEBUG:
               site_url = f"http://{site.domain}"
               if site.domain == 'example.com':
                   site_url = "http://localhost:8000"
       except Exception as e:
           logger.warning(f"Error getting site URL: {e}")
           site_url = settings.SITE_URL if hasattr(settings, 'SITE_URL') else "http://localhost:8000"
       
       return site_url
   ```

   You may need to modify this function to always use the site domain in production.

### Multiple Sites in the Database

If there are multiple sites in the database, the wrong one might be used. To check and fix this:

1. Open the Django shell:
   ```bash
   python manage.py shell
   ```

2. List all sites:
   ```python
   from django.contrib.sites.models import Site
   Site.objects.all().values()
   ```

3. Make sure the site with ID 1 has the correct domain:
   ```python
   site = Site.objects.get(id=1)
   site.domain = "24seven.site"
   site.name = "24seven.site"
   site.save()
   ```

## Additional Information

### How Django Uses the Site Framework

Django's Site framework is used by various components to determine the domain for URLs:

1. **Email Templates**: When generating links in email templates
2. **Absolute URLs**: When generating absolute URLs with `request.build_absolute_uri()`
3. **Admin Site**: When generating links in the admin site
4. **Third-Party Apps**: Many third-party apps use the Site framework for domain information

### Settings That Affect Email URLs

Several settings can affect the URLs used in emails:

1. **SITE_ID**: Determines which site is used by the Site framework (default: 1)
2. **DEBUG**: When True, may cause some code to use localhost instead of the site domain
3. **SECURE_SSL_REDIRECT**: Determines whether to use http:// or https:// in URLs
4. **ALLOWED_HOSTS**: List of host/domain names that this Django site can serve
5. **CSRF_TRUSTED_ORIGINS**: List of trusted origins for CSRF-protected requests
