{% load account_tags %}

<div class="list-group-item border-0">
    <div class="d-flex">
        <!-- Activity Icon -->
        <div class="flex-shrink-0">
            {% if activity.user %} {# Check if user exists #}
                {% if activity.user.avatar %}
                    <img src="{{ activity.user.avatar.url }}" 
                         alt="{{ activity.user.get_full_name|default:activity.user.username }}"
                         class="rounded-circle"
                          width="40" height="40">
                 {% elif activity.user.email %} {# Use gravatar only if email exists #}
                     <img src="{{ activity.user.email|gravatar_url:40 }}"
                          alt="{{ activity.user.get_full_name|default:activity.user.username }}"
                          class="rounded-circle"
                          width="40" height="40">
                 {% else %} {# Fallback icon if no avatar or email #}
                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                        <i class="bi bi-person text-muted"></i>
                    </div>
                 {% endif %}
            {% else %} {# Fallback icon for deleted user #}
                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                    <i class="bi bi-person-slash text-muted"></i>
                </div>
            {% endif %}
        </div>

        <!-- Activity Content -->
        <div class="flex-grow-1 ms-3">
            <div class="d-flex align-items-center mb-1">
                <div class="flex-grow-1">
                    <!-- Actor -->
                    <span class="fw-bold">
                        {% if activity.user %}
                            {{ activity.user.get_full_name|default:activity.user.username }}
                        {% else %}
                            [Deleted User]
                        {% endif %}
                    </span>

                    <!-- Action -->
                    {% if activity.type == 'content_created' %}
                        created a new document
                        "<a href="{{ activity.metadata.url }}" class="text-decoration-none">{{ activity.metadata.title }}</a>"
                    {% elif activity.type == 'content_updated' %}
                        updated the document
                        "<a href="{{ activity.metadata.url }}" class="text-decoration-none">{{ activity.metadata.title }}</a>"
                    {% elif activity.type == 'member_joined' %}
                        joined the team
                    {% elif activity.type == 'member_left' %}
                        left the team
                    {% elif activity.type == 'member_role_changed' %}
                        {% if activity.metadata.new_role == 'admin' %}
                            was promoted to administrator
                        {% else %}
                            was changed to {{ activity.metadata.new_role }}
                        {% endif %}
                    {% elif activity.type == 'invitation_sent' %}
                        sent an invitation to {{ activity.metadata.email|mask_email }}
                    {% elif activity.type == 'invitation_accepted' %}
                        accepted their invitation
                    {% elif activity.type == 'assistant_created' %}
                        created a new AI assistant
                        "<a href="{{ activity.metadata.url }}" class="text-decoration-none">{{ activity.metadata.name }}</a>"
                    {% elif activity.type == 'assistant_trained' %}
                        trained the AI assistant
                        "<a href="{{ activity.metadata.url }}" class="text-decoration-none">{{ activity.metadata.name }}</a>"
                    {% elif activity.type == 'company_settings_updated' %}
                        updated company settings
                    {% else %}
                        performed an action
                    {% endif %}
                </div>

                <!-- Timestamp -->
                <div class="flex-shrink-0 ms-2">
                    <small class="text-muted" title="{{ activity.created_at|date:'Y-m-d H:i:s' }}">
                        {{ activity.created_at|timesince }} ago
                    </small>
                </div>
            </div>

            <!-- Details -->
            {% if activity.metadata.details %}
                <p class="text-muted small mb-0">
                    {{ activity.metadata.details }}
                </p>
            {% endif %}

            <!-- Related Items -->
            {% if activity.metadata.related_items %}
                <div class="mt-2">
                    <div class="list-group list-group-flush small">
                        {% for item in activity.metadata.related_items %}
                            <div class="list-group-item px-0 py-2 border-0">
                                <div class="d-flex align-items-center">
                                    <!-- Item Icon -->
                                    <div class="flex-shrink-0 me-2">
                                        {% if item.type == 'file' %}
                                            <i class="bi bi-file-earmark text-muted"></i>
                                        {% elif item.type == 'user' %}
                                            <i class="bi bi-person text-muted"></i>
                                        {% elif item.type == 'assistant' %}
                                            <i class="bi bi-robot text-muted"></i>
                                        {% endif %}
                                    </div>

                                    <!-- Item Details -->
                                    <div class="flex-grow-1">
                                        {% if item.url %}
                                            <a href="{{ item.url }}" class="text-decoration-none">
                                                {{ item.name }}
                                            </a>
                                        {% else %}
                                            {{ item.name }}
                                        {% endif %}
                                        {% if item.description %}
                                            <span class="text-muted ms-1">
                                                {{ item.description }}
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Actions -->
    {% if activity.actions %}
        <div class="mt-2 ps-5">
            <div class="btn-group btn-group-sm">
                {% for action in activity.actions %}
                    <button type="button" 
                            class="btn btn-light"
                            {% if action.url %}
                                onclick="location.href='{{ action.url }}'"
                            {% endif %}
                            {% if action.onclick %}
                                onclick="{{ action.onclick }}"
                            {% endif %}
                            {% if action.disabled %}
                                disabled
                            {% endif %}>
                        {% if action.icon %}
                            <i class="bi bi-{{ action.icon }} me-1"></i>
                        {% endif %}
                        {{ action.label }}
                    </button>
                {% endfor %}
            </div>
        </div>
    {% endif %}

    <!-- Tags -->
    {% if activity.tags %}
        <div class="mt-2 ps-5">
            {% for tag in activity.tags %}
                <span class="badge bg-light text-dark me-1">
                    {{ tag }}
                </span>
            {% endfor %}
        </div>
    {% endif %}
</div>
