/**
 * Remove Gradients JavaScript
 * This script specifically targets the HTML structure shown in the screenshot
 * to ensure all gradients are removed
 */

document.addEventListener('DOMContentLoaded', function() {
    // Apply immediately
    removeGradients();

    // Set up a MutationObserver to watch for new messages
    const chatBox = document.getElementById('chat-box');
    if (chatBox) {
        // Use a debounced version to prevent excessive calls
        let timeout;
        const observer = new MutationObserver(function(mutations) {
            // Clear any existing timeout
            if (timeout) {
                clearTimeout(timeout);
            }

            // Set a new timeout to run the function after a delay
            timeout = setTimeout(function() {
                removeGradients();
            }, 500);
        });

        // Start observing the chat box for added messages
        observer.observe(chatBox, { childList: true, subtree: false });
    }

    // Also set up an interval to ensure styles are applied, but much less frequently
    setInterval(removeGradients, 5000);
});

/**
 * Remove gradients from all messages
 */
function removeGradients() {

    // Target the exact structure shown in the screenshot
    const assistantMessages = document.querySelectorAll('div.message.assistant-message.mb-3 span.message-content, div.message.assistant-message span.message-content, .message.assistant-message .message-content, .assistant-message .message-content');

    assistantMessages.forEach(function(el) {
        // Apply solid white background
        el.style.background = '#ffffff';
        el.style.backgroundColor = '#ffffff';
        el.style.backgroundImage = 'none';
        el.style.backgroundBlendMode = 'normal';
        el.style.backgroundPosition = 'initial';
        el.style.backgroundSize = 'initial';
        el.style.backgroundRepeat = 'initial';
        el.style.backgroundOrigin = 'initial';
        el.style.backgroundClip = 'initial';
        el.style.backgroundAttachment = 'initial';
        el.style.color = '#333333';
        el.style.border = '1px solid rgba(0, 0, 0, 0.05)';
        el.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.05)';

        // Remove any pseudo-elements
        removeGradientPseudoElements(el);
    });

    // Target user messages
    const userMessages = document.querySelectorAll('div.message.user-message.mb-3 span.message-content, div.message.user-message span.message-content, .message.user-message .message-content, .user-message .message-content');

    userMessages.forEach(function(el) {
        // Apply solid blue background
        el.style.background = '#3b7dd8';
        el.style.backgroundColor = '#3b7dd8';
        el.style.backgroundImage = 'none';
        el.style.backgroundBlendMode = 'normal';
        el.style.backgroundPosition = 'initial';
        el.style.backgroundSize = 'initial';
        el.style.backgroundRepeat = 'initial';
        el.style.backgroundOrigin = 'initial';
        el.style.backgroundClip = 'initial';
        el.style.backgroundAttachment = 'initial';
        el.style.color = '#ffffff';
        el.style.border = 'none';
        el.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';

        // Remove any pseudo-elements
        removeGradientPseudoElements(el);
    });

    // Target the message containers
    const messageContainers = document.querySelectorAll('div.message.assistant-message.mb-3, div.message.assistant-message, .message.assistant-message, .assistant-message, div.message.user-message.mb-3, div.message.user-message, .message.user-message, .user-message');

    messageContainers.forEach(function(el) {
        // Make container transparent
        el.style.background = 'transparent';
        el.style.backgroundColor = 'transparent';
        el.style.backgroundImage = 'none';
    });
}

/**
 * Remove gradient pseudo-elements
 */
function removeGradientPseudoElements(element) {
    // Create a unique class name for this element
    const uniqueClass = 'no-gradient-' + Math.random().toString(36).substr(2, 9);

    // Add the class to the element
    element.classList.add(uniqueClass);

    // Create a style tag to remove pseudo-elements
    const style = document.createElement('style');
    style.textContent = `
        .${uniqueClass}::before,
        .${uniqueClass}::after {
            display: none !important;
            content: none !important;
            background: none !important;
            background-image: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
        }
    `;

    // Add the style tag to the head
    document.head.appendChild(style);
}
