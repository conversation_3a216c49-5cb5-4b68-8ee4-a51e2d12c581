/**
 * TinyMCE Dark Mode Styles
 * Custom dark mode styling for TinyMCE editor
 */

/* Editor container */
.tox.tox-tinymce {
  border-color: #3a4556 !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

/* Toolbar */
.tox .tox-toolbar,
.tox .tox-toolbar__primary,
.tox .tox-toolbar__overflow {
  background-color: #252a33 !important;
  border-bottom: 1px solid #3a4556 !important;
}

/* Toolbar buttons */
.tox .tox-tbtn {
  color: #e2e8f0 !important;
}

.tox .tox-tbtn svg {
  fill: #e2e8f0 !important;
}

.tox .tox-tbtn:hover {
  background-color: #323b4e !important;
}

.tox .tox-tbtn--enabled,
.tox .tox-tbtn--enabled:hover {
  background-color: #4a7dff !important;
}

/* Dropdown menus */
.tox .tox-menu {
  background-color: #252a33 !important;
  border: 1px solid #3a4556 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

.tox .tox-collection__item {
  color: #e2e8f0 !important;
}

.tox .tox-collection__item--active {
  background-color: #323b4e !important;
}

.tox .tox-collection__item:hover {
  background-color: #323b4e !important;
}

/* Dialog */
.tox .tox-dialog {
  background-color: #252a33 !important;
  border: 1px solid #3a4556 !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3) !important;
}

.tox .tox-dialog__header {
  background-color: #252a33 !important;
  border-bottom: 1px solid #3a4556 !important;
}

.tox .tox-dialog__footer {
  background-color: #252a33 !important;
  border-top: 1px solid #3a4556 !important;
}

.tox .tox-dialog__title {
  color: #e2e8f0 !important;
}

.tox .tox-textfield,
.tox .tox-selectfield select,
.tox .tox-textarea {
  background-color: #1a1d21 !important;
  border: 1px solid #3a4556 !important;
  color: #e2e8f0 !important;
}

.tox .tox-textfield:focus,
.tox .tox-selectfield select:focus,
.tox .tox-textarea:focus {
  border-color: #4a7dff !important;
  box-shadow: 0 0 0 2px rgba(74, 125, 255, 0.25) !important;
}

/* Dialog buttons */
.tox .tox-button {
  background-color: #4a7dff !important;
  border-color: #4a7dff !important;
  color: white !important;
}

.tox .tox-button:hover {
  background-color: #3a6ae0 !important;
  border-color: #3a6ae0 !important;
}

.tox .tox-button--secondary {
  background-color: #323b4e !important;
  border-color: #3a4556 !important;
  color: #e2e8f0 !important;
}

.tox .tox-button--secondary:hover {
  background-color: #3d4760 !important;
  border-color: #3a4556 !important;
}

/* Status bar */
.tox .tox-statusbar {
  background-color: #252a33 !important;
  border-top: 1px solid #3a4556 !important;
  color: #a0aec0 !important;
}

.tox .tox-statusbar a {
  color: #4a7dff !important;
}

/* Edit area */
.tox .tox-edit-area__iframe {
  background-color: #1a1d21 !important;
}

/* Content inside the editor */
.mce-content-body {
  background-color: #1a1d21 !important;
  color: #e2e8f0 !important;
}

.mce-content-body p, 
.mce-content-body h1, 
.mce-content-body h2, 
.mce-content-body h3, 
.mce-content-body h4, 
.mce-content-body h5, 
.mce-content-body h6, 
.mce-content-body li, 
.mce-content-body td {
  color: #e2e8f0 !important;
}

.mce-content-body a {
  color: #4a7dff !important;
}

/* Table inside editor */
.mce-content-body table {
  border-color: #3a4556 !important;
}

.mce-content-body table td,
.mce-content-body table th {
  border-color: #3a4556 !important;
}

/* Code blocks */
.mce-content-body code {
  background-color: #252a33 !important;
  color: #a0aec0 !important;
}

/* Blockquote */
.mce-content-body blockquote {
  border-left: 4px solid #3a4556 !important;
  background-color: #252a33 !important;
  color: #a0aec0 !important;
}

/* Image captions */
.mce-content-body figure figcaption {
  color: #a0aec0 !important;
}

/* Selection */
.mce-content-body ::selection {
  background-color: rgba(74, 125, 255, 0.3) !important;
  color: #e2e8f0 !important;
}

/* Placeholder text */
.mce-content-body *[data-mce-placeholder] {
  color: #718096 !important;
}

/* Focus outline */
.tox-tinymce:focus-within {
  border-color: #4a7dff !important;
  box-shadow: 0 0 0 3px rgba(74, 125, 255, 0.25) !important;
}
