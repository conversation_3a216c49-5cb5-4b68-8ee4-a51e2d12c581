{% extends 'base/layout.html' %}
{% load static %}
{% load account_tags %}
{% load permission_tags %}
{% load crispy_forms_tags %}

{% block title %}{{ assistant.name }} - Community Dashboard{% endblock %}

{% block extra_css %}
<link href="{% static 'css/social-dashboard.css' %}" rel="stylesheet">
<style>
/* Rating stars styling */
.modal-stars .star {
    display: inline-block;
    margin: 0 5px;
    transition: all 0.2s ease;
}

.modal-stars .star.selected,
.modal-stars .star.hover {
    opacity: 1;
    transform: scale(1.2);
}

.modal-stars .star:hover {
    transform: scale(1.3);
}

/* Like button animation */
.like-button {
    transition: all 0.3s ease;
}

.like-button:hover {
    transform: scale(1.2);
}

.like-button.text-danger {
    animation: heartBeat 0.3s ease-in-out;
}

@keyframes heartBeat {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1); }
}
</style>
{% endblock %}

{% block body_class %}social-dashboard{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Dashboard Header -->
    <div class="dashboard-header mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    {% if assistant.avatar %}
                        <img src="{{ assistant.avatar.url }}" alt="{{ assistant.name }}" class="rounded-circle me-3" style="width: 60px; height: 60px; object-fit: cover;">
                    {% else %}
                        <div class="bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                            <i class="bi bi-robot text-primary" style="font-size: 1.8rem;"></i>
                        </div>
                    {% endif %}
                    <div>
                        <h1 class="h3 mb-0 d-flex align-items-center">
                            {{ assistant.name }}
                            {% if user.is_authenticated %}
                                <button
                                    class="like-button btn btn-sm p-1 ms-2 {% if is_favorited %}text-danger{% else %}text-secondary{% endif %}"
                                    data-item-id="{{ assistant.id }}"
                                    data-item-type="assistant"
                                    title="{% if is_favorited %}Remove from Favorites{% else %}Add to Favorites{% endif %}"
                                    style="background: none; border: none; line-height: 1;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-heart-fill" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" d="M8 1.314C12.438-3.248 23.534 4.735 8 15-7.534 4.736 3.562-3.248 8 1.314z"/>
                                    </svg>
                                </button>
                            {% endif %}
                        </h1>
                        <p class="text-muted mb-0">Community Dashboard</p>
                        {% if assistant.listing %}
                            <div class="rating-display-container mt-1" id="rating-display-{{ assistant.id }}">
                                {% load rating_tags %}
                                {% render_stars assistant.listing.avg_rating assistant.listing.total_ratings %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-md-end mt-3 mt-md-0">
                <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="btn btn-primary me-2">
                    <i class="bi bi-chat-dots me-1"></i> Chat Now
                </a>
                {% if user.is_authenticated and assistant.listing %}
                <button type="button"
                        class="btn btn-outline-secondary me-2 rate-assistant-btn"
                        data-bs-toggle="modal"
                        data-bs-target="#ratingModal"
                        data-assistant-id="{{ assistant.id }}"
                        data-assistant-name="{{ assistant.name|escapejs }}">
                    <i class="bi bi-star me-1"></i> Rate
                </button>
                {% endif %}
                <a href="{% url 'assistants:community_dashboard' company.id assistant.id %}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-people me-1"></i> Social Dashboard
                </a>
                {% if user.is_staff or user.is_superuser %}
                <a href="{% url 'assistants:moderation_dashboard' company.id assistant.id %}" class="btn btn-outline-danger">
                    <i class="bi bi-shield-fill me-1"></i> Moderation
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Dashboard Tabs -->
    <div class="dashboard-tabs mb-4">
        <ul class="nav nav-tabs" id="dashboardTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="feed-tab" data-bs-toggle="tab" data-bs-target="#feed-tab-pane" type="button" role="tab" aria-controls="feed-tab-pane" aria-selected="true">
                    <i class="bi bi-newspaper me-1"></i> Feed
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="stats-tab" data-bs-toggle="tab" data-bs-target="#stats-tab-pane" type="button" role="tab" aria-controls="stats-tab-pane" aria-selected="false">
                    <i class="bi bi-graph-up me-1"></i> Statistics
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="contributions-tab" data-bs-toggle="tab" data-bs-target="#contributions-tab-pane" type="button" role="tab" aria-controls="contributions-tab-pane" aria-selected="false">
                    <i class="bi bi-journal-text me-1"></i> Contributions
                </button>
            </li>
            {% if user.is_staff or user.is_superuser %}
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="reports-tab" data-bs-toggle="tab" data-bs-target="#reports-tab-pane" type="button" role="tab" aria-controls="reports-tab-pane" aria-selected="false">
                    <i class="bi bi-flag-fill me-1"></i> Reports
                    {% if pending_reports_count > 0 %}
                    <span class="badge bg-danger ms-1">{{ pending_reports_count }}</span>
                    {% endif %}
                </button>
            </li>
            {% endif %}
        </ul>
    </div>

    <!-- Tab Content -->
    <div class="tab-content" id="dashboardTabsContent">
        <!-- Feed Tab -->
        <div class="tab-pane fade show active" id="feed-tab-pane" role="tabpanel" aria-labelledby="feed-tab" tabindex="0">
            <div class="row">
                <!-- Main Content Column -->
                <div class="col-lg-8">
                    <!-- Create Post Card -->
                    <div class="create-post-card mb-4">
                        <div class="create-post-header">
                            <div class="post-avatar">
                                {% if user.profile.avatar %}
                                    <img src="{{ user.profile.avatar.url }}" alt="{{ user.username }}">
                                {% else %}
                                    <i class="bi bi-person"></i>
                                {% endif %}
                            </div>
                            <div class="create-post-input" data-bs-toggle="modal" data-bs-target="#createPostModal">
                                What's on your mind, {{ user.first_name|default:user.username }}?
                            </div>
                        </div>
                        <div class="create-post-actions">
                            <button class="create-post-action" data-bs-toggle="modal" data-bs-target="#createPostModal">
                                <i class="bi bi-journal-text text-primary"></i> Add Knowledge
                            </button>
                            <button class="create-post-action" data-bs-toggle="modal" data-bs-target="#askQuestionModal">
                                <i class="bi bi-question-circle text-success"></i> Ask Question
                            </button>
                        </div>
                    </div>

                    <!-- Activity Feed -->
                    <div id="activity-feed">
                        {% if recent_activities %}
                            {% for activity in recent_activities %}
                                <div class="post-card">
                                    <div class="post-header">
                                        <div class="post-avatar">
                                            {% if activity.user.profile.avatar %}
                                                <img src="{{ activity.user.profile.avatar.url }}" alt="{{ activity.user.username }}">
                                            {% else %}
                                                <i class="bi bi-person"></i>
                                            {% endif %}
                                        </div>
                                        <div class="post-user-info">
                                            <h6 class="post-username">
                                                {{ activity.user.get_full_name|default:activity.user.username }}
                                                {% if activity.user.reputation %}
                                                    <span class="reputation-badge reputation-{{ activity.user.reputation.level|lower|slugify }}">
                                                        {{ activity.user.reputation.level }}
                                                    </span>
                                                {% endif %}
                                            </h6>
                                            <div class="post-time">{{ activity.created_at|timesince }} ago</div>
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-link text-muted" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="bi bi-three-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                {% if activity.type == 'context' and activity.user == request.user %}
                                                    <li>
                                                        <a class="dropdown-item" href="{% url 'assistants:edit_context' company.id assistant.id activity.object_id %}">
                                                            <i class="bi bi-pencil"></i> Edit
                                                        </a>
                                                    </li>
                                                {% endif %}
                                                <li>
                                                    <button class="dropdown-item report-btn" data-bs-toggle="modal" data-bs-target="#reportModal"
                                                            data-content-type="{{ activity.content_type }}"
                                                            data-object-id="{{ activity.object_id }}">
                                                        <i class="bi bi-flag"></i> Report
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="post-content">
                                        {% if activity.type == 'context' %}
                                            <h5 class="mb-2">{{ activity.title }}</h5>
                                            <div class="post-text">{{ activity.content|safe|truncatewords_html:50 }}</div>
                                            {% if activity.content|wordcount > 50 %}
                                                <button class="btn btn-sm btn-link p-0 show-more-btn" data-context-id="{{ activity.object_id }}">
                                                    Show more
                                                </button>
                                            {% endif %}
                                        {% elif activity.type == 'question' %}
                                            <div class="post-text">{{ activity.content }}</div>
                                            {% if activity.answer %}
                                                <div class="mt-3 p-3 bg-light rounded">
                                                    <strong>{{ assistant.name }}:</strong> {{ activity.answer|linebreaksbr }}
                                                </div>
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                    <div class="post-actions">
                                        <button class="post-action-btn upvote-btn {% if activity.object_id in upvoted_contexts %}upvoted{% endif %}"
                                                data-context-id="{{ activity.object_id }}">
                                            <i class="bi bi-hand-thumbs-up"></i> Upvote
                                            <span class="upvote-count">{{ activity.upvotes|default:"0" }}</span>
                                        </button>
                                        <button class="post-action-btn" data-bs-toggle="modal" data-bs-target="#commentModal"
                                                data-content-type="{{ activity.content_type }}"
                                                data-object-id="{{ activity.object_id }}">
                                            <i class="bi bi-chat"></i> Comment
                                        </button>
                                        <button class="post-action-btn">
                                            <i class="bi bi-share"></i> Share
                                        </button>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="empty-state">
                                <i class="bi bi-newspaper"></i>
                                <h4>No Activity Yet</h4>
                                <p>Be the first to contribute knowledge or ask a question!</p>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Sidebar Column -->
                <div class="col-lg-4">
                    <!-- Community Stats Card -->
                    <div class="sidebar-card mb-4">
                        <div class="card-header">
                            <i class="bi bi-graph-up me-2"></i> Community Stats
                        </div>
                        <div class="card-body">
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="p-3 bg-light rounded text-center">
                                        <div class="h3 mb-0">{{ total_members }}</div>
                                        <div class="small text-muted">Members</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="p-3 bg-light rounded text-center">
                                        <div class="h3 mb-0">{{ total_contexts }}</div>
                                        <div class="small text-muted">Contributions</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="p-3 bg-light rounded text-center">
                                        <div class="h3 mb-0">{{ total_questions|default:"0" }}</div>
                                        <div class="small text-muted">Questions</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="p-3 bg-light rounded text-center">
                                        <div class="h3 mb-0">{{ total_interactions|default:"0" }}</div>
                                        <div class="small text-muted">Interactions</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Top Contributors Card -->
                    <div class="sidebar-card mb-4">
                        <div class="card-header">
                            <i class="bi bi-trophy me-2"></i> Top Contributors
                        </div>
                        <div class="card-body p-0">
                            <ul class="user-list">
                                {% for contributor in top_contributors %}
                                    <li class="user-item p-3">
                                        <div class="user-avatar">
                                            {% if contributor.user.profile.avatar %}
                                                <img src="{{ contributor.user.profile.avatar.url }}" alt="{{ contributor.user.username }}">
                                            {% else %}
                                                <i class="bi bi-person"></i>
                                            {% endif %}
                                        </div>
                                        <div class="user-info">
                                            <h6 class="user-name">
                                                {{ contributor.user.get_full_name|default:contributor.user.username }}
                                                <span class="reputation-badge reputation-{{ contributor.level|lower|slugify }}">
                                                    {{ contributor.level }}
                                                </span>
                                            </h6>
                                            <div class="user-meta">
                                                <i class="bi bi-journal-text me-1"></i> {{ contributor.contributions_count }} contributions
                                                <i class="bi bi-hand-thumbs-up ms-2 me-1"></i> {{ contributor.upvotes_received }} upvotes
                                            </div>
                                        </div>
                                    </li>
                                {% empty %}
                                    <li class="p-3 text-center text-muted">
                                        No contributors yet
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>

                    <!-- Popular Topics Card -->
                    <div class="sidebar-card">
                        <div class="card-header">
                            <i class="bi bi-tags me-2"></i> Popular Topics
                        </div>
                        <div class="card-body">
                            <div class="d-flex flex-wrap gap-2">
                                {% for keyword in popular_keywords %}
                                    <a href="?keyword={{ keyword.name }}" class="badge bg-light text-dark text-decoration-none p-2">
                                        {{ keyword.name }} <span class="badge bg-secondary ms-1">{{ keyword.count }}</span>
                                    </a>
                                {% empty %}
                                    <p class="text-muted">No popular topics yet.</p>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Tab -->
        <div class="tab-pane fade" id="stats-tab-pane" role="tabpanel" aria-labelledby="stats-tab" tabindex="0">
            <!-- Time Period Filter -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="dashboard-card">
                        <div class="card-body">
                            <form method="get" id="stats-period-form">
                                <input type="hidden" name="tab" value="stats">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <label for="period" class="form-label">Time Period</label>
                                    </div>
                                    <div class="col-md-6">
                                        <select class="form-select" id="period" name="period" onchange="document.getElementById('stats-period-form').submit()">
                                            <option value="week" {% if period == 'week' %}selected{% endif %}>Last 7 Days</option>
                                            <option value="month" {% if period == 'month' %}selected{% endif %}>Last 30 Days</option>
                                            <option value="year" {% if period == 'year' %}selected{% endif %}>Last Year</option>
                                            <option value="all" {% if period == 'all' %}selected{% endif %}>All Time</option>
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stats Overview Cards -->
            <div class="row g-4 mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-icon bg-primary bg-opacity-10 text-primary">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="stats-info">
                            <h3>{{ new_users|default:"0" }}</h3>
                            <p>New Users</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-icon bg-success bg-opacity-10 text-success">
                            <i class="bi bi-journal-text"></i>
                        </div>
                        <div class="stats-info">
                            <h3>{{ new_contexts|default:"0" }}</h3>
                            <p>New Contributions</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-icon bg-info bg-opacity-10 text-info">
                            <i class="bi bi-chat-dots"></i>
                        </div>
                        <div class="stats-info">
                            <h3>{{ new_interactions|default:"0" }}</h3>
                            <p>Interactions</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-icon bg-warning bg-opacity-10 text-warning">
                            <i class="bi bi-flag"></i>
                        </div>
                        <div class="stats-info">
                            <h3>{{ new_reports|default:"0" }}</h3>
                            <p>Reports</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row g-4 mb-4">
                <div class="col-md-6">
                    <div class="dashboard-card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">User Activity</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="userActivityChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="dashboard-card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">Content Distribution</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="contentDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- More Charts Row -->
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="dashboard-card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">Report Types</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="reportTypesChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="dashboard-card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">User Reputation Distribution</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="reputationDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contributions Tab -->
        <div class="tab-pane fade" id="contributions-tab-pane" role="tabpanel" aria-labelledby="contributions-tab" tabindex="0">
            <div class="row mb-4">
                <div class="col-md-8">
                    <h3 class="h4 mb-3">Your Contributions</h3>
                    <p class="text-muted mb-4">Contribute knowledge to help the community assistant provide better answers.</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createPostModal">
                        <i class="bi bi-plus-circle me-1"></i> Add Knowledge
                    </button>
                </div>
            </div>

            <!-- Filter and Sort Options -->
            <div class="dashboard-card mb-4">
                <div class="card-body">
                    <form method="get" id="contributions-filter-form" class="row g-3">
                        <input type="hidden" name="tab" value="contributions">
                        <div class="col-md-4">
                            <label for="filter" class="form-label">Filter By</label>
                            <select class="form-select" id="filter" name="filter">
                                <option value="all" {% if filter == 'all' %}selected{% endif %}>All Contributions</option>
                                <option value="mine" {% if filter == 'mine' %}selected{% endif %}>My Contributions</option>
                                <option value="upvoted" {% if filter == 'upvoted' %}selected{% endif %}>Upvoted by Me</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="sort" class="form-label">Sort By</label>
                            <select class="form-select" id="sort" name="sort">
                                <option value="newest" {% if sort == 'newest' %}selected{% endif %}>Newest First</option>
                                <option value="oldest" {% if sort == 'oldest' %}selected{% endif %}>Oldest First</option>
                                <option value="upvotes" {% if sort == 'upvotes' %}selected{% endif %}>Most Upvotes</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="search" name="search" placeholder="Search contributions..." value="{{ search|default:'' }}">
                                <button class="btn btn-outline-secondary" type="submit">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Contributions List -->
            <div id="contributions-list">
                {% if user_contexts %}
                    {% for context in user_contexts %}
                        <div class="contribution-card">
                            <div class="contribution-header">
                                <div class="d-flex align-items-center w-100">
                                    <div class="post-avatar me-3">
                                        {% if context.created_by.profile.avatar %}
                                            <img src="{{ context.created_by.profile.avatar.url }}" alt="{{ context.created_by.username }}">
                                        {% else %}
                                            <i class="bi bi-person"></i>
                                        {% endif %}
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0">
                                            {{ context.created_by.get_full_name|default:context.created_by.username }}
                                            {% if context.created_by.reputation %}
                                                <span class="reputation-badge reputation-{{ context.created_by.reputation.level|lower|slugify }}">
                                                    {{ context.created_by.reputation.level }}
                                                </span>
                                            {% endif %}
                                        </h6>
                                        <small class="text-muted">{{ context.created_at|timesince }} ago</small>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-link text-muted" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="bi bi-three-dots-vertical"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            {% if context.created_by == request.user %}
                                                <li>
                                                    <a class="dropdown-item" href="{% url 'assistants:edit_context' company.id assistant.id context.id %}">
                                                        <i class="bi bi-pencil"></i> Edit
                                                    </a>
                                                </li>
                                            {% endif %}
                                            <li>
                                                <button class="dropdown-item report-btn" data-bs-toggle="modal" data-bs-target="#reportModal"
                                                        data-content-type="assistants.communitycontext"
                                                        data-object-id="{{ context.id }}">
                                                    <i class="bi bi-flag"></i> Report
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="contribution-content">
                                <h5 class="contribution-title">{{ context.title }}</h5>
                                <div class="context-content" data-context-id="{{ context.id }}">
                                    <div class="content-preview">{{ context.text_content|safe|truncatewords_html:50 }}</div>
                                    <div class="content-full" style="display: none;">{{ context.text_content|safe }}</div>
                                </div>
                                {% if context.text_content|wordcount > 50 %}
                                    <button class="btn btn-sm btn-link p-0 show-more-btn" data-context-id="{{ context.id }}">
                                        Show more
                                    </button>
                                {% endif %}
                            </div>
                            <div class="contribution-meta">
                                <div>
                                    <span class="me-3">
                                        <i class="bi bi-hand-thumbs-up"></i> {{ context.upvotes|default:"0" }} upvotes
                                    </span>
                                    <span>
                                        <i class="bi bi-chat"></i> {{ context.comments_count|default:"0" }} comments
                                    </span>
                                </div>
                                <div>
                                    <span class="badge bg-light text-dark">
                                        <i class="bi bi-tag"></i> {{ context.keywords|default:"No tags" }}
                                    </span>
                                </div>
                            </div>
                            <div class="contribution-actions">
                                <button class="btn btn-sm btn-outline-primary upvote-btn {% if context.id in upvoted_contexts %}upvoted{% endif %}"
                                        data-context-id="{{ context.id }}">
                                    <i class="bi bi-hand-thumbs-up"></i> Upvote
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#commentModal"
                                        data-content-type="assistants.communitycontext"
                                        data-object-id="{{ context.id }}">
                                    <i class="bi bi-chat"></i> Comment
                                </button>
                                <button class="btn btn-sm btn-outline-secondary">
                                    <i class="bi bi-share"></i> Share
                                </button>
                            </div>
                        </div>
                    {% endfor %}

                    <!-- Pagination -->
                    {% if user_contexts.has_other_pages %}
                        <nav aria-label="Contributions pagination" class="mt-4">
                            <ul class="pagination justify-content-center">
                                {% if user_contexts.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?tab=contributions&page={{ user_contexts.previous_page_number }}{% if filter %}&filter={{ filter }}{% endif %}{% if sort %}&sort={{ sort }}{% endif %}{% if search %}&search={{ search }}{% endif %}">
                                            <i class="bi bi-chevron-left"></i> Previous
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link"><i class="bi bi-chevron-left"></i> Previous</span>
                                    </li>
                                {% endif %}

                                {% for i in user_contexts.paginator.page_range %}
                                    {% if user_contexts.number == i %}
                                        <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                                    {% else %}
                                        <li class="page-item">
                                            <a class="page-link" href="?tab=contributions&page={{ i }}{% if filter %}&filter={{ filter }}{% endif %}{% if sort %}&sort={{ sort }}{% endif %}{% if search %}&search={{ search }}{% endif %}">{{ i }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if user_contexts.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?tab=contributions&page={{ user_contexts.next_page_number }}{% if filter %}&filter={{ filter }}{% endif %}{% if sort %}&sort={{ sort }}{% endif %}{% if search %}&search={{ search }}{% endif %}">
                                            Next <i class="bi bi-chevron-right"></i>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">Next <i class="bi bi-chevron-right"></i></span>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                {% else %}
                    <div class="empty-state">
                        <i class="bi bi-journal-text"></i>
                        <h4>No Contributions Found</h4>
                        <p>{% if filter == 'mine' %}You haven't made any contributions yet.{% else %}No contributions match your search criteria.{% endif %}</p>
                        <button class="btn btn-primary mt-3" data-bs-toggle="modal" data-bs-target="#createPostModal">
                            <i class="bi bi-plus-circle me-1"></i> Add Your First Contribution
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Reports Tab (Staff/Superuser Only) -->
        {% if user.is_staff or user.is_superuser %}
        <div class="tab-pane fade" id="reports-tab-pane" role="tabpanel" aria-labelledby="reports-tab" tabindex="0">
            <div class="row mb-4">
                <div class="col-md-8">
                    <h3 class="h4 mb-3">Reported Content</h3>
                    <p class="text-muted mb-4">Review and moderate content reported by community members.</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="{% url 'assistants:moderation_dashboard' company.id assistant.id %}" class="btn btn-outline-danger">
                        <i class="bi bi-shield-fill me-1"></i> Full Moderation Dashboard
                    </a>
                </div>
            </div>

            <!-- Filter Options -->
            <div class="dashboard-card mb-4">
                <div class="card-body">
                    <form method="get" id="reports-filter-form" class="row g-3">
                        <input type="hidden" name="tab" value="reports">
                        <div class="col-md-4">
                            <label for="status" class="form-label">Filter By Status</label>
                            <select class="form-select" id="status" name="status" onchange="this.form.submit()">
                                <option value="all" {% if status == 'all' %}selected{% endif %}>All Reports</option>
                                <option value="pending" {% if status == 'pending' %}selected{% endif %}>Pending Review</option>
                                <option value="approved" {% if status == 'approved' %}selected{% endif %}>Approved</option>
                                <option value="rejected" {% if status == 'rejected' %}selected{% endif %}>Rejected</option>
                                <option value="removed" {% if status == 'removed' %}selected{% endif %}>Content Removed</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="report_type" class="form-label">Filter By Type</label>
                            <select class="form-select" id="report_type" name="report_type" onchange="this.form.submit()">
                                <option value="all" {% if report_type == 'all' %}selected{% endif %}>All Types</option>
                                <option value="spam" {% if report_type == 'spam' %}selected{% endif %}>Spam</option>
                                <option value="inappropriate" {% if report_type == 'inappropriate' %}selected{% endif %}>Inappropriate Content</option>
                                <option value="offensive" {% if report_type == 'offensive' %}selected{% endif %}>Offensive Language</option>
                                <option value="misinformation" {% if report_type == 'misinformation' %}selected{% endif %}>Misinformation</option>
                                <option value="other" {% if report_type == 'other' %}selected{% endif %}>Other</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="reports-search" class="form-label">Search</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="reports-search" name="search" placeholder="Search reports..." value="{{ search|default:'' }}">
                                <button class="btn btn-outline-secondary" type="submit">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Reports List -->
            <div id="reports-list">
                {% if reports %}
                    {% for report in reports %}
                        <div class="report-card mb-4">
                            <div class="report-header">
                                <div>
                                    <h5 class="mb-1">Report #{{ report.id }}</h5>
                                    <div>
                                        <span class="status-badge status-{{ report.status }}">
                                            {{ report.get_status_display }}
                                        </span>
                                        <span class="badge bg-secondary ms-2">
                                            {{ report.get_report_type_display }}
                                        </span>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">Reported {{ report.created_at|timesince }} ago</small>
                                    <div>
                                        <small class="text-muted">by {{ report.reported_by.username|default:"Anonymous" }}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="report-content mt-3">
                                <div class="mb-3">
                                    <strong>Reason:</strong>
                                    <p class="mb-0">{{ report.reason }}</p>
                                </div>
                                <div class="p-3 bg-light rounded">
                                    <strong>Reported Content:</strong>
                                    <div id="content-{{ report.id }}" class="mt-2">
                                        {% if report.content_object %}
                                            {% if report.content_type.model == 'communitycontext' %}
                                                <h6>{{ report.content_object.title }}</h6>
                                                <div>{{ report.content_object.text_content|safe|truncatewords_html:50 }}</div>
                                                <div class="mt-2">
                                                    <small class="text-muted">Contributed by {{ report.content_object.created_by.username }}</small>
                                                </div>
                                            {% elif report.content_type.model == 'comment' %}
                                                <div>{{ report.content_object.text|linebreaksbr }}</div>
                                                <div class="mt-2">
                                                    <small class="text-muted">Comment by {{ report.content_object.user.username }}</small>
                                                </div>
                                            {% else %}
                                                <div>{{ report.content_object }}</div>
                                            {% endif %}
                                        {% else %}
                                            <div class="text-muted">Content no longer available</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% if report.status == 'pending' %}
                                <div class="report-actions mt-3">
                                    <form method="post" action="{% url 'assistants:moderation_action' company.id assistant.id %}" class="d-inline-block">
                                        {% csrf_token %}
                                        <input type="hidden" name="action_type" value="approve">
                                        <input type="hidden" name="report_id" value="{{ report.id }}">
                                        <input type="hidden" name="next" value="{{ request.path }}?tab=reports{% if status %}&status={{ status }}{% endif %}{% if report_type %}&report_type={{ report_type }}{% endif %}{% if search %}&search={{ search }}{% endif %}">
                                        <button type="submit" class="btn btn-sm btn-success">
                                            <i class="bi bi-check-circle me-1"></i> Approve Content
                                        </button>
                                    </form>
                                    <form method="post" action="{% url 'assistants:moderation_action' company.id assistant.id %}" class="d-inline-block">
                                        {% csrf_token %}
                                        <input type="hidden" name="action_type" value="reject">
                                        <input type="hidden" name="report_id" value="{{ report.id }}">
                                        <input type="hidden" name="next" value="{{ request.path }}?tab=reports{% if status %}&status={{ status }}{% endif %}{% if report_type %}&report_type={{ report_type }}{% endif %}{% if search %}&search={{ search }}{% endif %}">
                                        <button type="submit" class="btn btn-sm btn-warning">
                                            <i class="bi bi-x-circle me-1"></i> Reject Report
                                        </button>
                                    </form>
                                    <form method="post" action="{% url 'assistants:moderation_action' company.id assistant.id %}" class="d-inline-block">
                                        {% csrf_token %}
                                        <input type="hidden" name="action_type" value="remove">
                                        <input type="hidden" name="report_id" value="{{ report.id }}">
                                        <input type="hidden" name="next" value="{{ request.path }}?tab=reports{% if status %}&status={{ status }}{% endif %}{% if report_type %}&report_type={{ report_type }}{% endif %}{% if search %}&search={{ search }}{% endif %}">
                                        <button type="submit" class="btn btn-sm btn-danger">
                                            <i class="bi bi-trash me-1"></i> Remove Content
                                        </button>
                                    </form>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#viewUserModal"
                                            data-user-id="{% if report.content_object.created_by %}{{ report.content_object.created_by.id }}{% elif report.content_object.user %}{{ report.content_object.user.id }}{% endif %}"
                                            data-username="{% if report.content_object.created_by %}{{ report.content_object.created_by.username }}{% elif report.content_object.user %}{{ report.content_object.user.username }}{% endif %}">
                                        <i class="bi bi-person me-1"></i> View User
                                    </button>
                                </div>
                            {% endif %}
                        </div>
                    {% endfor %}

                    <!-- Pagination -->
                    {% if reports.has_other_pages %}
                        <nav aria-label="Reports pagination" class="mt-4">
                            <ul class="pagination justify-content-center">
                                {% if reports.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?tab=reports&page={{ reports.previous_page_number }}{% if status %}&status={{ status }}{% endif %}{% if report_type %}&report_type={{ report_type }}{% endif %}{% if search %}&search={{ search }}{% endif %}">
                                            <i class="bi bi-chevron-left"></i> Previous
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link"><i class="bi bi-chevron-left"></i> Previous</span>
                                    </li>
                                {% endif %}

                                {% for i in reports.paginator.page_range %}
                                    {% if reports.number == i %}
                                        <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                                    {% else %}
                                        <li class="page-item">
                                            <a class="page-link" href="?tab=reports&page={{ i }}{% if status %}&status={{ status }}{% endif %}{% if report_type %}&report_type={{ report_type }}{% endif %}{% if search %}&search={{ search }}{% endif %}">{{ i }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if reports.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?tab=reports&page={{ reports.next_page_number }}{% if status %}&status={{ status }}{% endif %}{% if report_type %}&report_type={{ report_type }}{% endif %}{% if search %}&search={{ search }}{% endif %}">
                                            Next <i class="bi bi-chevron-right"></i>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">Next <i class="bi bi-chevron-right"></i></span>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                {% else %}
                    <div class="empty-state">
                        <i class="bi bi-shield-check"></i>
                        <h4>No Reports Found</h4>
                        <p>There are no reports matching your criteria.</p>
                    </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

<!-- Create Post Modal -->
<div class="modal fade" id="createPostModal" tabindex="-1" aria-labelledby="createPostModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createPostModalLabel">Add Knowledge</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{% url 'assistants:add_context' company.id assistant.id %}" method="post" enctype="multipart/form-data" id="knowledge-form">
                <div class="modal-body">
                    {% csrf_token %}
                    {{ context_form|crispy }}
                    <div class="word-counter mt-1 text-end">
                        <small class="text-muted" id="word-count">0</small>
                        <small class="text-muted"> / 500 words</small>
                    </div>
                    <div class="alert alert-info mt-3">
                        <i class="bi bi-info-circle me-2"></i>
                        <small>Keep your contribution under 500 words. Shorter, focused knowledge is more effective.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i> Add Knowledge
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Ask Question Modal -->
<div class="modal fade" id="askQuestionModal" tabindex="-1" aria-labelledby="askQuestionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="askQuestionModalLabel">Ask a Question</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{% url 'assistants:interact' company.id assistant.id %}" method="post" id="question-form">
                <div class="modal-body">
                    {% csrf_token %}
                    <input type="hidden" name="use_community_context" value="1">
                    <div class="mb-3">
                        <label for="question-text" class="form-label">Your Question</label>
                        <textarea class="form-control" id="question-text" name="message" rows="3" placeholder="What would you like to know?" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-send me-1"></i> Ask Question
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Report Modal -->
<div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reportModalLabel">Report Content</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{% url 'assistants:report_content' company.id assistant.id %}" method="post" id="report-form">
                <div class="modal-body">
                    {% csrf_token %}
                    <input type="hidden" name="content_type" id="report-content-type">
                    <input type="hidden" name="object_id" id="report-object-id">
                    <div class="mb-3">
                        <label for="report-type" class="form-label">Reason for Report</label>
                        <select class="form-select" id="report-type" name="report_type" required>
                            <option value="">Select a reason...</option>
                            <option value="spam">Spam</option>
                            <option value="inappropriate">Inappropriate Content</option>
                            <option value="offensive">Offensive Language</option>
                            <option value="misinformation">Misinformation</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="report-reason" class="form-label">Additional Details</label>
                        <textarea class="form-control" id="report-reason" name="reason" rows="3" placeholder="Please provide more details about why you're reporting this content"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-flag me-1"></i> Submit Report
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Comment Modal -->
<div class="modal fade" id="commentModal" tabindex="-1" aria-labelledby="commentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="commentModalLabel">Add Comment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{% url 'assistants:add_comment' company.id assistant.id %}" method="post" id="comment-form">
                <div class="modal-body">
                    {% csrf_token %}
                    <input type="hidden" name="content_type" id="comment-content-type">
                    <input type="hidden" name="object_id" id="comment-object-id">
                    <div class="mb-3">
                        <label for="comment-text" class="form-label">Your Comment</label>
                        <textarea class="form-control" id="comment-text" name="comment" rows="3" placeholder="Add your comment here" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-chat me-1"></i> Post Comment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Rating Modal -->
<div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="ratingModalLabel">Rate Assistant</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Select your rating for <strong id="modalAssistantName">this assistant</strong>:</p>
        <div class="modal-stars text-center mb-3" style="font-size: 2rem;"> <!-- Larger stars in modal -->
            {% for i in "12345"|make_list %}
                <span class="star" data-value="{{ i }}" style="cursor: pointer; color: gold; opacity: 0.5;">★</span>
            {% endfor %}
        </div>
        <form id="ratingForm">
            <input type="hidden" id="ratingAssistantId" name="assistant_id" value="">
            <input type="hidden" id="ratingValue" name="rating" value="">
            <div class="d-grid">
                <button type="submit" class="btn btn-primary">Submit Rating</button>
            </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- View User Modal (for Moderation) -->
{% if user.is_staff or user.is_superuser %}
<div class="modal fade" id="viewUserModal" tabindex="-1" aria-labelledby="viewUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewUserModalLabel">User Profile</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4 border-end">
                        <div class="text-center mb-4">
                            <div class="mx-auto mb-3" style="width: 100px; height: 100px; background-color: #e9ecef; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                <i class="bi bi-person" style="font-size: 3rem;"></i>
                            </div>
                            <h5 id="modal-username">Username</h5>
                            <div id="modal-user-reputation" class="mt-2">
                                <span class="reputation-badge reputation-new">New Member</span>
                            </div>
                        </div>
                        <div class="list-group list-group-flush">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                Reputation Score
                                <span id="modal-reputation-score" class="badge bg-primary rounded-pill">0</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                Contributions
                                <span id="modal-contributions-count" class="badge bg-success rounded-pill">0</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                Upvotes Received
                                <span id="modal-upvotes-count" class="badge bg-info rounded-pill">0</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                Reports Against
                                <span id="modal-reports-count" class="badge bg-warning rounded-pill">0</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <h5 class="mb-3">Moderation Actions</h5>
                        <div class="mb-4">
                            <form id="adjust-reputation-form" method="post" action="{% url 'assistants:moderation_action' company.id assistant.id %}" class="mb-3">
                                {% csrf_token %}
                                <input type="hidden" name="action_type" value="adjust_reputation">
                                <input type="hidden" name="target_user_id" id="adjust-reputation-user-id">
                                <input type="hidden" name="next" value="{{ request.path }}{% if request.GET.tab %}?tab={{ request.GET.tab }}{% endif %}">
                                <div class="input-group">
                                    <span class="input-group-text">Adjust Reputation</span>
                                    <input type="number" class="form-control" name="reputation_adjustment" placeholder="Enter value (+ or -)" required>
                                    <button class="btn btn-primary" type="submit">Apply</button>
                                </div>
                                <div class="form-text">Positive values increase reputation, negative values decrease it.</div>
                            </form>

                            <div class="d-flex gap-2 mb-3">
                                <form method="post" action="{% url 'assistants:moderation_action' company.id assistant.id %}" class="flex-grow-1">
                                    {% csrf_token %}
                                    <input type="hidden" name="action_type" value="warn">
                                    <input type="hidden" name="target_user_id" id="warn-user-id">
                                    <input type="hidden" name="notes" value="You have been warned by a moderator for violating community guidelines.">
                                    <input type="hidden" name="next" value="{{ request.path }}{% if request.GET.tab %}?tab={{ request.GET.tab }}{% endif %}">
                                    <button type="submit" class="btn btn-warning w-100">
                                        <i class="bi bi-exclamation-triangle me-1"></i> Warn User
                                    </button>
                                </form>

                                <button type="button" class="btn btn-danger flex-grow-1" data-bs-toggle="collapse" data-bs-target="#banUserForm" aria-expanded="false" aria-controls="banUserForm">
                                    <i class="bi bi-shield-fill-x me-1"></i> Ban User
                                </button>
                            </div>

                            <div class="collapse" id="banUserForm">
                                <div class="card card-body mb-3">
                                    <form method="post" action="{% url 'assistants:moderation_action' company.id assistant.id %}">
                                        {% csrf_token %}
                                        <input type="hidden" name="action_type" value="ban">
                                        <input type="hidden" name="target_user_id" id="ban-user-id">
                                        <input type="hidden" name="next" value="{{ request.path }}{% if request.GET.tab %}?tab={{ request.GET.tab }}{% endif %}">

                                        <div class="mb-3">
                                            <label for="ban-duration" class="form-label">Ban Duration (days)</label>
                                            <input type="number" class="form-control" id="ban-duration" name="ban_duration" value="7" min="0">
                                            <div class="form-text">Enter 0 for a permanent ban.</div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="ban-reason" class="form-label">Reason</label>
                                            <textarea class="form-control" id="ban-reason" name="notes" rows="2" required></textarea>
                                        </div>

                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-danger">
                                                <i class="bi bi-shield-fill-x me-1"></i> Confirm Ban
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <h5 class="mb-3">Recent Activity</h5>
                        <div id="user-recent-activity" class="list-group">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading user activity...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% block extra_js %}
<!-- TinyMCE for rich text editing -->
{{ context_form.media.js }}
<script src="{% static 'js/tinymce-word-limit.js' %}"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- CSRF Token for AJAX requests -->
<meta name="csrf-token" content="{{ csrf_token }}">

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Report modal data passing
    const reportModal = document.getElementById('reportModal');
    if (reportModal) {
        reportModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const contentType = button.getAttribute('data-content-type');
            const objectId = button.getAttribute('data-object-id');

            document.getElementById('report-content-type').value = contentType;
            document.getElementById('report-object-id').value = objectId;
        });
    }

    // Comment modal data passing
    const commentModal = document.getElementById('commentModal');
    if (commentModal) {
        commentModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const contentType = button.getAttribute('data-content-type');
            const objectId = button.getAttribute('data-object-id');

            document.getElementById('comment-content-type').value = contentType;
            document.getElementById('comment-object-id').value = objectId;
        });
    }

    // View User Modal data passing (for moderation)
    const viewUserModal = document.getElementById('viewUserModal');
    if (viewUserModal) {
        viewUserModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const userId = button.getAttribute('data-user-id');
            const username = button.getAttribute('data-username');

            // Set user ID in all forms
            document.getElementById('adjust-reputation-user-id').value = userId;
            document.getElementById('warn-user-id').value = userId;
            document.getElementById('ban-user-id').value = userId;

            // Set username in modal
            document.getElementById('modal-username').textContent = username;

            // This would typically fetch user data via AJAX
            // For now, we'll just set placeholder values
            document.getElementById('modal-reputation-score').textContent = '25';
            document.getElementById('modal-contributions-count').textContent = '5';
            document.getElementById('modal-upvotes-count').textContent = '12';
            document.getElementById('modal-reports-count').textContent = '1';

            // Set reputation badge
            const reputationBadge = document.querySelector('#modal-user-reputation .reputation-badge');
            reputationBadge.className = 'reputation-badge reputation-contributor';
            reputationBadge.textContent = 'Contributor';

            // This would typically fetch user activity via AJAX
            // For now, we'll just set a placeholder
            setTimeout(() => {
                document.getElementById('user-recent-activity').innerHTML = `
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">Added a new context</h6>
                            <small class="text-muted">3 days ago</small>
                        </div>
                        <p class="mb-1">"Introduction to Machine Learning"</p>
                    </div>
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">Asked a question</h6>
                            <small class="text-muted">1 week ago</small>
                        </div>
                        <p class="mb-1">"What's the difference between supervised and unsupervised learning?"</p>
                    </div>
                `;
            }, 1000);
        });
    }

    // Show more/less functionality for context content
    const showMoreButtons = document.querySelectorAll('.show-more-btn');

    showMoreButtons.forEach(button => {
        button.addEventListener('click', function() {
            const contextId = this.getAttribute('data-context-id');
            const contextElement = document.querySelector(`.context-content[data-context-id="${contextId}"]`);

            if (contextElement) {
                const previewElement = contextElement.querySelector('.content-preview');
                const fullElement = contextElement.querySelector('.content-full');

                if (previewElement.style.display !== 'none') {
                    previewElement.style.display = 'none';
                    fullElement.style.display = 'block';
                    this.textContent = 'Show less';
                } else {
                    previewElement.style.display = 'block';
                    fullElement.style.display = 'none';
                    this.textContent = 'Show more';
                }
            } else {
                // Fallback for elements without the proper structure
                this.textContent = this.textContent === 'Show more' ? 'Show less' : 'Show more';
            }
        });
    });

    // Upvote functionality
    const upvoteButtons = document.querySelectorAll('.upvote-btn');
    upvoteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const contextId = this.getAttribute('data-context-id');
            this.classList.toggle('upvoted');

            // This would typically send an AJAX request to update the upvote count
            // For now, we'll just update the UI
            const countElement = this.querySelector('.upvote-count');
            if (countElement) {
                let count = parseInt(countElement.textContent);
                if (this.classList.contains('upvoted')) {
                    count += 1;
                } else {
                    count = Math.max(0, count - 1);
                }
                countElement.textContent = count.toString();
            }
        });
    });

    // Initialize TinyMCE for the knowledge form
    if (typeof tinymce !== 'undefined') {
        tinymce.init({
            selector: '#id_text_content', // Target the specific textarea
            height: 300,
            menubar: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'help', 'wordcount', 'autoresize'
            ],
            toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | link image media table | code | help',
            images_upload_url: '/assistants/tinymce/upload/',
            file_picker_types: 'image',
            file_picker_callback: function (callback, value, meta) {
                if (meta.filetype === 'image') {
                    var input = document.createElement('input');
                    input.setAttribute('type', 'file');
                    input.setAttribute('accept', 'image/*');
                    input.onchange = function () {
                        var file = this.files[0];
                        var formData = new FormData();
                        formData.append('file', file);
                        fetch('/assistants/tinymce/upload/', {
                            method: 'POST',
                            headers: {
                                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                            },
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            callback(data.location, { title: file.name });
                        })
                        .catch(error => {
                            console.error('Error uploading image:', error);
                        });
                    };
                    input.click();
                }
            },
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px; }',
            statusbar: true,
            autoresize_bottom_margin: 20,
            setup: function (editor) {
                editor.on('change', function () {
                    editor.save(); // Important to save content back to the textarea
                    const textarea = document.getElementById(editor.id);
                    if (textarea) {
                        textarea.value = editor.getContent();
                    }

                    // Update word count on change
                    updateWordCount(editor);
                });

                // Handle content changes to resize editor properly
                editor.on('SetContent', function() {
                    if (editor.plugins.autoresize) {
                        editor.plugins.autoresize.resize();
                    }

                    // Update word count when content is set
                    updateWordCount(editor);
                });
            }
        }).then(editors => {
            if (editors && editors.length > 0) {
                console.log(`TinyMCE init SUCCESS for selector '#id_text_content'.`);
            } else {
                console.warn(`TinyMCE init completed for selector '#id_text_content', but no editor instances returned.`);
            }
        }).catch(error => {
            console.error(`TinyMCE init FAILED for selector '#id_text_content'. Error:`, error);
        });
    } else {
        console.error('TinyMCE object not found. Ensure TinyMCE script is loaded before this script.');
    }

    // Function to update word count
    function updateWordCount(editor) {
        if (typeof countTinyMCEWords === 'function') {
            const wordCount = countTinyMCEWords(editor.id);
            const wordCountElement = document.getElementById('word-count');
            if (wordCountElement) {
                wordCountElement.textContent = wordCount;

                // Add visual indicator if over limit
                if (wordCount > 500) {
                    wordCountElement.classList.add('text-danger');
                    wordCountElement.classList.remove('text-muted');
                } else {
                    wordCountElement.classList.remove('text-danger');
                    wordCountElement.classList.add('text-muted');
                }
            }
        }
    }

    // Add form submit handler to prevent submission if over word limit
    const form = document.getElementById('knowledge-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (typeof tinymce !== 'undefined' && tinymce.get('id_text_content')) {
                const editor = tinymce.get('id_text_content');
                const wordCount = countTinyMCEWords('id_text_content');

                if (wordCount > 500) {
                    e.preventDefault();
                    alert('Your content exceeds the 500 word limit. Please shorten your text to submit.');
                    return false;
                }

                // Save TinyMCE content to form field before submission
                editor.save();
            }
        });
    }

    // Auto-submit filter forms when select changes
    const filterForms = document.querySelectorAll('#contributions-filter-form select');
    filterForms.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });

    // Initialize charts if we're on the stats tab
    const urlParams = new URLSearchParams(window.location.search);
    const activeTab = urlParams.get('tab');
    if (activeTab === 'stats' || document.getElementById('stats-tab').classList.contains('active')) {
        initializeCharts();
    }

    // Initialize charts when stats tab is shown
    const statsTab = document.getElementById('stats-tab');
    if (statsTab) {
        statsTab.addEventListener('shown.bs.tab', function() {
            initializeCharts();
        });
    }

    // Function to initialize all charts
    function initializeCharts() {
        // Sample data for charts - in a real implementation, this would come from the backend

        // User Activity Chart
        const userActivityCtx = document.getElementById('userActivityChart');
        if (userActivityCtx) {
            const userActivityChart = new Chart(userActivityCtx, {
                type: 'line',
                data: {
                    labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'],
                    datasets: [{
                        label: 'New Users',
                        data: [5, 8, 12, 7, 10, 15, 9],
                        borderColor: 'rgba(13, 110, 253, 1)',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Active Users',
                        data: [20, 25, 30, 22, 28, 35, 32],
                        borderColor: 'rgba(25, 135, 84, 1)',
                        backgroundColor: 'rgba(25, 135, 84, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Content Distribution Chart
        const contentDistributionCtx = document.getElementById('contentDistributionChart');
        if (contentDistributionCtx) {
            const contentDistributionChart = new Chart(contentDistributionCtx, {
                type: 'bar',
                data: {
                    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                    datasets: [{
                        label: 'New Contexts',
                        data: [45, 60, 75, 55],
                        backgroundColor: 'rgba(25, 135, 84, 0.7)'
                    }, {
                        label: 'Upvotes',
                        data: [120, 150, 180, 140],
                        backgroundColor: 'rgba(13, 110, 253, 0.7)'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Report Types Chart
        const reportTypesCtx = document.getElementById('reportTypesChart');
        if (reportTypesCtx) {
            const reportTypesChart = new Chart(reportTypesCtx, {
                type: 'pie',
                data: {
                    labels: ['Spam', 'Inappropriate', 'Offensive', 'Misinformation', 'Other'],
                    datasets: [{
                        data: [15, 25, 20, 30, 10],
                        backgroundColor: [
                            'rgba(255, 193, 7, 0.7)',
                            'rgba(220, 53, 69, 0.7)',
                            'rgba(111, 66, 193, 0.7)',
                            'rgba(23, 162, 184, 0.7)',
                            'rgba(108, 117, 125, 0.7)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        title: {
                            display: false
                        }
                    }
                }
            });
        }

        // Reputation Distribution Chart
        const reputationDistributionCtx = document.getElementById('reputationDistributionChart');
        if (reputationDistributionCtx) {
            const reputationDistributionChart = new Chart(reputationDistributionCtx, {
                type: 'doughnut',
                data: {
                    labels: ['New Member', 'Contributor', 'Trusted Member', 'Expert', 'Community Leader'],
                    datasets: [{
                        data: [50, 30, 15, 4, 1],
                        backgroundColor: [
                            'rgba(108, 117, 125, 0.7)',
                            'rgba(13, 110, 253, 0.7)',
                            'rgba(25, 135, 84, 0.7)',
                            'rgba(111, 66, 193, 0.7)',
                            'rgba(255, 193, 7, 0.7)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        title: {
                            display: false
                        }
                    }
                }
            });
        }
    }

    // Handle tab changes in URL
    const dashboardTabs = document.getElementById('dashboardTabs');
    if (dashboardTabs) {
        const tabLinks = dashboardTabs.querySelectorAll('.nav-link');
        tabLinks.forEach(tabLink => {
            tabLink.addEventListener('shown.bs.tab', function(event) {
                const tabId = event.target.id;
                const tabName = tabId.replace('-tab', '');

                // Update URL without reloading the page
                const url = new URL(window.location);
                url.searchParams.set('tab', tabName);
                window.history.pushState({}, '', url);
            });
        });

        // Activate tab based on URL parameter
        if (activeTab) {
            const tabToActivate = document.getElementById(`${activeTab}-tab`);
            if (tabToActivate) {
                const tab = new bootstrap.Tab(tabToActivate);
                tab.show();
            }
        }
    }
});
</script>

<!-- Rating and Favorites Handling -->
<script>
// Helper function to get CSRF token
function getCsrfToken() {
    const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
    if (!csrfInput) {
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfMeta) return csrfMeta.getAttribute('content');
    }
    return csrfInput ? csrfInput.value : null;
}

// Handle rating modal
function handleRatingModal() {
    const ratingModal = document.getElementById('ratingModal');
    if (!ratingModal) return;

    // Set assistant info when modal opens
    ratingModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const assistantId = button.getAttribute('data-assistant-id');
        const assistantName = button.getAttribute('data-assistant-name');

        document.getElementById('modalAssistantName').textContent = assistantName;
        document.getElementById('ratingAssistantId').value = assistantId;

        // Clear any previously selected stars
        document.querySelectorAll('.modal-stars .star').forEach(star => {
            star.classList.remove('selected');
        });
    });

    // Handle star selection
    const stars = document.querySelectorAll('.modal-stars .star');
    stars.forEach(star => {
        star.addEventListener('click', function() {
            const value = this.getAttribute('data-value');
            document.getElementById('ratingValue').value = value;

            // Update visual selection
            stars.forEach(s => {
                if (parseInt(s.getAttribute('data-value')) <= parseInt(value)) {
                    s.classList.add('selected');
                } else {
                    s.classList.remove('selected');
                }
            });
        });

        // Hover effects
        star.addEventListener('mouseenter', function() {
            const value = parseInt(this.getAttribute('data-value'));
            stars.forEach(s => {
                if (parseInt(s.getAttribute('data-value')) <= value) {
                    s.classList.add('hover');
                }
            });
        });

        star.addEventListener('mouseleave', function() {
            stars.forEach(s => s.classList.remove('hover'));
        });
    });

    // Handle form submission
    const ratingForm = document.getElementById('ratingForm');
    ratingForm.addEventListener('submit', async function(event) {
        event.preventDefault();

        const assistantId = document.getElementById('ratingAssistantId').value;
        const ratingValue = document.getElementById('ratingValue').value;

        if (!ratingValue) {
            alert('Please select a rating');
            return;
        }

        const csrfToken = getCsrfToken();
        if (!csrfToken) {
            console.error('CSRF token not found');
            return;
        }

        try {
            const response = await fetch(`/directory/rate-assistant/${assistantId}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken
                },
                body: `assistant_id=${assistantId}&rating=${ratingValue}`
            });

            const result = await response.json();

            if (result.status === 'success') {
                // Close modal
                const modalInstance = bootstrap.Modal.getInstance(ratingModal);
                modalInstance.hide();

                // Update rating display if needed
                if (result.html) {
                    const ratingDisplay = document.getElementById(`rating-display-${assistantId}`);
                    if (ratingDisplay) {
                        ratingDisplay.innerHTML = result.html;
                    }
                }

                // Show success message using toast notification
                if (typeof showToast === 'function') {
                    showToast('Rating submitted successfully!', 'success');
                } else {
                    console.log('Rating submitted successfully!');
                }
            } else {
                if (typeof showToast === 'function') {
                    showToast(result.message || 'Error submitting rating', 'error');
                } else {
                    console.error(result.message || 'Error submitting rating');
                }
            }
        } catch (error) {
            console.error('Error:', error);
            if (typeof showToast === 'function') {
                showToast('An error occurred while submitting your rating', 'error');
            } else {
                console.error('An error occurred while submitting your rating');
            }
        }
    });
}

// Handle like/favorite buttons
function handleLikeButtons() {
    const likeButtons = document.querySelectorAll('.like-button');
    likeButtons.forEach(button => {
        button.addEventListener('click', async function(event) {
            event.preventDefault();

            const itemId = this.getAttribute('data-item-id');
            const itemType = this.getAttribute('data-item-type');
            const csrfToken = getCsrfToken();

            if (!csrfToken) {
                console.error('CSRF token not found');
                return;
            }

            try {
                const response = await fetch('{% url "directory:toggle_saved_item" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken
                    },
                    body: `item_id=${itemId}&item_type=${itemType}`
                });

                const result = await response.json();

                if (result.status === 'success' || result.action === 'unfavorited') {
                    // Toggle button appearance
                    this.classList.toggle('text-danger');
                    this.classList.toggle('text-secondary');

                    // Update tooltip
                    if (this.classList.contains('text-danger')) {
                        this.setAttribute('title', 'Remove from Favorites');
                    } else {
                        this.setAttribute('title', 'Add to Favorites');
                    }
                } else if (result.status === 'options') {
                    // Handle folder options if needed
                    // For simplicity, we'll just toggle the button for now
                    this.classList.toggle('text-danger');
                    this.classList.toggle('text-secondary');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('An error occurred while updating favorites');
            }
        });
    });
}

// Initialize handlers when the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    handleRatingModal();
    handleLikeButtons();
});
</script>
{% endblock %}
