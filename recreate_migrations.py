"""
<PERSON><PERSON>t to recreate and apply migrations for all apps.
This should be run after deleting all migration files and resetting the migration history.
"""
import os
import sys
import subprocess
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

def run_command(command):
    """Run a command and return the result."""
    print(f"Running: {' '.join(command)}")
    
    try:
        result = subprocess.run(
            command,
            check=False,  # Don't raise an exception on non-zero exit
            capture_output=True,
            text=True
        )
        
        print(f"Exit code: {result.returncode}")
        if result.stdout:
            print(f"STDOUT: {result.stdout}")
        if result.stderr:
            print(f"STDERR: {result.stderr}")
        
        return result.returncode == 0
    except Exception as e:
        print(f"Error running command: {e}")
        return False

def recreate_migrations():
    """Recreate migrations for all apps."""
    # List of apps to create migrations for
    apps = [
        "accounts",
        "assistants",
        "content",
        "directory",
        "site_settings",
        "superadmin",
    ]
    
    print("Creating migrations for all apps...")
    
    # Create migrations for each app
    for app in apps:
        print(f"\nCreating migrations for {app}...")
        success = run_command([sys.executable, "manage.py", "makemigrations", app])
        if not success:
            print(f"Failed to create migrations for {app}.")
    
    print("\nApplying migrations...")
    
    # First migrate the core Django apps
    core_apps = [
        "contenttypes",
        "auth",
        "admin",
        "sessions",
        "sites",
    ]
    
    for app in core_apps:
        print(f"\nMigrating {app}...")
        success = run_command([sys.executable, "manage.py", "migrate", app])
        if not success:
            print(f"Failed to migrate {app}.")
    
    # Then migrate third-party apps
    third_party_apps = [
        "debug_toolbar",
        "guardian",
        "impersonate",
        "crispy_forms",
        "crispy_bootstrap5",
        "tinymce",
    ]
    
    for app in third_party_apps:
        print(f"\nMigrating {app}...")
        success = run_command([sys.executable, "manage.py", "migrate", app])
        if not success:
            print(f"Failed to migrate {app}.")
    
    # Finally migrate our custom apps
    for app in apps:
        print(f"\nMigrating {app}...")
        success = run_command([sys.executable, "manage.py", "migrate", app])
        if not success:
            print(f"Failed to migrate {app}.")
    
    # Run a final migrate command to catch any remaining migrations
    print("\nRunning final migration to catch any remaining migrations...")
    run_command([sys.executable, "manage.py", "migrate"])
    
    print("\nMigration process completed.")

if __name__ == "__main__":
    # Confirm before proceeding
    confirm = input("This will recreate and apply migrations for all apps. Are you sure? (y/n): ")
    if confirm.lower() == 'y':
        recreate_migrations()
    else:
        print("Operation cancelled.")
