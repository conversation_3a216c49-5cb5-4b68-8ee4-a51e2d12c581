# Manual Fix Instructions

Since we're having issues with the automated scripts, here's how to fix the issue manually:

## Step 1: Modify the Migration File

We've already modified the migration file `accounts\migrations\0010_remove_registrationlink_role_remove_membership_role_and_more.py` to comment out the problematic operation:

```python
# Commented out because the table doesn't exist
# migrations.RemoveField(
#     model_name='membership',
#     name='accessible_folders',
# ),
```

## Step 2: Mark the Migration as Applied

Open a PostgreSQL command prompt:

```bash
psql -U postgres -d postgres2
```

Enter your password when prompted.

Then run this SQL command to mark the migration as applied:

```sql
INSERT INTO django_migrations (app, name, applied)
SELECT 'accounts', '0010_remove_registrationlink_role_remove_membership_role_and_more', NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM django_migrations 
    WHERE app = 'accounts' AND name = '0010_remove_registrationlink_role_remove_membership_role_and_more'
);
```

Verify that the migration is marked as applied:

```sql
SELECT app, name, applied 
FROM django_migrations 
WHERE app = 'accounts' AND name = '0010_remove_registrationlink_role_remove_membership_role_and_more';
```

Exit the PostgreSQL prompt:

```sql
\q
```

## Step 3: Run the Migrations

Now run the migrations:

```bash
python manage.py migrate
```

## Alternative Approach: Fake the Migration

If the above doesn't work, try faking the migration:

```bash
python manage.py migrate accounts 0010_remove_registrationlink_role_remove_membership_role_and_more --fake
```

Then run the remaining migrations:

```bash
python manage.py migrate
```

## Last Resort: Reset the Database

If all else fails, you might need to reset the database:

1. Backup your data
2. Drop the database
3. Create a new database
4. Run migrations from scratch
5. Restore your data

Here's how to reset the database:

```bash
# Connect to PostgreSQL
psql -U postgres

# Drop the database
DROP DATABASE postgres2;

# Create a new database
CREATE DATABASE postgres2;

# Exit PostgreSQL
\q

# Run migrations
python manage.py migrate

# Create a superuser
python manage.py createsuperuser
```
