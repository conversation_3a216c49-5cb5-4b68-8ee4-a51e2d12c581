"""
Script to collect static files for cPanel deployment.
Run this script after uploading your code to cPanel.
"""
import os
import sys
import subprocess
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set the Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')

print("Collecting static files for cPanel deployment...")
try:
    # Run the collectstatic command
    subprocess.run(
        [sys.executable, "manage.py", "collectstatic", "--noinput"],
        check=True
    )
    print("Static files collected successfully!")
    
    # Create necessary directories
    os.makedirs('logs', exist_ok=True)
    os.makedirs('media/tinymce_uploads', exist_ok=True)
    
    print("Created logs and media directories.")
    print("\nNext steps:")
    print("1. Make sure your .htaccess file is properly configured")
    print("2. Restart your application in cPanel")
    print("3. Check file permissions (755 for directories, 644 for files)")
    
except subprocess.CalledProcessError as e:
    print(f"Error collecting static files: {e}")
    sys.exit(1)
except Exception as e:
    print(f"Unexpected error: {e}")
    sys.exit(1)
