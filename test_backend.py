"""
Backend test script to check if the application's backend is working correctly.
"""

import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.test import Client
from django.urls import reverse
from django.contrib.auth.models import User
from accounts.models import Company
from assistants.models import Assistant
from directory.models import CompanyListing, DirectoryRating

def test_user_creation():
    """Test user creation functionality."""
    print("Testing user creation...")

    # Count users before
    user_count_before = User.objects.count()
    print(f"User count before: {user_count_before}")

    # Create a test user
    test_username = f"testuser_{user_count_before}"
    user = User.objects.create_user(
        username=test_username,
        email=f"{test_username}@example.com",
        password="testpassword123"
    )

    # Count users after
    user_count_after = User.objects.count()
    print(f"User count after: {user_count_after}")

    # Verify user was created
    assert user_count_after == user_count_before + 1, "User count should increase by 1"
    assert User.objects.filter(username=test_username).exists(), "User should exist in database"

    print("User creation test passed!")
    return user

def test_company_creation(user):
    """Test company creation functionality."""
    print("Testing company creation...")

    # Count companies before
    company_count_before = Company.objects.count()
    print(f"Company count before: {company_count_before}")

    # Create a test company
    test_company_name = f"Test Company {company_count_before}"
    company = Company.objects.create(
        name=test_company_name,
        owner=user
    )

    # Count companies after
    company_count_after = Company.objects.count()
    print(f"Company count after: {company_count_after}")

    # Verify company was created
    assert company_count_after == company_count_before + 1, "Company count should increase by 1"
    assert Company.objects.filter(name=test_company_name).exists(), "Company should exist in database"

    print("Company creation test passed!")
    return company

def test_assistant_creation(company):
    """Test assistant creation functionality."""
    print("Testing assistant creation...")

    # Count assistants before
    assistant_count_before = Assistant.objects.count()
    print(f"Assistant count before: {assistant_count_before}")

    # Create a test assistant
    test_assistant_name = f"Test Assistant {assistant_count_before}"
    assistant = Assistant.objects.create(
        name=test_assistant_name,
        company=company,
        assistant_type=Assistant.TYPE_GENERAL,
        is_active=True,
        is_public=True
    )

    # Count assistants after
    assistant_count_after = Assistant.objects.count()
    print(f"Assistant count after: {assistant_count_after}")

    # Verify assistant was created
    assert assistant_count_after == assistant_count_before + 1, "Assistant count should increase by 1"
    assert Assistant.objects.filter(name=test_assistant_name).exists(), "Assistant should exist in database"

    print("Assistant creation test passed!")
    return assistant

def test_authentication():
    """Test authentication functionality."""
    print("Testing authentication...")

    # Create a test user
    username = "auth_test_user"
    password = "auth_test_pass"

    # Delete user if it already exists
    User.objects.filter(username=username).delete()

    # Create the user
    User.objects.create_user(username=username, password=password)

    # Test login
    client = Client()
    login_successful = client.login(username=username, password=password)

    # Verify login
    assert login_successful, "Login should be successful"

    # Test accessing a protected page
    response = client.get(reverse('accounts:dashboard'))
    # Dashboard might redirect to company_create if user has no company
    assert response.status_code in [200, 302], "Should be able to access dashboard after login (with possible redirect)"

    if response.status_code == 302:
        print(f"Dashboard redirected to: {response.url}")
        # Follow the redirect
        response = client.get(response.url)
        assert response.status_code == 200, "Should be able to follow dashboard redirect"

    print("Authentication test passed!")
    return True

def run_all_backend_tests():
    """Run all backend tests in sequence."""
    print("Running all backend tests...")

    user = test_user_creation()
    company = test_company_creation(user)
    assistant = test_assistant_creation(company)
    auth_result = test_authentication()

    # Return True only if all tests passed
    return user and company and assistant and auth_result

if __name__ == "__main__":
    run_all_backend_tests()
