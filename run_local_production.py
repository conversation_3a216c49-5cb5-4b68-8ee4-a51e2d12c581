"""
<PERSON><PERSON>t to run Django in production mode locally without SSL.
This script uses a dedicated settings file for local production testing.
"""
import os
import sys
import subprocess

print("Starting Django server in local production mode with SSL security disabled...")
try:
    # Run the Django development server with our custom settings
    subprocess.run(
        [sys.executable, "manage.py", "runserver",
         "--settings=company_assistant.local_production_settings",
         "--insecure"],
        check=True
    )
except subprocess.CalledProcessError as e:
    print(f"Error running server: {e}")
    sys.exit(1)
except KeyboardInterrupt:
    print("\nServer stopped by user.")
    sys.exit(0)
