import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection
from django.conf import settings

# Get database settings
db_settings = settings.DATABASES['default']

# Connect directly to PostgreSQL
conn = psycopg2.connect(
    dbname=db_settings['NAME'],
    user=db_settings['USER'],
    password=db_settings['PASSWORD'],
    host=db_settings['HOST'],
    port=db_settings['PORT']
)
conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
cursor = conn.cursor()

# Add missing columns to accounts_company table
print("Adding missing columns to accounts_company table...")

# Check if the requested_tier column exists
cursor.execute("""
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'accounts_company' AND column_name = 'requested_tier';
""")
if not cursor.fetchone():
    print("Adding requested_tier column...")
    cursor.execute("""
    ALTER TABLE accounts_company 
    ADD COLUMN requested_tier varchar(10) NULL;
    """)
    print("requested_tier column added successfully!")
else:
    print("requested_tier column already exists.")

# Check if the tier_change_pending column exists
cursor.execute("""
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'accounts_company' AND column_name = 'tier_change_pending';
""")
if not cursor.fetchone():
    print("Adding tier_change_pending column...")
    cursor.execute("""
    ALTER TABLE accounts_company 
    ADD COLUMN tier_change_pending boolean NOT NULL DEFAULT false;
    """)
    print("tier_change_pending column added successfully!")
else:
    print("tier_change_pending column already exists.")

# Check if the tier_expiry_date column exists
cursor.execute("""
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'accounts_company' AND column_name = 'tier_expiry_date';
""")
if not cursor.fetchone():
    print("Adding tier_expiry_date column...")
    cursor.execute("""
    ALTER TABLE accounts_company 
    ADD COLUMN tier_expiry_date timestamp with time zone NULL;
    """)
    print("tier_expiry_date column added successfully!")
else:
    print("tier_expiry_date column already exists.")

# Check if the requested_tier_duration column exists
cursor.execute("""
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'accounts_company' AND column_name = 'requested_tier_duration';
""")
if not cursor.fetchone():
    print("Adding requested_tier_duration column...")
    cursor.execute("""
    ALTER TABLE accounts_company 
    ADD COLUMN requested_tier_duration varchar(10) NULL;
    """)
    print("requested_tier_duration column added successfully!")
else:
    print("requested_tier_duration column already exists.")

# Check if the featured_expiry_date column exists
cursor.execute("""
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'accounts_company' AND column_name = 'featured_expiry_date';
""")
if not cursor.fetchone():
    print("Adding featured_expiry_date column...")
    cursor.execute("""
    ALTER TABLE accounts_company 
    ADD COLUMN featured_expiry_date timestamp with time zone NULL;
    """)
    print("featured_expiry_date column added successfully!")
else:
    print("featured_expiry_date column already exists.")

# Check if the featured_request_pending column exists
cursor.execute("""
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'accounts_company' AND column_name = 'featured_request_pending';
""")
if not cursor.fetchone():
    print("Adding featured_request_pending column...")
    cursor.execute("""
    ALTER TABLE accounts_company 
    ADD COLUMN featured_request_pending boolean NOT NULL DEFAULT false;
    """)
    print("featured_request_pending column added successfully!")
else:
    print("featured_request_pending column already exists.")

# Check if the requested_featured_duration column exists
cursor.execute("""
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'accounts_company' AND column_name = 'requested_featured_duration';
""")
if not cursor.fetchone():
    print("Adding requested_featured_duration column...")
    cursor.execute("""
    ALTER TABLE accounts_company 
    ADD COLUMN requested_featured_duration varchar(10) NULL;
    """)
    print("requested_featured_duration column added successfully!")
else:
    print("requested_featured_duration column already exists.")

# Close the connection
cursor.close()
conn.close()

print("Company table updated successfully!")
