{% autoescape off %}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            text-align: center;
            padding: 40px 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header .icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 40px 30px;
        }
        .logo {
            max-width: 150px;
            margin-bottom: 20px;
        }
        .button {
            display: inline-block;
            padding: 16px 32px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white !important;
            text-decoration: none;
            border-radius: 8px;
            margin: 24px 0;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
            transition: all 0.3s ease;
        }
        .button:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
        }
        .button-container {
            text-align: center;
            margin: 32px 0;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 30px;
            border-top: 1px solid #e9ecef;
            font-size: 0.9em;
            color: #6c757d;
        }
        .important {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border: 1px solid #bbdefb;
            padding: 20px;
            border-radius: 8px;
            margin: 24px 0;
        }
        .warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffeaa7;
            color: #856404;
            font-weight: 600;
            padding: 16px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .security-info {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 24px 0;
            border-radius: 0 8px 8px 0;
        }
        .link-fallback {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            margin: 20px 0;
            word-break: break-all;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #495057;
        }
        .countdown {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            text-align: center;
            font-weight: 600;
            margin: 20px 0;
        }
        .help-section {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin: 24px 0;
        }
        .help-section h4 {
            color: #495057;
            margin-bottom: 12px;
        }
        .help-section ul {
            margin: 0;
            padding-left: 20px;
        }
        .help-section li {
            margin-bottom: 8px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="icon">🔐</div>
            <h1>Sign-in Verification Required</h1>
        </div>

        <div class="content">
            <p style="font-size: 18px; margin-bottom: 24px;">
                <strong>Hello {{ user.get_full_name|default:user.username }},</strong>
            </p>

            <p style="font-size: 16px; margin-bottom: 24px;">
                We detected a sign-in attempt to your 24seven account. For security reasons, we need to verify that it's really you.
            </p>

            <div class="security-info">
                <h4 style="margin-top: 0; color: #007bff;">🔍 Sign-in Details</h4>
                <ul style="margin-bottom: 0;">
                    <li><strong>Username:</strong> {{ user.username }}</li>
                    <li><strong>Time:</strong> {{ now|date:"F j, Y, g:i a" }}</li>
                    <li><strong>IP Address:</strong> {{ request.META.REMOTE_ADDR|default:"Unknown" }}</li>
                </ul>
            </div>

            <p style="font-size: 16px; text-align: center; margin: 32px 0 16px 0;">
                If this was you, please click the button below to approve this sign-in:
            </p>

            <div class="button-container">
                <a href="{{ approval_url }}" class="button">
                    ✅ Approve Sign-in
                </a>
            </div>

            <div class="countdown">
                ⏰ This link will expire in {{ expiry_hours }} hours
            </div>

            <div class="link-fallback">
                <p style="margin: 0 0 8px 0; font-weight: 600;">Button not working? Copy this link:</p>
                {{ approval_url }}
            </div>

            <div class="warning">
                ⚠️ <strong>Security Notice:</strong> This link can only be used once and will expire automatically.
            </div>

            <div class="help-section">
                <h4>🛡️ Security Information</h4>
                <ul>
                    <li>If you did not attempt to sign in, please ignore this email</li>
                    <li>Consider changing your password if you suspect unauthorized access</li>
                    <li>This verification helps protect your account from unauthorized access</li>
                    <li>The link is unique and can only be used once</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <div style="text-align: center; margin-bottom: 20px;">
                <h3 style="color: #007bff; margin: 0;">24seven</h3>
                <p style="margin: 8px 0; color: #6c757d;">Secure AI-Powered Collaboration</p>
            </div>

            <p style="text-align: center; margin-bottom: 16px;">
                <strong>Thanks,<br>The 24seven Team</strong>
            </p>

            <div style="text-align: center; border-top: 1px solid #e9ecef; padding-top: 20px;">
                <p style="font-size: 0.85em; color: #6c757d; margin: 0;">
                    This is an automated security message, please do not reply to this email.
                </p>
                <p style="font-size: 0.85em; margin: 8px 0 0 0;">
                    Need help? Visit our <a href="{{ site_url }}/contact/" style="color: #007bff;">contact page</a> or
                    <a href="{{ site_url }}/support/" style="color: #007bff;">support center</a>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
{% endautoescape %}
