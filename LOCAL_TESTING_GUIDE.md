# Local Testing Guide for Login Links

This guide provides instructions for testing the login link functionality locally before deploying to cPanel.

## Setup

1. **Run the Local Test Server**:
   ```bash
   python run_local_test.py
   ```
   This script:
   - Sets up the environment variables to simulate a cPanel environment
   - Creates necessary directories with proper permissions
   - Runs the Django development server

2. **Check the Site Domain Configuration**:
   ```bash
   python check_site_domain.py
   ```
   This script:
   - Prints information about the current site domain
   - Checks if the site domain is in ALLOWED_HOSTS and CSRF_TRUSTED_ORIGINS
   - Provides recommendations for fixing any issues

3. **Update the Site Domain** (if needed):
   ```bash
   python update_site_domain.py localhost:8000
   ```
   This script:
   - Updates the domain of the current site to the specified value
   - If no value is specified, defaults to 'localhost:8000'

## Testing Login Links

1. **Generate a Test Login Link**:
   ```bash
   python generate_test_login_link.py
   ```
   This script:
   - Asks for a username or email
   - Generates a login token for the specified user
   - Prints the login URL

2. **Test the Login Link**:
   - Copy the login URL from the output of the previous step
   - Paste it into your browser
   - You should be logged in automatically

3. **Verify the Login**:
   - Check that you are redirected to the home page
   - Check that you are logged in (your username should appear in the navigation)
   - Check the server logs for any errors or warnings

## Troubleshooting

### Login Link Doesn't Work

If the login link doesn't work, check the following:

1. **Site Domain Configuration**:
   - Run `python check_site_domain.py` to check the site domain configuration
   - Make sure the site domain is set to 'localhost:8000' for local testing
   - If not, run `python update_site_domain.py localhost:8000` to update it

2. **Directory Permissions**:
   - Make sure the 'session', 'cache', and 'token_storage' directories have the correct permissions
   - You can check this with `ls -la` on Linux/Mac or `dir` on Windows
   - If needed, set permissions with `chmod 777 session cache token_storage` on Linux/Mac

3. **Server Logs**:
   - Check the server logs for any errors or warnings
   - Look for messages from the 'accounts.auth_views' and 'accounts.auth_utils' modules

4. **Token Storage**:
   - Check if the token is being stored correctly
   - Look in the 'token_storage' directory for a file with the token as the filename
   - Check if the token data in the file is correct

### Session Issues

If you are logged in but lose the session after refreshing the page, check the following:

1. **Session Engine Configuration**:
   - Make sure the session engine is set to 'django.contrib.sessions.backends.file'
   - Check the 'SESSION_FILE_PATH' setting in settings.py

2. **Session Directory Permissions**:
   - Make sure the 'session' directory has the correct permissions
   - You can check this with `ls -la` on Linux/Mac or `dir` on Windows
   - If needed, set permissions with `chmod 777 session` on Linux/Mac

3. **Session Files**:
   - Check if session files are being created in the 'session' directory
   - Look for files with names like 'sessionid_...'

## Next Steps

Once you have verified that the login links work correctly in your local environment, you can deploy the changes to cPanel:

1. Upload the updated files to your cPanel environment:
   - `company_assistant/settings.py`
   - `accounts/auth_utils.py`
   - `accounts/auth_views.py`
   - `passenger_wsgi.py`

2. Create the required directories in your cPanel environment:
   ```bash
   mkdir -p logs cache token_storage session
   chmod 777 logs cache token_storage session
   ```

3. Restart the application in cPanel

4. Test the login links in the production environment

For more detailed instructions, see the `LOGIN_LINK_FIX.md` file.
