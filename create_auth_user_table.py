import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

# SQL to create the auth_user table
sql = """
CREATE TABLE IF NOT EXISTS "auth_user" (
    "id" serial NOT NULL PRIMARY KEY,
    "password" varchar(128) NOT NULL,
    "last_login" timestamp with time zone NULL,
    "is_superuser" boolean NOT NULL,
    "username" varchar(150) NOT NULL UNIQUE,
    "first_name" varchar(150) NOT NULL,
    "last_name" varchar(150) NOT NULL,
    "email" varchar(254) NOT NULL,
    "is_staff" boolean NOT NULL,
    "is_active" boolean NOT NULL,
    "date_joined" timestamp with time zone NOT NULL
);
"""

# Execute the SQL
with connection.cursor() as cursor:
    cursor.execute(sql)

# Create a superuser
sql_insert = """
INSERT INTO auth_user (
    id, password, is_superuser, username, first_name, last_name, 
    email, is_staff, is_active, date_joined
)
VALUES (
    1, 
    'pbkdf2_sha256$600000$5UAfuhrFSuLTHXvLIzNLWA$+vqA0NA+RE+DYzlJ5TJm7qjxw2tPkn/iqxdH4UQ3Mxw=', 
    true, 
    'admin', 
    '', 
    '', 
    '<EMAIL>', 
    true, 
    true, 
    NOW()
)
ON CONFLICT (id) DO NOTHING;
"""

with connection.cursor() as cursor:
    cursor.execute(sql_insert)

print("Auth user table created successfully!")
