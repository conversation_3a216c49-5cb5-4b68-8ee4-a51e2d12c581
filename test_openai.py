# test_openai.py
import os
import openai
import sys

# Add venv site-packages to path temporarily for this script
# Adjust path if your venv or Python version differs
venv_path = os.path.join(os.path.dirname(__file__), 'venv', 'Lib', 'site-packages')
if venv_path not in sys.path:
    sys.path.insert(0, venv_path)

# Use a dummy API key for initialization test
DUMMY_API_KEY = "sk-dummykeyforinitializationtest"

print(f"Python executable: {sys.executable}")
print(f"Attempting to initialize OpenAI client with key: {DUMMY_API_KEY[:5]}...")
try:
    # Use the simplest initialization
    openai_client = openai.OpenAI(api_key=DUMMY_API_KEY)
    print("OpenAI client initialized successfully!")
    print(f"Client object: {openai_client}")
except TypeError as te:
    print(f"\nCaught TypeError during initialization: {te}")
    print("\n--- Traceback ---")
    import traceback
    traceback.print_exc()
    print("--- End Traceback ---")
    print("\nThis indicates the 'proxies' issue is likely related to httpx/environment.")
except Exception as e:
    print(f"\nCaught unexpected error during initialization: {e}")
    print("\n--- Traceback ---")
    import traceback
    traceback.print_exc()
    print("--- End Traceback ---")
