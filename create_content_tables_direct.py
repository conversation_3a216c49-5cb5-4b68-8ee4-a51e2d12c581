import os
import django
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

print("Starting script to create content tables...")

# SQL to create the content_content table
content_sql = """
CREATE TABLE IF NOT EXISTS "content_content" (
    "id" serial NOT NULL PRIMARY KEY,
    "title" varchar(200) NOT NULL,
    "slug" varchar(200) NOT NULL,
    "content_type" varchar(20) NOT NULL,
    "body" text NOT NULL,
    "summary" text NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "updated_at" timestamp with time zone NOT NULL,
    "is_public" boolean NOT NULL,
    "is_archived" boolean NOT NULL,
    "assistant_id" integer NULL,
    "author_id" integer NULL,
    "company_id" integer NOT NULL
);
"""

# SQL to create the content_contentimage table
contentimage_sql = """
CREATE TABLE IF NOT EXISTS "content_contentimage" (
    "id" serial NOT NULL PRIMARY KEY,
    "image" varchar(100) NOT NULL,
    "alt_text" varchar(200) NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "content_id" integer NOT NULL
);
"""

# SQL to create the content_contentversion table
contentversion_sql = """
CREATE TABLE IF NOT EXISTS "content_contentversion" (
    "id" serial NOT NULL PRIMARY KEY,
    "body" text NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "change_summary" varchar(200) NOT NULL,
    "content_id" integer NOT NULL,
    "edited_by_id" integer NULL
);
"""

# SQL to create indexes
indexes_sql = """
CREATE INDEX IF NOT EXISTS "content_content_company_id_content_type_idx" ON "content_content" ("company_id", "content_type");
CREATE INDEX IF NOT EXISTS "content_content_company_id_is_public_idx" ON "content_content" ("company_id", "is_public");
CREATE INDEX IF NOT EXISTS "content_content_company_id_is_archived_idx" ON "content_content" ("company_id", "is_archived");
CREATE INDEX IF NOT EXISTS "content_contentimage_content_id_idx" ON "content_contentimage" ("content_id");
CREATE INDEX IF NOT EXISTS "content_contentversion_content_id_idx" ON "content_contentversion" ("content_id");
"""

# Execute the SQL
try:
    with connection.cursor() as cursor:
        print("Creating content_content table...")
        cursor.execute(content_sql)
        print("Content table created successfully!")
        
        print("Creating content_contentimage table...")
        cursor.execute(contentimage_sql)
        print("ContentImage table created successfully!")
        
        print("Creating content_contentversion table...")
        cursor.execute(contentversion_sql)
        print("ContentVersion table created successfully!")
        
        print("Creating indexes...")
        cursor.execute(indexes_sql)
        print("Indexes created successfully!")
    
    print("All content tables created successfully!")
except Exception as e:
    print(f"Error creating tables: {e}")
    sys.exit(1)
