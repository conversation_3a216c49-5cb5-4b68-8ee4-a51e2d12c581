# Reduced Redirects Deployment Guide

This guide provides instructions for deploying your Django application to cPanel with reduced redirects.

## Changes Made

1. **Completely Removed HTTP Middleware and Related Files**
   - The HTTP middleware file and all references to it have been removed from the codebase
   - HTTP-related templates and views have been removed
   - This eliminates unnecessary redirects when accessing the site

2. **Disabled HTTPS-related Security Settings**
   - `SECURE_SSL_REDIRECT` set to False
   - `SESSION_COOKIE_SECURE` set to False
   - `CSRF_COOKIE_SECURE` set to False
   - `SECURE_HSTS_SECONDS` set to 0
   - These changes prevent Django from forcing HTTPS connections

3. **Updated ALLOWED_HOSTS**
   - Added all possible domain variations: `24seven.info`, `www.24seven.info`, `pimms.hostgator.net`, `127.0.0.1`
   - This prevents redirects due to domain mismatches

4. **Updated passenger_wsgi.py**
   - Configured to use the correct Python interpreter path
   - Set to use production settings

5. **Updated Database Configuration**
   - Changed from MySQL to PostgreSQL
   - Updated requirements.production.txt to include psycopg2-binary

6. **Updated .env.production Template**
   - Added correct security settings
   - Updated database configuration

## Deployment Steps

1. **Prepare Your Local Project**
   ```bash
   # Create a deployment package
   zip -r deployment.zip . -x "*.git*" "*.pyc" "__pycache__/*" "*.sqlite3" "venv/*" ".env"
   ```

2. **Upload to cPanel**
   - Upload the deployment.zip file to your cPanel account
   - Extract the files in the appropriate directory

3. **Set Up Virtual Environment**
   ```bash
   # Create a Python application in cPanel
   # This will create a virtual environment automatically

   # Install dependencies
   pip install -r requirements.production.txt
   ```

4. **Configure Environment Variables**
   - Copy .env.production to .env
   - Update the values with your actual database credentials and settings

5. **Set File Permissions and Create Required Directories**
   ```bash
   # Set proper permissions for your application files
   find . -type d -exec chmod 755 {} \;
   find . -type f -exec chmod 644 {} \;
   chmod +x manage.py

   # Create necessary directories for media uploads
   mkdir -p media/tinymce_uploads

   # Make sure the web server can write to necessary directories
   chmod -R 755 media
   chmod -R 755 static
   ```

6. **Run Migrations and Collect Static Files**
   ```bash
   # Run database migrations
   python manage.py migrate

   # Collect static files (including TinyMCE)
   python manage.py collectstatic --noinput

   # Verify TinyMCE files were collected
   ls -la static/tinymce/

   # Create logs directory for TinyMCE debug logs
   mkdir -p logs
   touch logs/tinymce_debug.log
   chmod 755 logs/tinymce_debug.log
   ```

7. **Restart the Application**
   - In cPanel, go to "Setup Python App"
   - Restart your application
   - Alternatively, touch the passenger_wsgi.py file:
     ```bash
     touch passenger_wsgi.py
     ```

## Troubleshooting

### 500 Internal Server Error

1. **Check Error Logs**
   - Look in ~/logs/error_log for Apache errors
   - Check your application logs in the logs directory

2. **Verify Database Connection**
   - Make sure your PostgreSQL database is properly configured
   - Ensure the database user has the correct permissions

3. **Check File Permissions**
   - Make sure passenger_wsgi.py is readable by the web server
   - Ensure the virtual environment path in passenger_wsgi.py is correct

4. **Test with a Simple WSGI Application**
   Create a simple test_wsgi.py file:
   ```python
   def application(environ, start_response):
       status = '200 OK'
       output = b'Hello World! Python WSGI is working.'
       response_headers = [('Content-type', 'text/plain'),
                          ('Content-Length', str(len(output)))]
       start_response(status, response_headers)
       return [output]
   ```

   Then temporarily modify passenger_wsgi.py:
   ```python
   from test_wsgi import application
   ```

5. **Check for Missing Dependencies**
   ```bash
   pip install -r requirements.production.txt
   ```

## Additional Notes

- The application is now configured to work without HTTPS redirects
- All HTTP middleware and related files have been completely removed
- This configuration minimizes redirects but also disables some security features
- Consider enabling HTTPS in the future for better security

## TinyMCE Configuration

TinyMCE has been configured to work properly in the production environment:

1. **Static Files**
   - TinyMCE JavaScript files will be collected to the static directory during `collectstatic`
   - The `TINYMCE_JS_URL` and `TINYMCE_JS_ROOT` settings ensure proper paths
   - Make sure the TinyMCE static files are properly collected:
     ```bash
     # Verify TinyMCE files exist in static directory after collectstatic
     ls -la static/tinymce/
     ```

2. **Image Uploads**
   - The `media/tinymce_uploads` directory has been created for TinyMCE image uploads
   - The upload URL has been set to `/assistants/tinymce/upload/` to avoid redirects
   - File permissions have been set to allow the web server to write to this directory
   - Create the directory and set permissions:
     ```bash
     # Create TinyMCE uploads directory
     mkdir -p media/tinymce_uploads

     # Set proper permissions
     chmod 755 media/tinymce_uploads
     ```

3. **URL Configuration**
   - The production settings include the direct URL for TinyMCE image uploads
   - The URL `/assistants/tinymce/upload/` is configured to handle image uploads
   - This URL is set in the TinyMCE configuration to avoid redirects

4. **Logging**
   - Enhanced logging has been added for TinyMCE-related issues
   - Check the `tinymce_debug.log` file for detailed information:
     ```bash
     # View TinyMCE debug log
     tail -f tinymce_debug.log
     ```

5. **Troubleshooting TinyMCE Issues**
   - If TinyMCE editor doesn't load, check browser console for JavaScript errors
   - Verify that TinyMCE static files were properly collected during `collectstatic`
   - Check file permissions on the `media/tinymce_uploads` directory
   - Review server logs for any errors related to TinyMCE image uploads
   - Try clearing browser cache if TinyMCE doesn't load properly
   - Check network requests in browser developer tools for 404 errors

6. **Testing TinyMCE**
   - After deployment, test TinyMCE by editing content with rich text
   - Try uploading an image through the TinyMCE editor
   - Verify that uploaded images are properly displayed
   - Check that the image is saved to the `media/tinymce_uploads` directory
