/**
 * Navbar Toggle Button Dark Mode CSS
 * Improves the appearance of the navbar toggle button in dark mode
 */

/* Base styling for the navbar toggle button in dark mode */
[data-theme="dark"] .navbar-toggler {
  /* Background with subtle gradient */
  background: linear-gradient(145deg, #1a1a1a, #222222) !important;

  /* Border and shadow for depth */
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3),
              inset 0 1px 1px rgba(255, 255, 255, 0.05) !important;

  /* Rounded corners and padding */
  border-radius: 6px !important;
  padding: 0.5rem !important;

  /* Transition for hover effects */
  transition: all 0.2s ease-in-out !important;

  /* Add a subtle glass effect */
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

/* Hover state */
[data-theme="dark"] .navbar-toggler:hover {
  background: linear-gradient(145deg, #222222, #2a2a2a) !important;
  border-color: rgba(255, 255, 255, 0.15) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4),
              inset 0 1px 2px rgba(255, 255, 255, 0.1) !important;
  transform: translateY(-1px) !important;
}

/* Active/pressed state */
[data-theme="dark"] .navbar-toggler:active {
  background: linear-gradient(145deg, #181818, #202020) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3),
              inset 0 1px 1px rgba(0, 0, 0, 0.2) !important;
  transform: translateY(1px) !important;
}

/* Focus state - no outline but subtle glow */
[data-theme="dark"] .navbar-toggler:focus {
  box-shadow: 0 0 0 3px rgba(0, 102, 255, 0.25) !important;
  outline: none !important;
}

/* Custom hamburger icon styling */
[data-theme="dark"] .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(255, 255, 255, 0.85)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.5)) !important;
  transition: transform 0.3s ease !important;
}

/* Add a subtle rotation effect on hover */
[data-theme="dark"] .navbar-toggler:hover .navbar-toggler-icon {
  transform: rotate(5deg) !important;
}

/* Add a class for JavaScript enhancement */
[data-theme="dark"] .navbar-toggler.dark-mode-toggler {
  /* Additional styling can be applied via JavaScript */
  position: relative !important;
  z-index: 1060 !important; /* Ensure it's above other elements */
}

/* Add a subtle glow effect when expanded */
[data-theme="dark"] .navbar-toggler[aria-expanded="true"] {
  background: linear-gradient(145deg, #222222, #2d2d2d) !important;
  border-color: rgba(0, 102, 255, 0.3) !important;
  box-shadow: 0 0 15px rgba(0, 102, 255, 0.2),
              inset 0 1px 2px rgba(255, 255, 255, 0.1) !important;
}

/* Rotate icon when expanded */
[data-theme="dark"] .navbar-toggler[aria-expanded="true"] .navbar-toggler-icon {
  transform: rotate(90deg) !important;
}

/* Ensure the toggle button is visible on all screen sizes */
@media (max-width: 576px) {
  [data-theme="dark"] .navbar-toggler {
    padding: 0.4rem !important;
  }
}

/* Ensure the toggle button works well with the impersonation banner */
body.is-impersonating [data-theme="dark"] .navbar-toggler {
  margin-top: 2px !important;
}
