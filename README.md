# Company Assistant

A Django-based platform that helps companies leverage AI assistants for improved productivity and team collaboration.

## Features

- **Company Management**
  - Multi-company support
  - Team member management
  - Company profiles and settings
  - QR code generation for companies

- **AI Assistants**
  - Customizable AI assistants per company
  - OpenAI GPT-4 integration
  - Context-aware conversations
  - Knowledge base integration

- **Content Management**
  - Document upload and processing
  - Content categorization
  - Full-text search
  - Version control

- **Team Collaboration**
  - Team member invitations
  - Role-based access control
  - Activity tracking
  - Shared resources

## Prerequisites

- Python 3.9+
- pip
- virtualenv (recommended)
- OpenAI API key

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/company-assistant.git
cd company-assistant
```

2. Create and activate a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Create a `.env` file in the project root:
```bash
SECRET_KEY=your-secret-key
DEBUG=True
OPENAI_API_KEY=your-openai-api-key
```

5. Run migrations:
```bash
python manage.py migrate
```

6. Create a superuser:
```bash
python manage.py createsuperuser
```

7. Run the development server:
```bash
python manage.py runserver
```

## Development

### Project Structure

```
company_assistant/
├── accounts/            # User and company management
├── assistants/          # AI assistant functionality
├── content/             # Content management
├── directory/           # Company directory
├── static/             # Static files
├── templates/          # HTML templates
└── company_assistant/  # Project settings
```

### Running Tests

```bash
# Run all tests
python manage.py test

# Run tests with coverage
coverage run manage.py test
coverage report
```

### Code Style

This project follows PEP 8 guidelines. Before committing, ensure your code passes:

```bash
# Install development dependencies
pip install black flake8

# Format code
black .

# Check style
flake8
```

## Deployment

### Production Settings

1. Update `company_assistant/settings.py`:
   - Set `DEBUG = False`
   - Configure `ALLOWED_HOSTS`
   - Set up proper email backend
   - Enable security settings

2. Configure production database:
   - Uncomment PostgreSQL in requirements.txt
   - Update database settings

3. Set up static and media files:
   - Configure AWS S3 or similar for file storage
   - Run `python manage.py collectstatic`

### Using Docker

A Dockerfile and docker-compose.yml are provided for containerized deployment:

```bash
# Build and run with Docker Compose
docker-compose up --build
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- OpenAI for GPT-4 API
- Django community for the excellent web framework
- All contributors and users of this project

## Support

For support, email <EMAIL> or open an issue in the GitHub repository.
