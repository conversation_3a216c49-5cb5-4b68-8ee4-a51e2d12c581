from django.urls import path
from . import views

app_name = 'superadmin'

urlpatterns = [
    path('', views.DashboardView.as_view(), name='dashboard'),
    path('api/dashboard/', views.DashboardAPIView.as_view(), name='dashboard_api'),
    # Company Management
    path('companies/', views.CompanyListView.as_view(), name='company_list'),
    path('companies/<int:pk>/toggle-active/', views.CompanyActivateToggleView.as_view(), name='company_activate_toggle'),
    path('companies/<int:pk>/approve-tier/', views.CompanyApproveTierView.as_view(), name='company_approve_tier'),
    path('companies/<int:pk>/reject-tier/', views.CompanyRejectTierView.as_view(), name='company_reject_tier'),
    path('companies/<int:pk>/toggle-featured/', views.CompanyFeaturedToggleView.as_view(), name='company_featured_toggle'),
    path('companies/<int:pk>/approve-featured/', views.CompanyApproveFeaturedView.as_view(), name='company_approve_featured'), # New
    path('companies/<int:pk>/reject-featured/', views.CompanyRejectFeaturedView.as_view(), name='company_reject_featured'), # New
    path('companies/<int:pk>/update-expiry/', views.CompanyUpdateExpiryView.as_view(), name='company_update_expiry'), # New URL for company expiry update
    path('companies/<int:pk>/set-standard-tier/', views.CompanySetStandardTierView.as_view(), name='company_set_standard_tier'), # New URL
    path('users/<int:user_pk>/impersonate/', views.CompanyImpersonateView.as_view(), name='company_impersonate'), # Impersonate URL
    # Assistant Management
    path('assistants/', views.AssistantListView.as_view(), name='assistant_list'),
    path('assistants/<int:pk>/toggle-active/', views.AssistantActivateToggleView.as_view(), name='assistant_activate_toggle'),
    path('assistants/<int:pk>/approve-tier/', views.AssistantApproveTierView.as_view(), name='assistant_approve_tier'),
    path('assistants/<int:pk>/reject-tier/', views.AssistantRejectTierView.as_view(), name='assistant_reject_tier'),
    path('assistants/<int:pk>/toggle-featured/', views.AssistantFeaturedToggleView.as_view(), name='assistant_featured_toggle'),
    path('assistants/<int:pk>/approve-featured/', views.AssistantApproveFeaturedView.as_view(), name='assistant_approve_featured'), # New
    path('assistants/<int:pk>/reject-featured/', views.AssistantRejectFeaturedView.as_view(), name='assistant_reject_featured'), # New
    path('assistants/<int:pk>/set-standard-tier/', views.AssistantSetStandardTierView.as_view(), name='assistant_set_standard_tier'), # New URL
    # URL for inline expiry date updates
    path('assistants/<int:pk>/update-expiry/', views.AssistantUpdateExpiryView.as_view(), name='assistant_update_expiry'),
    # Community Assistant Management
    path('community-assistants/', views.CommunityAssistantListView.as_view(), name='community_assistant_list'),
]
