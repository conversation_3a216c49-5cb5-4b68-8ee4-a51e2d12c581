import os
import django
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

print("Starting script to create auth_user_groups table...")

# SQL to create the auth_user_groups table
auth_user_groups_sql = """
CREATE TABLE IF NOT EXISTS "auth_user_groups" (
    "id" serial NOT NULL PRIMARY KEY,
    "user_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
    "group_id" integer NOT NULL REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT "auth_user_groups_user_id_group_id_94350c0c_uniq" UNIQUE ("user_id", "group_id")
);
"""

# SQL to create the auth_user_user_permissions table
auth_user_user_permissions_sql = """
CREATE TABLE IF NOT EXISTS "auth_user_user_permissions" (
    "id" serial NOT NULL PRIMARY KEY,
    "user_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
    "permission_id" integer NOT NULL REFERENCES "auth_permission" ("id") DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT "auth_user_user_permissions_user_id_permission_id_14a6b632_uniq" UNIQUE ("user_id", "permission_id")
);
"""

# SQL to create indexes
indexes_sql = """
CREATE INDEX IF NOT EXISTS "auth_user_groups_user_id_6a12ed8b" ON "auth_user_groups" ("user_id");
CREATE INDEX IF NOT EXISTS "auth_user_groups_group_id_97559544" ON "auth_user_groups" ("group_id");
CREATE INDEX IF NOT EXISTS "auth_user_user_permissions_user_id_a95ead1b" ON "auth_user_user_permissions" ("user_id");
CREATE INDEX IF NOT EXISTS "auth_user_user_permissions_permission_id_1fbb5f2c" ON "auth_user_user_permissions" ("permission_id");
"""

# Execute the SQL
try:
    with connection.cursor() as cursor:
        print("Creating auth_user_groups table...")
        cursor.execute(auth_user_groups_sql)
        print("auth_user_groups table created successfully!")
        
        print("Creating auth_user_user_permissions table...")
        cursor.execute(auth_user_user_permissions_sql)
        print("auth_user_user_permissions table created successfully!")
        
        print("Creating indexes...")
        cursor.execute(indexes_sql)
        print("Indexes created successfully!")
    
    print("All tables created successfully!")
except Exception as e:
    print(f"Error creating tables: {e}")
    sys.exit(1)
