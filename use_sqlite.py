"""
<PERSON><PERSON><PERSON> to switch back to SQLite database for development.
This is useful when you're having issues with PostgreSQL.
"""
import os
import sys

def update_local_settings():
    """Update local_settings.py to use SQLite."""
    settings_path = os.path.join("company_assistant", "local_settings.py")
    
    # Check if the file exists
    if not os.path.exists(settings_path):
        print(f"Error: {settings_path} not found.")
        return False
    
    # Read the current settings file
    with open(settings_path, 'r') as f:
        content = f.read()
    
    # Check if PostgreSQL configuration exists
    if "ENGINE': 'django.db.backends.postgresql" not in content:
        print("PostgreSQL configuration not found in local_settings.py.")
        return False
    
    # Create SQLite configuration
    sqlite_config = """# Database settings for SQLite (fallback)
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': 'db.sqlite3',
    }
}
"""
    
    # Replace PostgreSQL configuration with SQLite
    import re
    new_content = re.sub(
        r"# Database settings for PostgreSQL.*?}",
        sqlite_config,
        content,
        flags=re.DOTALL
    )
    
    # Write the updated settings file
    with open(settings_path, 'w') as f:
        f.write(new_content)
    
    print(f"Updated {settings_path} to use SQLite.")
    return True

def main():
    """Main function to switch to SQLite."""
    print("Switch to SQLite Script")
    print("======================")
    
    confirm = input("This will switch your database configuration to SQLite. Continue? (y/n): ").strip().lower()
    if confirm != 'y':
        print("Operation cancelled.")
        return
    
    # Update local_settings.py
    if update_local_settings():
        print("\nSwitched to SQLite successfully!")
        print("\nNext steps:")
        print("1. Run migrations: python manage.py migrate")
        print("2. Create a superuser: python manage.py createsuperuser")
        print("3. Run the development server: python manage.py runserver")
    else:
        print("\nFailed to switch to SQLite.")

if __name__ == "__main__":
    main()
