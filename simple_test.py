import os
import sys
import django

print("Python version:", sys.version)
print("Django version:", django.get_version())

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

print("Django setup complete")

# Try to import models
try:
    from django.contrib.auth.models import User
    print("Successfully imported User model")
    
    # Count users
    user_count = User.objects.count()
    print(f"Found {user_count} users")
except Exception as e:
    print(f"Error importing or using User model: {e}")
    import traceback
    traceback.print_exc()

print("Test complete")
