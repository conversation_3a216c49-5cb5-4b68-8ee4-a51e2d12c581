/**
 * Tablet Mobile Contact Fix CSS
 * Makes tablet mode use mobile styling for contact information
 * without affecting desktop mode
 */

/* Apply mobile contact styling to tablet mode (between 769px and 991.98px) */
@media (min-width: 769px) and (max-width: 991.98px) {
  /* Contact info styling for better readability */
  .directory-card .contact-info .contact-text,
  .contact-info[data-tablet-optimized="true"] .contact-text {
    color: #ffffff !important; /* Brighter white for better contrast */
    font-weight: 500 !important; /* Slightly bolder for better visibility */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important; /* Add shadow for better contrast */
  }

  /* Enhance contact info icons for better visibility */
  .directory-card .contact-info li i,
  .contact-info[data-tablet-optimized="true"] li i {
    color: #5a9cff !important; /* Brighter blue for icons */
    text-shadow: 0 0 5px rgba(90, 156, 255, 0.3) !important; /* Subtle glow effect */
    font-size: 1rem !important; /* Slightly larger icons */
    margin-right: 0.5rem !important; /* Consistent spacing */
  }

  /* Specific icon colors for different contact types */
  .directory-card .contact-info li i.bi-geo-alt,
  .contact-info[data-tablet-optimized="true"] li i.bi-geo-alt {
    color: #ff6b6b !important; /* Bright red for location */
  }

  .directory-card .contact-info li i.bi-telephone,
  .contact-info[data-tablet-optimized="true"] li i.bi-telephone {
    color: #4ade80 !important; /* Bright green for phone */
  }

  .directory-card .contact-info li i.bi-envelope,
  .contact-info[data-tablet-optimized="true"] li i.bi-envelope {
    color: #c084fc !important; /* Bright purple for email */
  }

  .directory-card .contact-info li i.bi-link-45deg,
  .contact-info[data-tablet-optimized="true"] li i.bi-link-45deg {
    color: #67e8f9 !important; /* Bright cyan for website */
  }

  /* Adjust contact info column for better visibility */
  .company-directory-page .directory-card .col-md-2.d-flex.flex-column.justify-content-center {
    padding: 0 0.5rem !important;
  }

  /* Improve contact info list styling */
  .directory-card .contact-info,
  .contact-info[data-tablet-optimized="true"] {
    margin-bottom: 0 !important;
    padding: 0 !important;
  }

  .directory-card .contact-info li,
  .contact-info[data-tablet-optimized="true"] li {
    margin-bottom: 0.5rem !important;
    display: flex !important;
    align-items: flex-start !important;
  }

  /* Ensure email addresses don't overflow */
  .directory-card .contact-info .email-text,
  .contact-info[data-tablet-optimized="true"] .email-text {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 100% !important;
  }

  /* Ensure website URLs don't overflow */
  .directory-card .contact-info .website-url,
  .contact-info[data-tablet-optimized="true"] .website-url {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 100% !important;
  }

  /* Ensure address text wraps properly */
  .directory-card .contact-info li:first-child .contact-text,
  .contact-info[data-tablet-optimized="true"] li:first-child .contact-text {
    white-space: normal !important;
    word-break: break-word !important;
  }
}
