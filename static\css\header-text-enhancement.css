/**
 * Header Text Enhancement
 * Makes the header text pop more to match the button styling
 */

/* Company name styling in the assistant header */
.general-assistant-header h4 {
    font-weight: 700 !important; /* Bolder text */
    color: #0052cc !important; /* Vibrant blue color */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important; /* Subtle text shadow for depth */
    letter-spacing: 0.02em !important; /* Slightly increased letter spacing */
    font-size: 1.5rem !important; /* Slightly larger font size */
    margin-bottom: 0.25rem !important; /* Adjust spacing */
    transform: translateZ(0) !important; /* Force hardware acceleration */
    transition: all 0.2s ease !important; /* Smooth transition */
}

/* Assistant name styling in the assistant header */
.general-assistant-header h5 {
    font-weight: 600 !important; /* Bolder text */
    color: #333333 !important; /* Darker text for better contrast */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05) !important; /* Subtle text shadow for depth */
    letter-spacing: 0.01em !important; /* Slightly increased letter spacing */
    font-size: 1.2rem !important; /* Slightly larger font size */
    margin-bottom: 0 !important; /* Remove bottom margin */
    transform: translateZ(0) !important; /* Force hardware acceleration */
    transition: all 0.2s ease !important; /* Smooth transition */
}

/* Remove text-muted class effect from assistant name */
.general-assistant-header h5.text-muted {
    color: #333333 !important; /* Override muted color */
}

/* Hover effects for both headings */
.general-assistant-header h4:hover,
.general-assistant-header h5:hover {
    transform: translateY(-1px) translateZ(0) !important; /* Slight lift effect */
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15) !important; /* Enhanced shadow on hover */
}
