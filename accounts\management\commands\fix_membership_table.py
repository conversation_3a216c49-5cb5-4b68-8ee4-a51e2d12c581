from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    help = 'Checks and fixes the accounts_membership table structure'

    def handle(self, *args, **options):
        self.stdout.write('Checking accounts_membership table structure...')
        
        # Check if the date_joined column exists
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT column_name, data_type, character_maximum_length
                FROM information_schema.columns
                WHERE table_name = 'accounts_membership'
                ORDER BY ordinal_position;
            """)
            columns = cursor.fetchall()
            
            self.stdout.write('\nTable structure:')
            self.stdout.write('Column Name | Data Type | Max Length')
            self.stdout.write('-' * 50)
            for column in columns:
                self.stdout.write(f"{column[0]} | {column[1]} | {column[2]}")
            
            # Check if date_joined column exists
            date_joined_exists = any(column[0] == 'date_joined' for column in columns)
            if date_joined_exists:
                self.stdout.write(self.style.SUCCESS('\nThe date_joined column exists in the table!'))
            else:
                self.stdout.write(self.style.WARNING('\nThe date_joined column does NOT exist in the table!'))
                
                # Add the date_joined column
                self.stdout.write('Adding date_joined column to accounts_membership table...')
                cursor.execute("""
                    ALTER TABLE accounts_membership 
                    ADD COLUMN IF NOT EXISTS date_joined timestamp with time zone NOT NULL DEFAULT now();
                """)
                self.stdout.write(self.style.SUCCESS('Column added successfully!'))
            
            # Check if role column exists
            role_exists = any(column[0] == 'role' for column in columns)
            if role_exists:
                self.stdout.write(self.style.WARNING('\nThe role column exists but should be removed!'))
                
                # Drop the role column
                self.stdout.write('Dropping role column from accounts_membership table...')
                cursor.execute("""
                    ALTER TABLE accounts_membership 
                    DROP COLUMN IF EXISTS role;
                """)
                self.stdout.write(self.style.SUCCESS('Column dropped successfully!'))
            
            # Check if created_at column exists
            created_at_exists = any(column[0] == 'created_at' for column in columns)
            if created_at_exists:
                self.stdout.write(self.style.WARNING('\nThe created_at column exists but should be removed!'))
                
                # Drop the created_at column
                self.stdout.write('Dropping created_at column from accounts_membership table...')
                cursor.execute("""
                    ALTER TABLE accounts_membership 
                    DROP COLUMN IF EXISTS created_at;
                """)
                self.stdout.write(self.style.SUCCESS('Column dropped successfully!'))
            
            # Check if updated_at column exists
            updated_at_exists = any(column[0] == 'updated_at' for column in columns)
            if updated_at_exists:
                self.stdout.write(self.style.WARNING('\nThe updated_at column exists but should be removed!'))
                
                # Drop the updated_at column
                self.stdout.write('Dropping updated_at column from accounts_membership table...')
                cursor.execute("""
                    ALTER TABLE accounts_membership 
                    DROP COLUMN IF EXISTS updated_at;
                """)
                self.stdout.write(self.style.SUCCESS('Column dropped successfully!'))
            
            # Fix the unique constraint
            self.stdout.write('Fixing unique constraint...')
            cursor.execute("""
                ALTER TABLE accounts_membership 
                DROP CONSTRAINT IF EXISTS accounts_membership_company_id_user_id_role_unique;
            """)
            
            cursor.execute("""
                ALTER TABLE accounts_membership 
                DROP CONSTRAINT IF EXISTS accounts_membership_company_id_user_id_unique;

                ALTER TABLE accounts_membership 
                ADD CONSTRAINT accounts_membership_company_id_user_id_unique 
                UNIQUE (company_id, user_id);
            """)
            self.stdout.write(self.style.SUCCESS('Constraint fixed successfully!'))
                
        self.stdout.write(self.style.SUCCESS('Table check and fix completed successfully!'))
