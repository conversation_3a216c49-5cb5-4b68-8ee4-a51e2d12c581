from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.conf import settings

from .models import Assistant
from .media_utils import check_media_directories, fix_logo_paths

@login_required
def check_media_dirs(request):
    """Check and fix media directories."""
    if not request.user.is_superuser:
        return JsonResponse({'error': 'Permission denied'}, status=403)

    # If it's an AJAX request or explicitly requesting JSON
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.GET.get('format') == 'json':
        # Check directories
        dir_results = check_media_directories()

        # Fix logo paths if requested
        fix_results = None
        if request.GET.get('fix') == 'true':
            fix_results = fix_logo_paths()

        return JsonResponse({
            'directories': dir_results,
            'fix_results': fix_results
        })

    # Otherwise render the HTML template
    return render(request, 'assistants/check_media_dirs.html')

@csrf_exempt
def test_logo_upload(request, assistant_id):
    """Test view for logo upload."""
    assistant = get_object_or_404(Assistant, id=assistant_id)

    if request.method == 'POST':
        if 'logo' in request.FILES:
            logo_file = request.FILES['logo']
            print(f"DEBUG TEST VIEW: Logo file in request.FILES: name={logo_file.name}, size={logo_file.size}, content_type={logo_file.content_type}")

            # Enhanced logo handling with better error checking
            from PIL import Image
            import os
            from django.conf import settings
            from .media_utils import ensure_media_directories
            from django.core.files import File

            try:
                # Reset file pointer
                logo_file.seek(0)

                # Check if it's a valid image
                img = Image.open(logo_file)
                img.verify()  # Verify it's a valid image
                logo_file.seek(0)  # Reset file pointer
                print(f"DEBUG TEST VIEW: Logo file is a valid image: format={img.format}, size={img.size}")

                # Ensure all media directories exist
                ensure_media_directories()
                print(f"DEBUG TEST VIEW: Ensured all media directories exist")

                # Create a unique filename
                import uuid
                from django.utils.text import slugify
                base_name, ext = os.path.splitext(logo_file.name)
                safe_name = f"{slugify(base_name)}_{uuid.uuid4().hex[:8]}{ext.lower()}"
                target_name = f"assistant_logos/{safe_name}"
                target_path = os.path.join(settings.MEDIA_ROOT, target_name)

                print(f"DEBUG TEST VIEW: Saving logo to {target_path}")

                # Save the file directly to the filesystem
                with open(target_path, 'wb+') as destination:
                    for chunk in logo_file.chunks():
                        destination.write(chunk)

                # Verify the file was saved
                if os.path.exists(target_path):
                    print(f"DEBUG TEST VIEW: Logo file saved successfully to {target_path}")

                    # Update the model with the new path using Django's File object
                    with open(target_path, 'rb') as f:
                        django_file = File(f)
                        # Save the file to the model field
                        assistant.logo.save(target_name, django_file, save=False)

                    print(f"DEBUG TEST VIEW: Set assistant.logo to {assistant.logo.name}")
                    assistant.save()

                    # Force a refresh from the database
                    from django.db import connection
                    connection.close()
                    assistant.refresh_from_db()
                    print(f"DEBUG TEST VIEW: Refreshed from database, logo name: {assistant.logo.name}")

                    # Verify the logo URL
                    logo_url = assistant.logo_url()
                    if logo_url:
                        print(f"DEBUG TEST VIEW: Logo URL: {logo_url}")
                        return JsonResponse({
                            'status': 'success',
                            'message': 'Logo uploaded successfully',
                            'logo_url': logo_url
                        })
                    else:
                        print(f"DEBUG TEST VIEW: Logo URL is None")
                        return JsonResponse({
                            'status': 'error',
                            'message': 'Logo saved but URL is not available'
                        }, status=500)
                else:
                    print(f"DEBUG TEST VIEW: Failed to save logo file to {target_path}")
                    return JsonResponse({
                        'status': 'error',
                        'message': 'Failed to save logo file'
                    }, status=500)
            except Exception as e:
                print(f"DEBUG TEST VIEW: Error processing logo file: {e}")
                return JsonResponse({
                    'status': 'error',
                    'message': f'Error processing logo file: {str(e)}'
                }, status=500)
        else:
            return JsonResponse({
                'status': 'error',
                'message': 'No logo file found in request'
            }, status=400)

    return render(request, 'assistants/test_logo_upload.html', {
        'assistant': assistant
    })
