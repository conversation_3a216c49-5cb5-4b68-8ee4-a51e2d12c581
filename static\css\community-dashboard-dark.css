/**
 * Community Assistant Social Dashboard Dark Mode
 * Ensures the community assistant social dashboard always has a dark background
 * to prevent white flash on page reload
 *
 * Comprehensive coverage of all dashboard elements including:
 * - Main navigation and tabs
 * - Feed tabs (Feed, Statistics, Contributions, Moderation)
 * - User posts and comments
 * - Question and answer sections
 * - Stats panels and counters
 * - Topic tags and badges
 * - User avatars and profile sections
 * - Action buttons and interactive elements
 * - Empty state messages
 * - Modal dialogs and popups
 * - Dropdown menus and options
 */

/* Main dashboard container */
html body .community-dashboard,
html body .community-container,
html body [class*="community-dashboard"],
html body [class*="community-container"],
html body [id*="community-dashboard"],
html body [id*="community-container"],
html body div[class*="community"],
html body section[class*="community"],
html body .card,
html body .dashboard-card,
html body .community-card,
html body .feed-card,
html body .stats-card,
html body [class*="dashboard-card"],
html body [class*="community-card"],
html body [class*="feed-card"],
html body [class*="stats-card"] {
    background-color: #121212 !important;
    background: #121212 !important;
    border-color: #333333 !important;
    color: #ffffff !important;
    transition: none !important;
}

/* Feed items and posts */
html body .feed-item,
html body .post,
html body .comment,
html body .question,
html body .answer,
html body [class*="feed-item"],
html body [class*="post"],
html body [class*="comment"],
html body [class*="question"],
html body [class*="answer"] {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    border-color: #333333 !important;
    color: #ffffff !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

/* Tabs and navigation */
html body .nav-tabs,
html body .nav-pills,
html body .tab-content,
html body .tab-pane,
html body [class*="nav-tabs"],
html body [class*="nav-pills"],
html body [class*="tab-content"],
html body [class*="tab-pane"] {
    background-color: transparent !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

/* Stats and metrics */
html body .stats,
html body .metrics,
html body .counter,
html body .community-stats,
html body .stat-card,
html body .stat-container,
html body .stat-box,
html body .stat-number,
html body .stat-label,
html body .contributors,
html body .top-contributors,
html body [class*="stats"],
html body [class*="metrics"],
html body [class*="counter"],
html body [class*="community-stats"],
html body [class*="stat-card"],
html body [class*="stat-container"],
html body [class*="stat-box"],
html body [class*="stat-number"],
html body [class*="stat-label"],
html body [class*="contributors"],
html body [class*="top-contributors"] {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

/* Stats numbers and values */
html body .stat-value,
html body .stat-number,
html body .counter-value,
html body [class*="stat-value"],
html body [class*="stat-number"],
html body [class*="counter-value"] {
    color: #ffffff !important;
    font-weight: bold !important;
}

/* Buttons and interactive elements */
html body .community-dashboard .btn,
html body .community-container .btn,
html body [class*="community"] .btn,
html body .feed-item .btn,
html body .post .btn,
html body .comment-btn,
html body .upvote-btn,
html body .share-btn,
html body .add-knowledge-btn,
html body .ask-question-btn,
html body .chat-now-btn,
html body .dashboard-btn,
html body .action-btn,
html body [class*="comment-btn"],
html body [class*="upvote-btn"],
html body [class*="share-btn"],
html body [class*="add-knowledge-btn"],
html body [class*="ask-question-btn"],
html body [class*="chat-now-btn"],
html body [class*="dashboard-btn"],
html body [class*="action-btn"] {
    background-color: #252525 !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

/* Post action buttons */
html body .upvote,
html body .comment,
html body .share,
html body .action-icon,
html body [class*="upvote"],
html body [class*="comment"],
html body [class*="share"],
html body [class*="action-icon"] {
    background-color: transparent !important;
    color: #aaaaaa !important;
}

/* Primary buttons */
html body .community-dashboard .btn-primary,
html body .community-container .btn-primary,
html body [class*="community"] .btn-primary {
    background-color: #0077ff !important;
    border-color: #0066dd !important;
    color: #ffffff !important;
}

/* Input fields */
html body .community-dashboard input,
html body .community-dashboard textarea,
html body .community-dashboard select,
html body .community-container input,
html body .community-container textarea,
html body .community-container select,
html body [class*="community"] input,
html body [class*="community"] textarea,
html body [class*="community"] select {
    background-color: #252525 !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

/* Tags and badges */
html body .tag,
html body .badge,
html body .topic-tag,
html body .popular-topic,
html body .topic-pill,
html body .topic-badge,
html body .machine-learning,
html body .python,
html body .data-science,
html body .neural-networks,
html body [class*="tag"],
html body [class*="badge"],
html body [class*="topic-tag"],
html body [class*="popular-topic"],
html body [class*="topic-pill"],
html body [class*="topic-badge"] {
    background-color: #333333 !important;
    color: #ffffff !important;
    border-radius: 16px !important;
    padding: 4px 12px !important;
    margin: 2px !important;
    display: inline-block !important;
    font-size: 12px !important;
}

/* Special tag colors */
html body .machine-learning,
html body [class*="machine-learning"] {
    background-color: #4a2b87 !important;
}

html body .python,
html body [class*="python"] {
    background-color: #306998 !important;
}

html body .data-science,
html body [class*="data-science"] {
    background-color: #2c8a6b !important;
}

html body .neural-networks,
html body [class*="neural-networks"] {
    background-color: #8a2c61 !important;
}

/* Specific elements from the screenshot */
html body .feed,
html body #feed,
html body .community-stats,
html body .top-contributors,
html body .popular-topics,
html body .community-dashboard-header,
html body .community-dashboard-content,
html body .community-dashboard-sidebar,
html body .community-dashboard-main,
html body .community-dashboard-footer,
html body .knowledge-section,
html body .question-section,
html body .no-contributors-yet,
html body [id="feed"],
html body [class*="feed"],
html body [class*="community-stats"],
html body [class*="top-contributors"],
html body [class*="popular-topics"],
html body [class*="community-dashboard-header"],
html body [class*="community-dashboard-content"],
html body [class*="community-dashboard-sidebar"],
html body [class*="community-dashboard-main"],
html body [class*="community-dashboard-footer"],
html body [class*="knowledge-section"],
html body [class*="question-section"],
html body [class*="no-contributors-yet"] {
    background-color: #121212 !important;
    background: #121212 !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

/* Empty state messages */
html body .empty-state,
html body .no-content,
html body .no-contributors,
html body .no-topics,
html body [class*="empty-state"],
html body [class*="no-content"],
html body [class*="no-contributors"],
html body [class*="no-topics"] {
    color: #aaaaaa !important;
    background-color: transparent !important;
}

/* Assistant profile section */
html body .assistant-profile,
html body .assistant-header,
html body .assistant-info,
html body .assistant-name,
html body .assistant-description,
html body [class*="assistant-profile"],
html body [class*="assistant-header"],
html body [class*="assistant-info"],
html body [class*="assistant-name"],
html body [class*="assistant-description"] {
    background-color: #121212 !important;
    background: #121212 !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

/* Feed navigation and tabs */
html body .feed-tabs,
html body .feed-nav,
html body .feed-filter,
html body .feed-header,
html body .feed-footer,
html body [class*="feed-tabs"],
html body [class*="feed-nav"],
html body [class*="feed-filter"],
html body [class*="feed-header"],
html body [class*="feed-footer"],
html body .nav-item,
html body .nav-link,
html body .tab-pane,
html body [role="tablist"],
html body [role="tab"],
html body [role="tabpanel"] {
    background-color: #121212 !important;
    background: #121212 !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

/* Active tab styling */
html body .nav-link.active,
html body [role="tab"][aria-selected="true"] {
    background-color: #1e1e1e !important;
    border-color: #444444 !important;
    color: #ffffff !important;
    border-bottom: 2px solid #0077ff !important;
}

/* User posts and comments section */
html body .post-container,
html body .comment-container,
html body .question-container,
html body .answer-container,
html body .user-post,
html body .user-comment,
html body .assistant-response,
html body [class*="post-container"],
html body [class*="comment-container"],
html body [class*="question-container"],
html body [class*="answer-container"],
html body [class*="user-post"],
html body [class*="user-comment"],
html body [class*="assistant-response"] {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    border-color: #333333 !important;
    color: #ffffff !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

/* User and assistant avatars */
html body .assistant-avatar,
html body .assistant-icon,
html body .assistant-logo,
html body .user-avatar,
html body .user-icon,
html body .user-logo,
html body .avatar,
html body .profile-pic,
html body [class*="assistant-avatar"],
html body [class*="assistant-icon"],
html body [class*="assistant-logo"],
html body [class*="user-avatar"],
html body [class*="user-icon"],
html body [class*="user-logo"],
html body [class*="avatar"],
html body [class*="profile-pic"] {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
}

/* Post metadata and timestamps */
html body .post-meta,
html body .post-time,
html body .timestamp,
html body .post-date,
html body .minutes-ago,
html body [class*="post-meta"],
html body [class*="post-time"],
html body [class*="timestamp"],
html body [class*="post-date"],
html body [class*="minutes-ago"] {
    color: #aaaaaa !important;
}

/* Ensure all text is white */
html body .community-dashboard *,
html body .community-container *,
html body [class*="community"] * {
    color: #ffffff !important;
}

/* Override for specific text that should be colored */
html body .community-dashboard .text-primary,
html body .community-container .text-primary,
html body [class*="community"] .text-primary {
    color: #0077ff !important;
}

html body .community-dashboard .text-success,
html body .community-container .text-success,
html body [class*="community"] .text-success {
    color: #28a745 !important;
}

html body .community-dashboard .text-danger,
html body .community-container .text-danger,
html body [class*="community"] .text-danger {
    color: #dc3545 !important;
}

html body .community-dashboard .text-warning,
html body .community-container .text-warning,
html body [class*="community"] .text-warning {
    color: #ffc107 !important;
}

/* Ensure icons have transparent backgrounds */
html body .community-dashboard i,
html body .community-container i,
html body [class*="community"] i {
    background-color: transparent !important;
}

/* Main navigation bar and browser UI elements */
html body .browser-header,
html body .browser-toolbar,
html body .browser-tabs,
html body .browser-navigation,
html body .browser-controls,
html body .browser-address-bar,
html body .browser-buttons,
html body .main-header,
html body .main-navigation,
html body .main-menu,
html body .top-bar,
html body .app-bar,
html body .header-bar,
html body [class*="browser-header"],
html body [class*="browser-toolbar"],
html body [class*="browser-tabs"],
html body [class*="browser-navigation"],
html body [class*="browser-controls"],
html body [class*="browser-address-bar"],
html body [class*="browser-buttons"],
html body [class*="main-header"],
html body [class*="main-navigation"],
html body [class*="main-menu"],
html body [class*="top-bar"],
html body [class*="app-bar"],
html body [class*="header-bar"] {
    background-color: #121212 !important;
    background: #121212 !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

/* Main navigation links and icons */
html body .main-nav-link,
html body .main-nav-icon,
html body .main-nav-item,
html body .main-nav-text,
html body .main-menu-item,
html body .main-menu-link,
html body .main-menu-icon,
html body .main-menu-text,
html body [class*="main-nav-link"],
html body [class*="main-nav-icon"],
html body [class*="main-nav-item"],
html body [class*="main-nav-text"],
html body [class*="main-menu-item"],
html body [class*="main-menu-link"],
html body [class*="main-menu-icon"],
html body [class*="main-menu-text"] {
    color: #ffffff !important;
}

/* White backgrounds that need to be darkened */
html body .white-bg,
html body .bg-white,
html body .background-white,
html body [class*="white-bg"],
html body [class*="bg-white"],
html body [class*="background-white"] {
    background-color: #121212 !important;
    background: #121212 !important;
}

/* Specific elements from the screenshot */
html body .home-icon,
html body .dashboard-icon,
html body .manage-icon,
html body .companies-icon,
html body .assistants-icon,
html body .directory-icon,
html body .favorites-icon,
html body .community-icon,
html body [class*="home-icon"],
html body [class*="dashboard-icon"],
html body [class*="manage-icon"],
html body [class*="companies-icon"],
html body [class*="assistants-icon"],
html body [class*="directory-icon"],
html body [class*="favorites-icon"],
html body [class*="community-icon"] {
    background-color: transparent !important;
    color: #ffffff !important;
}

/* Community dashboard tabs (Feed, Statistics, Contributions, Moderation) */
html body .feed-tab,
html body .statistics-tab,
html body .contributions-tab,
html body .moderation-tab,
html body [class*="feed-tab"],
html body [class*="statistics-tab"],
html body [class*="contributions-tab"],
html body [class*="moderation-tab"] {
    background-color: #121212 !important;
    background: #121212 !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

/* Chat Now and Community Dashboard buttons */
html body .chat-now,
html body .community-dashboard-btn,
html body [class*="chat-now"],
html body [class*="community-dashboard-btn"] {
    background-color: #252525 !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

/* Primary action buttons (Chat Now) */
html body .chat-now.btn-primary,
html body [class*="chat-now"].btn-primary {
    background-color: #0077ff !important;
    border-color: #0066dd !important;
    color: #ffffff !important;
}

/* Browser UI elements and top navigation */
html body .browser-chrome,
html body .browser-ui,
html body .browser-window,
html body .browser-frame,
html body .browser-container,
html body .browser-viewport,
html body .browser-content,
html body .browser-page,
html body .browser-tab,
html body .browser-tab-active,
html body .browser-tab-inactive,
html body .browser-tab-content,
html body .browser-tab-title,
html body .browser-tab-icon,
html body .browser-tab-close,
html body .browser-address,
html body .browser-url,
html body .browser-search,
html body .browser-input,
html body .browser-button,
html body [class*="browser-chrome"],
html body [class*="browser-ui"],
html body [class*="browser-window"],
html body [class*="browser-frame"],
html body [class*="browser-container"],
html body [class*="browser-viewport"],
html body [class*="browser-content"],
html body [class*="browser-page"],
html body [class*="browser-tab"],
html body [class*="browser-tab-active"],
html body [class*="browser-tab-inactive"],
html body [class*="browser-tab-content"],
html body [class*="browser-tab-title"],
html body [class*="browser-tab-icon"],
html body [class*="browser-tab-close"],
html body [class*="browser-address"],
html body [class*="browser-url"],
html body [class*="browser-search"],
html body [class*="browser-input"],
html body [class*="browser-button"] {
    background-color: #121212 !important;
    background: #121212 !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

/* Specific elements from the screenshot - top navigation bar */
html body header,
html body nav,
html body .header,
html body .navbar,
html body .nav,
html body .navigation,
html body .top-navigation,
html body .main-navigation,
html body .site-header,
html body .site-navigation,
html body .app-header,
html body .app-navigation,
html body [class*="header"],
html body [class*="navbar"],
html body [class*="nav"],
html body [class*="navigation"],
html body [class*="top-navigation"],
html body [class*="main-navigation"],
html body [class*="site-header"],
html body [class*="site-navigation"],
html body [class*="app-header"],
html body [class*="app-navigation"] {
    background-color: #121212 !important;
    background: #121212 !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

/* Sidebar elements */
html body .sidebar,
html body .side-nav,
html body .side-menu,
html body .side-bar,
html body .left-sidebar,
html body .left-nav,
html body .left-menu,
html body .left-bar,
html body .right-sidebar,
html body .right-nav,
html body .right-menu,
html body .right-bar,
html body .sidenav,
html body .side-navigation,
html body .side-panel,
html body .side-column,
html body .nav-sidebar,
html body .nav-side,
html body .menu-sidebar,
html body .menu-side,
html body [class*="sidebar"],
html body [class*="side-nav"],
html body [class*="side-menu"],
html body [class*="side-bar"],
html body [class*="left-sidebar"],
html body [class*="left-nav"],
html body [class*="left-menu"],
html body [class*="left-bar"],
html body [class*="right-sidebar"],
html body [class*="right-nav"],
html body [class*="right-menu"],
html body [class*="right-bar"],
html body [class*="sidenav"],
html body [class*="side-navigation"],
html body [class*="side-panel"],
html body [class*="side-column"],
html body [class*="nav-sidebar"],
html body [class*="nav-side"],
html body [class*="menu-sidebar"],
html body [class*="menu-side"] {
    background-color: #121212 !important;
    background: #121212 !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

/* Sidebar navigation items */
html body .sidebar-item,
html body .sidebar-link,
html body .sidebar-nav-item,
html body .sidebar-nav-link,
html body .sidebar-menu-item,
html body .sidebar-menu-link,
html body .side-nav-item,
html body .side-nav-link,
html body .side-menu-item,
html body .side-menu-link,
html body [class*="sidebar-item"],
html body [class*="sidebar-link"],
html body [class*="sidebar-nav-item"],
html body [class*="sidebar-nav-link"],
html body [class*="sidebar-menu-item"],
html body [class*="sidebar-menu-link"],
html body [class*="side-nav-item"],
html body [class*="side-nav-link"],
html body [class*="side-menu-item"],
html body [class*="side-menu-link"] {
    background-color: transparent !important;
    color: #ffffff !important;
}

/* Active sidebar items */
html body .sidebar-item.active,
html body .sidebar-link.active,
html body .sidebar-nav-item.active,
html body .sidebar-nav-link.active,
html body .sidebar-menu-item.active,
html body .sidebar-menu-link.active,
html body .side-nav-item.active,
html body .side-nav-link.active,
html body .side-menu-item.active,
html body .side-menu-link.active,
html body [class*="sidebar-item"].active,
html body [class*="sidebar-link"].active,
html body [class*="sidebar-nav-item"].active,
html body [class*="sidebar-nav-link"].active,
html body [class*="sidebar-menu-item"].active,
html body [class*="sidebar-menu-link"].active,
html body [class*="side-nav-item"].active,
html body [class*="side-nav-link"].active,
html body [class*="side-menu-item"].active,
html body [class*="side-menu-link"].active {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
    border-left: 3px solid #0077ff !important;
}

/* Sidebar icons */
html body .sidebar-icon,
html body .side-nav-icon,
html body .side-menu-icon,
html body .nav-sidebar-icon,
html body .sidebar-item-icon,
html body .sidebar-link-icon,
html body .sidebar-nav-icon,
html body .sidebar-menu-icon,
html body [class*="sidebar-icon"],
html body [class*="side-nav-icon"],
html body [class*="side-menu-icon"],
html body [class*="nav-sidebar-icon"],
html body [class*="sidebar-item-icon"],
html body [class*="sidebar-link-icon"],
html body [class*="sidebar-nav-icon"],
html body [class*="sidebar-menu-icon"] {
    background-color: transparent !important;
    color: #ffffff !important;
}

/* Specific sidebar elements from the screenshot */
html body .home-link,
html body .dashboard-link,
html body .manage-link,
html body .companies-link,
html body .assistants-link,
html body .directory-link,
html body .favorites-link,
html body .community-link,
html body [class*="home-link"],
html body [class*="dashboard-link"],
html body [class*="manage-link"],
html body [class*="companies-link"],
html body [class*="assistants-link"],
html body [class*="directory-link"],
html body [class*="favorites-link"],
html body [class*="community-link"] {
    background-color: #121212 !important;
    background: #121212 !important;
    color: #ffffff !important;
}

/* Specific sidebar elements from the screenshot - active state */
html body .home-link.active,
html body .dashboard-link.active,
html body .manage-link.active,
html body .companies-link.active,
html body .assistants-link.active,
html body .directory-link.active,
html body .favorites-link.active,
html body .community-link.active,
html body [class*="home-link"].active,
html body [class*="dashboard-link"].active,
html body [class*="manage-link"].active,
html body [class*="companies-link"].active,
html body [class*="assistants-link"].active,
html body [class*="directory-link"].active,
html body [class*="favorites-link"].active,
html body [class*="community-link"].active {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
    border-left: 3px solid #0077ff !important;
}

/* Specific navigation elements from the screenshot */
html body .home,
html body .dashboard,
html body .manage,
html body .companies,
html body .assistants,
html body .directory,
html body .favorites,
html body .community-assistants,
html body [class*="home"],
html body [class*="dashboard"],
html body [class*="manage"],
html body [class*="companies"],
html body [class*="assistants"],
html body [class*="directory"],
html body [class*="favorites"],
html body [class*="community-assistants"] {
    background-color: #121212 !important;
    background: #121212 !important;
    color: #ffffff !important;
}

/* Specific navigation icons from the screenshot */
html body .home-icon,
html body .dashboard-icon,
html body .manage-icon,
html body .companies-icon,
html body .assistants-icon,
html body .directory-icon,
html body .favorites-icon,
html body .community-assistants-icon,
html body [class*="home-icon"],
html body [class*="dashboard-icon"],
html body [class*="manage-icon"],
html body [class*="companies-icon"],
html body [class*="assistants-icon"],
html body [class*="directory-icon"],
html body [class*="favorites-icon"],
html body [class*="community-assistants-icon"] {
    background-color: transparent !important;
    color: #ffffff !important;
}

/* Specific navigation text from the screenshot */
html body .home-text,
html body .dashboard-text,
html body .manage-text,
html body .companies-text,
html body .assistants-text,
html body .directory-text,
html body .favorites-text,
html body .community-assistants-text,
html body [class*="home-text"],
html body [class*="dashboard-text"],
html body [class*="manage-text"],
html body [class*="companies-text"],
html body [class*="assistants-text"],
html body [class*="directory-text"],
html body [class*="favorites-text"],
html body [class*="community-assistants-text"] {
    color: #ffffff !important;
}
