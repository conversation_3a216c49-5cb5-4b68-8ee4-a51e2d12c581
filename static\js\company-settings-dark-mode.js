/**
 * Company Settings Dark Mode Handler
 * Ensures dark mode is properly applied to company settings pages
 * Uses CSS variables for consistent styling
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if dark mode is active
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';

    // Get CSS variables for dark mode
    const darkBgPrimary = getComputedStyle(document.documentElement).getPropertyValue('--dark-bg-primary') || '#121212';
    const darkBgSecondary = getComputedStyle(document.documentElement).getPropertyValue('--dark-bg-secondary') || '#1a1a1a';
    const darkBgTertiary = getComputedStyle(document.documentElement).getPropertyValue('--dark-bg-tertiary') || '#252525';
    const darkBorderColor = getComputedStyle(document.documentElement).getPropertyValue('--dark-border-color') || '#333333';
    const darkTextPrimary = getComputedStyle(document.documentElement).getPropertyValue('--dark-text-primary') || '#ffffff';
    const darkTextSecondary = getComputedStyle(document.documentElement).getPropertyValue('--dark-text-secondary') || '#aaaaaa';
    const darkShadow = getComputedStyle(document.documentElement).getPropertyValue('--dark-shadow') || '0 4px 12px rgba(0, 0, 0, 0.2)';

    // Apply dark mode styles to company settings elements
    applyDarkModeToCompanySettings(isDarkMode);

    // Listen for theme changes
    document.addEventListener('themeChanged', function(e) {
        const isDarkMode = e.detail.theme === 'dark';
        applyDarkModeToCompanySettings(isDarkMode);
    });

    // Function to apply dark mode to company settings elements
    function applyDarkModeToCompanySettings(isDarkMode) {
        if (isDarkMode) {
            // Apply dark mode to main container
            document.querySelectorAll('.container.py-4').forEach(container => {
                container.style.backgroundColor = darkBgPrimary;
                container.style.color = darkTextPrimary;
            });

            // Apply dark mode to cards
            document.querySelectorAll('.card').forEach(card => {
                card.style.backgroundColor = darkBgSecondary;
                card.style.borderColor = darkBorderColor;
                card.style.color = darkTextPrimary;
                card.style.boxShadow = darkShadow;
            });

            // Apply dark mode to card headers
            document.querySelectorAll('.card-header').forEach(header => {
                header.style.backgroundColor = darkBgTertiary;
                header.style.borderBottomColor = darkBorderColor;
                header.style.color = darkTextPrimary;
            });

            // Apply dark mode to card bodies
            document.querySelectorAll('.card-body').forEach(body => {
                body.style.backgroundColor = darkBgSecondary;
                body.style.color = darkTextPrimary;
            });

            // Apply dark mode to form controls
            document.querySelectorAll('.form-control, .form-select').forEach(control => {
                control.style.backgroundColor = darkBgTertiary;
                control.style.borderColor = darkBorderColor;
                control.style.color = darkTextPrimary;
            });

            // Apply dark mode to form labels
            document.querySelectorAll('.form-label').forEach(label => {
                label.style.color = darkTextPrimary;
            });

            // Apply dark mode to form help text
            document.querySelectorAll('.form-text').forEach(text => {
                text.style.color = darkTextSecondary;
            });

            // Apply dark mode to checkboxes
            document.querySelectorAll('.form-check-input').forEach(checkbox => {
                checkbox.style.backgroundColor = darkBgTertiary;
                checkbox.style.borderColor = darkBorderColor;
            });

            // Apply dark mode to buttons
            document.querySelectorAll('.btn-light').forEach(button => {
                button.style.backgroundColor = darkBorderColor;
                button.style.borderColor = '#444444';
                button.style.color = darkTextPrimary;
            });

            // Apply dark mode to any elements with bg-light class
            document.querySelectorAll('.bg-light, .bg-light-subtle').forEach(el => {
                el.style.backgroundColor = darkBgPrimary;
                el.style.color = darkTextPrimary;
            });

            // Specific override for bg-light-subtle
            document.querySelectorAll('.bg-light-subtle').forEach(el => {
                el.style.backgroundColor = '#121212';
            });

            // Handle Bootstrap 5 utility classes
            document.querySelectorAll('.bg-body-secondary').forEach(el => {
                el.style.backgroundColor = darkBgSecondary;
            });

            document.querySelectorAll('.bg-body-tertiary').forEach(el => {
                el.style.backgroundColor = darkBgTertiary;
            });

            document.querySelectorAll('.text-body-secondary').forEach(el => {
                el.style.color = darkTextSecondary;
            });

            document.querySelectorAll('.border-light-subtle').forEach(el => {
                el.style.borderColor = darkBorderColor;
            });

            // Apply dark mode to directory categories section
            document.querySelectorAll('#directory-categories, .directory-categories').forEach(el => {
                el.style.backgroundColor = darkBgSecondary;
                el.style.color = darkTextPrimary;
                el.style.borderColor = darkBorderColor;
            });

            // Apply dark mode to description textarea
            document.querySelectorAll('textarea#id_description').forEach(el => {
                el.style.backgroundColor = darkBgTertiary;
                el.style.color = darkTextPrimary;
                el.style.borderColor = darkBorderColor;
            });

            // Apply dark mode to select elements
            document.querySelectorAll('select').forEach(el => {
                el.style.backgroundColor = darkBgTertiary;
                el.style.color = darkTextPrimary;
                el.style.borderColor = darkBorderColor;
            });

            // Apply dark mode to option elements
            document.querySelectorAll('option').forEach(el => {
                el.style.backgroundColor = darkBgTertiary;
                el.style.color = darkTextPrimary;
            });

            // Apply dark mode to "No Change" option
            document.querySelectorAll('option[value="---"]').forEach(el => {
                el.style.color = darkTextSecondary;
                el.style.fontStyle = 'italic';
            });

            // Apply dark mode to checkboxes
            document.querySelectorAll('input[type="checkbox"]').forEach(el => {
                el.style.backgroundColor = darkBgTertiary;
                el.style.borderColor = darkBorderColor;
            });

            // Apply dark mode to "Yes" label
            document.querySelectorAll('.form-check-label').forEach(el => {
                el.style.color = darkTextPrimary;
            });

            // Apply dark mode to advanced settings section
            document.querySelectorAll('#advanced-settings, .advanced-settings').forEach(el => {
                el.style.backgroundColor = darkBgSecondary;
                el.style.color = darkTextPrimary;
                el.style.borderColor = darkBorderColor;
            });
        }
    }

    // Use a more efficient approach for observing DOM changes
    // Only create observer if window.optimizedDarkMode is not true
    if (!window._isDarkModeActive) {
        // Set up a mutation observer with debouncing to prevent excessive calls
        let debounceTimer;
        const observer = new MutationObserver(function(mutations) {
            // Clear any existing timeout
            if (debounceTimer) {
                clearTimeout(debounceTimer);
            }

            // Set a new timeout to run the function after a delay
            debounceTimer = setTimeout(function() {
                if (document.documentElement.getAttribute('data-theme') === 'dark') {
                    applyDarkModeToCompanySettings(true);
                }
            }, 1000); // 1 second debounce
        });

        // Observe only significant changes
        observer.observe(document.body, {
            childList: true,
            subtree: false,
            attributeFilter: ['class', 'style']
        });
    }
});
