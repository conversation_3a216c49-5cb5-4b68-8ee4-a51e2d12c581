/**
 * Final Overrides CSS
 * This file is loaded last and overrides any other styles
 * Updated to ensure button fixes are not overridden
 */

/* Ensure assistant header is always visible in dark mode */
[data-theme="dark"] .general-assistant-header,
[data-theme="dark"] .assistant-header,
[data-theme="dark"] div.general-assistant-header,
[data-theme="dark"] div.assistant-header {
    display: block !important;
    visibility: visible !important;
}

/* Ensure message content is always 100% width */
.message-content,
span.message-content,
div.message-content,
.tinymce-content,
span.tinymce-content,
div.tinymce-content,
.message-content.tinymce-content,
span.message-content.tinymce-content,
div.message-content.tinymce-content,
.tinymce-content.message-content,
span.tinymce-content.message-content,
div.tinymce-content.message-content {
    width: 100% !important;
    max-width: 100% !important;
    display: block !important;
}

/* Ensure all direct children of message content expand to fill width */
.message-content > *,
.tinymce-content > *,
.message-content.tinymce-content > *,
.tinymce-content.message-content > * {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
}

/* Ensure chat container is properly styled in dark mode */
[data-theme="dark"] .chat-container,
[data-theme="dark"] .general-chat-container,
[data-theme="dark"] div.chat-container,
[data-theme="dark"] div.general-chat-container {
    display: block !important;
    visibility: visible !important;
    background: linear-gradient(145deg, #1a1a1a, #151515) !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
    border-radius: 12px !important;
    overflow: hidden !important;
}

/* Ensure chat box is properly styled in dark mode */
[data-theme="dark"] .chat-box,
[data-theme="dark"] .general-chat-box,
[data-theme="dark"] #chat-box,
[data-theme="dark"] div.chat-box,
[data-theme="dark"] div.general-chat-box {
    background: #121212 !important;
    border: none !important;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.2) !important;
    padding: 1.5rem !important;
}

/* Ensure filter form is properly styled in dark mode */
[data-theme="dark"] .filter-form,
[data-theme="dark"] form.filter-form,
[data-theme="dark"] div.filter-form {
    display: block !important;
    visibility: visible !important;
    background-color: #1a1a1a !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
    color: #ffffff !important;
}

/* Ensure filter form input group is properly styled in dark mode */
[data-theme="dark"] .filter-form .input-group,
[data-theme="dark"] form.filter-form .input-group {
    background-color: transparent !important;
    border: none !important;
}

/* Ensure filter form input group text is properly styled in dark mode */
[data-theme="dark"] .filter-form .input-group-text,
[data-theme="dark"] form.filter-form .input-group-text {
    background-color: #252525 !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

/* Ensure filter form input is properly styled in dark mode */
[data-theme="dark"] .filter-form .form-control,
[data-theme="dark"] form.filter-form .form-control {
    background-color: #252525 !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

/* Ensure filter form search button is properly styled in dark mode */
[data-theme="dark"] .filter-form .btn-primary,
[data-theme="dark"] form.filter-form .btn-primary {
    background-color: #0077ff !important;
    border-color: #0066dd !important;
    color: #ffffff !important;
}

/* Ensure header buttons are properly styled in dark mode */
[data-theme="dark"] .general-assistant-header button,
[data-theme="dark"] .general-assistant-header .btn,
[data-theme="dark"] .general-assistant-header .btn-outline-secondary,
[data-theme="dark"] .general-assistant-header .btn-outline-primary,
[data-theme="dark"] .general-assistant-header .btn-sm,
[data-theme="dark"] .general-assistant-header .like-button,
[data-theme="dark"] .general-assistant-header .rate-assistant-btn {
    background-color: rgba(255, 255, 255, 0.05) !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2),
                0 1px 3px rgba(0, 0, 0, 0.1),
                inset 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
    position: relative !important;
    z-index: 1051 !important;
    cursor: pointer !important;
    pointer-events: auto !important;
    overflow: visible !important;
}

/* Ensure header button icons are properly styled in dark mode */
[data-theme="dark"] .general-assistant-header button i,
[data-theme="dark"] .general-assistant-header .btn i,
[data-theme="dark"] .general-assistant-header .btn-outline-secondary i,
[data-theme="dark"] .general-assistant-header .btn-outline-primary i,
[data-theme="dark"] .general-assistant-header .btn-sm i,
[data-theme="dark"] .general-assistant-header .like-button i,
[data-theme="dark"] .general-assistant-header .rate-assistant-btn i,
[data-theme="dark"] .general-assistant-header .bi {
    color: #ffffff !important;
    text-shadow: 0 0 5px rgba(0, 102, 255, 0.3) !important;
    background-color: transparent !important;
    background: none !important;
}

/* Special styling for heart icon in like button */
[data-theme="dark"] .general-assistant-header .like-button .bi-heart-fill {
    color: #ff3366 !important;
    text-shadow: 0 0 5px rgba(255, 51, 102, 0.5) !important;
}

/* Target the exact structure shown in the screenshot - LIGHT MODE ONLY */
[data-theme="light"] div.message.assistant-message.mb-3 span.message-content,
[data-theme="light"] div.message.assistant-message span.message-content,
[data-theme="light"] .message.assistant-message .message-content,
[data-theme="light"] .assistant-message .message-content,
[data-theme="light"] span.message-content {
    background: #ffffff !important;
    background-color: #ffffff !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    background-position: initial !important;
    background-size: initial !important;
    background-repeat: initial !important;
    background-origin: initial !important;
    background-clip: initial !important;
    background-attachment: initial !important;
    color: #333333 !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

/* Target user messages - LIGHT MODE ONLY */
[data-theme="light"] div.message.user-message.mb-3 span.message-content,
[data-theme="light"] div.message.user-message span.message-content,
[data-theme="light"] .message.user-message .message-content,
[data-theme="light"] .user-message .message-content {
    background: #3b7dd8 !important;
    background-color: #3b7dd8 !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    background-position: initial !important;
    background-size: initial !important;
    background-repeat: initial !important;
    background-origin: initial !important;
    background-clip: initial !important;
    background-attachment: initial !important;
    color: #ffffff !important;
    border: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Remove any pseudo-elements that might add gradients */
.message-content::before,
.message-content::after,
span.message-content::before,
span.message-content::after,
div.message.assistant-message.mb-3 span.message-content::before,
div.message.assistant-message.mb-3 span.message-content::after,
div.message.user-message.mb-3 span.message-content::before,
div.message.user-message.mb-3 span.message-content::after {
    display: none !important;
    content: none !important;
    background: none !important;
    background-image: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}
