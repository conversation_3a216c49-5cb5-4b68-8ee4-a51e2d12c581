# QR Code Design Specification with Centered 'A' Logo

## Overview

This document provides detailed specifications for generating a QR code with a distinctive centered logo consisting of a black letter 'A' on a white background. The design must follow precise requirements to ensure the QR code remains scannable while maintaining visual appeal.

## Visual Requirements

1. **Base QR Code**: Black modules on white background
2. **Center Logo**: 
   - A white letter 'A' background with a black letter 'A' on top
   - The white 'A' must be exactly 10% larger than the black 'A'
   - The white 'A' must be completely solid (no holes where QR code shows through)
   - The black 'A' must be completely protected from contact with QR code elements

## Technical Specifications

### QR Code Generation

- **Version**: 1 (or auto-fit to data)
- **Error Correction**: Level H (high, 30% recovery capacity)
- **Box Size**: 10 pixels per module
- **Border**: 4 modules

### Font Selection

- **Primary Font**: Arial <PERSON> (preferred)
- **Alternatives**: Impact, Arial Bold
- **Fallback**: Any available bold sans-serif font
- **Last Resort**: Default system font

### Size Proportions

- **Black 'A' Size**: 35% of the minimum QR code dimension
- **White 'A' Background Size**: Exactly 110% of the black 'A' size
- **Padding**: 5 pixels around the white 'A' to ensure solidity

### Positioning

- Both letters must be perfectly centered in the QR code
- The black 'A' must be perfectly centered on the white 'A' background

## Implementation Algorithm

1. **Generate Base QR Code**:
   ```python
   qr = qrcode.QRCode(
       version=1,
       error_correction=qrcode.constants.ERROR_CORRECT_H,
       box_size=10,
       border=4,
   )
   qr.add_data(data)
   qr.make(fit=True)
   qr_img = qr.make_image(fill_color="black", back_color="white").convert('RGBA')
   ```

2. **Calculate Dimensions**:
   ```python
   qr_width, qr_height = qr_img.size
   center_x, center_y = qr_width // 2, qr_height // 2
   
   # Black 'A' size
   black_letter_size = int(min(qr_width, qr_height) * 0.35)
   
   # White 'A' size - exactly 10% larger
   white_letter_size = int(black_letter_size * 1.1)
   ```

3. **Load Appropriate Font**:
   ```python
   # Try to load Arial Black or alternatives
   # For the black 'A'
   black_font = load_font("Arial Black", black_letter_size)
   
   # For the white 'A' - same font family but larger size
   white_font = load_font("Arial Black", white_letter_size)
   ```

4. **Calculate Text Dimensions and Positions**:
   ```python
   # Get dimensions of both letters
   black_width, black_height = get_text_dimensions(letter, black_font)
   white_width, white_height = get_text_dimensions(letter, white_font)
   
   # Calculate positions to center both letters
   white_x = center_x - white_width // 2
   white_y = center_y - white_height // 2
   
   black_x = center_x - black_width // 2
   black_y = center_y - black_height // 2
   ```

5. **Draw Solid White 'A' Background**:
   ```python
   # Create a temporary image for the white 'A'
   white_temp = Image.new('RGBA', qr_img.size, (0, 0, 0, 0))
   white_draw = ImageDraw.Draw(white_temp)
   
   # Draw the white 'A' multiple times with offsets to ensure solidity
   padding = 5
   
   # First draw a larger, more blurred white 'A' as a base
   for dx in range(-padding-2, padding+3, 2):
       for dy in range(-padding-2, padding+3, 2):
           white_draw.text(
               (white_x + dx, white_y + dy),
               letter,
               fill=(255, 255, 255, 255),
               font=white_font
           )
   
   # Then draw the regular white 'A' on top for a cleaner edge
   for dx in range(-padding, padding+1):
       for dy in range(-padding, padding+1):
           white_draw.text(
               (white_x + dx, white_y + dy),
               letter,
               fill=(255, 255, 255, 255),
               font=white_font
           )
   ```

6. **Draw Black 'A' on Top**:
   ```python
   # Create a temporary image for the black 'A'
   black_temp = Image.new('RGBA', qr_img.size, (0, 0, 0, 0))
   black_draw = ImageDraw.Draw(black_temp)
   
   # Draw the black 'A' - single pass is sufficient as it sits on solid white
   black_draw.text(
       (black_x, black_y),
       letter,
       fill=(0, 0, 0, 255),
       font=black_font
   )
   ```

7. **Composite All Layers**:
   ```python
   # Create a transparent overlay layer
   overlay_layer = Image.new('RGBA', qr_img.size, (0, 0, 0, 0))
   
   # Add white 'A' to overlay
   overlay_layer = Image.alpha_composite(overlay_layer, white_temp)
   
   # Add black 'A' to overlay
   overlay_layer = Image.alpha_composite(overlay_layer, black_temp)
   
   # Composite overlay with QR code
   final_image = Image.alpha_composite(qr_img, overlay_layer).convert('RGB')
   ```

## Critical Implementation Notes

1. **Solid White Background**: The most critical aspect is ensuring the white 'A' background is completely solid with no holes. This requires multiple drawing passes with slight offsets.

2. **Exact Size Ratio**: The white 'A' must be exactly 10% larger than the black 'A' - not more, not less.

3. **Font Consistency**: Use the same font family for both letters to maintain visual harmony.

4. **Perfect Centering**: Both letters must be perfectly centered relative to the QR code and to each other.

5. **Error Correction**: Always use high error correction (Level H) to ensure the QR code remains scannable despite the center logo.

## Testing

1. Generate the QR code with a test URL
2. Verify visually that:
   - The white 'A' background is solid with no QR code showing through
   - The black 'A' sits perfectly centered on the white background
   - The white 'A' is approximately 10% larger than the black 'A'
3. Test scanning the QR code with multiple devices and apps to ensure it's scannable

## Example Implementation

A complete Python implementation using the PIL/Pillow library is provided in the `utils/qr_generator.py` file. The key function is `generate_qr_with_a(data, letter="A")` which handles the entire process described above.

## Visual Reference

The final QR code should look like a standard black and white QR code with a distinctive centered logo consisting of a bold black letter 'A' sitting on top of a slightly larger solid white letter 'A' background. The white background completely protects the black 'A' from any contact with the QR code elements.
