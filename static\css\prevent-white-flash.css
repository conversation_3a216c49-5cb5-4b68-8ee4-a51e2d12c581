/**
 * Prevent White Flash CSS
 * This file ensures the background is dark during page load to prevent white flash
 */

/* Set dark background on html and body elements */
html, 
body {
  background-color: #121212 !important;
  color: #ffffff !important;
  transition: none !important; /* Disable transitions during load */
}

/* Set dark background on main content */
main {
  background-color: #121212 !important;
  color: #ffffff !important;
}

/* Set dark background on chat container */
.chat-container, 
.general-chat-container {
  background-color: #1e1e1e !important;
  border-color: #333333 !important;
}

/* Set dark background on chat box */
.chat-box, 
.general-chat-box, 
#chat-box {
  background-color: #252525 !important;
  border-color: #333333 !important;
}

/* Ensure all modals have dark background */
.modal-content {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

/* Ensure all cards have dark background */
.card {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

/* Ensure all inputs have dark background */
input, 
textarea, 
select {
  background-color: #252525 !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

/* Ensure all buttons have dark background */
button {
  background-color: #333333 !important;
  color: #ffffff !important;
  border-color: #444444 !important;
}

/* Override any light backgrounds */
[style*="background-color: white"],
[style*="background-color: #fff"],
[style*="background-color: rgb(255, 255, 255)"],
[style*="background-color: rgba(255, 255, 255"],
[style*="background: white"],
[style*="background: #fff"],
[style*="background: rgb(255, 255, 255)"],
[style*="background: rgba(255, 255, 255"] {
  background-color: #121212 !important;
  background: #121212 !important;
  color: #ffffff !important;
}
