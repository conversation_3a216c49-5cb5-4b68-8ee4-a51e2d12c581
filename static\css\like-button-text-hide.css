/**
 * Like Button Text Hide
 * Hides the text in like buttons and only shows the heart icon
 */

/* Hide text in like buttons */
.like-button .bi-heart + span,
.like-button .bi-heart-fill + span,
.like-button > span:not(.icon),
.btn-like > span:not(.icon),
.btn-favorite > span:not(.icon),
.favorite-button > span:not(.icon) {
  display: none !important;
}

/* Hide any text nodes that are direct children of the button */
.like-button:not(:empty)::after,
.btn-like:not(:empty)::after,
.btn-favorite:not(:empty)::after,
.favorite-button:not(:empty)::after {
  content: "" !important;
}

/* Remove margin from heart icon since there's no text next to it */
.like-button .bi-heart,
.like-button .bi-heart-fill,
.btn-like .bi-heart,
.btn-like .bi-heart-fill,
.btn-favorite .bi-heart,
.btn-favorite .bi-heart-fill,
.favorite-button .bi-heart,
.favorite-button .bi-heart-fill {
  margin-right: 0 !important;
}

/* Remove the me-1 class effect from heart icons */
.like-button .bi-heart.me-1,
.like-button .bi-heart-fill.me-1,
.btn-like .bi-heart.me-1,
.btn-like .bi-heart-fill.me-1,
.btn-favorite .bi-heart.me-1,
.btn-favorite .bi-heart-fill.me-1,
.favorite-button .bi-heart.me-1,
.favorite-button .bi-heart-fill.me-1 {
  margin-right: 0 !important;
}

/* Ensure the button is properly sized for just the icon */
.like-button,
.btn-like,
.btn-favorite,
.favorite-button {
  width: 36px !important;
  height: 36px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 50% !important;
}

/* Make heart icons slightly larger since they're alone */
.like-button .bi-heart,
.like-button .bi-heart-fill,
.btn-like .bi-heart,
.btn-like .bi-heart-fill,
.btn-favorite .bi-heart,
.btn-favorite .bi-heart-fill,
.favorite-button .bi-heart,
.favorite-button .bi-heart-fill {
  font-size: 1.3rem !important;
}

/* Make SVG heart icons slightly larger */
.like-button svg,
.btn-like svg,
.btn-favorite svg,
.favorite-button svg {
  width: 20px !important;
  height: 20px !important;
}
