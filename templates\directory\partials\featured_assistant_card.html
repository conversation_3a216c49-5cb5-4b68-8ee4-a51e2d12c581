{% load static account_tags rating_tags %}

{# Featured Assistant Card - Enhanced design for carousel display #}
<div class="list-group-item position-relative" data-assistant-id="{{ listing.assistant.id }}">
    {# Show Featured badge #}
    {% if listing.assistant.is_featured %}
    <span class="badge bg-success position-absolute top-0 start-0 m-2" style="z-index: 10; font-size: 0.7em; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <i class="bi bi-star-fill me-1"></i>Featured
    </span>
    {% endif %}

    {# Tier badges are not shown in the featured section #}

    <div class="row g-4 text-center">
        {# Logo #}
        <div class="col-12 d-flex justify-content-center">
            <a href="{% url 'assistants:assistant_chat' slug=listing.assistant.slug %}" class="text-decoration-none">
                <div class="logo-container">
                    {% with logo_url=listing.assistant.get_logo_url %}
                        {% if logo_url %}
                            <img src="{{ logo_url }}" alt="{{ listing.assistant.name }} logo">
                        {% else %}
                            <i class="bi bi-robot logo-placeholder"></i>
                        {% endif %}
                    {% endwith %}
                </div>
            </a>
        </div>

        {# Assistant Name and Company #}
        <div class="col-12">
            <h6 class="mb-2">
                <a href="{% url 'assistants:assistant_chat' slug=listing.assistant.slug %}" class="text-decoration-none directory-item-link-wrapper">
                    {{ listing.assistant.name }}
                </a>
            </h6>
            <p class="mb-2 text-muted">
                <i class="bi bi-building me-1"></i>{{ listing.assistant.company.name }}
            </p>
            <div class="mb-3">
                <span class="badge bg-primary bg-opacity-10 text-primary tag-badge">{{ listing.assistant.get_assistant_type_display }}</span>
                {% for category in listing.categories %}
                    <span class="badge bg-secondary tag-badge">{{ category }}</span>
                {% endfor %}
                {% for tag in listing.tags %}
                    <span class="badge bg-light text-dark border tag-badge">{{ tag }}</span>
                {% endfor %}
            </div>
        </div>

        {# Description - Added for better information #}
        <div class="col-12">
            {% with full_desc=listing.short_description|default:listing.assistant.description|default:"" %}
            <p class="mb-3 item-description text-start">
                {{ full_desc|default:"No description available."|truncatewords:15 }}
            </p>
            {% endwith %}
        </div>

        {# Rating and Action Buttons #}
        <div class="col-12 d-flex justify-content-center align-items-center">
            {# Rating Display #}
            {% with avg_rating=listing.avg_rating total_ratings=listing.total_ratings %}
                {% if avg_rating > 0 %}
                    <div class="rating-display-container me-3" id="rating-display-{{ listing.assistant.id }}">
                        {% render_stars avg_rating total_ratings %}
                    </div>
                {% else %}
                    <div class="text-muted fst-italic me-3" id="no-rating-placeholder-{{ listing.assistant.id }}">(No ratings yet)</div>
                {% endif %}
            {% endwith %}

            {# Rate Button #}
            {% if user.is_authenticated %}
                <button type="button"
                        class="btn btn-outline-secondary btn-sm rate-assistant-btn action-btn me-2"
                        data-bs-toggle="modal"
                        data-bs-target="#ratingModal"
                        data-assistant-id="{{ listing.assistant.id }}"
                        data-assistant-name="{{ listing.assistant.name|escapejs }}">
                    <i class="bi bi-star"></i> Rate
                </button>

                {# Favorite Button #}
                <button
                    class="like-button btn btn-sm p-1 {% if listing.assistant.id in saved_assistant_ids %}text-danger{% else %}text-secondary{% endif %}"
                    data-item-id="{{ listing.assistant.id }}"
                    data-item-type="assistant"
                    title="{% if listing.assistant.id in saved_assistant_ids %}Unlike{% else %}Like{% endif %}"
                    style="background: none; border: none; cursor: pointer;">
                    <i class="bi bi-heart-fill" style="font-size: 1.2rem;"></i>
                </button>
            {% endif %}
        </div>
    </div>
</div>
