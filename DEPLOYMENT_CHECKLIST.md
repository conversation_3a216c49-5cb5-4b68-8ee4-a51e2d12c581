# Deployment Checklist

Use this checklist to ensure you've completed all necessary steps for deploying your Django application to cPanel.

## Pre-Deployment

- [ ] Update `SECRET_KEY` to a secure random value
- [ ] Set `DEBUG = False` in production settings
- [ ] Configure `ALLOWED_HOSTS` with your domain name(s)
- [ ] Update database settings for MySQL
- [ ] Configure email settings
- [ ] Enable security settings (HTTPS, HSTS, etc.)
- [ ] Run tests to ensure everything works
- [ ] Collect static files locally to verify configuration
- [ ] Create a superuser account for production
- [ ] Back up your local database

## cPanel Setup

- [ ] Create a MySQL database in cPanel
- [ ] Create a database user and assign permissions
- [ ] Set up a Python application in cPanel
- [ ] Configure domain/subdomain settings

## Deployment

- [ ] Create a deployment package (zip file)
- [ ] Upload the package to cPanel
- [ ] Extract the package in the appropriate directory
- [ ] Create and configure virtual environment
- [ ] Install production dependencies
- [ ] Create and configure `.env` file with production values
- [ ] Run database migrations
- [ ] Collect static files
- [ ] Create a superuser account
- [ ] Configure `.htaccess` file
- [ ] Set appropriate file permissions
- [ ] Restart the Python application

## Post-Deployment Verification

- [ ] Verify the site loads correctly
- [ ] Check that static files are served properly
- [ ] Verify media uploads work
- [ ] Test user authentication
- [ ] Test form submissions
- [ ] Check email functionality
- [ ] Verify database connections
- [ ] Test AI/LLM integrations
- [ ] Check mobile responsiveness
- [ ] Verify SSL/HTTPS is working

## Security Checks

- [ ] Ensure debug mode is off
- [ ] Verify sensitive settings are not exposed
- [ ] Check that HTTPS redirects are working
- [ ] Verify secure cookie settings
- [ ] Test CSRF protection
- [ ] Check for exposed API keys or credentials
- [ ] Verify proper file permissions

## Performance Optimization

- [ ] Enable caching if needed
- [ ] Compress static assets
- [ ] Optimize database queries
- [ ] Configure appropriate timeouts
- [ ] Set up monitoring

## Documentation

- [ ] Update deployment documentation
- [ ] Document any custom configurations
- [ ] Create maintenance procedures
- [ ] Document backup and restore procedures
