from io import BytesIO
from django.core.files.base import ContentFile

def generate_styled_qr_with_letter_a_in_triangle(full_url, model_slug_for_filename, model_name_for_filename_prefix):
    """
    Generates a QR code with a black letter 'A' on top of a slightly larger white letter 'A' background.
    The white background is completely solid to protect the black 'A' from any contact with the QR code.
    Returns a ContentFile object or None if an error occurs.
    """
    try:
        print(f"QR Gen: Generating for {model_name_for_filename_prefix} '{model_slug_for_filename}' -> URL: {full_url}")

        # Import the function from utils.qr_generator to ensure consistent design
        from utils.qr_generator import generate_qr_with_a

        # Generate the QR code using the unified design
        qr_img = generate_qr_with_a(full_url, letter="A")

        # Save to BytesIO
        temp_buffer = BytesIO()
        qr_img.save(temp_buffer, format='PNG')
        temp_buffer.seek(0)

        # Create filename
        filename = f'{model_name_for_filename_prefix}_qr_{model_slug_for_filename}.png'

        # Return ContentFile
        return ContentFile(temp_buffer.read(), name=filename)

    except Exception as e:
        import traceback
        print(f"QR Gen Error for {model_name_for_filename_prefix} '{model_slug_for_filename}': {str(e)}")
        traceback.print_exc()
        return None
