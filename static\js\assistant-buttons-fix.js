/**
 * Assistant <PERSON><PERSON> Fix
 * Specifically targets and fixes the assistant buttons that aren't working
 */

(function() {
    // Run immediately and also after DOM is loaded to ensure it works in all cases
    fixAssistantButtons();

    document.addEventListener('DOMContentLoaded', function() {
        console.log('Assistant Buttons Fix: Initializing on DOMContentLoaded');
        fixAssistantButtons();

        // Run once after a short delay to ensure all other scripts have run
        setTimeout(fixAssistantButtons, 1000);

        // Set up a MutationObserver to watch for changes to the DOM with debounce
        let debounceTimer;
        const observer = new MutationObserver(function(mutations) {
            // Clear any existing timer
            clearTimeout(debounceTimer);

            // Set a new timer to run after a delay
            debounceTimer = setTimeout(() => {
                fixAssistantButtons();
            }, 500); // 500ms debounce
        });

        // Start observing the document with the configured parameters
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    });

    // Function to fix assistant buttons
    function fixAssistantButtons() {
        // List of specific assistant button selectors
        const buttonSelectors = [
            // Rating button
            '.rate-btn',
            'button[data-action="rate"]',
            'button.rate-assistant-btn',
            'a.rate-assistant-btn',

            // Favorite button
            '.favorite-btn',
            'button[data-action="favorite"]',
            'button.favorite-assistant-btn',
            'a.favorite-assistant-btn',

            // Share URL button
            '.share-url-btn',
            'button[data-action="share"]',
            'button.share-assistant-btn',
            'a.share-assistant-btn',

            // Reset conversation button
            '.reset-conversation-btn',
            'button[data-action="reset"]',
            'button.reset-conversation',
            'a.reset-conversation',

            // Send button
            '#send-button',
            'button[type="submit"]',
            'button.send-message-btn',
            'a.send-message-btn'
        ];

        // Join all selectors
        const selector = buttonSelectors.join(', ');

        // Find all buttons
        const buttons = document.querySelectorAll(selector);

        // Fix each button
        buttons.forEach(function(button) {
            // Skip if already fixed
            if (button.dataset.assistantButtonFixed === 'true') {
                return;
            }

            // Ensure button has proper styling
            button.style.cursor = 'pointer';
            button.style.pointerEvents = 'auto';

            // Ensure button is visible
            button.style.opacity = '1';
            button.style.visibility = 'visible';
            button.style.display = button.style.display || 'inline-block';

            // Ensure button has proper z-index
            button.style.position = button.style.position || 'relative';
            button.style.zIndex = '1050';

            // Mark as fixed
            button.dataset.assistantButtonFixed = 'true';

            // Add a direct click handler
            button.addEventListener('click', function(e) {

                // For rate button
                if (button.classList.contains('rate-btn') ||
                    button.classList.contains('rate-assistant-btn') ||
                    button.getAttribute('data-action') === 'rate') {

                    // Try to find and show the rating modal
                    const ratingModal = document.getElementById('ratingModal');
                    if (ratingModal) {
                        const bsModal = new bootstrap.Modal(ratingModal);
                        bsModal.show();
                    }
                }

                // For favorite button
                if (button.classList.contains('favorite-btn') ||
                    button.classList.contains('favorite-assistant-btn') ||
                    button.getAttribute('data-action') === 'favorite') {

                    // Try to toggle favorite status
                    const assistantId = button.getAttribute('data-assistant-id');
                    if (assistantId) {
                        toggleFavorite(assistantId);
                    }
                }

                // For share URL button
                if (button.classList.contains('share-url-btn') ||
                    button.classList.contains('share-assistant-btn') ||
                    button.getAttribute('data-action') === 'share') {

                    // Try to copy the current URL to clipboard
                    navigator.clipboard.writeText(window.location.href)
                        .then(() => {
                            alert('URL copied to clipboard!');
                        })
                        .catch(err => {
                            console.error('Could not copy URL: ', err);
                        });
                }

                // For reset conversation button
                if (button.classList.contains('reset-conversation-btn') ||
                    button.classList.contains('reset-conversation') ||
                    button.getAttribute('data-action') === 'reset') {

                    // Try to reset the conversation
                    if (confirm('Are you sure you want to reset this conversation?')) {
                        window.location.reload();
                    }
                }

                // For send button
                if (button.id === 'send-button' ||
                    button.classList.contains('send-message-btn') ||
                    (button.getAttribute('type') === 'submit' &&
                     button.closest('form') &&
                     button.closest('form').id === 'chat-form')) {

                    // Try to submit the form
                    const form = button.closest('form');
                    if (form) {
                        form.submit();
                    }
                }
            });
        });
    }

    // Helper function to toggle favorite status
    function toggleFavorite(assistantId) {
        if (!assistantId) return;

        fetch(`/assistant/toggle_favorite/${assistantId}/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Update the button appearance
                const favoriteBtn = document.querySelector('.favorite-btn');
                if (favoriteBtn) {
                    if (data.is_favorite) {
                        favoriteBtn.innerHTML = '<i class="bi bi-heart-fill"></i> Favorited';
                        favoriteBtn.classList.add('active');
                    } else {
                        favoriteBtn.innerHTML = '<i class="bi bi-heart"></i> Favorite';
                        favoriteBtn.classList.remove('active');
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error toggling favorite:', error);
        });
    }

    // Helper function to get CSRF token
    function getCsrfToken() {
        const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
        if (!csrfInput) {
            const csrfMeta = document.querySelector('meta[name="csrf-token"]');
            if (csrfMeta) return csrfMeta.getAttribute('content');
        }
        return csrfInput ? csrfInput.value : null;
    }
})();
