# Generated manually to fix the categories field conflict

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0005_merge_20250521_1925"),
    ]

    operations = [
        # First check if the categories column exists
        migrations.RunSQL(
            sql="""
            DO $$
            BEGIN
                -- Check if the categories column exists
                IF EXISTS (
                    SELECT 1
                    FROM information_schema.columns
                    WHERE table_name = 'accounts_companyinformation'
                    AND column_name = 'categories'
                ) THEN
                    -- Column exists, do nothing
                    RAISE NOTICE 'categories column already exists, skipping...';
                ELSE
                    -- Column doesn't exist, add it
                    ALTER TABLE accounts_companyinformation
                    ADD COLUMN categories text NOT NULL DEFAULT '';
                END IF;
            END
            $$;
            """,
            reverse_sql="""
            -- No reverse operation needed
            """
        ),
    ]
