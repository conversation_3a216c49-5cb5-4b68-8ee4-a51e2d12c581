"""
User Management test script to test user-related functionality.
"""

import os
import django
import uuid
import time

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.test import Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.contrib.auth import get_user_model
from accounts.models import Company, CompanyInvitation

def test_user_registration():
    """Test user registration functionality."""
    print("Testing user registration...")

    # Create a unique username
    unique_id = str(uuid.uuid4())[:8]
    username = f"testuser_{unique_id}"
    email = f"{username}@example.com"
    password = "TestPassword123!"

    # Create client
    client = Client()

    # Get the registration page
    response = client.get(reverse('accounts:register'))
    assert response.status_code == 200, "Registration page should load"

    # Submit registration form
    response = client.post(reverse('accounts:register'), {
        'username': username,
        'email': email,
        'password1': password,
        'password2': password,
    })

    # Check if registration was successful (should redirect)
    assert response.status_code in [200, 302], "Registration should be successful"

    # Check if user was created
    assert User.objects.filter(username=username).exists(), "User should be created"

    print("User registration test passed!")
    return True

def test_user_login_logout():
    """Test user login and logout functionality."""
    print("Testing user login and logout...")

    # Create a test user
    username = "login_test_user"
    password = "LoginTest123!"

    # Delete user if it already exists
    User.objects.filter(username=username).delete()

    # Create the user
    user = User.objects.create_user(username=username, password=password)

    # Create client
    client = Client()

    # Test login page
    response = client.get(reverse('accounts:login'))
    assert response.status_code == 200, "Login page should load"

    # Test login with invalid credentials
    response = client.post(reverse('accounts:login'), {
        'username': username,
        'password': 'wrong_password',
    })
    assert not response.wsgi_request.user.is_authenticated, "User should not be authenticated with wrong password"

    # Test login with valid credentials
    response = client.post(reverse('accounts:login'), {
        'username': username,
        'password': password,
    }, follow=True)
    assert response.wsgi_request.user.is_authenticated, "User should be authenticated after login"

    # Test logout (using POST method)
    response = client.post(reverse('accounts:logout'), follow=True)
    assert not response.wsgi_request.user.is_authenticated, "User should not be authenticated after logout"

    print("User login and logout test passed!")
    return True

def test_password_change():
    """Test password change functionality."""
    print("Testing password change...")

    # Create a test user
    username = "pwd_test_user"
    old_password = "OldPassword123!"
    new_password = "NewPassword123!"

    # Delete user if it already exists
    User.objects.filter(username=username).delete()

    # Create the user
    user = User.objects.create_user(username=username, password=old_password)

    # Create client and login
    client = Client()
    client.login(username=username, password=old_password)

    # Test password change page
    response = client.get(reverse('accounts:password_change'))
    assert response.status_code == 200, "Password change page should load"

    # Test password change with valid data
    response = client.post(reverse('accounts:password_change'), {
        'old_password': old_password,
        'new_password1': new_password,
        'new_password2': new_password,
    }, follow=True)  # Follow redirects

    # Should redirect to password_change_done or dashboard
    assert response.status_code == 200, "Password change should be successful"

    # Logout and try to login with new password
    client.logout()
    login_successful = client.login(username=username, password=new_password)
    assert login_successful, "Login with new password should be successful"

    print("Password change test passed!")
    return True

def test_user_profile():
    """Test user profile functionality."""
    print("Testing user profile...")

    # Create a test user
    username = "profile_test_user"
    password = "ProfileTest123!"

    # Delete user if it already exists
    User.objects.filter(username=username).delete()

    # Create the user
    user = User.objects.create_user(
        username=username,
        password=password,
        first_name="Test",
        last_name="User",
        email="<EMAIL>"
    )

    # Create client and login
    client = Client()
    client.login(username=username, password=password)

    # Test user settings page
    response = client.get(reverse('accounts:user_settings'))
    assert response.status_code == 200, "User settings page should load"

    # Test updating user profile
    response = client.post(reverse('accounts:user_settings'), {
        'form_type': 'profile',
        'first_name': 'Updated',
        'last_name': 'Name',
        'email': '<EMAIL>',
    })

    # Should redirect after successful update
    assert response.status_code in [200, 302], "Profile update should be successful"

    # Refresh user from database
    user.refresh_from_db()

    # Check if profile was updated
    assert user.first_name == 'Updated', "First name should be updated"
    assert user.last_name == 'Name', "Last name should be updated"
    assert user.email == '<EMAIL>', "Email should be updated"

    print("User profile test passed!")
    return True

def run_all_user_management_tests():
    """Run all user management tests."""
    print("Running all user management tests...")

    results = []
    results.append(test_user_registration())
    results.append(test_user_login_logout())
    results.append(test_password_change())
    results.append(test_user_profile())

    # Return True only if all tests passed
    return all(results)

if __name__ == "__main__":
    run_all_user_management_tests()
