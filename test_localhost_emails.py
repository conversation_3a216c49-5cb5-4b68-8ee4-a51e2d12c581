#!/usr/bin/env python
"""
Test script to verify that email links use localhost in DEBUG mode.
"""

import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.conf import settings
from accounts.email_utils import get_site_url
from accounts.auth_utils import store_signin_approval
from django.contrib.auth import get_user_model
from django.contrib.sites.models import Site

User = get_user_model()

def test_site_url():
    """Test that get_site_url returns localhost in DEBUG mode."""
    print("Testing get_site_url function...")
    
    # Check current DEBUG setting
    print(f"DEBUG setting: {settings.DEBUG}")
    
    # Get the site URL
    site_url = get_site_url()
    print(f"Site URL returned: {site_url}")
    
    # Check current site domain
    try:
        current_site = Site.objects.get_current()
        print(f"Current site domain: {current_site.domain}")
        print(f"Current site name: {current_site.name}")
    except Exception as e:
        print(f"Error getting current site: {e}")
    
    # Verify the result
    if settings.DEBUG:
        expected_url = "http://localhost:8000"
        if site_url == expected_url:
            print("✅ SUCCESS: Site URL correctly uses localhost in DEBUG mode")
        else:
            print(f"❌ FAILURE: Expected '{expected_url}', got '{site_url}'")
    else:
        print("ℹ️  INFO: Not in DEBUG mode, using production site domain")
    
    return site_url

def test_signin_approval_url():
    """Test that signin approval URLs use localhost in DEBUG mode."""
    print("\nTesting signin approval URL generation...")
    
    # Get or create a test user
    try:
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'is_active': True,
            }
        )
        
        if created:
            print(f"Created test user: {user.username}")
        else:
            print(f"Using existing test user: {user.username}")
        
        # Generate a signin approval token (this will create the URL internally)
        print("Generating signin approval token...")
        token = store_signin_approval(user, 1)  # 1 hour expiry
        
        # The URL is generated inside store_signin_approval using get_site_url()
        site_url = get_site_url()
        approval_url = f"{site_url}/accounts/approve-signin/{token}/"
        
        print(f"Generated approval URL: {approval_url}")
        
        # Verify the URL uses localhost in DEBUG mode
        if settings.DEBUG:
            if approval_url.startswith("http://localhost:8000"):
                print("✅ SUCCESS: Signin approval URL correctly uses localhost in DEBUG mode")
            else:
                print(f"❌ FAILURE: Signin approval URL should start with 'http://localhost:8000'")
        else:
            print("ℹ️  INFO: Not in DEBUG mode, using production domain")
            
        return approval_url
        
    except Exception as e:
        print(f"❌ ERROR: Failed to test signin approval URL: {e}")
        return None

def main():
    """Run all tests."""
    print("=" * 60)
    print("TESTING EMAIL LOCALHOST CONFIGURATION")
    print("=" * 60)
    
    # Test 1: Basic site URL function
    site_url = test_site_url()
    
    # Test 2: Signin approval URL generation
    approval_url = test_signin_approval_url()
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    if settings.DEBUG:
        if site_url == "http://localhost:8000":
            print("✅ get_site_url() correctly returns localhost")
        else:
            print("❌ get_site_url() does not return localhost")
            
        if approval_url and approval_url.startswith("http://localhost:8000"):
            print("✅ Signin approval URLs correctly use localhost")
        else:
            print("❌ Signin approval URLs do not use localhost")
            
        print("\n🎯 RESULT: Email links should now point to localhost:8000 in development")
    else:
        print("ℹ️  Running in production mode - using site domain from database")

if __name__ == '__main__':
    main()
