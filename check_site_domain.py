#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to check the current site domain configuration in the Django Site framework.
This helps verify that the site domain is set correctly for activation emails and other site-related URLs.
"""

import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.sites.models import Site
from django.conf import settings
from accounts.email_utils import get_site_url

def check_site_domain():
    """
    Check the current site domain configuration.
    
    Returns:
        dict: Information about the current site configuration
    """
    try:
        # Get the current site
        site = Site.objects.get_current()
        
        # Get the site URL from the email_utils function
        site_url = get_site_url()
        
        # Check if CSRF_TRUSTED_ORIGINS includes the site domain
        csrf_trusted_origins = getattr(settings, 'CSRF_TRUSTED_ORIGINS', [])
        site_domain_in_csrf = any(site.domain in origin for origin in csrf_trusted_origins)
        
        # Check if ALLOWED_HOSTS includes the site domain
        allowed_hosts = getattr(settings, 'ALLOWED_HOSTS', [])
        site_domain_in_allowed_hosts = site.domain in allowed_hosts or '*' in allowed_hosts
        
        # Check if DEBUG is enabled
        debug_enabled = getattr(settings, 'DEBUG', False)
        
        # Check if SECURE_SSL_REDIRECT is enabled
        ssl_redirect = getattr(settings, 'SECURE_SSL_REDIRECT', False)
        
        return {
            'site_id': site.id,
            'site_domain': site.domain,
            'site_name': site.name,
            'site_url': site_url,
            'debug_enabled': debug_enabled,
            'ssl_redirect': ssl_redirect,
            'site_domain_in_csrf': site_domain_in_csrf,
            'site_domain_in_allowed_hosts': site_domain_in_allowed_hosts,
            'csrf_trusted_origins': csrf_trusted_origins,
            'allowed_hosts': allowed_hosts,
        }
    except Exception as e:
        return {
            'error': str(e)
        }

def main():
    """Main function to check the site domain configuration."""
    print("Checking site domain configuration...")
    
    # Check the site domain
    info = check_site_domain()
    
    if 'error' in info:
        print(f"Error checking site domain: {info['error']}")
        return
    
    # Print the information
    print("\nSite Configuration:")
    print(f"Site ID: {info['site_id']}")
    print(f"Site Domain: {info['site_domain']}")
    print(f"Site Name: {info['site_name']}")
    print(f"Site URL: {info['site_url']}")
    print(f"DEBUG Enabled: {info['debug_enabled']}")
    print(f"SSL Redirect: {info['ssl_redirect']}")
    print(f"Site Domain in CSRF_TRUSTED_ORIGINS: {info['site_domain_in_csrf']}")
    print(f"Site Domain in ALLOWED_HOSTS: {info['site_domain_in_allowed_hosts']}")
    
    # Print recommendations
    print("\nRecommendations:")
    
    if info['site_domain'] == 'example.com':
        print("- Update the site domain to your actual domain (e.g., '24seven.site')")
        print("  Run: python update_site_domain.py 24seven.site")
    
    if not info['site_domain_in_csrf'] and not info['debug_enabled']:
        print(f"- Add '{info['site_domain']}' to CSRF_TRUSTED_ORIGINS in settings.py")
    
    if not info['site_domain_in_allowed_hosts'] and '*' not in info['allowed_hosts']:
        print(f"- Add '{info['site_domain']}' to ALLOWED_HOSTS in settings.py")
    
    if info['debug_enabled'] and 'production' in info['site_domain']:
        print("- Disable DEBUG in settings.py for production")
    
    if not info['ssl_redirect'] and 'production' in info['site_domain']:
        print("- Enable SECURE_SSL_REDIRECT in settings.py for production")

if __name__ == "__main__":
    main()
