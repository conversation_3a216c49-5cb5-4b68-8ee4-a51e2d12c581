# Generated by Django 5.2.1 on 2025-05-20 05:56

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("accounts", "0001_initial"),
        ("assistants", "0001_initial"),
        ("auth", "0012_alter_user_first_name_max_length"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="registrationlink",
            name="accessible_folders",
            field=models.ManyToManyField(
                blank=True,
                help_text="Select folders the user joining via this link should have access to.",
                related_name="registration_links",
                to="assistants.assistantfolder",
            ),
        ),
        migrations.AddField(
            model_name="registrationlink",
            name="company",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="registration_links",
                to="accounts.company",
            ),
        ),
        migrations.AddField(
            model_name="registrationlink",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="created_registration_links",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="registrationlink",
            name="intended_group",
            field=models.ForeignKey(
                blank=True,
                help_text="The role (group) the user will be assigned upon joining via this link.",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="registration_links",
                to="auth.group",
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="profile",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterUniqueTogether(
            name="membership",
            unique_together={("user", "company")},
        ),
    ]
