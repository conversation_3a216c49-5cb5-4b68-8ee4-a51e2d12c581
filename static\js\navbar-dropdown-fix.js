/**
 * Navbar Dropdown Fix JavaScript
 * Ensures dropdown menus in the navbar work correctly without expanding the header
 */

document.addEventListener('DOMContentLoaded', function() {
    // Fix dropdown behavior
    fixNavbarDropdowns();
    
    // Also run after a short delay to ensure all elements are loaded
    setTimeout(fixNavbarDropdowns, 500);
});

/**
 * Fix navbar dropdown behavior
 */
function fixNavbarDropdowns() {
    // Get all dropdown toggles in the navbar
    const dropdownToggles = document.querySelectorAll('.navbar .dropdown-toggle');
    
    // Process each dropdown toggle
    dropdownToggles.forEach(toggle => {
        // Get the dropdown menu associated with this toggle
        const dropdownMenu = toggle.nextElementSibling;
        if (!dropdownMenu || !dropdownMenu.classList.contains('dropdown-menu')) {
            return;
        }
        
        // Ensure the dropdown menu has the correct classes
        dropdownMenu.classList.add('dropdown-menu-end');
        
        // Ensure the parent has position relative
        const dropdownParent = toggle.parentElement;
        if (dropdownParent && dropdownParent.classList.contains('dropdown')) {
            dropdownParent.style.position = 'relative';
        }
        
        // Fix dropdown behavior on mobile
        if (window.innerWidth <= 991.98) {
            // On mobile, we want to ensure the dropdown appears within the collapsed navbar
            // This is handled by the CSS, but we need to ensure the toggle works correctly
            toggle.addEventListener('click', function(e) {
                // Prevent the default action (which would be to follow the href)
                e.preventDefault();
                
                // Toggle the show class on the dropdown menu
                dropdownMenu.classList.toggle('show');
                
                // Update aria-expanded attribute
                const isExpanded = dropdownMenu.classList.contains('show');
                toggle.setAttribute('aria-expanded', isExpanded);
            });
        }
    });
    
    // Fix dropdown behavior on window resize
    window.addEventListener('resize', function() {
        // Reset dropdown menus when window is resized
        document.querySelectorAll('.navbar .dropdown-menu.show').forEach(menu => {
            menu.classList.remove('show');
        });
        
        // Reset dropdown toggles
        document.querySelectorAll('.navbar .dropdown-toggle[aria-expanded="true"]').forEach(toggle => {
            toggle.setAttribute('aria-expanded', 'false');
        });
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        // Check if the click was outside any dropdown
        const isOutsideDropdown = !e.target.closest('.navbar .dropdown');
        
        if (isOutsideDropdown) {
            // Close all open dropdowns
            document.querySelectorAll('.navbar .dropdown-menu.show').forEach(menu => {
                menu.classList.remove('show');
            });
            
            // Reset dropdown toggles
            document.querySelectorAll('.navbar .dropdown-toggle[aria-expanded="true"]').forEach(toggle => {
                toggle.setAttribute('aria-expanded', 'false');
            });
        }
    });
}
