# Serve static and media files directly
<IfModule mod_rewrite.c>
    RewriteEngine On

    # Let Whitenoise handle static files
    # Note: These rules are a fallback in case Whitenoise middleware doesn't catch the request
    RewriteCond %{REQUEST_URI} ^/static/
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule ^(.*)$ $1 [L]

    # Alternative static files rule if the above doesn't work
    RewriteCond %{REQUEST_URI} ^/static/
    RewriteRule ^static/(.*)$ staticfiles/$1 [L]

    # Serve media files directly
    RewriteCond %{REQUEST_URI} ^/media/
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule ^(.*)$ $1 [L]

    # Alternative media files rule
    RewriteCond %{REQUEST_URI} ^/media/
    RewriteRule ^media/(.*)$ media/$1 [L]

    # Pass all other requests to Passenger
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(.*)$ passenger_wsgi.py/$1 [QSA,L]
</IfModule>

# Compress text files
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
</IfModule>

# Set expiration for static files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>

# PHP settings
<IfModule mod_php7.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_time 300
</IfModule>
