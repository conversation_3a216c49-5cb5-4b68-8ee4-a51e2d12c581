# Generated by Django 4.2.21 (modified for compatibility)

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("accounts", "0002_initial"),
        ("assistants", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="CompanyCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("slug", models.SlugField(blank=True, max_length=110, unique=True)),
            ],
            options={
                "verbose_name": "company category",
                "verbose_name_plural": "company categories",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="DirectorySettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "featured_scroll_direction",
                    models.Char<PERSON>ield(
                        choices=[
                            ("horizontal", "Horizontal"),
                            ("vertical", "Vertical"),
                        ],
                        default="horizontal",
                        help_text="Scroll direction for featured assistants carousel.",
                        max_length=10,
                    ),
                ),
                (
                    "featured_transition_effect",
                    models.CharField(
                        choices=[
                            ("slide", "Slide"),
                            ("fade", "Fade"),
                            ("cube", "Cube"),
                            ("coverflow", "Coverflow"),
                            ("flip", "Flip"),
                        ],
                        default="slide",
                        help_text="Transition effect for featured assistants carousel.",
                        max_length=10,
                    ),
                ),
                (
                    "featured_visible_count",
                    models.PositiveIntegerField(
                        default=1,
                        help_text="Number of featured assistants visible at a time.",
                        validators=[django.core.validators.MinValueValidator(1)],
                    ),
                ),
                (
                    "featured_autoplay",
                    models.BooleanField(
                        default=True, help_text="Enable autoplay for featured carousel."
                    ),
                ),
                (
                    "featured_autoplay_delay",
                    models.PositiveIntegerField(
                        default=5000,
                        help_text="Animation speed in milliseconds. This is how long it takes for the specified number of items (visible count) to cross the screen.",
                        validators=[django.core.validators.MinValueValidator(1000)],
                    ),
                ),
                (
                    "hide_standard_tier_assistants",
                    models.BooleanField(
                        default=False,
                        help_text="If checked, assistants with the 'Standard' tier will not be shown in the public directory.",
                        verbose_name="Hide Standard Tier Assistants",
                    ),
                ),
                (
                    "hide_standard_tier_companies",
                    models.BooleanField(
                        default=False,
                        help_text="If checked, companies with the 'Standard' tier will not be shown in the public directory.",
                        verbose_name="Hide Standard Tier Companies",
                    ),
                ),
            ],
            options={
                "verbose_name": "Directory Settings",
                "verbose_name_plural": "Directory Settings",
            },
        ),
        migrations.CreateModel(
            name="AssistantListing",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_listed", models.BooleanField(default=True)),
                ("short_description", models.TextField(blank=True)),
                ("long_description", models.TextField(blank=True)),
                ("categories", models.JSONField(blank=True, default=list)),
                ("tags", models.JSONField(blank=True, default=list)),
                ("capabilities", models.JSONField(blank=True, default=list)),
                (
                    "avg_rating",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=3),
                ),
                ("total_ratings", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "assistant",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="listing",
                        to="assistants.assistant",
                    ),
                ),
            ],
            options={
                "verbose_name": "assistant listing",
                "verbose_name_plural": "assistant listings",
                "ordering": ["-avg_rating", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="CompanyListing",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_listed", models.BooleanField(default=True)),
                ("featured", models.BooleanField(default=False)),
                ("description", models.TextField(blank=True)),
                ("website", models.URLField(blank=True)),
                ("social_links", models.JSONField(blank=True, default=dict)),
                ("tags", models.JSONField(blank=True, default=list)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "avg_rating",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=3),
                ),
                ("total_ratings", models.IntegerField(default=0)),
                (
                    "categories",
                    models.ManyToManyField(
                        blank=True,
                        related_name="company_listings",
                        to="directory.companycategory",
                    ),
                ),
                (
                    "company",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="listing",
                        to="accounts.company",
                    ),
                ),
            ],
            options={
                "verbose_name": "company listing",
                "verbose_name_plural": "company listings",
                "ordering": ["-featured", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="FavoriteFolder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "item_type",
                    models.CharField(
                        choices=[("company", "Company"), ("assistant", "Assistant")],
                        help_text="Specifies whether this folder is for companies or assistants.",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="favorite_folders",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "favorite folder",
                "verbose_name_plural": "favorite folders",
                "ordering": ["name"],
                "unique_together": {("user", "name", "item_type")},
            },
        ),
        migrations.CreateModel(
            name="DirectoryRating",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "rating",
                    models.IntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ]
                    ),
                ),
                (
                    "comment",
                    models.TextField(
                        blank=True, help_text="Optional comment about the rating"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "assistant",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="directory_ratings",
                        to="assistants.assistant",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="directory_ratings",
                        to="accounts.company",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="directory_ratings",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "directory rating",
                "verbose_name_plural": "directory ratings",
                "ordering": ["-created_at"],
                "constraints": [
                    models.CheckConstraint(
                        check=models.Q(
                            models.Q(
                                ("assistant__isnull", False), ("company__isnull", True)
                            ),
                            models.Q(
                                ("assistant__isnull", True), ("company__isnull", False)
                            ),
                            _connector="OR",
                        ),
                        name="rating_must_link_to_one_item",
                    )
                ],
                "unique_together": {("user", "assistant"), ("user", "company")},
            },
        ),
        migrations.CreateModel(
            name="SavedItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "item_type",
                    models.CharField(
                        choices=[("company", "Company"), ("assistant", "Assistant")],
                        max_length=20,
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "assistant",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="assistants.assistant",
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="accounts.company",
                    ),
                ),
                (
                    "folder",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="saved_items",
                        to="directory.favoritefolder",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="saved_items",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "saved item",
                "verbose_name_plural": "saved items",
                "ordering": ["-created_at"],
                "unique_together": {("user", "assistant"), ("user", "company")},
            },
        ),
    ]
