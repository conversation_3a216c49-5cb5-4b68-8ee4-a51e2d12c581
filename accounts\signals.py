from django.db.models.signals import post_save
from django.dispatch import receiver
from django.urls import reverse
from django.contrib.auth.models import Permission, Group # Import Group
from django.contrib.contenttypes.models import ContentType # Import ContentType
from guardian.shortcuts import assign_perm # Import assign_perm
from .models import Company
from .utils import generate_qr_code
from .permissions import OWNER_PERMS_COMPANY # Import owner permissions list


@receiver(post_save, sender=Company)
def ensure_company_information_exists(sender, instance, created, **kwargs):
    """
    Ensures that a CompanyInformation record is created for every new Company.
    """
    if created:
        if not hasattr(instance, 'info') or instance.info is None:
            CompanyInformation.objects.create(company=instance)
            print(f"Created CompanyInformation for new Company: {instance.name}")
        # else:
            # print(f"CompanyInformation already exists or was created for Company: {instance.name}") # Optional: for verbosity


# --- New Signal Handler for RegistrationLink ---
from .models import RegistrationLink, CompanyInformation # Import RegistrationLink model and CompanyInformation
from directory.models import CompanyListing # Import CompanyListing for directory sync
from directory.qr_utils import generate_styled_qr_with_letter_a_in_triangle # New QR util
from django.contrib.sites.models import Site
from django.conf import settings

@receiver(post_save, sender=RegistrationLink)
def registration_link_post_save(sender, instance, created, **kwargs):
    """
    Generate QR code for a RegistrationLink after it's saved.
    """
    from utils.qr_generator import generate_model_qr_code

    # Only generate if created or if qr_code field is empty
    if created or not instance.qr_code:
        try:
            # Get the absolute URL for the link
            url_path = instance.get_absolute_url() # This method already exists on the model
            if url_path: # Ensure URL was generated
                # Generate and save the QR code image to the qr_code field
                success = generate_model_qr_code(instance, url_path, field_name='qr_code')
                if success:
                    # Save again ONLY if QR code was successfully generated
                    # Use update_fields to prevent recursive signal calls if this signal is triggered again
                    instance.save(update_fields=['qr_code'])
                    print(f"Successfully generated QR code for RegistrationLink: {instance.token}")
                else:
                    print(f"Failed to generate QR code for RegistrationLink: {instance.token}")
            else:
                print(f"Could not generate URL for RegistrationLink: {instance.token}, skipping QR code generation.")
        except Exception as e:
            # Catch potential errors like URL reversing failure or QR generation issues
            print(f"Error generating QR code in registration_link_post_save signal for {instance.token}: {e}")


def _update_company_listing(company):
    """
    Helper function to synchronize a Company's listing status.
    """
    should_be_listed = False
    company_info = None

    try:
        company_info = company.info
        if company_info:
            should_be_listed = company.is_active and company_info.list_in_directory
        else:
            print(f"Warning: Company '{company.name}' (ID: {company.id}) has no CompanyInformation. Cannot sync listing status.")
            return
    except Company.info.RelatedObjectDoesNotExist:
        print(f"Warning: CompanyInformation does not exist for Company '{company.name}' (ID: {company.id}). Cannot sync listing status.")
        return
    except AttributeError:
        print(f"Warning: Company '{company.name}' (ID: {company.id}) attribute 'info' missing. Cannot sync listing status.")
        return

    listing, listing_created = CompanyListing.objects.get_or_create(
        company=company,
        defaults={
            'description': company_info.description if company_info else '',
            'website': company_info.website if company_info else '',
            'is_listed': should_be_listed
        }
    )

    if listing_created:
        print(f"Created CompanyListing for '{company.name}'. is_listed set to {listing.is_listed}, Description: '{company_info.description[:50] if company_info else ''}...', Website: '{company_info.website if company_info else ''}'.")
    else: # If not created, check for any changes and update
        updated_fields = []
        if listing.is_listed != should_be_listed:
            listing.is_listed = should_be_listed
            updated_fields.append('is_listed')

        # company_info should be valid here due to earlier checks in the function
        if company_info:
            if listing.description != company_info.description:
                listing.description = company_info.description
                updated_fields.append('description')
            if listing.website != company_info.website:
                listing.website = company_info.website
                updated_fields.append('website')

        if updated_fields:
            listing.save(update_fields=updated_fields)
            print(f"Updated CompanyListing for '{company.name}'. Fields updated: {updated_fields}.")
        # else:
            # print(f"No updates needed for CompanyListing of '{company.name}'.") # Optional: for verbosity

@receiver(post_save, sender=Company)
def company_post_save_receiver(sender, instance, created, **kwargs):
    """
    Handles post_save for Company model.
    Assigns permissions for new companies and synchronizes company listing.
    Styled QR code generation is handled by a separate signal: company_post_save_generate_styled_qr.
    """
    # Handles permission assignments for new companies.
    # Original QR code generation logic has been moved to company_post_save_generate_styled_qr.
    # This function now focuses on permissions and directory listing synchronization.
    if created:
        owner = instance.owner
        if owner:
            print(f"Assigning Owner permissions for new Company '{instance.name}' to User '{owner.username}'")
            for perm_codename in OWNER_PERMS_COMPANY:
                try:
                    assign_perm(perm_codename, owner, instance)
                except Permission.DoesNotExist:
                    print(f"  Warning: Permission '{perm_codename}' not found during owner assignment. Skipping.")
                except Exception as e:
                    print(f"  Error assigning perm '{perm_codename}' to owner '{owner.username}' for new company '{instance.name}': {e}")
        else:
             print(f"  Warning: New Company '{instance.name}' (ID: {instance.id}) was created without an owner. Cannot assign owner permissions.")
        try:
            admin_group = Group.objects.get(name='Company Administrators')
            member_group = Group.objects.get(name='Company Members')
            groups_to_assign = [admin_group, member_group]
            company_level_perms = ['accounts.manage_company_assistants']
            for group in groups_to_assign:
                for perm_name in company_level_perms:
                    try:
                        assign_perm(perm_name, group, instance)
                        print(f"Assigned Company perm '{perm_name}' for Company ID {instance.id} to Group '{group.name}'")
                    except Permission.DoesNotExist:
                         print(f"  Warning: Permission '{perm_name}' not found. Skipping assignment for Group '{group.name}'.")
                    except Exception as e:
                         print(f"  Error assigning company perm '{perm_name}' to group '{group.name}' for company {instance.id}: {e}")
        except Group.DoesNotExist as e:
            print(f"Warning: Could not find required group during company permission assignment: {e}.")
        except Exception as e:
            print(f"Error assigning default group permissions to company {instance.id}: {e}")

    # --- QR Code generation is now handled by company_post_save_generate_styled_qr ---
    # Call the refactored listing sync logic
    _update_company_listing(instance)

@receiver(post_save, sender=CompanyInformation)
def company_info_post_save_receiver(sender, instance, created, **kwargs):
    """
    Handles post_save for CompanyInformation model.
    Calls company listing synchronization.
    The 'instance' here is a CompanyInformation object.
    """
    # The _update_company_listing function correctly handles
    # the synchronization logic using the related Company object.
    try:
        _update_company_listing(instance.company)
    except Company.DoesNotExist:
        # This can happen if the CompanyInformation object's 'company' field is not set
        # or points to a non-existent Company. This should ideally not occur
        # if data integrity is maintained (e.g., OneToOneField(Company, on_delete=models.CASCADE))
        print(f"Error in company_info_post_save_receiver: CompanyInformation (ID: {instance.pk}) refers to a non-existent Company. Cannot sync listing.")
    except Exception as e:
        # Catch any other unexpected errors from _update_company_listing
        company_id_str = f"Company ID {instance.company_id}" if hasattr(instance, 'company_id') else "an unknown company"
        print(f"Error during _update_company_listing for CompanyInformation (ID: {instance.pk}) related to {company_id_str}: {e}")

# The sync_company_listing_status function is now replaced by the helper _update_company_listing


@receiver(post_save, sender=Company)
def company_post_save_generate_qr(sender, instance, created, **kwargs):
    """
    Generates a QR code with an 'A' in the center for the Company if it has a slug and
    either the QR code is missing or the slug has changed.
    The QR code points to the company's public detail page.
    """
    from utils.qr_generator import generate_model_qr_code
    import logging
    logger = logging.getLogger(__name__)

    # Always generate a slug if it doesn't exist
    if not instance.slug:
        from django.utils.text import slugify
        try:
            instance.slug = slugify(instance.name)
            # Save without triggering this signal again
            Company.objects.filter(pk=instance.pk).update(slug=instance.slug)
            logger.info(f"Generated slug '{instance.slug}' for Company '{instance.name}' (PK: {instance.pk})")
        except Exception as e:
            logger.error(f"Error generating slug for Company '{instance.name}' (PK: {instance.pk}): {str(e)}")
            return

    regenerate_qr = False
    if created:
        regenerate_qr = True
        logger.info(f"New company '{instance.name}', preparing to generate QR code.")
    elif not instance.qr_code:
        regenerate_qr = True
        logger.info(f"Company '{instance.name}' is missing QR code, preparing to generate QR.")
    else:
        if instance.qr_code.name and instance.slug not in instance.qr_code.name:
            regenerate_qr = True
            logger.info(f"Company '{instance.name}' QR filename '{instance.qr_code.name}' does not match slug '{instance.slug}'. Regenerating QR.")

    if regenerate_qr:
        try:
            # Generate QR code using the company's detail URL
            url_path = reverse('accounts:public_company_detail', kwargs={'slug': instance.slug})
            success = generate_model_qr_code(instance, url_path, field_name='qr_code')

            if success:
                # Save the company with the new QR code
                Company.objects.filter(pk=instance.pk).update(qr_code=instance.qr_code, updated_at=instance.updated_at)
                logger.info(f"Successfully generated and saved QR code for Company: {instance.name}")
            else:
                logger.error(f"Failed to generate QR code for Company: {instance.name}")

        except Exception as e:
            import traceback
            logger.error(f"Error in company_post_save_generate_qr for Company '{instance.name}': {str(e)}")
            logger.error(traceback.format_exc())
