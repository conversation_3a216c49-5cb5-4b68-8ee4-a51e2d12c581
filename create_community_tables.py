import os
import django
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

print("Starting script to create community-related tables...")

# SQL to create the assistants_communitycontext table
communitycontext_sql = """
CREATE TABLE IF NOT EXISTS "assistants_communitycontext" (
    "id" serial NOT NULL PRIMARY KEY,
    "title" varchar(255) NULL,
    "text_content" text NOT NULL,
    "keywords" jsonb NOT NULL,
    "is_active" boolean NOT NULL,
    "times_used" integer NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "updated_at" timestamp with time zone NOT NULL,
    "assistant_id" integer NOT NULL REFERENCES "assistants_assistant" ("id") DEFERRABLE INITIALLY DEFERRED,
    "created_by_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# SQL to create the assistants_flaggedquestion table
flaggedquestion_sql = """
CREATE TABLE IF NOT EXISTS "assistants_flaggedquestion" (
    "id" serial NOT NULL PRIMARY KEY,
    "question" text NOT NULL,
    "notes" text NOT NULL,
    "is_resolved" boolean NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "resolved_at" timestamp with time zone NULL,
    "assistant_id" integer NOT NULL REFERENCES "assistants_assistant" ("id") DEFERRABLE INITIALLY DEFERRED,
    "created_by_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
    "resolved_by_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# SQL to create the assistants_contextimage table
contextimage_sql = """
CREATE TABLE IF NOT EXISTS "assistants_contextimage" (
    "id" serial NOT NULL PRIMARY KEY,
    "image" varchar(100) NOT NULL,
    "caption" varchar(255) NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "context_id" integer NOT NULL REFERENCES "assistants_communitycontext" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# SQL to create the assistants_contextupvote table
contextupvote_sql = """
CREATE TABLE IF NOT EXISTS "assistants_contextupvote" (
    "id" serial NOT NULL PRIMARY KEY,
    "created_at" timestamp with time zone NOT NULL,
    "context_id" integer NOT NULL REFERENCES "assistants_communitycontext" ("id") DEFERRABLE INITIALLY DEFERRED,
    "user_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT "assistants_contextupvote_user_id_context_id_unique" UNIQUE ("user_id", "context_id")
);
"""

# SQL to create indexes
indexes_sql = """
CREATE INDEX IF NOT EXISTS "assistants_communitycontext_assistant_id_created_at_idx" ON "assistants_communitycontext" ("assistant_id", "created_at");
CREATE INDEX IF NOT EXISTS "assistants_communitycontext_is_active_idx" ON "assistants_communitycontext" ("is_active");
CREATE INDEX IF NOT EXISTS "assistants_communitycontext_created_by_id_idx" ON "assistants_communitycontext" ("created_by_id");

CREATE INDEX IF NOT EXISTS "assistants_flaggedquestion_assistant_id_created_at_idx" ON "assistants_flaggedquestion" ("assistant_id", "created_at");
CREATE INDEX IF NOT EXISTS "assistants_flaggedquestion_is_resolved_idx" ON "assistants_flaggedquestion" ("is_resolved");
CREATE INDEX IF NOT EXISTS "assistants_flaggedquestion_created_by_id_idx" ON "assistants_flaggedquestion" ("created_by_id");
CREATE INDEX IF NOT EXISTS "assistants_flaggedquestion_resolved_by_id_idx" ON "assistants_flaggedquestion" ("resolved_by_id");

CREATE INDEX IF NOT EXISTS "assistants_contextimage_context_id_idx" ON "assistants_contextimage" ("context_id");

CREATE INDEX IF NOT EXISTS "assistants_contextupvote_context_id_idx" ON "assistants_contextupvote" ("context_id");
CREATE INDEX IF NOT EXISTS "assistants_contextupvote_user_id_idx" ON "assistants_contextupvote" ("user_id");
"""

# Execute the SQL
try:
    with connection.cursor() as cursor:
        print("Creating assistants_communitycontext table...")
        cursor.execute(communitycontext_sql)
        print("assistants_communitycontext table created successfully!")
        
        print("Creating assistants_flaggedquestion table...")
        cursor.execute(flaggedquestion_sql)
        print("assistants_flaggedquestion table created successfully!")
        
        print("Creating assistants_contextimage table...")
        cursor.execute(contextimage_sql)
        print("assistants_contextimage table created successfully!")
        
        print("Creating assistants_contextupvote table...")
        cursor.execute(contextupvote_sql)
        print("assistants_contextupvote table created successfully!")
        
        print("Creating indexes...")
        cursor.execute(indexes_sql)
        print("Indexes created successfully!")
    
    print("All tables created successfully!")
except Exception as e:
    print(f"Error creating tables: {e}")
    sys.exit(1)
