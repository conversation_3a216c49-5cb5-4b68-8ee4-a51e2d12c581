from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    help = 'Checks and fixes the content_contentimage table structure'

    def handle(self, *args, **options):
        self.stdout.write('Checking content_contentimage table structure...')
        
        # Check if the table exists
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'content_contentimage'
                );
            """)
            table_exists = cursor.fetchone()[0]
            
            if table_exists:
                self.stdout.write(self.style.SUCCESS('The content_contentimage table exists!'))
            else:
                self.stdout.write(self.style.WARNING('The content_contentimage table does NOT exist!'))
                
                # Create the table
                self.stdout.write('Creating content_contentimage table...')
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS "content_contentimage" (
                        "id" serial NOT NULL PRIMARY KEY,
                        "image" varchar(100) NOT NULL,
                        "alt_text" varchar(200) NOT NULL,
                        "created_at" timestamp with time zone NOT NULL,
                        "content_id" integer NOT NULL REFERENCES "content_content" ("id") DEFERRABLE INITIALLY DEFERRED
                    );
                """)
                self.stdout.write(self.style.SUCCESS('Table created successfully!'))
                
                # Create indexes
                self.stdout.write('Creating indexes...')
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS "content_contentimage_content_id_idx" 
                    ON "content_contentimage" ("content_id");
                """)
                self.stdout.write(self.style.SUCCESS('Indexes created successfully!'))
                
        self.stdout.write(self.style.SUCCESS('Table check and fix completed successfully!'))
