from django.urls import path
from django.views.generic.base import RedirectView
from . import views # Import the views now

app_name = 'directory'

# All core directory functionality (search, public profiles) is handled
# This urls.py handles the public directory listings.
urlpatterns = [
    # Directory Listings
    path('companies/', views.CompanyDirectoryListView.as_view(), name='company_list'),
    path('assistants/', views.AssistantDirectoryListView.as_view(), name='assistant_list'),
    # Redirect to community assistants list in assistants app
    path('community-assistants/', RedirectView.as_view(pattern_name='assistants:community_assistants_list'), name='community_assistant_list'),

    # Favorites / Saved Items
    path('favorites/', views.MyFavoritesListView.as_view(), name='my_favorites'),
    path('favorites/toggle/', views.toggle_saved_item, name='toggle_saved_item'), # Initial interaction (POST/GET) -> returns options or unfavorites
    path('favorites/save-no-folder/', views.save_item_no_folder, name='save_item_no_folder'), # POST
    path('favorites/create-folder-save/', views.create_folder_and_save, name='create_folder_and_save'), # POST
    path('favorites/create-folder/', views.create_folder, name='create_folder'), # POST - Create folder without saving an item
    path('favorites/add-to-folder/', views.add_item_to_folder, name='add_item_to_folder'), # POST
    path('favorites/list-folders/', views.list_folders_for_item_type, name='list_folders_for_item_type'), # GET
    path('favorites/folder/<int:folder_id>/edit/', views.edit_favorite_folder, name='edit_favorite_folder'), # POST
    path('favorites/folder/<int:folder_id>/delete/', views.delete_favorite_folder, name='delete_favorite_folder'), # POST
    path('favorites/filter-by-folder/', views.filter_favorites_by_folder, name='filter_favorites_by_folder'), # GET for AJAX filtering

    # Rating Endpoints
    path('rate-assistant/<int:assistant_id>/', views.rate_assistant_view, name='rate_assistant'), # POST endpoint for submitting assistant rating
    path('rate-company/', views.rate_company_view, name='rate_company'), # POST endpoint for submitting company rating
    path('render-stars/assistant/<int:assistant_id>/', views.get_rendered_stars_view, name='render_stars_partial'), # GET endpoint for fetching assistant stars HTML (Added /assistant/)
    path('render-stars/company/<int:company_id>/', views.get_rendered_company_stars_view, name='render_company_stars_partial'), # GET endpoint for fetching company stars HTML

    # Directory Settings
    path('settings/', views.directory_settings_view, name='settings'), # Added URL for settings view
    path('settings/toggle-company-filter/', views.toggle_company_filter, name='toggle_company_filter'), # Toggle company filter
    path('settings/toggle-assistant-filter/', views.toggle_assistant_filter, name='toggle_assistant_filter'), # Toggle assistant filter

    # Add path for search view if it's meant to be part of this app
    # path('search/', views.search_view, name='search'),
]
