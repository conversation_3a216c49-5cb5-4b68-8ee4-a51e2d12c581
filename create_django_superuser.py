import os
import django
from django.contrib.auth import get_user_model

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Get the User model
User = get_user_model()

# Superuser details
username = 'admin'
email = '<EMAIL>'
password = 'admin123'  # You should change this to a secure password

# Check if the user already exists
if User.objects.filter(username=username).exists():
    print(f"User '{username}' already exists.")
else:
    # Create the superuser
    User.objects.create_superuser(username=username, email=email, password=password)
    print(f"Superuser '{username}' created successfully.")
    print(f"Email: {email}")
    print(f"Password: {password}")
    print("\nYou can now log in to the admin interface at http://127.0.0.1:8000/admin/")
