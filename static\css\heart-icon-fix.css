/**
 * Heart Icon Fix
 * This CSS ensures all heart icons are white by default and only turn pink/red when liked
 */

/* GLOBAL RESET - Make ALL heart icons white by default */
.like-button i,
.btn-like i,
.btn-favorite i,
.favorite-button i,
.like-button svg,
.btn-like svg,
.btn-favorite svg,
.favorite-button svg,
.like-button .bi-heart,
.btn-like .bi-heart,
.btn-favorite .bi-heart,
.favorite-button .bi-heart,
.like-button .bi-heart-fill,
.btn-like .bi-heart-fill,
.btn-favorite .bi-heart-fill,
.favorite-button .bi-heart-fill {
  color: #ffffff !important;
  fill: #ffffff !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5)) !important;
}

/* ONLY liked hearts should be pink/red */
.like-button.text-danger i,
.btn-like.text-danger i,
.btn-favorite.text-danger i,
.favorite-button.text-danger i,
.like-button.text-danger svg,
.btn-like.text-danger svg,
.btn-favorite.text-danger svg,
.favorite-button.text-danger svg,
.like-button.text-danger .bi-heart,
.btn-like.text-danger .bi-heart,
.btn-favorite.text-danger .bi-heart,
.favorite-button.text-danger .bi-heart,
.like-button.text-danger .bi-heart-fill,
.btn-like.text-danger .bi-heart-fill,
.btn-favorite.text-danger .bi-heart-fill,
.favorite-button.text-danger .bi-heart-fill {
  color: #ff3366 !important;
  fill: #ff3366 !important;
  filter: drop-shadow(0 0 3px rgba(255, 51, 102, 0.4)) !important;
}

/* Dark mode - ensure the same behavior */
[data-theme="dark"] .like-button i,
[data-theme="dark"] .btn-like i,
[data-theme="dark"] .btn-favorite i,
[data-theme="dark"] .favorite-button i,
[data-theme="dark"] .like-button svg,
[data-theme="dark"] .btn-like svg,
[data-theme="dark"] .btn-favorite svg,
[data-theme="dark"] .favorite-button svg,
[data-theme="dark"] .like-button .bi-heart,
[data-theme="dark"] .btn-like .bi-heart,
[data-theme="dark"] .btn-favorite .bi-heart,
[data-theme="dark"] .favorite-button .bi-heart,
[data-theme="dark"] .like-button .bi-heart-fill,
[data-theme="dark"] .btn-like .bi-heart-fill,
[data-theme="dark"] .btn-favorite .bi-heart-fill,
[data-theme="dark"] .favorite-button .bi-heart-fill {
  color: #ffffff !important;
  fill: #ffffff !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5)) !important;
}

/* ONLY liked hearts should be pink/red in dark mode */
[data-theme="dark"] .like-button.text-danger i,
[data-theme="dark"] .btn-like.text-danger i,
[data-theme="dark"] .btn-favorite.text-danger i,
[data-theme="dark"] .favorite-button.text-danger i,
[data-theme="dark"] .like-button.text-danger svg,
[data-theme="dark"] .btn-like.text-danger svg,
[data-theme="dark"] .btn-favorite.text-danger svg,
[data-theme="dark"] .favorite-button.text-danger svg,
[data-theme="dark"] .like-button.text-danger .bi-heart,
[data-theme="dark"] .btn-like.text-danger .bi-heart,
[data-theme="dark"] .btn-favorite.text-danger .bi-heart,
[data-theme="dark"] .favorite-button.text-danger .bi-heart,
[data-theme="dark"] .like-button.text-danger .bi-heart-fill,
[data-theme="dark"] .btn-like.text-danger .bi-heart-fill,
[data-theme="dark"] .btn-favorite.text-danger .bi-heart-fill,
[data-theme="dark"] .favorite-button.text-danger .bi-heart-fill {
  color: #ff3366 !important;
  fill: #ff3366 !important;
  filter: drop-shadow(0 0 3px rgba(255, 51, 102, 0.5)) !important;
}

/* Hover effects - maintain white for unliked */
.like-button:not(.text-danger):hover i,
.btn-like:not(.text-danger):hover i,
.btn-favorite:not(.text-danger):hover i,
.favorite-button:not(.text-danger):hover i,
.like-button:not(.text-danger):hover svg,
.btn-like:not(.text-danger):hover svg,
.btn-favorite:not(.text-danger):hover svg,
.favorite-button:not(.text-danger):hover svg,
.like-button:not(.text-danger):hover .bi-heart,
.btn-like:not(.text-danger):hover .bi-heart,
.btn-favorite:not(.text-danger):hover .bi-heart,
.favorite-button:not(.text-danger):hover .bi-heart,
.like-button:not(.text-danger):hover .bi-heart-fill,
.btn-like:not(.text-danger):hover .bi-heart-fill,
.btn-favorite:not(.text-danger):hover .bi-heart-fill,
.favorite-button:not(.text-danger):hover .bi-heart-fill {
  color: #ffffff !important;
  fill: #ffffff !important;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.6)) !important;
}

/* Hover effects - maintain pink/red for liked */
.like-button.text-danger:hover i,
.btn-like.text-danger:hover i,
.btn-favorite.text-danger:hover i,
.favorite-button.text-danger:hover i,
.like-button.text-danger:hover svg,
.btn-like.text-danger:hover svg,
.btn-favorite.text-danger:hover svg,
.favorite-button.text-danger:hover svg,
.like-button.text-danger:hover .bi-heart,
.btn-like.text-danger:hover .bi-heart,
.btn-favorite.text-danger:hover .bi-heart,
.favorite-button.text-danger:hover .bi-heart,
.like-button.text-danger:hover .bi-heart-fill,
.btn-like.text-danger:hover .bi-heart-fill,
.btn-favorite.text-danger:hover .bi-heart-fill,
.favorite-button.text-danger:hover .bi-heart-fill {
  color: #ff3366 !important;
  fill: #ff3366 !important;
  filter: drop-shadow(0 0 5px rgba(255, 51, 102, 0.7)) !important;
}

/* Ensure all backgrounds are transparent */
.like-button,
.btn-like,
.btn-favorite,
.favorite-button,
.like-button.text-danger,
.btn-like.text-danger,
.btn-favorite.text-danger,
.favorite-button.text-danger {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
}
