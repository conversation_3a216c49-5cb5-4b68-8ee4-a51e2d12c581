import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

# SQL to create the site_settings_siteconfiguration table
sql = """
CREATE TABLE IF NOT EXISTS "site_settings_siteconfiguration" (
    "id" serial NOT NULL PRIMARY KEY,
    "site_name" varchar(255) NOT NULL,
    "default_assistant_logo" varchar(100) NULL
);
"""

# Execute the SQL
with connection.cursor() as cursor:
    cursor.execute(sql)

# Insert the default site configuration
sql_insert = """
INSERT INTO site_settings_siteconfiguration (id, site_name)
VALUES (1, 'Company Assistant Platform')
ON CONFLICT (id) DO NOTHING;
"""

with connection.cursor() as cursor:
    cursor.execute(sql_insert)

print("Site configuration table created successfully!")
