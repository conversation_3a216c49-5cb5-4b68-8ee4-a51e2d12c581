from django import forms
from .models import DirectorySettings

class DirectorySettingsForm(forms.ModelForm):
    """Form for editing directory-wide settings."""

    class Meta:
        model = DirectorySettings
        fields = [
            'featured_scroll_direction',
            'featured_transition_effect',
            'featured_visible_count',
            'featured_autoplay',
            'featured_autoplay_delay',
            'hide_standard_tier_assistants', # Add new field
            'hide_standard_tier_companies', # Add company field
        ]
        help_texts = {
            'featured_scroll_direction': "Scroll direction for the featured assistants carousel.",
            'featured_transition_effect': "Transition effect for the featured assistants carousel.",
            'featured_visible_count': "Number of featured assistants visible at a time.",
            'featured_autoplay': "Enable autoplay for the featured carousel.",
            'featured_autoplay_delay': "Animation speed in milliseconds. This is how long it takes for the specified number of items (visible count) to cross the screen.",
            'hide_standard_tier_assistants': "If checked, assistants with the 'Standard' tier will not be shown in the public directory.", # Add help text
            'hide_standard_tier_companies': "If checked, companies with the 'Standard' tier will not be shown in the public directory.", # Add help text
        }
        widgets = {
            'featured_autoplay_delay': forms.NumberInput(attrs={'min': '1000', 'step': '100'}),
            'featured_visible_count': forms.NumberInput(attrs={'min': '1', 'max': '10'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add Bootstrap classes if needed, assuming crispy forms isn't automatically applied
        for field_name, field in self.fields.items():
            if isinstance(field.widget, (forms.Select, forms.NumberInput)):
                field.widget.attrs.update({'class': 'form-select form-select-sm'})
            elif isinstance(field.widget, forms.CheckboxInput):
                 field.widget.attrs.update({'class': 'form-check-input'})
