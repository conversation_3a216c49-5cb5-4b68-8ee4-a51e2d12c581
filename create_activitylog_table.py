import os
import django
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

print("Starting script to create accounts_activitylog table...")

# SQL to create the accounts_activitylog table
activitylog_sql = """
CREATE TABLE IF NOT EXISTS "accounts_activitylog" (
    "id" serial NOT NULL PRIMARY KEY,
    "activity_type" varchar(50) NOT NULL,
    "description" text NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "metadata" jsonb NOT NULL,
    "company_id" integer NOT NULL REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED,
    "user_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# SQL to create indexes
indexes_sql = """
CREATE INDEX IF NOT EXISTS "accounts_activitylog_company_id_idx" ON "accounts_activitylog" ("company_id");
CREATE INDEX IF NOT EXISTS "accounts_activitylog_user_id_idx" ON "accounts_activitylog" ("user_id");
CREATE INDEX IF NOT EXISTS "accounts_activitylog_created_at_idx" ON "accounts_activitylog" ("created_at");
CREATE INDEX IF NOT EXISTS "accounts_activitylog_activity_type_idx" ON "accounts_activitylog" ("activity_type");
"""

# Execute the SQL
try:
    with connection.cursor() as cursor:
        print("Creating accounts_activitylog table...")
        cursor.execute(activitylog_sql)
        print("accounts_activitylog table created successfully!")
        
        print("Creating indexes...")
        cursor.execute(indexes_sql)
        print("Indexes created successfully!")
    
    print("All tables created successfully!")
except Exception as e:
    print(f"Error creating tables: {e}")
    sys.exit(1)
