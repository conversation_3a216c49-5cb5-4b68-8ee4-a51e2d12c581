{% extends 'base/layout.html' %}
{% load static %}

{% block title %}Flagged Questions - {{ assistant.name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row align-items-center mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Flagged Questions</h1>
            <p class="text-muted mb-0">
                Questions that need better answers for {{ assistant.name }}
            </p>
        </div>
        <div class="col-auto">
            <a href="{% url 'assistants:add_context' assistant.company.id assistant.id %}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>
                Add Context
            </a>
            <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="btn btn-light ms-2">
                <i class="bi bi-chat-dots me-2"></i>
                Chat
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="status" class="form-label">Filter by Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Questions</option>
                        <option value="unresolved" {% if request.GET.status == 'unresolved' %}selected{% endif %}>Unresolved</option>
                        <option value="resolved" {% if request.GET.status == 'resolved' %}selected{% endif %}>Resolved</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" value="{{ request.GET.search|default:'' }}" placeholder="Search in questions...">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="bi bi-filter me-2"></i>
                        Filter
                    </button>
                    <a href="{% url 'assistants:flagged_questions' assistant.company.id assistant.id %}" class="btn btn-outline-secondary">
                        <i class="bi bi-x-circle me-2"></i>
                        Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Flagged Questions List -->
    <div class="card shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th scope="col">Question</th>
                            <th scope="col">Flagged By</th>
                            <th scope="col">Date</th>
                            <th scope="col">Status</th>
                            <th scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for flagged in flagged_questions %}
                            <tr>
                                <td>
                                    <a href="{% url 'assistants:flagged_question_detail' assistant.company.id assistant.id flagged.id %}" class="text-decoration-none">
                                        {{ flagged.question|truncatechars:100 }}
                                    </a>
                                </td>
                                <td>{{ flagged.user.username }}</td>
                                <td>{{ flagged.created_at|date:"M d, Y" }}</td>
                                <td>
                                    {% if flagged.is_resolved %}
                                        <span class="badge bg-success">Resolved</span>
                                    {% else %}
                                        <span class="badge bg-warning text-dark">Unresolved</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'assistants:flagged_question_detail' assistant.company.id assistant.id flagged.id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{% url 'assistants:add_context_to_flagged' assistant.company.id assistant.id flagged.id %}" class="btn btn-sm btn-outline-success">
                                        <i class="bi bi-plus-circle"></i>
                                    </a>
                                    {% if not flagged.is_resolved and request.user == flagged.user or request.user == assistant.company.owner %}
                                        <a href="{% url 'assistants:resolve_flagged' assistant.company.id assistant.id flagged.id %}" class="btn btn-sm btn-outline-secondary">
                                            <i class="bi bi-check-circle"></i>
                                        </a>
                                    {% endif %}
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <p class="text-muted mb-0">No flagged questions found.</p>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if flagged_questions.has_other_pages %}
        <nav aria-label="Flagged questions pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if flagged_questions.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ flagged_questions.previous_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">&laquo;&laquo;</span>
                    </li>
                    <li class="page-item disabled">
                        <span class="page-link">&laquo;</span>
                    </li>
                {% endif %}

                {% for i in flagged_questions.paginator.page_range %}
                    {% if flagged_questions.number == i %}
                        <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                    {% elif i > flagged_questions.number|add:'-3' and i < flagged_questions.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ i }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">{{ i }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if flagged_questions.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ flagged_questions.next_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ flagged_questions.paginator.num_pages }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">&raquo;</span>
                    </li>
                    <li class="page-item disabled">
                        <span class="page-link">&raquo;&raquo;</span>
                    </li>
                {% endif %}
            </ul>
        </nav>
    {% endif %}
</div>
{% endblock %}
