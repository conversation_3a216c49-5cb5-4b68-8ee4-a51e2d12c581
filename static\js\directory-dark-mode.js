/**
 * Directory Dark Mode Handler
 * Ensures dark mode is properly applied to directory pages
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if dark mode is active
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';

    // Apply dark mode styles to directory elements
    applyDarkModeToDirectory(isDarkMode);

    // Listen for theme changes
    document.addEventListener('themeChanged', function(e) {
        const isDarkMode = e.detail.theme === 'dark';
        applyDarkModeToDirectory(isDarkMode);
    });

    // Function to apply dark mode to directory elements
    function applyDarkModeToDirectory(isDarkMode) {
        if (isDarkMode) {
            // Apply dark mode to main content and body
            document.querySelectorAll('main, .main-content, .content, body.bg-light').forEach(el => {
                el.style.backgroundColor = '#121212';
                el.style.background = 'linear-gradient(to bottom, #121212, #0a0a0a)';
                el.style.color = '#ffffff';
            });

            // Apply dark mode to any element with bg-light class
            document.querySelectorAll('.bg-light').forEach(el => {
                el.style.backgroundColor = '#121212';
                el.style.color = '#ffffff';
            });

            // Apply dark mode to directory cards
            document.querySelectorAll('.card, .directory-card').forEach(card => {
                card.style.background = 'linear-gradient(145deg, #1e1e1e, #252525)';
                card.style.borderColor = '#333333';
                card.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)';
                card.style.color = '#ffffff';

                // Add hover effect
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.boxShadow = '0 12px 24px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                    this.style.transition = 'all 0.3s ease';
                });

                // Add leave effect
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)';
                    this.style.transition = 'all 0.3s ease';
                });

                // Style card title
                const cardTitle = card.querySelector('.card-title');
                if (cardTitle) {
                    cardTitle.style.color = '#ffffff';
                    cardTitle.style.fontWeight = '600';
                }

                // Style card text
                const cardText = card.querySelector('.card-text');
                if (cardText) {
                    cardText.style.color = '#cccccc';
                }

                // Style card footer
                const cardFooter = card.querySelector('.card-footer');
                if (cardFooter) {
                    cardFooter.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
                    cardFooter.style.borderTopColor = '#333333';
                }
            });

            // Apply dark mode to buttons
            document.querySelectorAll('.btn-primary').forEach(btn => {
                btn.style.background = 'linear-gradient(to bottom, #0077ff, #0055cc)';
                btn.style.borderColor = 'transparent';
                btn.style.color = '#ffffff';
                btn.style.boxShadow = '0 4px 10px rgba(0, 102, 255, 0.3)';

                // Add hover effect
                btn.addEventListener('mouseenter', function() {
                    this.style.background = 'linear-gradient(to bottom, #0088ff, #0066dd)';
                    this.style.boxShadow = '0 6px 15px rgba(0, 102, 255, 0.4)';
                    this.style.transform = 'translateY(-2px)';
                    this.style.transition = 'all 0.3s ease';
                });

                // Add leave effect
                btn.addEventListener('mouseleave', function() {
                    this.style.background = 'linear-gradient(to bottom, #0077ff, #0055cc)';
                    this.style.boxShadow = '0 4px 10px rgba(0, 102, 255, 0.3)';
                    this.style.transform = 'translateY(0)';
                    this.style.transition = 'all 0.3s ease';
                });
            });

            // Apply dark mode to badges
            document.querySelectorAll('.badge').forEach(badge => {
                if (badge.classList.contains('bg-primary')) {
                    badge.style.backgroundColor = '#0077ff';
                } else if (badge.classList.contains('bg-secondary')) {
                    badge.style.backgroundColor = '#555555';
                } else {
                    badge.style.backgroundColor = '#333333';
                }
                badge.style.color = '#ffffff';
            });

            // Apply dark mode to sections with white or light background
            document.querySelectorAll('section, .container, .container-fluid').forEach(section => {
                const bgColor = getComputedStyle(section).backgroundColor;
                if (bgColor === 'rgb(255, 255, 255)' || bgColor === '#ffffff' || bgColor === 'white' ||
                    bgColor === 'rgb(248, 249, 250)' || bgColor === '#f8f9fa' || bgColor === 'rgba(248, 249, 250, 1)') {
                    section.style.backgroundColor = '#121212';
                    section.style.color = '#ffffff';
                }
            });

            // Apply dark mode to company directory specific elements
            document.querySelectorAll('.featured-companies').forEach(section => {
                section.style.backgroundColor = '#1a1a1a';
                section.style.borderRadius = '8px';
                section.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
                section.style.padding = '20px';
            });

            // Apply dark mode to company cards
            document.querySelectorAll('.company-card').forEach(card => {
                card.style.backgroundColor = '#252525';
                card.style.border = '1px solid #333333';
                card.style.borderRadius = '8px';
                card.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
                card.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';

                // Add hover effect
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.3)';
                });

                // Add leave effect
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
                });
            });

            // Apply dark mode to company logo containers
            document.querySelectorAll('.company-logo-container').forEach(container => {
                container.style.backgroundColor = '#1a1a1a';
                container.style.borderBottom = '1px solid #333333';
                container.style.padding = '15px';
            });

            // Apply dark mode to company search container
            document.querySelectorAll('.company-search-container').forEach(container => {
                container.style.backgroundColor = '#1a1a1a';
                container.style.borderRadius = '8px';
                container.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
                container.style.padding = '20px';
                container.style.marginBottom = '30px';
            });

            // Apply dark mode to search input
            document.querySelectorAll('.search-input').forEach(input => {
                input.style.backgroundColor = '#252525';
                input.style.border = '1px solid #333333';
                input.style.color = '#ffffff';

                // Add focus effect
                input.addEventListener('focus', function() {
                    this.style.borderColor = '#0077ff';
                    this.style.boxShadow = '0 0 0 3px rgba(0, 119, 255, 0.25)';
                });

                // Add blur effect
                input.addEventListener('blur', function() {
                    this.style.borderColor = '#333333';
                    this.style.boxShadow = 'none';
                });
            });

            // Apply dark mode to search button
            document.querySelectorAll('.search-button').forEach(button => {
                button.style.backgroundColor = '#0077ff';
                button.style.border = 'none';
                button.style.color = '#ffffff';

                // Add hover effect
                button.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#0066dd';
                });

                // Add leave effect
                button.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '#0077ff';
                });
            });

            // Apply dark mode to headings
            document.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(heading => {
                heading.style.color = '#ffffff';
            });

            // Apply dark mode to paragraphs
            document.querySelectorAll('p').forEach(p => {
                p.style.color = '#cccccc';
            });

            // Apply dark mode to links
            document.querySelectorAll('a:not(.btn)').forEach(link => {
                link.style.color = '#0088ff';

                // Add hover effect
                link.addEventListener('mouseenter', function() {
                    this.style.color = '#00aaff';
                    this.style.transition = 'color 0.2s ease';
                });

                // Add leave effect
                link.addEventListener('mouseleave', function() {
                    this.style.color = '#0088ff';
                    this.style.transition = 'color 0.2s ease';
                });
            });

            // Apply dark mode to company logos
            document.querySelectorAll('.company-logo, img.card-img-top').forEach(logo => {
                logo.style.backgroundColor = '#1a1a1a';
                logo.style.border = '1px solid #333333';
                logo.style.borderRadius = '8px';
                logo.style.padding = '10px';
            });

            // Apply dark mode to search box
            document.querySelectorAll('.search-box, .form-control').forEach(input => {
                input.style.backgroundColor = '#1a1a1a';
                input.style.borderColor = '#333333';
                input.style.color = '#ffffff';

                // Add focus effect
                input.addEventListener('focus', function() {
                    this.style.borderColor = '#0077ff';
                    this.style.boxShadow = '0 0 0 3px rgba(0, 119, 255, 0.25)';
                    this.style.transition = 'all 0.3s ease';
                });

                // Add blur effect
                input.addEventListener('blur', function() {
                    this.style.borderColor = '#333333';
                    this.style.boxShadow = 'none';
                    this.style.transition = 'all 0.3s ease';
                });
            });

            // Apply dark mode to footer
            const footer = document.querySelector('footer');
            if (footer) {
                footer.style.backgroundColor = '#1a1a1a';
                footer.style.borderTopColor = '#333333';
                footer.style.color = '#cccccc';
            }

            // Apply dark mode to call-to-action section
            document.querySelectorAll('.cta-section').forEach(cta => {
                cta.style.background = 'linear-gradient(145deg, #1a1a1a, #121212)';
                cta.style.borderColor = '#333333';
                cta.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.4)';
            });
        } else {
            // Reset styles for light mode
            document.querySelectorAll(`
                main, .main-content, .content,
                .card, .directory-card,
                .btn-primary, .badge,
                section, .container, .container-fluid,
                h1, h2, h3, h4, h5, h6, p, a:not(.btn),
                .company-logo, img.card-img-top,
                .search-box, .form-control,
                footer, .cta-section,
                .featured-companies, .company-card, .company-logo-container,
                .company-search-container, .search-input, .search-button
            `).forEach(el => {
                el.style.backgroundColor = '';
                el.style.background = '';
                el.style.borderColor = '';
                el.style.borderBottom = '';
                el.style.borderRadius = '';
                el.style.boxShadow = '';
                el.style.color = '';
                el.style.transform = '';
                el.style.transition = '';
                el.style.padding = '';
                el.style.marginBottom = '';

                // Remove event listeners by cloning and replacing
                const newEl = el.cloneNode(true);
                el.parentNode.replaceChild(newEl, el);
            });
        }
    }

    // Apply dark mode immediately if needed
    if (isDarkMode) {
        applyDarkModeToDirectory(true);
    }

    // Use a more efficient approach for observing DOM changes
    // Only create observer if window.optimizedDarkMode is not true
    if (!window._isDarkModeActive) {
        // Set up a mutation observer with debouncing to prevent excessive calls
        let debounceTimer;
        const observer = new MutationObserver(function(mutations) {
            // Clear any existing timeout
            if (debounceTimer) {
                clearTimeout(debounceTimer);
            }

            // Set a new timeout to run the function after a delay
            debounceTimer = setTimeout(function() {
                const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
                if (isDarkMode) {
                    applyDarkModeToDirectory(true);
                }
            }, 1000); // 1 second debounce
        });

        // Observe only significant changes
        observer.observe(document.body, {
            childList: true,
            subtree: false,
            attributeFilter: ['class', 'style']
        });
    }
});
