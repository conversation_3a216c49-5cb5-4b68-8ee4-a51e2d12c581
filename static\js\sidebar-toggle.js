// Enhanced Sidebar Toggle Logic
document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const sidebar = document.getElementById('chat-sidebar');
    const sidebarToggleBtnMobile = document.getElementById('sidebar-toggle-btn-mobile');
    const sidebarToggleBtnDesktop = document.getElementById('sidebar-toggle-btn-desktop');
    const mainContent = document.querySelector('.col-12');

    // Function to show sidebar
    function showSidebar() {
        if (!sidebar) {
            return;
        }

        // Force sidebar to be visible with inline styles
        sidebar.classList.add('active');
        sidebar.style.left = '0';
        sidebar.style.display = 'block';
        sidebar.style.visibility = 'visible';
        sidebar.style.opacity = '1';
        sidebar.style.zIndex = '1080';

        // Update toggle button text
        const toggleTextElements = document.querySelectorAll('.toggle-text');
        toggleTextElements.forEach((el) => {
            el.textContent = 'Close';
        });

        // Prevent body scrolling
        document.body.style.overflow = 'hidden';

        // Ensure main content doesn't shift
        if (mainContent) {
            mainContent.style.position = 'relative';
            mainContent.style.zIndex = '1';
            mainContent.style.marginLeft = '0';
            mainContent.style.width = '100%';
        }
    }

    // Function to hide sidebar
    function hideSidebar() {
        if (!sidebar) {
            return;
        }

        sidebar.classList.remove('active');
        sidebar.style.left = '-350px';

        // Update toggle button text
        const toggleTextElements = document.querySelectorAll('.toggle-text');
        toggleTextElements.forEach((el) => {
            el.textContent = 'Menu';
        });

        // Restore body scrolling
        document.body.style.overflow = '';

        // Ensure main content doesn't shift
        if (mainContent) {
            mainContent.style.marginLeft = '0';
            mainContent.style.width = '100%';
        }
    }

    // Function to toggle sidebar visibility - simplified direct approach
    function toggleSidebar(e) {
        if (!sidebar) {
            console.log('DEBUG - Sidebar Toggle: Sidebar element not found');
            return;
        }

        console.log('DEBUG - Sidebar Toggle: Toggling sidebar');

        // Direct toggle approach
        if (sidebar.classList.contains('active')) {
            // Hide sidebar directly
            hideSidebar();
        } else {
            // Show sidebar directly
            showSidebar();
        }
    }

    // Export the toggleSidebar function for use in other scripts
    window.toggleSidebar = toggleSidebar;

    // Direct toggle function for inline handlers
    function toggleSidebarDirect(button) {
        // Check if sidebar exists
        if (!sidebar) {
            // Try to find it again
            const sidebarRetry = document.getElementById('chat-sidebar');
            if (!sidebarRetry) {
                return false;
            }
        }

        // Force the sidebar to be visible first to ensure transitions work
        if (sidebar) {
            sidebar.style.display = 'block';
            sidebar.style.visibility = 'visible';

            // Use a small timeout to ensure the display change takes effect
            setTimeout(() => {
                if (sidebar.classList.contains('active')) {
                    hideSidebar();
                } else {
                    showSidebar();
                }
            }, 10);
        }

        return false; // Prevent default action
    }

    // Export the toggleSidebarDirect function for use in other scripts
    window.toggleSidebarDirect = toggleSidebarDirect;

    // Add event listeners to toggle buttons
    if (sidebarToggleBtnMobile) {
        // Remove any existing listeners
        sidebarToggleBtnMobile.replaceWith(sidebarToggleBtnMobile.cloneNode(true));
        // Get the fresh reference
        sidebarToggleBtnMobile = document.getElementById('sidebar-toggle-btn-mobile');
        // Add direct onclick handler
        sidebarToggleBtnMobile.onclick = function(e) {
            toggleSidebar(e);
            return false; // Prevent default and stop propagation
        };
    }

    if (sidebarToggleBtnDesktop) {
        // Remove any existing listeners
        sidebarToggleBtnDesktop.replaceWith(sidebarToggleBtnDesktop.cloneNode(true));
        // Get the fresh reference
        sidebarToggleBtnDesktop = document.getElementById('sidebar-toggle-btn-desktop');
        // Add direct onclick handler
        sidebarToggleBtnDesktop.onclick = function(e) {
            toggleSidebar(e);
            return false; // Prevent default and stop propagation
        };
    }

    // Add event listener to close sidebar when clicking outside
    document.addEventListener('click', function(e) {
        // If sidebar is active and click is outside sidebar
        if (sidebar && sidebar.classList.contains('active')) {
            // Check if click is outside the sidebar
            if (!sidebar.contains(e.target) && !e.target.closest('#test-sidebar-toggle')) {
                hideSidebar();
            }
        }
    });

    // Add event listeners for the test-sidebar-toggle buttons
    const testSidebarToggle = document.getElementById('test-sidebar-toggle');
    const testSidebarToggleMobile = document.getElementById('test-sidebar-toggle-mobile');
    const sidebarToggleInside = document.getElementById('sidebar-toggle-inside');

    if (testSidebarToggle) {
        // Remove any existing listeners first
        const newBtn = testSidebarToggle.cloneNode(true);
        testSidebarToggle.parentNode.replaceChild(newBtn, testSidebarToggle);

        // Get fresh reference
        const freshBtn = document.getElementById('test-sidebar-toggle');

        if (freshBtn) {
            freshBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                toggleSidebarDirect(this);
                return false;
            });
        }
    }

    if (testSidebarToggleMobile) {
        // Remove any existing listeners first
        const newBtn = testSidebarToggleMobile.cloneNode(true);
        testSidebarToggleMobile.parentNode.replaceChild(newBtn, testSidebarToggleMobile);

        // Get fresh reference
        const freshBtn = document.getElementById('test-sidebar-toggle-mobile');

        if (freshBtn) {
            freshBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                toggleSidebarDirect(this);
                return false;
            });
        }
    }

    if (sidebarToggleInside) {
        // Remove any existing listeners first
        const newBtn = sidebarToggleInside.cloneNode(true);
        sidebarToggleInside.parentNode.replaceChild(newBtn, sidebarToggleInside);

        // Get fresh reference
        const freshBtn = document.getElementById('sidebar-toggle-inside');

        if (freshBtn) {
            freshBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                toggleSidebarDirect(this);
                return false;
            });
        }
    }

    // Close sidebar with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && sidebar && sidebar.classList.contains('active')) {
            hideSidebar();
        }
    });

    // Ensure sidebar is properly initialized
    if (sidebar) {
        // Make sure sidebar is initially hidden
        sidebar.classList.remove('active');
        sidebar.style.left = '-350px'; // Explicitly set left position

        // Force sidebar to be positioned below header
        sidebar.style.top = '56px';
        sidebar.style.height = 'calc(100vh - 56px)';

        // Initialize toggle button text
        const toggleTextElements = document.querySelectorAll('.toggle-text');
        toggleTextElements.forEach((el) => {
            el.textContent = 'Menu';
        });
    }
});
