/**
 * Robust TinyMCE initialization for community assistants
 * This script provides multiple fallback mechanisms to ensure TinyMCE loads properly
 */

// Global variables
var tinyMCELoaded = false;
var tinyMCEInitialized = false;
var initializationAttempts = 0;
var MAX_ATTEMPTS = 3;

// Function to show the fallback textarea
function showFallbackTextarea() {
    console.log("Showing fallback textarea");
    var fallback = document.getElementById('tinymce-fallback');
    if (fallback) {
        fallback.style.display = 'block';
    }
}

// Function to load TinyMCE from different sources
function loadTinyMCE() {
    console.log("Attempting to load TinyMCE");

    // If TinyMCE is already loaded, initialize it
    if (typeof tinymce !== 'undefined') {
        console.log("TinyMCE already loaded");
        tinyMCELoaded = true;
        initTinyMCE();
        return;
    }

    // Try loading from staticfiles
    var script = document.createElement('script');
    script.src = '/staticfiles/tinymce/tinymce.min.js';
    script.onload = function() {
        console.log("TinyMCE loaded from staticfiles");
        tinyMCELoaded = true;
        initTinyMCE();
    };
    script.onerror = function() {
        console.error("Failed to load TinyMCE from staticfiles, trying CDN");
        // Try loading from CDN
        var cdnScript = document.createElement('script');
        cdnScript.src = 'https://cdn.tiny.cloud/1/no-api-key/tinymce/5/tinymce.min.js';
        cdnScript.onload = function() {
            console.log("TinyMCE loaded from CDN");
            tinyMCELoaded = true;
            initTinyMCE();
        };
        cdnScript.onerror = function() {
            console.error("Failed to load TinyMCE from CDN");
            showFallbackTextarea();
        };
        document.head.appendChild(cdnScript);
    };
    document.head.appendChild(script);
}

// Function to initialize TinyMCE
function initTinyMCE() {
    console.log("Initializing TinyMCE, attempt #" + (initializationAttempts + 1));

    // Check if we've exceeded the maximum number of attempts
    if (initializationAttempts >= MAX_ATTEMPTS) {
        console.error("Maximum initialization attempts reached");
        showFallbackTextarea();
        return;
    }

    initializationAttempts++;

    // Check if TinyMCE is loaded
    if (typeof tinymce === 'undefined') {
        console.error("TinyMCE not loaded");
        loadTinyMCE();
        return;
    }

    // Check if the textarea exists
    var textarea = document.getElementById('id_text_content');
    if (!textarea) {
        console.error("Textarea not found");
        setTimeout(initTinyMCE, 500);
        return;
    }

    // Remove any existing instances
    try {
        if (tinymce.get('id_text_content')) {
            tinymce.get('id_text_content').remove();
        }
    } catch (e) {
        console.error("Error removing existing TinyMCE instance:", e);
    }

    // Initialize TinyMCE
    try {
        tinymce.init({
            selector: '#id_text_content',
            height: 300,
            menubar: false,
            plugins: [
                'advlist autolink lists link image charmap print preview anchor',
                'searchreplace visualblocks code fullscreen',
                'insertdatetime media table paste code help wordcount'
            ],
            toolbar: 'undo redo | formatselect | bold italic backcolor | ' +
                    'alignleft aligncenter alignright alignjustify | ' +
                    'bullist numlist outdent indent | removeformat | help',
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
            setup: function (editor) {
                editor.on('init', function() {
                    console.log("TinyMCE editor initialized");
                    tinyMCEInitialized = true;

                    // Force visibility of the editor container
                    var editorContainer = editor.getContainer();
                    if (editorContainer) {
                        editorContainer.style.visibility = 'visible';
                        editorContainer.style.display = 'block';
                    }

                    // Force visibility of all TinyMCE elements
                    document.querySelectorAll('.tox-tinymce, .tox-toolbar__primary, .tox-toolbar-overlord, .tox-edit-area, .tox-edit-area__iframe').forEach(function(el) {
                        el.style.visibility = 'visible';
                        el.style.display = el.tagName === 'DIV' ? 'block' : 'flex';
                    });
                });

                editor.on('change', function() {
                    editor.save();
                });
            }
        });
    } catch (e) {
        console.error("Error initializing TinyMCE:", e);
        showFallbackTextarea();
    }
}

// Initialize when the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM loaded, loading TinyMCE");
    setTimeout(loadTinyMCE, 100);

    // Also initialize when the contributions tab is clicked
    var contributionsTab = document.getElementById('contributions-tab');
    if (contributionsTab) {
        contributionsTab.addEventListener('click', function() {
            console.log("Contributions tab clicked");
            setTimeout(initTinyMCE, 100);
        });
    }

    // Add click handler to the initialize button
    var initButton = document.querySelector('button[data-action="init-tinymce"]');
    if (initButton) {
        initButton.addEventListener('click', function() {
            console.log("Initialize button clicked");
            // Reset the initialization attempts counter
            initializationAttempts = 0;
            initTinyMCE();
        });
    }

    // Handle form submission
    var form = document.getElementById('knowledge-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            console.log("Form submitted");

            // Save TinyMCE content if available
            if (typeof tinymce !== 'undefined' && tinymce.get('id_text_content')) {
                tinymce.get('id_text_content').save();
            }

            // Check if we need to use the fallback textarea
            var fallbackTextarea = document.querySelector('[name=fallback_content]');
            var mainTextarea = document.getElementById('id_text_content');

            if (fallbackTextarea && fallbackTextarea.value && (!mainTextarea.value || mainTextarea.value.trim() === '')) {
                console.log("Using fallback textarea content");
                mainTextarea.value = fallbackTextarea.value;
            }
        });
    }
});

// Also try to initialize when the window is fully loaded
window.addEventListener('load', function() {
    console.log("Window loaded");
    setTimeout(initTinyMCE, 500);
});

// Direct initialization attempt
if (typeof tinymce !== 'undefined') {
    console.log("TinyMCE already available, initializing directly");
    setTimeout(initTinyMCE, 100);
} else {
    console.log("TinyMCE not available yet, will initialize when loaded");
    setTimeout(loadTinyMCE, 100);
}
