import decimal
from django import template
from django.utils.html import format_html

register = template.Library()

@register.simple_tag
def render_stars(avg_rating, total_ratings=0):
    """
    Renders a static star rating display based on average rating.

    Args:
        avg_rating: The average rating (float, Decimal, or None).
        total_ratings: The total number of ratings (int).

    Returns:
        HTML string for the star rating display.
    """
    try:
        # Ensure avg_rating is a Decimal for consistent handling, default to 0 if None or invalid
        rating_decimal = decimal.Decimal(avg_rating if avg_rating is not None else 0)
    except (decimal.InvalidOperation, TypeError):
        rating_decimal = decimal.Decimal(0)

    # Use the actual decimal value for more accurate star display
    # We'll show full stars, half stars, and empty stars
    stars_html = ''
    for i in range(1, 6):
        if i <= int(rating_decimal):
            # Full star
            stars_html += '<i class="bi bi-star-fill"></i>'
        elif i <= int(rating_decimal) + 0.5 and rating_decimal % 1 >= 0.5:
            # Half star (if the decimal part is >= 0.5)
            stars_html += '<i class="bi bi-star-half"></i>'
        else:
            # Empty star
            stars_html += '<i class="bi bi-star"></i>'

    rating_text = ''
    plural = '' # Default plural value
    if total_ratings > 0:
        # Set plural suffix based on number of ratings
        plural = 's' if total_ratings != 1 else ''
        # Format the rating text
        rating_text = f'({total_ratings} rating{plural})'
    else:
        rating_text = '(No ratings yet)'
        # Ensure plural is set even if no ratings
        plural = 's'

    # Construct the title string separately with exact rating value
    title_text = f"{float(rating_decimal):.2f} average rating from {total_ratings} user{plural}"

    # Combine stars and text in the standard structure
    # This tag now ONLY renders the static display.
    # Pass only the necessary arguments for the placeholders {} within the spans.
    return format_html(
        '<div class="star-rating mb-1" title="{}">' # Use the pre-formatted title
        '<span class="stars">{}</span>'
        '<span class="rating-count ms-1">{}</span>'
        '</div>',
        title_text,              # Argument for title="{}"
        format_html(stars_html), # Argument for <span class="stars">{}</span>
        rating_text              # Argument for <span class="rating-count ms-1">{}</span>
    )
