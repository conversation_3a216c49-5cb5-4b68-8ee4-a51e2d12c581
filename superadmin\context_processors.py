"""
Context processors for superadmin app to provide notification data across all templates.
"""

def superadmin_notifications(request):
    """
    Add superadmin notification counts to template context.
    Only adds data if user is a superuser.
    """
    if not request.user.is_authenticated or not request.user.is_superuser:
        return {}
    
    try:
        from .utils import calculate_pending_notifications
        
        # Get all notification data
        notifications = calculate_pending_notifications()
        
        # Add additional calculated values for sidebar
        notifications.update({
            # Total pending for companies (approvals + tier changes + featured requests)
            'total_company_pending': (
                notifications.get('pending_company_approvals', 0) +
                notifications.get('pending_company_tier_changes', 0) +
                notifications.get('pending_company_featured_requests', 0)
            ),
            
            # Total pending for communities (approvals + tier changes + featured requests)
            'total_community_pending': (
                notifications.get('pending_community_approvals', 0)
            ),
            
            # Total pending for assistants (tier changes + featured requests + inactive)
            'total_assistant_pending': (
                notifications.get('pending_assistant_tier_changes', 0) +
                notifications.get('pending_assistant_featured_requests', 0)
            ),
            
            # Get inactive assistants count for approval notifications
            'pending_assistant_approvals': 0,  # Will be calculated below
        })
        
        # Calculate inactive assistants (pending approval)
        try:
            from assistants.models import Assistant
            notifications['pending_assistant_approvals'] = Assistant.objects.filter(is_active=False).count()
            
            # Update total assistant pending to include approvals
            notifications['total_assistant_pending'] += notifications['pending_assistant_approvals']
            
        except Exception:
            notifications['pending_assistant_approvals'] = 0
        
        return notifications
        
    except Exception as e:
        # Log error and return empty dict to prevent template errors
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error in superadmin_notifications context processor: {e}")
        
        return {
            'pending_company_approvals': 0,
            'pending_community_approvals': 0,
            'pending_company_tier_changes': 0,
            'pending_assistant_tier_changes': 0,
            'pending_company_featured_requests': 0,
            'pending_assistant_featured_requests': 0,
            'pending_assistant_approvals': 0,
            'total_pending_notifications': 0,
            'total_company_pending': 0,
            'total_community_pending': 0,
            'total_assistant_pending': 0,
            'community_assistants_count': 0,
        }
