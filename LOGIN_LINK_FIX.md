# Login Link Expiration Fix Guide

This guide provides instructions for fixing the issue where login links sent via email are reported as expired or don't log you in when deployed to cPanel.

## Problem Description

When the application is deployed to cPanel, login links sent via email may:
1. Be reported as expired even when they are used immediately
2. Verify successfully but fail to actually log the user in
3. Lose session state after token verification

This happens because:
1. The default cache backend (LocMemCache) doesn't persist between server restarts
2. The default session engine (database) may not work correctly in cPanel
3. cPanel may restart the application periodically, causing all in-memory data to be lost
4. The token verification doesn't have a fallback mechanism for retrieving tokens

## Solution

We've implemented a robust solution that ensures login links work correctly even after server restarts:

1. **Enhanced Cache Configuration**:
   - Added a file-based cache backend that persists between server restarts
   - Created a dedicated cache for session tokens

2. **Token Storage Improvements**:
   - Tokens are now stored in both cache and file system
   - Added detailed token data including expiration time

3. **Improved Token Verification**:
   - Enhanced error handling and logging
   - Added fallback to file-based storage if token is not found in cache

4. **Session Configuration**:
   - Configured file-based sessions for cPanel environments
   - Increased session cookie age to 2 weeks
   - Added session directory with proper permissions

5. **Debugging Improvements**:
   - Added detailed logging for the login process
   - Created test scripts to verify token and session functionality

## Files Changed

1. `company_assistant/settings.py` (updated)
2. `accounts/auth_utils.py` (updated)
3. `accounts/auth_views.py` (updated)
4. `passenger_wsgi.py` (updated)

## Deployment Steps

### Step 1: Upload the Updated Files

Upload these files to your cPanel environment:
- `company_assistant/settings.py`
- `accounts/auth_utils.py`
- `accounts/auth_views.py`
- `passenger_wsgi.py`

### Step 2: Create Required Directories

Create these directories in your cPanel environment:
```bash
mkdir -p logs cache token_storage
chmod 755 logs cache token_storage
```

### Step 3: Restart the Application

Restart your application in cPanel:
- Go to "Setup Python App" in cPanel and click "Restart App", or
- Use SSH to touch the WSGI file: `touch passenger_wsgi.py`

### Step 4: Test the Login Links

1. Request a login link by entering your email on the login page
2. Check your email for the login link
3. Click the link to verify it works correctly

## Testing the Fix

We've created a test script to verify the token persistence and expiration:

```bash
python test_token_persistence.py
```

This script will:
1. Generate a token and verify it immediately
2. Simulate a server restart by clearing the cache
3. Verify the token still works after the simulated restart
4. Test token expiration to ensure expired tokens are rejected

## Troubleshooting

### Login Links Still Reported as Expired

If login links are still reported as expired after applying the fix:

1. **Check File Permissions**:
   ```bash
   ls -la token_storage
   ```
   Make sure the web server has read/write access to the `token_storage` directory.

2. **Check Log Files**:
   ```bash
   cat logs/auth.log
   ```
   Look for any errors related to token verification.

3. **Verify Production Settings**:
   Make sure `passenger_wsgi.py` is using the production settings:
   ```python
   os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.production_settings')
   ```

4. **Test Token Persistence**:
   Run the test script to verify token persistence:
   ```bash
   python test_token_persistence.py
   ```

### File-Based Cache Not Working

If the file-based cache is not working:

1. **Check Cache Directory**:
   ```bash
   ls -la cache
   ```
   Make sure the directory exists and has the correct permissions.

2. **Check for Errors**:
   ```bash
   cat logs/django.log
   ```
   Look for any errors related to cache operations.

3. **Try Database Cache**:
   If file-based cache doesn't work, you can try using the database cache by updating `company_assistant/production_settings.py`:
   ```python
   # Create the cache table first
   # python manage.py createcachetable

   CACHES = {
       'default': CACHE_CONFIGS['db'],
       'session_tokens': {
           'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
           'LOCATION': 'django_token_cache',
           'TIMEOUT': CACHE_TIMEOUT,
       },
   }
   ```

## Additional Information

### How the Token System Works

1. **Token Generation**:
   - When a user requests a login link, a unique token is generated
   - The token is stored in both cache and file system with the user's ID
   - An email with the login link is sent to the user

2. **Token Verification**:
   - When the user clicks the login link, the token is extracted from the URL
   - The system first checks the cache for the token
   - If not found in cache, it checks the file system
   - If the token is valid and not expired, the user is logged in
   - The token is deleted after use for security

### Security Considerations

The token system has several security features:

1. **One-Time Use**: Tokens can only be used once
2. **Expiration**: Tokens expire after 24 hours
3. **Secure Storage**: Tokens are stored with minimal information
4. **Cleanup**: Expired tokens are automatically cleaned up

### Performance Considerations

The file-based token storage is designed to be a fallback mechanism and should not impact performance significantly. However, if you have a high-traffic site, you may want to consider using Redis for token storage instead of the file system.
