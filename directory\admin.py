from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import CompanyListing, AssistantListing, SavedItem, FavoriteFolder, DirectorySettings # Re-added DirectorySettings import

@admin.register(DirectorySettings)
class DirectorySettingsAdmin(admin.ModelAdmin):
    """Admin interface for Directory Settings."""
    # Prevent adding more than one settings object
    def has_add_permission(self, request):
        return not DirectorySettings.objects.exists()

    # Prevent deleting the settings object
    def has_delete_permission(self, request, obj=None):
        return False

    fieldsets = (
        (_('Featured Assistants Carousel'), {
            'fields': (
                'featured_scroll_direction',
                'featured_transition_effect',
                'featured_visible_count',
                'featured_autoplay',
                'featured_autoplay_delay',
            ),
            'description': "Configure the appearance and behavior of the featured assistants carousel on the directory page."
        }),
        (_('Standard Tier Visibility'), {
            'fields': ('hide_standard_tier_assistants', 'hide_standard_tier_companies'), # Add company field
            'description': "Control whether items with the 'Standard' tier are visible in the public directory." # Updated description
        }),
        (_('Community Assistants Visibility'), {
            'fields': ('hide_community_assistants', 'hide_standard_tier_community_assistants'),
            'description': "Control whether community assistants are visible in the public directory."
        }),
    )

@admin.register(FavoriteFolder)
class FavoriteFolderAdmin(admin.ModelAdmin):
    list_display = ('name', 'user', 'item_type', 'created_at')
    list_filter = ('item_type', 'user')
    search_fields = ('name', 'user__username')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(CompanyListing)
class CompanyListingAdmin(admin.ModelAdmin):
    list_display = ('company', 'is_listed', 'featured', 'created_at')
    list_filter = ('is_listed', 'featured', 'created_at')
    search_fields = ('company__name', 'description')
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'created_at'

    fieldsets = (
        (None, {
            'fields': ('company', 'is_listed', 'featured')
        }),
        (_('Details'), {
            'fields': ('description', 'website')
        }),
        (_('Categorization'), {
            'fields': ('categories', 'tags'),
            'classes': ('collapse',)
        }),
        (_('Social Media'), {
            'fields': ('social_links',),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(AssistantListing)
class AssistantListingAdmin(admin.ModelAdmin):
    list_display = ('assistant', 'get_assistant_tier', 'is_listed', 'avg_rating', 'total_ratings', 'created_at') # Changed featured to get_assistant_tier
    list_filter = ('is_listed', 'assistant__tier', 'created_at') # Changed featured to assistant__tier
    search_fields = ('assistant__name', 'short_description', 'long_description')
    readonly_fields = ('created_at', 'updated_at', 'avg_rating', 'total_ratings')
    date_hierarchy = 'created_at'

    fieldsets = (
        (None, {
            'fields': ('assistant', 'is_listed') # Removed featured
        }),
        (_('Descriptions'), {
            'fields': ('short_description', 'long_description')
        }),
        (_('Categorization'), {
            'fields': ('categories', 'tags', 'capabilities'),
            'classes': ('collapse',)
        }),
        (_('Statistics'), {
            'fields': ('avg_rating', 'total_ratings'),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    @admin.display(ordering='assistant__tier', description='Tier')
    def get_assistant_tier(self, obj):
        return obj.assistant.get_tier_display() # Use the display value

@admin.register(SavedItem)
class SavedItemAdmin(admin.ModelAdmin):
    list_display = ('user', 'item_type', 'get_item_name', 'created_at')
    list_filter = ('item_type', 'created_at')
    search_fields = ('user__username', 'company__name', 'assistant__name', 'notes')
    readonly_fields = ('created_at',)
    date_hierarchy = 'created_at'

    def get_item_name(self, obj):
        if obj.item_type == 'company':
            return obj.company.name
        return obj.assistant.name
    get_item_name.short_description = _('Item Name')
