import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

print("Starting ContentImage table creation script...")

# SQL to create the content_contentimage table
contentimage_sql = """
CREATE TABLE IF NOT EXISTS "content_contentimage" (
    "id" serial NOT NULL PRIMARY KEY,
    "image" varchar(100) NOT NULL,
    "alt_text" varchar(200) NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "content_id" integer NOT NULL REFERENCES "content_content" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# SQL to create indexes
indexes_sql = """
CREATE INDEX IF NOT EXISTS "content_contentimage_content_id_idx" ON "content_contentimage" ("content_id");
"""

# Execute the SQL
try:
    with connection.cursor() as cursor:
        print("Creating content_contentimage table...")
        cursor.execute(contentimage_sql)
        print("ContentImage table created successfully!")
        
        print("Creating indexes...")
        cursor.execute(indexes_sql)
        print("Indexes created successfully!")
    
    print("Tables created successfully!")
except Exception as e:
    print(f"Error creating tables: {e}")
