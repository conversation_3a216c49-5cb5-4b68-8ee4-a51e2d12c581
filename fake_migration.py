"""
<PERSON><PERSON><PERSON> to fake the problematic migration.
"""
import os
import sys
import django
from django.core.management import call_command

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

def fake_migration():
    """Fake the problematic migration."""
    print("Faking migration accounts.0010_remove_registrationlink_role_remove_membership_role_and_more...")
    try:
        call_command('migrate', 'accounts', '0010_remove_registrationlink_role_remove_membership_role_and_more', fake=True)
        print("Migration faked successfully.")
    except Exception as e:
        print(f"Error faking migration: {e}")
        return
    
    print("Now trying to apply the remaining migrations...")
    try:
        call_command('migrate')
        print("All migrations applied successfully.")
    except Exception as e:
        print(f"Error applying remaining migrations: {e}")
        return
    
    print("Migration fix completed.")

if __name__ == "__main__":
    fake_migration()
