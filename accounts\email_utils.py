"""
Email utility functions for the accounts app.
This module provides functions for sending various types of emails:
- Sign-in approval emails
- Team invitation emails
- Bulk invitation emails
"""

import logging
from django.conf import settings
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.contrib.sites.models import Site

logger = logging.getLogger(__name__)

def get_site_url():
    """Get the site URL from the Site model or settings."""
    try:
        site = Site.objects.get_current()
        site_url = f"https://{site.domain}"
        if settings.DEBUG:
            # In DEBUG mode, always use localhost regardless of site domain
            site_url = "http://localhost:8000"
    except Exception as e:
        logger.warning(f"Error getting site URL: {e}")
        site_url = settings.SITE_URL if hasattr(settings, 'SITE_URL') else "http://localhost:8000"

    return site_url

def send_html_email(subject, to_email, template_html, template_txt, context=None, from_email=None):
    """
    Send an HTML email with a text alternative.

    Args:
        subject (str): Email subject
        to_email (str or list): Recipient email address(es)
        template_html (str): Path to HTML template
        template_txt (str): Path to text template
        context (dict): Context for rendering templates
        from_email (str): Sender email address (defaults to settings.DEFAULT_FROM_EMAIL)

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    if context is None:
        context = {}

    # Add site_url to context if not present
    if 'site_url' not in context:
        context['site_url'] = get_site_url()

    # Render HTML and text content
    html_content = render_to_string(template_html, context)
    text_content = render_to_string(template_txt, context)

    # Use default from email if not specified
    if from_email is None:
        from_email = settings.DEFAULT_FROM_EMAIL

    # Create email message
    if isinstance(to_email, str):
        to_email = [to_email]

    try:
        msg = EmailMultiAlternatives(subject, text_content, from_email, to_email)
        msg.attach_alternative(html_content, "text/html")
        msg.send()
        logger.info(f"Email sent to {to_email}")
        return True
    except Exception as e:
        logger.error(f"Error sending email to {to_email}: {e}")
        return False

def send_signin_approval_email(user, approval_url, expiry_hours=24):
    """
    Send a sign-in approval email with a verification link.

    Args:
        user: User object
        approval_url: URL for approving the sign-in
        expiry_hours: Hours until the approval link expires

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    subject = "Sign-in Verification Required"
    to_email = user.email

    context = {
        'user': user,
        'approval_url': approval_url,
        'expiry_hours': expiry_hours,
    }

    return send_html_email(
        subject=subject,
        to_email=to_email,
        template_html='accounts/email/signin_approval.html',
        template_txt='accounts/email/signin_approval.txt',
        context=context
    )

def send_team_invitation_email(invitation, site_config=None):
    """
    Send a team invitation email.

    Args:
        invitation: TeamInvitation object
        site_config: SiteConfiguration object (optional)

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    site_url = get_site_url()
    accept_url = f"{site_url}/accounts/invitations/accept/{invitation.token}/"

    # Calculate expiry days
    from django.utils import timezone
    expiry_days = (invitation.expires_at - timezone.now()).days
    if expiry_days < 1:
        expiry_days = 1

    # Get inviter name
    inviter_name = "A team member"
    if invitation.invited_by:
        inviter_name = invitation.invited_by.get_full_name() or invitation.invited_by.username

    subject = f"You've Been Invited to Join {invitation.company.name} on 24seven"

    context = {
        'invite': invitation,
        'company': invitation.company,
        'inviter_name': inviter_name,
        'accept_url': accept_url,
        'expiry_days': expiry_days,
        'site_url': site_url,
        'site_config': site_config,
    }

    return send_html_email(
        subject=subject,
        to_email=invitation.email,
        template_html='accounts/email/team_invitation.html',
        template_txt='accounts/email/team_invitation.txt',
        context=context
    )

def send_bulk_invitation_emails(invitations, site_config=None):
    """
    Send invitation emails to multiple recipients.

    Args:
        invitations: List of TeamInvitation objects
        site_config: SiteConfiguration object (optional)

    Returns:
        tuple: (success_count, failed_emails)
    """
    success_count = 0
    failed_emails = []

    for invitation in invitations:
        success = send_team_invitation_email(invitation, site_config)
        if success:
            success_count += 1
        else:
            failed_emails.append(invitation.email)

    return success_count, failed_emails
