/**
 * Facebook Style Dark Mode CSS
 * Ensures the Facebook-style community dashboard has proper dark mode styling
 * to prevent white flash when navigating between pages
 */

/* Override Facebook style variables in dark mode */
[data-theme="dark"] {
    --fb-blue: #1877f2;
    --fb-light-blue: #1e3a5f;
    --fb-bg: #121212;
    --fb-text: #ffffff;
    --fb-secondary-text: #aaaaaa;
    --fb-divider: #333333;
    --fb-hover: #252525;
    --fb-comment-bg: #1e1e1e;
}

/* Override body background in dark mode */
[data-theme="dark"] body.facebook-style {
    background-color: #121212 !important;
    background: #121212 !important;
    color: #ffffff !important;
    transition: none !important;
}

/* Override navbar styling in dark mode */
[data-theme="dark"] .facebook-style .navbar {
    background-color: #121212 !important;
    background: #121212 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid #333333;
}

[data-theme="dark"] .facebook-style .navbar-brand {
    color: #ffffff;
}

/* Left sidebar in dark mode */
[data-theme="dark"] .facebook-sidebar {
    background-color: #121212 !important;
}

[data-theme="dark"] .facebook-sidebar .nav-link {
    color: #ffffff;
}

[data-theme="dark"] .facebook-sidebar .nav-link:hover {
    background-color: #252525;
}

/* Post creation card in dark mode */
[data-theme="dark"] .post-create-card {
    background-color: #1e1e1e !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .post-input {
    background-color: #252525;
    color: #ffffff;
}

[data-theme="dark"] .post-input:focus {
    background-color: #333333;
    border: 1px solid #444444;
}

[data-theme="dark"] .post-actions {
    border-top: 1px solid #333333;
}

[data-theme="dark"] .post-action-btn {
    color: #aaaaaa;
}

[data-theme="dark"] .post-action-btn:hover {
    background-color: #252525;
}

/* Post card in dark mode */
[data-theme="dark"] .post-card {
    background-color: #1e1e1e !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .post-header {
    border-bottom: 1px solid #333333;
}

[data-theme="dark"] .post-user {
    color: #ffffff;
}

[data-theme="dark"] .post-time {
    color: #aaaaaa;
}

[data-theme="dark"] .post-footer {
    border-top: 1px solid #333333;
}

/* Keywords styling in dark mode */
[data-theme="dark"] .keyword-badge {
    background-color: #1e3a5f;
    color: #ffffff;
}

/* Modal styling in dark mode */
[data-theme="dark"] .facebook-modal .modal-content {
    background-color: #1e1e1e;
    border: 1px solid #333333;
}

[data-theme="dark"] .facebook-modal .modal-header {
    border-bottom: 1px solid #333333;
}

[data-theme="dark"] .facebook-modal .modal-footer {
    border-top: 1px solid #333333;
}

/* Stats cards in dark mode */
[data-theme="dark"] .stat-card {
    background-color: #1e1e1e !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .stat-card h6 {
    color: #aaaaaa;
}

/* Card styling for all cards in Facebook style */
[data-theme="dark"] .facebook-style .card {
    background-color: #1e1e1e !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    color: #ffffff !important;
}

[data-theme="dark"] .facebook-style .card-header {
    background-color: #252525 !important;
    border-bottom: 1px solid #333333 !important;
}

[data-theme="dark"] .facebook-style .card-body {
    background-color: #1e1e1e !important;
}

[data-theme="dark"] .facebook-style .card-footer {
    background-color: #252525 !important;
    border-top: 1px solid #333333 !important;
}

/* List group styling */
[data-theme="dark"] .facebook-style .list-group-item {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

[data-theme="dark"] .facebook-style .list-group-item-action:hover {
    background-color: #252525 !important;
}

/* Alert styling */
[data-theme="dark"] .facebook-style .alert-info {
    background-color: #1e3a5f !important;
    border-color: #264b7a !important;
    color: #ffffff !important;
}

[data-theme="dark"] .facebook-style .alert-warning {
    background-color: #3a3000 !important;
    border-color: #4d4000 !important;
    color: #ffffff !important;
}

/* Text colors */
[data-theme="dark"] .facebook-style .text-muted {
    color: #aaaaaa !important;
}

/* Button styling */
[data-theme="dark"] .facebook-style .btn-light {
    background-color: #252525 !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

[data-theme="dark"] .facebook-style .btn-outline-primary {
    border-color: #1877f2 !important;
    color: #1877f2 !important;
}

[data-theme="dark"] .facebook-style .btn-outline-primary:hover {
    background-color: #1e3a5f !important;
    color: #ffffff !important;
}

/* Form elements */
[data-theme="dark"] .facebook-style input,
[data-theme="dark"] .facebook-style textarea,
[data-theme="dark"] .facebook-style select {
    background-color: #252525 !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

/* TinyMCE customization */
[data-theme="dark"] .facebook-style .tox-tinymce {
    border: 1px solid #333333 !important;
}

/* Ensure all backgrounds are properly styled */
[data-theme="dark"] .facebook-style .bg-light {
    background-color: #1e1e1e !important;
}

/* Ensure all white backgrounds are properly styled */
[data-theme="dark"] .facebook-style [style*="background-color: white"],
[data-theme="dark"] .facebook-style [style*="background-color: #fff"],
[data-theme="dark"] .facebook-style [style*="background-color: rgb(255, 255, 255)"],
[data-theme="dark"] .facebook-style [style*="background-color: rgba(255, 255, 255"] {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
}
