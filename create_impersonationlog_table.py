import os
import django
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

print("Starting script to create impersonate_impersonationlog table...")

# SQL to create the impersonate_impersonationlog table
impersonationlog_sql = """
CREATE TABLE IF NOT EXISTS "impersonate_impersonationlog" (
    "id" serial NOT NULL PRIMARY KEY,
    "session_key" varchar(40) NOT NULL,
    "session_started_at" timestamp with time zone NULL,
    "session_ended_at" timestamp with time zone NULL,
    "impersonator_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
    "impersonating_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# SQL to create indexes
indexes_sql = """
CREATE INDEX IF NOT EXISTS "impersonate_impersonationlog_impersonator_id_idx" ON "impersonate_impersonationlog" ("impersonator_id");
CREATE INDEX IF NOT EXISTS "impersonate_impersonationlog_impersonating_id_idx" ON "impersonate_impersonationlog" ("impersonating_id");
CREATE INDEX IF NOT EXISTS "impersonate_impersonationlog_session_started_at_idx" ON "impersonate_impersonationlog" ("session_started_at");
"""

# Execute the SQL
try:
    with connection.cursor() as cursor:
        print("Creating impersonate_impersonationlog table...")
        cursor.execute(impersonationlog_sql)
        print("ImpersonationLog table created successfully!")
        
        print("Creating indexes...")
        cursor.execute(indexes_sql)
        print("Indexes created successfully!")
    
    print("All tables created successfully!")
except Exception as e:
    print(f"Error creating tables: {e}")
    sys.exit(1)
