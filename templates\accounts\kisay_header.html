{% load static account_tags rating_tags %}

<div class="company-header d-flex align-items-center justify-content-between p-3 bg-white shadow-sm rounded mb-4">
    <div class="d-flex align-items-center">
        <!-- Company Logo -->
        <div class="company-logo me-3">
            {% if company.info.logo and company.info.logo.url %}
                <img src="{{ company.info.logo.url }}" alt="{{ company.name }} Logo" class="rounded-circle" style="width: 60px; height: 60px; object-fit: cover;">
            {% else %}
                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                    <i class="bi bi-building text-muted" style="font-size: 1.8rem;"></i>
                </div>
            {% endif %}
        </div>
        
        <!-- Company Info -->
        <div>
            <h1 class="h4 mb-0">{{ company.name }}</h1>
            <p class="text-muted small mb-0">{{ company.industry }}</p>
        </div>
    </div>
    
    <!-- Actions -->
    <div class="d-flex align-items-center">
        <!-- Rating Display -->
        <div class="me-3">
            <div id="rating-display-{{ company.id }}">
                {% if company_listing %}
                    {% render_stars company_listing.avg_rating company_listing.total_ratings %}
                {% else %}
                    {% render_stars 0 0 %}
                {% endif %}
            </div>
            {% if user.is_authenticated %}
                <button type="button" class="btn btn-sm btn-link text-decoration-none p-0 mt-1"
                        data-bs-toggle="modal" data-bs-target="#ratingModal"
                        data-company-id="{{ company.id }}"
                        data-company-name="{{ company.name }}">
                    <i class="bi bi-star me-1"></i>Rate
                </button>
            {% endif %}
        </div>
        
        <!-- Like Button -->
        {% if user.is_authenticated %}
            <button
                class="like-button btn btn-sm p-1 {% if is_favorited %}text-danger{% else %}text-secondary{% endif %}"
                data-item-id="{{ company.id }}"
                data-item-type="company"
                title="{% if is_favorited %}Remove from Favorites{% else %}Add to Favorites{% endif %}"
                style="background: none; border: none;">
                <i class="bi bi-heart{% if is_favorited %}-fill{% endif %} me-1"></i>
                {% if is_favorited %}Favorited{% else %}Favorite{% endif %}
            </button>
        {% endif %}
        
        <!-- Share Button -->
        <button class="btn btn-sm btn-outline-secondary ms-2" id="shareButton">
            <i class="bi bi-share me-1"></i> Share
        </button>
        
        <!-- QR Code Button -->
        <button class="btn btn-sm btn-outline-secondary ms-2" data-bs-toggle="modal" data-bs-target="#qrCodeModal">
            <i class="bi bi-qr-code me-1"></i> QR
        </button>
    </div>
</div>
