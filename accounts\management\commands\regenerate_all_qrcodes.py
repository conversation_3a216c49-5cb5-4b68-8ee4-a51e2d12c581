from django.core.management.base import BaseCommand
from django.urls import reverse
from accounts.models import Company
from assistants.models import Assistant
from utils.qr_generator import generate_model_qr_code
import time
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Regenerate QR codes for all entities with the new "A" logo design (companies, assistants, community assistants)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force regeneration of QR codes even if they already exist',
        )
        parser.add_argument(
            '--companies-only',
            action='store_true',
            help='Only regenerate QR codes for companies',
        )
        parser.add_argument(
            '--assistants-only',
            action='store_true',
            help='Only regenerate QR codes for assistants',
        )
        parser.add_argument(
            '--community-only',
            action='store_true',
            help='Only regenerate QR codes for community entities',
        )

    def handle(self, **options):
        # force parameter is no longer used since we're regenerating all QR codes
        # force = options.get('force', False)
        companies_only = options.get('companies_only', False)
        assistants_only = options.get('assistants_only', False)
        community_only = options.get('community_only', False)

        # If no specific filter is provided, regenerate all
        regenerate_all = not (companies_only or assistants_only or community_only)

        self.stdout.write(self.style.SUCCESS("Starting QR code regeneration with the new 'A' logo design..."))

        # Process companies
        if regenerate_all or companies_only or community_only:
            self.regenerate_company_qrcodes(community_only)

        # Process assistants
        if regenerate_all or assistants_only:
            self.regenerate_assistant_qrcodes()

        self.stdout.write(self.style.SUCCESS("QR code regeneration completed!"))

    def regenerate_company_qrcodes(self, community_only=False):
        """Regenerate QR codes for companies."""
        # Build the query
        query = {}
        # Always regenerate all QR codes with the new design
        # if not force:
        #     query['qr_code'] = ''  # Only get companies without QR codes
        if community_only:
            query['entity_type'] = 'community'

        # Get the companies
        companies = Company.objects.filter(**query)
        total = companies.count()

        self.stdout.write(f"Found {total} companies that need QR codes")

        # Process each company
        success_count = 0
        fail_count = 0

        for i, company in enumerate(companies, 1):
            self.stdout.write(f"Processing {i}/{total}: {company.name} (ID: {company.id})")

            # Try multiple attempts for each company
            max_attempts = 3
            success = False

            for attempt in range(max_attempts):
                try:
                    # Try to use the company detail URL
                    try:
                        url_path = reverse('accounts:company_detail', kwargs={'slug': company.slug})
                    except Exception as e:
                        # Fallback to direct URL if reverse fails
                        url_path = f"/accounts/company/{company.id}/"
                        self.stdout.write(self.style.WARNING(
                            f"Using fallback URL path for {company.name}: {url_path}"
                        ))

                    success = generate_model_qr_code(company, url_path, field_name='qr_code', letter="A")

                    if success:
                        # Save the company with the new QR code
                        company.save(update_fields=['qr_code'])
                        self.stdout.write(self.style.SUCCESS(
                            f"Successfully generated QR code for {company.name} (attempt {attempt+1})"
                        ))
                        success_count += 1
                        break
                    else:
                        self.stdout.write(self.style.WARNING(
                            f"Failed to generate QR code for {company.name} (attempt {attempt+1})"
                        ))
                        # Small delay before retry
                        time.sleep(0.5)
                except Exception as e:
                    self.stdout.write(self.style.ERROR(
                        f"Error generating QR code for {company.name}: {str(e)}"
                    ))
                    # Continue to next attempt

            if not success:
                fail_count += 1

        # Print summary
        self.stdout.write("\nCompany QR Code Summary:")
        self.stdout.write(self.style.SUCCESS(f"Successfully generated QR codes for {success_count} companies"))
        if fail_count > 0:
            self.stdout.write(self.style.ERROR(f"Failed to generate QR codes for {fail_count} companies"))
        else:
            self.stdout.write(self.style.SUCCESS("All company QR codes were generated successfully"))

    def regenerate_assistant_qrcodes(self):
        """Regenerate QR codes for assistants."""
        # Build the query
        query = {}
        # Always regenerate all QR codes with the new design
        # if not force:
        #     query['qr_code'] = ''  # Only get assistants without QR codes

        # Get the assistants
        assistants = Assistant.objects.filter(**query)
        total = assistants.count()

        self.stdout.write(f"Found {total} assistants that need QR codes")

        # Process each assistant
        success_count = 0
        fail_count = 0

        for i, assistant in enumerate(assistants, 1):
            self.stdout.write(f"Processing {i}/{total}: {assistant.name} (ID: {assistant.id})")

            # Try multiple attempts for each assistant
            max_attempts = 3
            success = False

            for attempt in range(max_attempts):
                try:
                    # Use the assistant's chat URL
                    url_path = reverse('assistants:assistant_chat', kwargs={'slug': assistant.slug})
                    success = generate_model_qr_code(assistant, url_path, field_name='qr_code', letter="A")

                    if success:
                        # Save the assistant with the new QR code
                        assistant.save(update_fields=['qr_code'])
                        self.stdout.write(self.style.SUCCESS(
                            f"Successfully generated QR code for {assistant.name} (attempt {attempt+1})"
                        ))
                        success_count += 1
                        break
                    else:
                        self.stdout.write(self.style.WARNING(
                            f"Failed to generate QR code for {assistant.name} (attempt {attempt+1})"
                        ))
                        # Small delay before retry
                        time.sleep(0.5)
                except Exception as e:
                    self.stdout.write(self.style.ERROR(
                        f"Error generating QR code for {assistant.name}: {str(e)}"
                    ))
                    # Continue to next attempt

            if not success:
                fail_count += 1

        # Print summary
        self.stdout.write("\nAssistant QR Code Summary:")
        self.stdout.write(self.style.SUCCESS(f"Successfully generated QR codes for {success_count} assistants"))
        if fail_count > 0:
            self.stdout.write(self.style.ERROR(f"Failed to generate QR codes for {fail_count} assistants"))
        else:
            self.stdout.write(self.style.SUCCESS("All assistant QR codes were generated successfully"))
