/* Social Media Dashboard Styles */

body {
    background-color: #f0f2f5;
}

.social-dashboard {
    max-width: 1200px;
    margin: 0 auto;
}

/* Header Styles */
.dashboard-header {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    padding: 20px;
}

/* Card Styles */
.dashboard-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    transition: box-shadow 0.3s ease;
}

.dashboard-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.dashboard-card .card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 15px 20px;
}

.dashboard-card .card-body {
    padding: 20px;
}

/* Post Styles */
.post-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    transition: box-shadow 0.3s ease;
}

.post-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.post-header {
    padding: 12px 16px;
    display: flex;
    align-items: center;
}

.post-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
}

.post-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.post-user-info {
    flex-grow: 1;
}

.post-username {
    font-weight: 600;
    margin-bottom: 0;
}

.post-time {
    font-size: 0.8rem;
    color: #65676b;
}

.post-content {
    padding: 0 16px 16px;
}

.post-text {
    margin-bottom: 12px;
    white-space: pre-line;
}

.post-image {
    width: 100%;
    border-radius: 8px;
    margin-bottom: 12px;
}

.post-actions {
    display: flex;
    border-top: 1px solid #e4e6eb;
    padding: 8px 16px;
}

.post-action-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border-radius: 4px;
    background: none;
    border: none;
    color: #65676b;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}

.post-action-btn:hover {
    background-color: #f2f3f5;
}

.post-action-btn i {
    margin-right: 6px;
}

/* Create Post Card */
.create-post-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    padding: 12px 16px;
}

.create-post-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.create-post-input {
    flex-grow: 1;
    margin-left: 12px;
    background-color: #f0f2f5;
    border-radius: 20px;
    padding: 8px 12px;
    border: none;
    cursor: pointer;
}

.create-post-actions {
    display: flex;
    border-top: 1px solid #e4e6eb;
    padding-top: 8px;
}

.create-post-action {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border-radius: 4px;
    background: none;
    border: none;
    color: #65676b;
    font-weight: 600;
    cursor: pointer;
}

.create-post-action:hover {
    background-color: #f2f3f5;
}

.create-post-action i {
    margin-right: 6px;
}

/* Sidebar Styles */
.sidebar-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.sidebar-card .card-header {
    padding: 12px 16px;
    font-weight: 600;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.sidebar-card .card-body {
    padding: 12px 16px;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu-item {
    padding: 8px 0;
    display: flex;
    align-items: center;
    color: #050505;
    text-decoration: none;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.sidebar-menu-item:hover {
    background-color: #f2f3f5;
}

.sidebar-menu-item i {
    width: 36px;
    font-size: 1.2rem;
    text-align: center;
    margin-right: 8px;
}

/* Stats Cards */
.stats-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    padding: 16px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    font-size: 1.5rem;
}

.stats-info h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.stats-info p {
    color: #65676b;
    margin: 0;
}

/* Moderation Tools */
.moderation-tools {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.moderation-tools .card-header {
    padding: 12px 16px;
    font-weight: 600;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.moderation-tools .list-group-item {
    border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 12px 16px;
}

.moderation-tools .list-group-item:last-child {
    border-bottom: none;
}

.moderation-tools .list-group-item i {
    margin-right: 8px;
}

/* User Reputation Badges */
.reputation-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 8px;
}

.reputation-new {
    background-color: #e4e6eb;
    color: #65676b;
}

.reputation-contributor {
    background-color: #e3f2fd;
    color: #0d6efd;
}

.reputation-trusted {
    background-color: #d1e7dd;
    color: #198754;
}

.reputation-expert {
    background-color: #cff4fc;
    color: #0dcaf0;
}

.reputation-leader {
    background-color: #fff3cd;
    color: #ffc107;
}

/* Activity Feed */
.activity-feed {
    list-style: none;
    padding: 0;
    margin: 0;
}

.activity-item {
    padding: 12px 0;
    border-bottom: 1px solid #e4e6eb;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-content {
    display: flex;
    align-items: flex-start;
}

.activity-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #e4e6eb;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.activity-text {
    flex-grow: 1;
}

.activity-time {
    font-size: 0.8rem;
    color: #65676b;
    margin-top: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sidebar {
        margin-bottom: 20px;
    }
}

/* Flagged content styles */
.flagged-content {
    border-left: 4px solid #dc3545;
    background-color: #f8f9fa;
    padding: 12px;
    margin-bottom: 16px;
    border-radius: 0 8px 8px 0;
}

.flagged-content .flagged-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.flagged-content .flagged-reason {
    font-style: italic;
    color: #6c757d;
    margin-bottom: 8px;
}

/* Upvote button styles */
.upvote-btn {
    background: none;
    border: none;
    color: #65676b;
    display: flex;
    align-items: center;
    font-weight: 500;
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.upvote-btn:hover {
    background-color: #f2f3f5;
}

.upvote-btn.upvoted {
    color: #0d6efd;
}

.upvote-btn i {
    margin-right: 6px;
}

.upvote-count {
    margin-left: 6px;
    font-weight: 600;
}

/* Tabs styling */
.dashboard-tabs {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.dashboard-tabs .nav-link {
    padding: 12px 16px;
    font-weight: 500;
    color: #65676b;
    border: none;
}

.dashboard-tabs .nav-link.active {
    color: #0d6efd;
    border-bottom: 3px solid #0d6efd;
    background-color: transparent;
}

.dashboard-tabs .nav-link:hover:not(.active) {
    background-color: #f2f3f5;
}

/* Notification styles */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* User list styles */
.user-list {
    list-style: none;
    padding: 0;
}

.user-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e4e6eb;
}

.user-item:last-child {
    border-bottom: none;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-info {
    flex-grow: 1;
}

.user-name {
    font-weight: 600;
    margin-bottom: 0;
}

.user-meta {
    font-size: 0.8rem;
    color: #65676b;
}

.user-actions {
    margin-left: 8px;
}

/* Moderation action buttons */
.mod-action-btn {
    padding: 4px 8px;
    font-size: 0.8rem;
    border-radius: 4px;
}

/* Report card styles */
.report-card {
    border-left: 4px solid #ffc107;
    background-color: #fff;
    padding: 12px;
    margin-bottom: 16px;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.report-card .report-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.report-card .report-content {
    margin-bottom: 8px;
}

.report-card .report-actions {
    display: flex;
    gap: 8px;
}

/* Status badges */
.status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-pending {
    background-color: #fff3cd;
    color: #ffc107;
}

.status-approved {
    background-color: #d1e7dd;
    color: #198754;
}

.status-rejected {
    background-color: #f8d7da;
    color: #dc3545;
}

.status-resolved {
    background-color: #cff4fc;
    color: #0dcaf0;
}

/* Contribution card styles */
.contribution-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    padding: 16px;
}

.contribution-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.contribution-title {
    font-weight: 600;
    margin-bottom: 8px;
}

.contribution-content {
    margin-bottom: 12px;
}

.contribution-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #65676b;
}

.contribution-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
}

/* Tooltip styles */
.tooltip-inner {
    max-width: 200px;
    padding: 8px 12px;
    color: #fff;
    text-align: center;
    background-color: #000;
    border-radius: 4px;
}

/* Dropdown menu styles */
.dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.dropdown-item {
    padding: 8px 16px;
}

.dropdown-item:hover {
    background-color: #f2f3f5;
}

.dropdown-item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

/* Chart styles */
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 20px;
}

/* Loading spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #0d6efd;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Empty state styles */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #65676b;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 16px;
    color: #e4e6eb;
}

.empty-state h4 {
    font-weight: 600;
    margin-bottom: 8px;
}

.empty-state p {
    max-width: 400px;
    margin: 0 auto;
}
