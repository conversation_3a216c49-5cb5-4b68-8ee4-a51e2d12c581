"""
<PERSON><PERSON><PERSON> to create a new PostgreSQL database for the Django project.
"""
import os
import sys
import psycopg2
from psycopg2 import sql
from getpass import getpass

def create_database(db_name, username, password):
    """Create a PostgreSQL database."""
    print(f"Attempting to create database '{db_name}'...")
    
    try:
        # Connect to PostgreSQL (to postgres database by default)
        conn = psycopg2.connect(
            dbname='postgres',  # Connect to default postgres database
            user=username,
            password=password,
            host='localhost',
            port='5432'
        )
        conn.autocommit = True  # Important for creating databases
        cursor = conn.cursor()
        
        # Check if database already exists
        cursor.execute(
            sql.SQL("SELECT 1 FROM pg_database WHERE datname = %s"),
            [db_name]
        )
        exists = cursor.fetchone()
        
        if exists:
            print(f"Database '{db_name}' already exists.")
            
            # Ask if user wants to drop and recreate
            recreate = input(f"Do you want to drop and recreate '{db_name}'? (y/n): ").strip().lower()
            if recreate == 'y':
                # Terminate all connections to the database
                print(f"Terminating all connections to '{db_name}'...")
                cursor.execute(
                    sql.SQL("""
                    SELECT pg_terminate_backend(pg_stat_activity.pid)
                    FROM pg_stat_activity
                    WHERE pg_stat_activity.datname = %s
                    AND pid <> pg_backend_pid()
                    """),
                    [db_name]
                )
                
                # Drop the database
                print(f"Dropping database '{db_name}'...")
                cursor.execute(
                    sql.SQL("DROP DATABASE IF EXISTS {}").format(sql.Identifier(db_name))
                )
                print(f"Database '{db_name}' dropped.")
                
                # Create the database
                print(f"Creating database '{db_name}'...")
                cursor.execute(
                    sql.SQL("CREATE DATABASE {}").format(sql.Identifier(db_name))
                )
                print(f"Database '{db_name}' created successfully.")
            else:
                print(f"Using existing database '{db_name}'.")
        else:
            # Create the database
            print(f"Creating database '{db_name}'...")
            cursor.execute(
                sql.SQL("CREATE DATABASE {}").format(sql.Identifier(db_name))
            )
            print(f"Database '{db_name}' created successfully.")
        
        cursor.close()
        conn.close()
        
        return True
    except Exception as e:
        print(f"Error creating database: {e}")
        return False

def update_settings(db_name, username, password):
    """Update local_settings.py with new database settings."""
    try:
        settings_path = os.path.join("company_assistant", "local_settings.py")
        
        # Check if the file exists
        if not os.path.exists(settings_path):
            print(f"Error: {settings_path} not found.")
            return False
        
        # Read the current settings file
        with open(settings_path, 'r') as f:
            content = f.read()
        
        # Update the database settings
        import re
        content = re.sub(
            r"'NAME': '.*?'", 
            f"'NAME': '{db_name}'", 
            content
        )
        content = re.sub(
            r"'USER': '.*?'", 
            f"'USER': '{username}'", 
            content
        )
        content = re.sub(
            r"'PASSWORD': '.*?'", 
            f"'PASSWORD': '{password}'", 
            content
        )
        
        # Write the updated settings file
        with open(settings_path, 'w') as f:
            f.write(content)
        
        print(f"Updated {settings_path} with new database settings.")
        return True
    except Exception as e:
        print(f"Error updating settings file: {e}")
        return False

def main():
    """Main function to create a new database."""
    print("PostgreSQL Database Creation Script")
    print("==================================")
    
    # Get database name
    db_name = input("Enter new database name: ").strip()
    if not db_name:
        print("Error: Database name cannot be empty.")
        return
    
    # Get username
    username = input("Enter PostgreSQL username [postgres]: ").strip() or "postgres"
    
    # Get password
    password = getpass("Enter PostgreSQL password: ")
    if not password:
        print("Error: Password cannot be empty.")
        return
    
    # Create the database
    success = create_database(db_name, username, password)
    
    if success:
        # Update local_settings.py
        update_settings(db_name, username, password)
        
        print("\nNext steps:")
        print("1. Run migrations: python manage.py migrate")
        print("2. Create a superuser: python manage.py createsuperuser")
        print("3. Run the development server: python manage.py runserver")
    else:
        print("\nFailed to create the database. Please check your PostgreSQL installation and credentials.")

if __name__ == "__main__":
    main()
