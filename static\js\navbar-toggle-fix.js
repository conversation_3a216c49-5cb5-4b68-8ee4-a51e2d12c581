/**
 * Navbar Toggle Button Fix
 * Ensures proper styling of the navbar toggle button in dark mode
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get the navbar toggler
    const navbarToggler = document.querySelector('.navbar-toggler');

    if (navbarToggler) {
        // Function to apply proper styling based on theme
        function applyTogglerStyling() {
            const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark' ||
                              document.body.getAttribute('data-theme') === 'dark';

            if (isDarkMode) {
                // Ensure proper styling in dark mode
                navbarToggler.classList.add('dark-mode-toggler');

                // Add a subtle animation on hover
                navbarToggler.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                });

                navbarToggler.addEventListener('mouseleave', function() {
                    // Don't reset transform if expanded
                    if (this.getAttribute('aria-expanded') !== 'true') {
                        this.style.transform = '';
                    }
                });

                // Add click effect
                navbarToggler.addEventListener('click', function() {
                    // Add a ripple effect
                    const ripple = document.createElement('span');
                    ripple.classList.add('navbar-toggler-ripple');
                    ripple.style.position = 'absolute';
                    ripple.style.width = '100%';
                    ripple.style.height = '100%';
                    ripple.style.top = '0';
                    ripple.style.left = '0';
                    ripple.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
                    ripple.style.borderRadius = '6px';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.opacity = '1';
                    ripple.style.transition = 'all 0.6s ease-out';

                    // Add the ripple to the button
                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);

                    // Trigger the ripple animation
                    setTimeout(() => {
                        ripple.style.transform = 'scale(2.5)';
                        ripple.style.opacity = '0';
                    }, 10);

                    // Remove the ripple after animation
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            } else {
                // Reset styling for light mode
                navbarToggler.classList.remove('dark-mode-toggler');
                navbarToggler.style.transform = '';
                navbarToggler.style.position = '';
                navbarToggler.style.overflow = '';

                // Remove event listeners by cloning and replacing
                const newToggler = navbarToggler.cloneNode(true);
                navbarToggler.parentNode.replaceChild(newToggler, navbarToggler);
            }
        }

        // Apply styling immediately
        applyTogglerStyling();

        // Listen for theme changes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'data-theme') {
                    applyTogglerStyling();
                }
            });
        });

        // Observe both html and body elements for theme changes
        observer.observe(document.documentElement, { attributes: true });
        observer.observe(document.body, { attributes: true });

        // Add CSS for ripple effect
        const style = document.createElement('style');
        style.textContent = `
            @keyframes navbar-toggler-ripple {
                to {
                    transform: scale(2.5);
                    opacity: 0;
                }
            }

            .navbar-toggler-ripple {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border-radius: 6px;
                background-color: rgba(255, 255, 255, 0.2);
                transform: scale(0);
                opacity: 1;
                transition: all 0.6s ease-out;
            }
        `;
        document.head.appendChild(style);
    }
});
