/* Superadmin Custom Styles */

:root {
  --admin-primary: #4e73df;
  --admin-primary-dark: #3a56c5;
  --admin-secondary: #858796;
  --admin-success: #1cc88a;
  --admin-info: #36b9cc;
  --admin-warning: #f6c23e;
  --admin-danger: #e74a3b;
  --admin-light: #f8f9fc;
  --admin-dark: #5a5c69;
  --admin-sidebar-width: 250px;
  --admin-card-border: rgba(0, 0, 0, 0.125);
  --admin-card-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  --admin-gradient-primary: linear-gradient(180deg, var(--admin-primary) 10%, var(--admin-primary-dark) 100%);
}

/* General Admin Styles */
.superadmin-container {
  padding: 1.5rem;
  background-color: var(--admin-light);
  min-height: calc(100vh - 56px);
}

/* Sidebar Styles */
.superadmin-sidebar {
  background: var(--admin-gradient-primary);
  min-height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  width: var(--admin-sidebar-width);
  z-index: 1040;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  transition: all 0.3s ease;
}

.superadmin-sidebar .sidebar-brand {
  height: 4.375rem;
  text-decoration: none;
  font-size: 1.2rem;
  font-weight: 700;
  padding: 1.5rem 1rem;
  text-align: center;
  letter-spacing: 0.05rem;
  z-index: 1;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.superadmin-sidebar .sidebar-brand i {
  font-size: 1.5rem;
  margin-right: 0.5rem;
}

.superadmin-sidebar .nav-item {
  position: relative;
}

.superadmin-sidebar .nav-item .nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  transition: all 0.2s ease;
}

.superadmin-sidebar .nav-item .nav-link:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

.superadmin-sidebar .nav-item .nav-link.active {
  color: white;
  font-weight: 600;
  background-color: rgba(255, 255, 255, 0.2);
}

.superadmin-sidebar .nav-item .nav-link i {
  margin-right: 0.5rem;
  font-size: 1rem;
  width: 1.5rem;
  text-align: center;
}

.superadmin-sidebar .sidebar-divider {
  margin: 1rem 0;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
}

.superadmin-sidebar .sidebar-heading {
  padding: 0 1rem;
  font-weight: 600;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  text-transform: uppercase;
  letter-spacing: 0.05rem;
  margin-top: 1rem;
}

/* Main Content Styles */
.superadmin-content {
  margin-left: var(--admin-sidebar-width);
  padding: 1.5rem;
  transition: all 0.3s ease;
}

/* Dashboard Cards */
.superadmin-card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid var(--admin-card-border);
  border-radius: 0.35rem;
  box-shadow: var(--admin-card-shadow);
  transition: transform 0.2s, box-shadow 0.2s;
  height: 100%;
}

.superadmin-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.superadmin-card .card-header {
  background-color: #f8f9fc;
  border-bottom: 1px solid var(--admin-card-border);
  padding: 0.75rem 1.25rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.superadmin-card .card-header h5,
.superadmin-card .card-header h6 {
  margin-bottom: 0;
  font-weight: 600;
  color: var(--admin-dark);
}

.superadmin-card .card-body {
  flex: 1 1 auto;
  padding: 1.25rem;
}

.superadmin-card .card-icon {
  width: 40px;
  height: 40px;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #fff;
  background-color: var(--admin-primary);
  box-shadow: 0 0.1rem 0.5rem rgba(0, 0, 0, 0.1);
  margin-right: 0.75rem;
}

.superadmin-card.card-primary .card-icon {
  background-color: var(--admin-primary);
}

.superadmin-card.card-success .card-icon {
  background-color: var(--admin-success);
}

.superadmin-card.card-info .card-icon {
  background-color: var(--admin-info);
}

.superadmin-card.card-warning .card-icon {
  background-color: var(--admin-warning);
  color: #212529;
}

.superadmin-card .card-stat {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--admin-dark);
  margin-bottom: 0.25rem;
  line-height: 1;
}

.superadmin-card.card-primary {
  border-left: 0.25rem solid var(--admin-primary);
}

.superadmin-card.card-success {
  border-left: 0.25rem solid var(--admin-success);
}

.superadmin-card.card-info {
  border-left: 0.25rem solid var(--admin-info);
}

.superadmin-card.card-warning {
  border-left: 0.25rem solid var(--admin-warning);
}

.superadmin-card.card-danger {
  border-left: 0.25rem solid var(--admin-danger);
}

/* Table Styles */
.superadmin-table {
  width: 100%;
  margin-bottom: 1rem;
  color: #212529;
  border-collapse: separate;
  border-spacing: 0;
}

.superadmin-table th {
  padding: 1rem;
  vertical-align: middle;
  background-color: #f8f9fc;
  border-bottom: 2px solid #e3e6f0;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.05rem;
  color: var(--admin-dark);
}

.superadmin-table td {
  padding: 0.75rem 1rem;
  vertical-align: middle;
  border-top: 1px solid #e3e6f0;
}

.superadmin-table tbody tr {
  transition: background-color 0.2s;
}

.superadmin-table tbody tr:hover {
  background-color: rgba(78, 115, 223, 0.05);
}

.superadmin-table .table-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  align-items: center;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.35rem 0.65rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
}

.status-badge-active {
  color: #fff;
  background-color: var(--admin-success);
}

.status-badge-inactive {
  color: #fff;
  background-color: var(--admin-danger);
}

.status-badge-pending {
  color: #212529;
  background-color: var(--admin-warning);
}

.status-badge-featured {
  color: #fff;
  background-color: var(--admin-info);
}

/* Filter Form Styles */
.filter-form {
  background-color: #fff;
  border-radius: 0.35rem;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  box-shadow: var(--admin-card-shadow);
}

.filter-form .form-control,
.filter-form .form-select {
  border-radius: 0.25rem;
  border: 1px solid #d1d3e2;
  font-size: 0.85rem;
  padding: 0.5rem 1rem;
}

.filter-form .form-control:focus,
.filter-form .form-select:focus {
  border-color: #bac8f3;
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.filter-form label {
  font-weight: 600;
  font-size: 0.85rem;
  color: var(--admin-dark);
}

/* Button Styles */
.btn-admin {
  font-weight: 600;
  font-size: 0.85rem;
  border-radius: 0.25rem;
  padding: 0.375rem 0.75rem;
  transition: all 0.15s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Dashboard card button styles */
.superadmin-card .btn {
  border-radius: 4px;
  font-weight: 600;
  padding: 0.375rem 0.75rem;
  transition: all 0.2s ease;
  min-width: 140px;
  text-align: center;
}

.superadmin-card .btn-admin-primary {
  background: linear-gradient(to bottom, var(--admin-primary), var(--admin-primary-dark));
  border-color: var(--admin-primary-dark);
  box-shadow: 0 2px 4px rgba(78, 115, 223, 0.3);
}

.superadmin-card .btn-admin-primary:hover,
.superadmin-card .btn-admin-primary:active,
.superadmin-card .btn-admin-primary:focus {
  background: linear-gradient(to bottom, #4668c5, #3a56c5);
  border-color: #3a56c5;
  box-shadow: 0 4px 8px rgba(78, 115, 223, 0.4);
  transform: translateY(-1px);
}

.superadmin-card .btn-admin-warning {
  background: linear-gradient(to bottom, var(--admin-warning), #e5b43a);
  border-color: #e5b43a;
  box-shadow: 0 2px 4px rgba(246, 194, 62, 0.3);
}

.superadmin-card .btn-admin-warning:hover,
.superadmin-card .btn-admin-warning:active,
.superadmin-card .btn-admin-warning:focus {
  background: linear-gradient(to bottom, #f4b30d, #e5a70d);
  border-color: #e5a70d;
  box-shadow: 0 4px 8px rgba(246, 194, 62, 0.4);
  transform: translateY(-1px);
}

.superadmin-card .btn-admin-info {
  background: linear-gradient(to bottom, var(--admin-info), #2a9faf);
  border-color: #2a9faf;
  box-shadow: 0 2px 4px rgba(54, 185, 204, 0.3);
}

.superadmin-card .btn-admin-info:hover,
.superadmin-card .btn-admin-info:active,
.superadmin-card .btn-admin-info:focus {
  background: linear-gradient(to bottom, #30a7b9, #2a9faf);
  border-color: #2a9faf;
  box-shadow: 0 4px 8px rgba(54, 185, 204, 0.4);
  transform: translateY(-1px);
}

.superadmin-card .btn-admin-success {
  background: linear-gradient(to bottom, var(--admin-success), #169b6b);
  border-color: #169b6b;
  box-shadow: 0 2px 4px rgba(28, 200, 138, 0.3);
}

.superadmin-card .btn-admin-success:hover,
.superadmin-card .btn-admin-success:active,
.superadmin-card .btn-admin-success:focus {
  background: linear-gradient(to bottom, #19b97d, #169b6b);
  border-color: #169b6b;
  box-shadow: 0 4px 8px rgba(28, 200, 138, 0.4);
  transform: translateY(-1px);
}

.btn-admin-primary {
  color: #fff;
  background-color: var(--admin-primary);
  border-color: var(--admin-primary);
}

.btn-admin-primary:hover {
  background-color: var(--admin-primary-dark);
  border-color: var(--admin-primary-dark);
  color: #fff;
}

.btn-admin-success {
  color: #fff;
  background-color: var(--admin-success);
  border-color: var(--admin-success);
}

.btn-admin-success:hover {
  background-color: #169b6b;
  border-color: #169b6b;
  color: #fff;
}

.btn-admin-danger {
  color: #fff;
  background-color: var(--admin-danger);
  border-color: var(--admin-danger);
}

.btn-admin-danger:hover {
  background-color: #d52a1a;
  border-color: #d52a1a;
  color: #fff;
}

.btn-admin-warning {
  color: #212529;
  background-color: var(--admin-warning);
  border-color: var(--admin-warning);
}

.btn-admin-warning:hover {
  background-color: #f4b30d;
  border-color: #f4b30d;
  color: #212529;
}

.btn-admin-info {
  color: #fff;
  background-color: var(--admin-info);
  border-color: var(--admin-info);
}

.btn-admin-info:hover {
  background-color: #2a9faf;
  border-color: #2a9faf;
  color: #fff;
}

.btn-admin-light {
  color: #212529;
  background-color: #f8f9fc;
  border-color: #d1d3e2;
}

.btn-admin-light:hover {
  background-color: #e2e6ea;
  border-color: #d1d3e2;
  color: #212529;
}

.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-icon i {
  margin-right: 0.5rem;
}

/* Action Button Styles */
.table-actions .btn {
  min-width: 38px;
  height: 38px;
}

.table-actions .dropdown-toggle {
  min-width: 80px;
}

.table-actions form {
  display: inline-block;
}

/* Feature/Deactivate buttons */
.btn-feature, .btn-deactivate {
  width: 38px;
  height: 38px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-right: 0.25rem;
}

.btn-feature {
  background-color: var(--admin-warning);
  color: #212529;
  border-color: var(--admin-warning);
}

.btn-feature:hover {
  background-color: #f4b30d;
  border-color: #f4b30d;
}

.btn-feature.active {
  background-color: var(--admin-warning);
  color: #212529;
}

.btn-deactivate {
  background-color: var(--admin-info);
  color: #fff;
  border-color: var(--admin-info);
}

.btn-deactivate:hover {
  background-color: #2a9faf;
  border-color: #2a9faf;
}

.btn-deactivate.active {
  background-color: #f8f9fc;
  color: #212529;
  border-color: #d1d3e2;
}

/* Pagination Styles */
.superadmin-pagination {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

.superadmin-pagination .page-item .page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: var(--admin-primary);
  background-color: #fff;
  border: 1px solid #dddfeb;
}

.superadmin-pagination .page-item.active .page-link {
  z-index: 3;
  color: #fff;
  background-color: var(--admin-primary);
  border-color: var(--admin-primary);
}

.superadmin-pagination .page-item.disabled .page-link {
  color: var(--admin-secondary);
  pointer-events: none;
  cursor: auto;
  background-color: #fff;
  border-color: #dddfeb;
}

/* Quick Action Buttons */
.superadmin-card .btn-admin-primary {
  transition: all 0.3s ease;
  border-width: 2px;
}

.superadmin-card .btn-admin-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 0.5rem 1rem rgba(78, 115, 223, 0.3);
}

.superadmin-card .btn-admin-primary i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
  transition: all 0.3s ease;
}

.superadmin-card .btn-admin-primary:hover i {
  transform: scale(1.1);
}

/* Responsive Styles */
@media (max-width: 991.98px) {
  .superadmin-sidebar {
    width: 100%;
    height: auto;
    position: relative;
    min-height: auto;
  }

  .superadmin-content {
    margin-left: 0;
  }

  .superadmin-sidebar .sidebar-brand {
    height: auto;
    padding: 1rem;
  }

  .superadmin-sidebar .nav {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }

  .superadmin-sidebar .nav-item {
    width: auto;
  }

  .superadmin-sidebar .nav-item .nav-link {
    padding: 0.5rem 1rem;
  }

  .superadmin-sidebar .sidebar-divider {
    display: none;
  }

  .superadmin-sidebar .sidebar-heading {
    display: none;
  }
}

@media (max-width: 767.98px) {
  .superadmin-table {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .superadmin-card .card-stat {
    font-size: 1.5rem;
  }

  .superadmin-card .card-icon {
    font-size: 1.5rem;
  }

  .filter-form {
    padding: 1rem;
  }

  .table-actions {
    flex-direction: column;
    gap: 0.25rem;
  }

  .table-actions .btn {
    width: 100%;
  }
}

/* Toggle Sidebar Button */
.sidebar-toggle {
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 1050;
  display: none;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: var(--admin-primary);
  color: white;
  border: none;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sidebar-toggle:hover {
  background-color: var(--admin-primary-dark);
}

.sidebar-toggle i {
  font-size: 1.25rem;
}

/* Collapsed Sidebar */
@media (min-width: 992px) {
  .sidebar-collapsed .superadmin-sidebar {
    width: 6.5rem;
  }

  .sidebar-collapsed .superadmin-content {
    margin-left: 6.5rem;
  }

  .sidebar-collapsed .superadmin-sidebar .sidebar-brand {
    padding: 1rem 0;
  }

  .sidebar-collapsed .superadmin-sidebar .sidebar-brand span {
    display: none;
  }

  .sidebar-collapsed .superadmin-sidebar .nav-item .nav-link {
    text-align: center;
    padding: 0.75rem 1rem;
    width: 6.5rem;
  }

  .sidebar-collapsed .superadmin-sidebar .nav-item .nav-link span {
    display: none;
  }

  .sidebar-collapsed .superadmin-sidebar .nav-item .nav-link i {
    margin-right: 0;
    font-size: 1.25rem;
    width: 1.5rem;
  }

  .sidebar-collapsed .superadmin-sidebar .sidebar-heading {
    text-align: center;
    padding: 0;
    font-size: 0.65rem;
  }
}

/* Mobile Sidebar */
@media (max-width: 991.98px) {
  .superadmin-sidebar {
    width: var(--admin-sidebar-width);
    position: fixed;
    top: 0;
    left: -100%;
    height: 100vh;
    z-index: 1050;
    transition: all 0.3s ease;
  }

  .sidebar-toggle {
    display: flex;
  }

  .sidebar-mobile-open .superadmin-sidebar {
    left: 0;
  }

  .sidebar-mobile-open .sidebar-overlay {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1040;
  }

  .superadmin-sidebar .nav {
    flex-direction: column;
  }

  .superadmin-sidebar .nav-item {
    width: 100%;
  }
}

/* Editable Fields */
.editable-field {
  position: relative;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.editable-field:hover {
  background-color: rgba(78, 115, 223, 0.1);
}

.editable-field:hover::after {
  content: "\F4C9"; /* Bootstrap Icons pencil */
  font-family: "bootstrap-icons";
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--admin-primary);
  font-size: 0.85rem;
}

.editable-field input {
  display: none;
  width: 100%;
  padding: 0.25rem 0.5rem;
  border: 1px solid #d1d3e2;
  border-radius: 0.25rem;
}

.editable-field.editing span {
  display: none;
}

.editable-field.editing input {
  display: block;
}

/* Header */
.superadmin-header {
  background-color: #fff;
  border-bottom: 1px solid #e3e6f0;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.superadmin-header h1 {
  margin-bottom: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--admin-dark);
}

.superadmin-header .breadcrumb {
  margin-bottom: 0;
  background-color: transparent;
  padding: 0;
}

.superadmin-header .breadcrumb-item {
  font-size: 0.85rem;
}

.superadmin-header .breadcrumb-item.active {
  color: var(--admin-primary);
  font-weight: 600;
}

.superadmin-header .breadcrumb-item + .breadcrumb-item::before {
  content: "\F285"; /* Bootstrap Icons chevron-right */
  font-family: "bootstrap-icons";
  color: var(--admin-secondary);
}
