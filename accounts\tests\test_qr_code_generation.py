from django.test import TestCase
from django.contrib.auth.models import User
from django.utils.text import slugify
from accounts.models import Company, CompanyInformation
from assistants.models import Assistant
import os
import uuid

class QRCodeGenerationTests(TestCase):
    """Test QR code generation for companies and assistants."""

    def setUp(self):
        """Set up test data."""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create a test company
        company_name = f"Test Company {uuid.uuid4().hex[:8]}"
        self.company = Company.objects.create(
            name=company_name,
            slug=slugify(company_name),
            owner=self.user,
            is_active=True
        )
        
        # Create company information
        self.company_info = CompanyInformation.objects.create(
            company=self.company,
            mission="Test mission statement",
            website="https://example.com",
            contact_email="<EMAIL>"
        )
        
        # Create a test assistant
        assistant_name = f"Test Assistant {uuid.uuid4().hex[:8]}"
        self.assistant = Assistant.objects.create(
            name=assistant_name,
            slug=slugify(assistant_name),
            company=self.company,
            created_by=self.user,
            assistant_type='general',
            model='gpt-3.5-turbo',
            temperature=0.7,
            max_tokens=2048,
            system_prompt="You are a helpful assistant.",
            greeting_message="Hello! How can I help you today?",
            is_active=True,
            is_public=True
        )
        
        # Create a test community assistant
        community_assistant_name = f"Test Community Assistant {uuid.uuid4().hex[:8]}"
        self.community_assistant = Assistant.objects.create(
            name=community_assistant_name,
            slug=slugify(community_assistant_name),
            company=self.company,
            created_by=self.user,
            assistant_type='community',
            model='gpt-3.5-turbo',
            temperature=0.7,
            max_tokens=2048,
            system_prompt="You are a helpful community assistant.",
            greeting_message="Hello! How can I help you today?",
            is_active=True,
            is_public=True
        )

    def test_company_qr_code_generation(self):
        """Test that QR codes are automatically generated for companies."""
        # Refresh the company from the database
        self.company.refresh_from_db()
        
        # Check that the QR code was generated
        self.assertTrue(self.company.qr_code)
        self.assertTrue(os.path.exists(self.company.qr_code.path))
        
        # Check that the QR code filename contains the company slug
        self.assertIn(self.company.slug, self.company.qr_code.name)

    def test_assistant_qr_code_generation(self):
        """Test that QR codes are automatically generated for assistants."""
        # Refresh the assistant from the database
        self.assistant.refresh_from_db()
        
        # Check that the QR code was generated
        self.assertTrue(self.assistant.qr_code)
        self.assertTrue(os.path.exists(self.assistant.qr_code.path))
        
        # Check that the QR code filename contains the assistant slug
        self.assertIn(self.assistant.slug, self.assistant.qr_code.name)

    def test_community_assistant_qr_code_generation(self):
        """Test that QR codes are automatically generated for community assistants."""
        # Refresh the community assistant from the database
        self.community_assistant.refresh_from_db()
        
        # Check that the QR code was generated
        self.assertTrue(self.community_assistant.qr_code)
        self.assertTrue(os.path.exists(self.community_assistant.qr_code.path))
        
        # Check that the QR code filename contains the community assistant slug
        self.assertIn(self.community_assistant.slug, self.community_assistant.qr_code.name)

    def test_qr_code_regeneration_on_slug_change(self):
        """Test that QR codes are regenerated when the slug changes."""
        # Change the company slug
        old_qr_code_path = self.company.qr_code.path
        new_slug = f"{self.company.slug}-updated"
        self.company.slug = new_slug
        self.company.save()
        
        # Refresh the company from the database
        self.company.refresh_from_db()
        
        # Check that the QR code was regenerated
        self.assertTrue(self.company.qr_code)
        self.assertTrue(os.path.exists(self.company.qr_code.path))
        
        # Check that the new QR code filename contains the new slug
        self.assertIn(new_slug, self.company.qr_code.name)
        
        # Check that the old QR code file no longer exists or is different
        if os.path.exists(old_qr_code_path):
            self.assertNotEqual(old_qr_code_path, self.company.qr_code.path)

    def tearDown(self):
        """Clean up after tests."""
        # Delete QR code files
        if self.company.qr_code:
            if os.path.exists(self.company.qr_code.path):
                os.remove(self.company.qr_code.path)
        
        if self.assistant.qr_code:
            if os.path.exists(self.assistant.qr_code.path):
                os.remove(self.assistant.qr_code.path)
        
        if self.community_assistant.qr_code:
            if os.path.exists(self.community_assistant.qr_code.path):
                os.remove(self.community_assistant.qr_code.path)
