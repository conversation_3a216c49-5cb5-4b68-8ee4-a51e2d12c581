import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

print("Starting script to check assistants_interaction table structure...")

# Check if the table exists
with connection.cursor() as cursor:
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = 'assistants_interaction'
        );
    """)
    table_exists = cursor.fetchone()[0]
    
    if table_exists:
        print("The assistants_interaction table exists!")
        
        # Check the table structure
        cursor.execute("""
            SELECT column_name, data_type, character_maximum_length
            FROM information_schema.columns
            WHERE table_name = 'assistants_interaction'
            ORDER BY ordinal_position;
        """)
        columns = cursor.fetchall()
        
        print("\nTable structure:")
        print("Column Name | Data Type | Max Length")
        print("-" * 50)
        for column in columns:
            print(f"{column[0]} | {column[1]} | {column[2]}")
    else:
        print("WARNING: The assistants_interaction table does NOT exist!")
    
    # Check if the many-to-many table exists
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = 'assistants_interaction_used_contexts'
        );
    """)
    m2m_table_exists = cursor.fetchone()[0]
    
    if m2m_table_exists:
        print("\nThe assistants_interaction_used_contexts table exists!")
        
        # Check the table structure
        cursor.execute("""
            SELECT column_name, data_type, character_maximum_length
            FROM information_schema.columns
            WHERE table_name = 'assistants_interaction_used_contexts'
            ORDER BY ordinal_position;
        """)
        columns = cursor.fetchall()
        
        print("\nTable structure:")
        print("Column Name | Data Type | Max Length")
        print("-" * 50)
        for column in columns:
            print(f"{column[0]} | {column[1]} | {column[2]}")
    else:
        print("\nWARNING: The assistants_interaction_used_contexts table does NOT exist!")
