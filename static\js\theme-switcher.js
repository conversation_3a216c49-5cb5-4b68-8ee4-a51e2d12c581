/**
 * Theme Switcher - Removed
 * Dark mode is now the only theme, theme switching functionality has been completely removed
 */

document.addEventListener('DOMContentLoaded', function() {
    // Always use dark mode
    const theme = 'dark';

    // Apply dark mode to all relevant elements
    document.documentElement.setAttribute('data-theme', theme);
    document.body.setAttribute('data-theme', theme);
    document.body.classList.add('dark-mode');
    localStorage.setItem('theme', theme);

    // Force dark mode on main content
    const mainContent = document.querySelector('main');
    if (mainContent) {
        mainContent.classList.add('dark-mode');
        mainContent.style.backgroundColor = '#121212';
        mainContent.style.color = '#ffffff';
    }

    // Remove any existing theme toggle buttons
    const themeToggleBtn = document.querySelector('.theme-toggle-btn');
    if (themeToggleBtn) {
        themeToggleBtn.remove();
    }
});
