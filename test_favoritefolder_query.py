import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import the models
from directory.models import FavoriteFolder
from django.contrib.auth import get_user_model

User = get_user_model()

def test_query():
    """Test querying the FavoriteFolder model with item_type field."""
    print("Testing FavoriteFolder query with item_type field...")
    
    # Get all users
    users = User.objects.all()
    print(f"Found {users.count()} users")
    
    if users.exists():
        user = users.first()
        print(f"Testing with user: {user.username}")
        
        # Try to query FavoriteFolder with item_type
        try:
            folders = FavoriteFolder.objects.filter(user=user, item_type='assistant')
            print(f"Query successful! Found {folders.count()} assistant folders for user {user.username}")
            
            # Try to create a test folder
            try:
                test_folder_name = "Test Folder"
                folder, created = FavoriteFolder.objects.get_or_create(
                    user=user,
                    name=test_folder_name,
                    item_type='assistant'
                )
                if created:
                    print(f"Created new folder: {folder.name} with item_type={folder.item_type}")
                    # Clean up
                    folder.delete()
                    print(f"Deleted test folder")
                else:
                    print(f"Found existing folder: {folder.name} with item_type={folder.item_type}")
            except Exception as e:
                print(f"Error creating folder: {e}")
        except Exception as e:
            print(f"Error querying with item_type: {e}")
    else:
        print("No users found in the database")

if __name__ == "__main__":
    test_query()
