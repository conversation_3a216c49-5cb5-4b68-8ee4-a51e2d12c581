/**
 * Company Logo Size Override CSS
 * This file contains ultra-specific rules to ensure company logos are properly sized
 * regardless of the original image dimensions
 */

/* Ultra-specific rules for company logo containers */
html body .company-logo-container,
html body div.company-logo-container,
html body .card-header .company-logo-container,
html body .card-body .company-logo-container,
html body .company-logo-container-sidebar,
html body .card-header.bg-light.d-flex.align-items-center .company-logo-container,
html body .card-header > .company-logo-container,
html body .card-header div.company-logo-container {
    height: 160px !important;
    width: 160px !important;
    max-width: 160px !important;
    max-height: 160px !important;
    min-width: 160px !important;
    min-height: 160px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    overflow: hidden !important;
    margin-right: 20px !important;
    box-sizing: border-box !important;
}

/* Ultra-specific rules for company logo images */
html body .company-logo-container img,
html body div.company-logo-container img,
html body .card-header .company-logo-container img,
html body .card-body .company-logo-container img,
html body .company-logo-container-sidebar img,
html body img.company-header-logo,
html body .card-header img.company-header-logo,
html body img.company-sidebar-logo,
html body .card-body img.company-sidebar-logo,
html body img[src*="company_logos"],
html body img[src*="MAKJOBS"],
html body img[alt*="Logo"],
html body img[src*="media/company_logos"],
html body img[src*="media/company_logos/MAKJOBS"],
html body img[src*="media/company_logos/MAKJOBS_Logo_with_Crane_Emblem"],
html body img[src*="media/company_logos/MAKJOBS_Logo_with_Crane_Emblem_dFk2oFV.png"],
html body .card-header.bg-light.d-flex.align-items-center img,
html body .card-header.bg-light.d-flex.align-items-center img.company-header-logo,
html body .card-header.bg-light.d-flex.align-items-center .company-logo-container img,
html body .card-header > img,
html body .card-header > img.company-header-logo,
html body .card-header > .company-logo-container img,
html body img.company-header-logo[src*="company_logos"],
html body img.company-header-logo[src*="media/company_logos"],
html body img.company-header-logo[alt*="Logo"] {
    height: 160px !important;
    width: 160px !important;
    max-width: 160px !important;
    max-height: 160px !important;
    min-width: auto !important;
    min-height: auto !important;
    object-fit: contain !important;
    border: none !important;
    padding: 0 !important;
    box-sizing: border-box !important;
}

/* Target the specific element with ID img.company-header-logo */
html body img#img\\.company-header-logo,
html body img[id="img.company-header-logo"] {
    height: 160px !important;
    width: 160px !important;
    max-width: 160px !important;
    max-height: 160px !important;
    min-width: auto !important;
    min-height: auto !important;
    object-fit: contain !important;
}

/* Ensure card header has proper alignment with larger logo */
html body .card-header.bg-light.d-flex.align-items-center {
    padding: 20px 25px !important;
    min-height: 200px !important;
}
