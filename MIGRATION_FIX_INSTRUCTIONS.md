# Migration Fix Instructions

You're encountering a migration conflict in your Django project. The error occurs because there are two migrations trying to add the same fields to the `assistants_assistant` table:

1. `0009_assistant_featured_autoplay_and_more` (already applied)
2. `0009_add_featured_settings` (pending)

There's also a merge migration `0010_merge_20250402_0632` that's supposed to resolve this conflict, but it hasn't been applied yet.

## Option 1: Using Django Management Commands

Try running these commands in sequence:

```bash
# 1. Fake the conflicting migration
python manage.py migrate assistants 0009_add_featured_settings --fake

# 2. Apply the merge migration
python manage.py migrate assistants 0010_merge_20250402_0632

# 3. Continue with the remaining migrations
python manage.py migrate
```

## Option 2: Using SQL (if Option 1 doesn't work)

If the Django management commands don't work, you can directly modify the database:

1. Open a PostgreSQL command prompt:
   ```bash
   psql -U postgres -d postgres2
   ```
   (Enter your password when prompted)

2. Run these SQL commands:
   ```sql
   -- Mark the conflicting migration as applied
   INSERT INTO django_migrations (app, name, applied)
   SELECT 'assistants', '0009_add_featured_settings', NOW()
   WHERE NOT EXISTS (
       SELECT 1 FROM django_migrations 
       WHERE app = 'assistants' AND name = '0009_add_featured_settings'
   );

   -- Mark the merge migration as applied
   INSERT INTO django_migrations (app, name, applied)
   SELECT 'assistants', '0010_merge_20250402_0632', NOW()
   WHERE NOT EXISTS (
       SELECT 1 FROM django_migrations 
       WHERE app = 'assistants' AND name = '0010_merge_20250402_0632'
   );

   -- Verify the migrations are marked as applied
   SELECT app, name, applied 
   FROM django_migrations 
   WHERE app = 'assistants' 
   ORDER BY applied;
   ```

3. Exit the PostgreSQL prompt:
   ```sql
   \q
   ```

4. Run the remaining migrations:
   ```bash
   python manage.py migrate
   ```

## Option 3: Using the Python Script

We've created a Python script to fix the database schema and migrations. Run:

```bash
python fix_database.py
```

Then run the migrations:

```bash
python manage.py migrate
```

## Verifying the Fix

After applying any of these fixes, verify that the migrations are properly applied:

```bash
python manage.py showmigrations assistants
```

You should see both `0009_add_featured_settings` and `0010_merge_20250402_0632` marked as applied (with an [X]).

## Troubleshooting

If you continue to have issues:

1. Check if the columns already exist in the database:
   ```sql
   SELECT column_name 
   FROM information_schema.columns 
   WHERE table_name = 'assistants_assistant';
   ```

2. If the columns exist but the migrations are failing, you might need to manually mark the migrations as applied.

3. If all else fails, you might need to:
   - Backup your data
   - Drop the database
   - Create a new database
   - Run migrations from scratch
   - Restore your data
