"""
Script to check company visibility in the directory.
"""
import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from accounts.models import Company, CompanyInformation
from directory.models import CompanyListing, DirectorySettings
from django.db.models import Q

def check_company_visibility():
    """Check company visibility in the directory."""
    print("\n=== Company Visibility Check ===\n")
    
    # Get directory settings
    try:
        settings = DirectorySettings.load()
        print(f"Directory Settings:")
        print(f"- Hide Standard Tier Companies: {settings.hide_standard_tier_companies}")
        print(f"- Hide Standard Tier Assistants: {settings.hide_standard_tier_assistants}")
    except Exception as e:
        print(f"Error loading directory settings: {e}")
        settings = None
    
    # Count companies
    total_companies = Company.objects.count()
    active_companies = Company.objects.filter(is_active=True).count()
    
    # Companies with CompanyInformation
    companies_with_info = Company.objects.filter(info__isnull=False).count()
    
    # Companies set to be listed in directory
    public_companies = Company.objects.filter(
        is_active=True,
        info__list_in_directory=True
    ).count()
    
    # Companies with CompanyListing
    companies_with_listing = Company.objects.filter(
        listing__isnull=False
    ).count()
    
    # Companies that should be visible in directory
    visible_companies = Company.objects.filter(
        is_active=True,
        info__list_in_directory=True,
        listing__is_listed=True
    ).count()
    
    # Print counts
    print("\nCompany Counts:")
    print(f"- Total Companies: {total_companies}")
    print(f"- Active Companies: {active_companies}")
    print(f"- Companies with CompanyInformation: {companies_with_info}")
    print(f"- Companies set to be listed in directory: {public_companies}")
    print(f"- Companies with CompanyListing: {companies_with_listing}")
    print(f"- Companies that should be visible in directory: {visible_companies}")
    
    # Check for companies missing CompanyInformation
    companies_missing_info = Company.objects.filter(info__isnull=True)
    if companies_missing_info.exists():
        print(f"\nCompanies missing CompanyInformation ({companies_missing_info.count()}):")
        for company in companies_missing_info:
            print(f"- {company.name} (ID: {company.id})")
    
    # Check for companies missing CompanyListing
    companies_missing_listing = Company.objects.filter(
        is_active=True,
        info__list_in_directory=True,
        listing__isnull=True
    )
    if companies_missing_listing.exists():
        print(f"\nCompanies missing CompanyListing ({companies_missing_listing.count()}):")
        for company in companies_missing_listing:
            print(f"- {company.name} (ID: {company.id})")
    
    # Check for companies that should be visible but aren't
    companies_should_be_visible = Company.objects.filter(
        is_active=True,
        info__list_in_directory=True
    ).exclude(
        listing__is_listed=True
    )
    if companies_should_be_visible.exists():
        print(f"\nCompanies that should be visible but aren't ({companies_should_be_visible.count()}):")
        for company in companies_should_be_visible:
            print(f"- {company.name} (ID: {company.id})")
    
    # List all companies with their visibility status
    print("\nAll Companies with Visibility Status:")
    for company in Company.objects.all():
        is_active = company.is_active
        has_info = hasattr(company, 'info') and company.info is not None
        list_in_directory = has_info and company.info.list_in_directory
        has_listing = hasattr(company, 'listing') and company.listing is not None
        is_listed = has_listing and company.listing.is_listed
        
        status = []
        if not is_active:
            status.append("INACTIVE")
        if not has_info:
            status.append("NO_INFO")
        elif not list_in_directory:
            status.append("NOT_PUBLIC")
        if not has_listing:
            status.append("NO_LISTING")
        elif not is_listed:
            status.append("LISTING_NOT_VISIBLE")
        
        if not status:
            status.append("SHOULD_BE_VISIBLE")
        
        print(f"- {company.name} (ID: {company.id}): {', '.join(status)}")

if __name__ == "__main__":
    check_company_visibility()
