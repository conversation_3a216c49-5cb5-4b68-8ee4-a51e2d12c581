/**
 * Dark Background Fix
 * Applies dark background to elements immediately during page load
 * to prevent white flash
 * - Applies to filter forms
 * - Applies to community assistant social dashboard
 * - Applies to Facebook-style community dashboard
 */

// Execute immediately when the script is loaded
(function() {
    // Create a style element
    const style = document.createElement('style');

    // Add the CSS rules
    style.textContent = `
        /* Immediate filter form styling to prevent flash */
        .filter-form,
        form.filter-form,
        #form-filter-form,
        .form-filter-form,
        div.filter-form,
        div > form.filter-form,
        div > #form-filter-form,
        div > .form-filter-form,
        .container .filter-form,
        .container form.filter-form,
        .container #form-filter-form,
        .container .form-filter-form,
        [class*="filter-form"],
        [id*="filter-form"],
        form[class*="filter"],
        div[class*="filter"],
        *[class*="filter-form"],
        *[id*="filter-form"] {
            background-color: #121212 !important;
            background: #121212 !important;
            border: 1px solid #333333 !important;
            border-radius: 8px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
            color: #ffffff !important;
            transition: none !important;
        }

        /* Community Assistant Social Dashboard immediate styling */
        .community-dashboard,
        .community-container,
        [class*="community-dashboard"],
        [class*="community-container"],
        [id*="community-dashboard"],
        [id*="community-container"],
        div[class*="community"],
        section[class*="community"],
        .card,
        .dashboard-card,
        .community-card,
        .feed-card,
        .stats-card,
        [class*="dashboard-card"],
        [class*="community-card"],
        [class*="feed-card"],
        [class*="stats-card"],
        .feed,
        #feed,
        .community-stats,
        .top-contributors,
        .popular-topics,
        [id="feed"],
        [class*="feed"],
        [class*="community-stats"],
        [class*="top-contributors"],
        [class*="popular-topics"] {
            background-color: #121212 !important;
            background: #121212 !important;
            border-color: #333333 !important;
            color: #ffffff !important;
            transition: none !important;
        }

        /* Feed items and posts */
        .feed-item,
        .post,
        .comment,
        .question,
        .answer,
        [class*="feed-item"],
        [class*="post"],
        [class*="comment"],
        [class*="question"],
        [class*="answer"] {
            background-color: #1e1e1e !important;
            background: #1e1e1e !important;
            border-color: #333333 !important;
            color: #ffffff !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
        }

        /* Facebook-style immediate styling */
        body.facebook-style,
        body.facebook-style .container,
        body.facebook-style .row,
        body.facebook-style .col,
        body.facebook-style [class*="col-"] {
            background-color: #121212 !important;
            background: #121212 !important;
            color: #ffffff !important;
            transition: none !important;
        }

        body.facebook-style .navbar {
            background-color: #121212 !important;
            background: #121212 !important;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
            border-bottom: 1px solid #333333 !important;
        }

        body.facebook-style .card {
            background-color: #1e1e1e !important;
            background: #1e1e1e !important;
            border: 1px solid #333333 !important;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
            color: #ffffff !important;
        }

        body.facebook-style .card-header {
            background-color: #252525 !important;
            background: #252525 !important;
            border-bottom: 1px solid #333333 !important;
        }

        body.facebook-style .card-body {
            background-color: #1e1e1e !important;
            background: #1e1e1e !important;
        }

        body.facebook-style .list-group-item {
            background-color: #1e1e1e !important;
            background: #1e1e1e !important;
            border-color: #333333 !important;
            color: #ffffff !important;
        }

        body.facebook-style .alert-info {
            background-color: #1e3a5f !important;
            background: #1e3a5f !important;
            border-color: #264b7a !important;
            color: #ffffff !important;
        }

        body.facebook-style .alert-warning {
            background-color: #3a3000 !important;
            background: #3a3000 !important;
            border-color: #4d4000 !important;
            color: #ffffff !important;
        }

        body.facebook-style .text-muted {
            color: #aaaaaa !important;
        }

        body.facebook-style .btn-light {
            background-color: #252525 !important;
            background: #252525 !important;
            border-color: #333333 !important;
            color: #ffffff !important;
        }

        body.facebook-style input,
        body.facebook-style textarea,
        body.facebook-style select {
            background-color: #252525 !important;
            background: #252525 !important;
            border-color: #333333 !important;
            color: #ffffff !important;
        }

        body.facebook-style .bg-light {
            background-color: #1e1e1e !important;
            background: #1e1e1e !important;
        }
    `;

    // Add the style element to the head
    document.head.appendChild(style);

    // Function to apply dark background to filter forms
    function applyDarkBackgroundToFilters() {
        // Select all filter forms
        const filterForms = document.querySelectorAll('.filter-form, form.filter-form, #form-filter-form, .form-filter-form, [class*="filter-form"], [id*="filter-form"]');

        // Apply dark background to each form
        filterForms.forEach(form => {
            form.style.backgroundColor = '#121212';
            form.style.background = '#121212';
            form.style.borderColor = '#333333';
            form.style.color = '#ffffff';

            // Apply styles to form controls
            form.querySelectorAll('.form-control, input, select, textarea').forEach(control => {
                control.style.backgroundColor = '#252525';
                control.style.borderColor = '#444444';
                control.style.color = '#ffffff';
            });

            // Apply styles to input group text
            form.querySelectorAll('.input-group-text').forEach(text => {
                text.style.backgroundColor = '#252525';
                text.style.borderColor = '#444444';
                text.style.color = '#ffffff';
            });

            // Apply styles to buttons
            form.querySelectorAll('.btn-primary').forEach(btn => {
                btn.style.backgroundColor = '#0077ff';
                btn.style.borderColor = '#0066dd';
                btn.style.color = '#ffffff';
            });

            // Apply styles to labels and headings
            form.querySelectorAll('label, h1, h2, h3, h4, h5, h6').forEach(element => {
                element.style.color = '#ffffff';
            });
        });
    }

    // Function to apply dark background to community assistant social dashboard
    function applyDarkBackgroundToCommunityDashboard() {
        // Select all community dashboard elements
        const dashboardElements = document.querySelectorAll(`
            .community-dashboard, .community-container,
            [class*="community-dashboard"], [class*="community-container"],
            [id*="community-dashboard"], [id*="community-container"],
            div[class*="community"], section[class*="community"],
            .card, .dashboard-card, .community-card, .feed-card, .stats-card,
            [class*="dashboard-card"], [class*="community-card"],
            [class*="feed-card"], [class*="stats-card"],
            .feed, #feed, .community-stats, .top-contributors, .popular-topics,
            [id="feed"], [class*="feed"], [class*="community-stats"],
            [class*="top-contributors"], [class*="popular-topics"],
            .community-dashboard-header, .community-dashboard-content,
            .community-dashboard-sidebar, .community-dashboard-main,
            .community-dashboard-footer, .knowledge-section, .question-section,
            .no-contributors-yet, .assistant-profile, .assistant-header,
            .assistant-info, .assistant-name, .assistant-description,
            .empty-state, .no-content, .no-contributors, .no-topics,
            [class*="community-dashboard-header"], [class*="community-dashboard-content"],
            [class*="community-dashboard-sidebar"], [class*="community-dashboard-main"],
            [class*="community-dashboard-footer"], [class*="knowledge-section"],
            [class*="question-section"], [class*="no-contributors-yet"],
            [class*="assistant-profile"], [class*="assistant-header"],
            [class*="assistant-info"], [class*="assistant-name"],
            [class*="assistant-description"], [class*="empty-state"],
            [class*="no-content"], [class*="no-contributors"], [class*="no-topics"],
            .browser-header, .browser-toolbar, .browser-tabs, .browser-navigation,
            .browser-controls, .browser-address-bar, .browser-buttons,
            .main-header, .main-navigation, .main-menu, .top-bar, .app-bar, .header-bar,
            [class*="browser-header"], [class*="browser-toolbar"], [class*="browser-tabs"],
            [class*="browser-navigation"], [class*="browser-controls"], [class*="browser-address-bar"],
            [class*="browser-buttons"], [class*="main-header"], [class*="main-navigation"],
            [class*="main-menu"], [class*="top-bar"], [class*="app-bar"], [class*="header-bar"],
            .home-icon, .dashboard-icon, .manage-icon, .companies-icon, .assistants-icon,
            .directory-icon, .favorites-icon, .community-icon, .feed-tab, .statistics-tab,
            .contributions-tab, .moderation-tab, .chat-now, .community-dashboard-btn,
            [class*="home-icon"], [class*="dashboard-icon"], [class*="manage-icon"],
            [class*="companies-icon"], [class*="assistants-icon"], [class*="directory-icon"],
            [class*="favorites-icon"], [class*="community-icon"], [class*="feed-tab"],
            [class*="statistics-tab"], [class*="contributions-tab"], [class*="moderation-tab"],
            [class*="chat-now"], [class*="community-dashboard-btn"],
            .white-bg, .bg-white, .background-white,
            [class*="white-bg"], [class*="bg-white"], [class*="background-white"]
        `);

        // Apply dark background to each element
        dashboardElements.forEach(element => {
            element.style.backgroundColor = '#121212';
            element.style.background = '#121212';
            element.style.borderColor = '#333333';
            element.style.color = '#ffffff';

            // Apply styles to feed items and posts
            element.querySelectorAll(`
                .feed-item, .post, .comment, .question, .answer,
                .post-container, .comment-container, .question-container, .answer-container,
                .user-post, .user-comment, .assistant-response,
                [class*="feed-item"], [class*="post"], [class*="comment"],
                [class*="question"], [class*="answer"], [class*="post-container"],
                [class*="comment-container"], [class*="question-container"],
                [class*="answer-container"], [class*="user-post"],
                [class*="user-comment"], [class*="assistant-response"]
            `).forEach(item => {
                item.style.backgroundColor = '#1e1e1e';
                item.style.background = '#1e1e1e';
                item.style.borderColor = '#333333';
                item.style.color = '#ffffff';
                item.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.2)';
            });

            // Apply styles to post metadata and timestamps
            element.querySelectorAll(`
                .post-meta, .post-time, .timestamp, .post-date, .minutes-ago,
                [class*="post-meta"], [class*="post-time"], [class*="timestamp"],
                [class*="post-date"], [class*="minutes-ago"]
            `).forEach(meta => {
                meta.style.color = '#aaaaaa';
            });

            // Apply styles to buttons
            element.querySelectorAll('.btn').forEach(btn => {
                btn.style.backgroundColor = '#252525';
                btn.style.borderColor = '#444444';
                btn.style.color = '#ffffff';
            });

            // Apply styles to primary buttons
            element.querySelectorAll('.btn-primary').forEach(btn => {
                btn.style.backgroundColor = '#0077ff';
                btn.style.borderColor = '#0066dd';
                btn.style.color = '#ffffff';
            });

            // Apply styles to input fields
            element.querySelectorAll('input, textarea, select').forEach(input => {
                input.style.backgroundColor = '#252525';
                input.style.borderColor = '#444444';
                input.style.color = '#ffffff';
            });

            // Apply styles to tags and badges
            element.querySelectorAll('.tag, .badge, [class*="tag"], [class*="badge"]').forEach(tag => {
                tag.style.backgroundColor = '#333333';
                tag.style.color = '#ffffff';
            });
        });
    }

    // Function to apply dark background to Facebook-style elements
    function applyDarkBackgroundToFacebookStyle() {
        // Check if the page has Facebook-style elements
        if (!document.body.classList.contains('facebook-style')) return;

        // Apply dark mode styles to body and container
        document.body.style.backgroundColor = '#121212';
        document.body.style.background = '#121212';
        document.body.style.color = '#ffffff';

        // Apply styles to containers, rows, and columns
        document.querySelectorAll('.container, .row, .col, [class*="col-"]').forEach(el => {
            el.style.backgroundColor = '#121212';
            el.style.background = '#121212';
            el.style.color = '#ffffff';
        });

        // Apply styles to navbar
        document.querySelectorAll('.navbar').forEach(navbar => {
            navbar.style.backgroundColor = '#121212';
            navbar.style.background = '#121212';
            navbar.style.boxShadow = '0 1px 2px rgba(0, 0, 0, 0.3)';
            navbar.style.borderBottom = '1px solid #333333';
        });

        // Apply styles to cards
        document.querySelectorAll('.card').forEach(card => {
            card.style.backgroundColor = '#1e1e1e';
            card.style.background = '#1e1e1e';
            card.style.borderColor = '#333333';
            card.style.color = '#ffffff';
            card.style.boxShadow = '0 1px 2px rgba(0, 0, 0, 0.3)';
        });

        // Apply styles to card headers
        document.querySelectorAll('.card-header').forEach(header => {
            header.style.backgroundColor = '#252525';
            header.style.background = '#252525';
            header.style.borderBottom = '1px solid #333333';
        });

        // Apply styles to card bodies
        document.querySelectorAll('.card-body').forEach(body => {
            body.style.backgroundColor = '#1e1e1e';
            body.style.background = '#1e1e1e';
        });

        // Apply styles to list group items
        document.querySelectorAll('.list-group-item').forEach(item => {
            item.style.backgroundColor = '#1e1e1e';
            item.style.background = '#1e1e1e';
            item.style.borderColor = '#333333';
            item.style.color = '#ffffff';
        });

        // Apply styles to alerts
        document.querySelectorAll('.alert-info').forEach(alert => {
            alert.style.backgroundColor = '#1e3a5f';
            alert.style.background = '#1e3a5f';
            alert.style.borderColor = '#264b7a';
            alert.style.color = '#ffffff';
        });

        document.querySelectorAll('.alert-warning').forEach(alert => {
            alert.style.backgroundColor = '#3a3000';
            alert.style.background = '#3a3000';
            alert.style.borderColor = '#4d4000';
            alert.style.color = '#ffffff';
        });

        // Apply styles to text colors
        document.querySelectorAll('.text-muted').forEach(text => {
            text.style.color = '#aaaaaa';
        });

        // Apply styles to buttons
        document.querySelectorAll('.btn-light').forEach(btn => {
            btn.style.backgroundColor = '#252525';
            btn.style.background = '#252525';
            btn.style.borderColor = '#333333';
            btn.style.color = '#ffffff';
        });

        // Apply styles to form elements
        document.querySelectorAll('input, textarea, select').forEach(input => {
            input.style.backgroundColor = '#252525';
            input.style.background = '#252525';
            input.style.borderColor = '#333333';
            input.style.color = '#ffffff';
        });

        // Apply styles to bg-light elements
        document.querySelectorAll('.bg-light').forEach(el => {
            el.style.backgroundColor = '#1e1e1e';
            el.style.background = '#1e1e1e';
        });
    }

    // Function to apply all dark backgrounds
    function applyAllDarkBackgrounds() {
        applyDarkBackgroundToFilters();
        applyDarkBackgroundToCommunityDashboard();
        applyDarkBackgroundToFacebookStyle();
    }

    // Apply styles immediately
    if (document.readyState === 'loading') {
        // If the document is still loading, add event listener
        document.addEventListener('DOMContentLoaded', applyAllDarkBackgrounds);
    } else {
        // If the document is already loaded, run the function
        applyAllDarkBackgrounds();
    }

    // Also apply styles when the DOM content is loaded
    document.addEventListener('DOMContentLoaded', applyAllDarkBackgrounds);

    // Create a MutationObserver to watch for new elements
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // Check if any of the added nodes match our selectors
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) { // Element node
                        let shouldApplyFilters = false;
                        let shouldApplyCommunityDashboard = false;
                        let shouldApplyFacebookStyle = false;

                        // Check if the node itself is a filter form
                        if (node.classList && (
                            node.classList.contains('filter-form') ||
                            node.id === 'form-filter-form' ||
                            node.classList.contains('form-filter-form')
                        )) {
                            shouldApplyFilters = true;
                        }

                        // Check if the node is a community dashboard element
                        if (node.classList && (
                            node.classList.contains('community-dashboard') ||
                            node.classList.contains('community-container') ||
                            node.classList.contains('feed') ||
                            node.classList.contains('card') ||
                            node.classList.contains('community-stats') ||
                            node.classList.contains('top-contributors') ||
                            node.classList.contains('popular-topics') ||
                            node.classList.contains('post') ||
                            node.classList.contains('comment') ||
                            node.classList.contains('question') ||
                            node.classList.contains('answer') ||
                            node.classList.contains('knowledge-section') ||
                            node.classList.contains('question-section') ||
                            node.classList.contains('browser-header') ||
                            node.classList.contains('browser-toolbar') ||
                            node.classList.contains('browser-tabs') ||
                            node.classList.contains('browser-navigation') ||
                            node.classList.contains('browser-controls') ||
                            node.classList.contains('browser-address-bar') ||
                            node.classList.contains('browser-buttons') ||
                            node.classList.contains('main-header') ||
                            node.classList.contains('main-navigation') ||
                            node.classList.contains('main-menu') ||
                            node.classList.contains('top-bar') ||
                            node.classList.contains('app-bar') ||
                            node.classList.contains('header-bar') ||
                            node.classList.contains('white-bg') ||
                            node.classList.contains('bg-white') ||
                            node.classList.contains('background-white') ||
                            node.classList.contains('sidebar') ||
                            node.classList.contains('side-nav') ||
                            node.classList.contains('side-menu') ||
                            node.classList.contains('side-bar') ||
                            node.classList.contains('left-sidebar') ||
                            node.classList.contains('left-nav') ||
                            node.classList.contains('left-menu') ||
                            node.classList.contains('left-bar') ||
                            node.classList.contains('right-sidebar') ||
                            node.classList.contains('right-nav') ||
                            node.classList.contains('right-menu') ||
                            node.classList.contains('right-bar') ||
                            node.classList.contains('sidenav') ||
                            node.classList.contains('side-navigation') ||
                            node.classList.contains('side-panel') ||
                            node.classList.contains('side-column') ||
                            node.classList.contains('nav-sidebar') ||
                            node.classList.contains('nav-side') ||
                            node.classList.contains('menu-sidebar') ||
                            node.classList.contains('menu-side') ||
                            node.classList.contains('sidebar-item') ||
                            node.classList.contains('sidebar-link') ||
                            node.classList.contains('sidebar-nav-item') ||
                            node.classList.contains('sidebar-nav-link') ||
                            node.classList.contains('sidebar-menu-item') ||
                            node.classList.contains('sidebar-menu-link') ||
                            node.classList.contains('side-nav-item') ||
                            node.classList.contains('side-nav-link') ||
                            node.classList.contains('side-menu-item') ||
                            node.classList.contains('side-menu-link') ||
                            node.classList.contains('home-link') ||
                            node.classList.contains('dashboard-link') ||
                            node.classList.contains('manage-link') ||
                            node.classList.contains('companies-link') ||
                            node.classList.contains('assistants-link') ||
                            node.classList.contains('directory-link') ||
                            node.classList.contains('favorites-link') ||
                            node.classList.contains('community-link') ||
                            node.id === 'feed' ||
                            (node.className && (
                                node.className.includes('community') ||
                                node.className.includes('dashboard') ||
                                node.className.includes('feed') ||
                                node.className.includes('post') ||
                                node.className.includes('comment') ||
                                node.className.includes('stats') ||
                                node.className.includes('contributors') ||
                                node.className.includes('topics') ||
                                node.className.includes('assistant') ||
                                node.className.includes('knowledge') ||
                                node.className.includes('question') ||
                                node.className.includes('browser') ||
                                node.className.includes('navigation') ||
                                node.className.includes('header') ||
                                node.className.includes('toolbar') ||
                                node.className.includes('menu') ||
                                node.className.includes('bar') ||
                                node.className.includes('white') ||
                                node.className.includes('sidebar') ||
                                node.className.includes('side-nav') ||
                                node.className.includes('side-menu') ||
                                node.className.includes('side-bar') ||
                                node.className.includes('sidenav') ||
                                node.className.includes('side-navigation') ||
                                node.className.includes('side-panel') ||
                                node.className.includes('side-column') ||
                                node.className.includes('nav-sidebar') ||
                                node.className.includes('nav-side') ||
                                node.className.includes('menu-sidebar') ||
                                node.className.includes('menu-side') ||
                                node.className.includes('home-link') ||
                                node.className.includes('dashboard-link') ||
                                node.className.includes('manage-link') ||
                                node.className.includes('companies-link') ||
                                node.className.includes('assistants-link') ||
                                node.className.includes('directory-link') ||
                                node.className.includes('favorites-link') ||
                                node.className.includes('community-link')
                            ))
                        )) {
                            shouldApplyCommunityDashboard = true;
                        }

                        // Check if the node is a Facebook-style element or if the body has the facebook-style class
                        if (document.body.classList.contains('facebook-style') ||
                            (node.classList && node.classList.contains('facebook-style'))) {
                            shouldApplyFacebookStyle = true;
                        }

                        // Check if the node contains our target elements
                        if (node.querySelectorAll) {
                            // Check for filter forms
                            const filterForms = node.querySelectorAll('.filter-form, form.filter-form, #form-filter-form, .form-filter-form');
                            if (filterForms.length > 0) {
                                shouldApplyFilters = true;
                            }

                            // Check for community dashboard elements
                            const dashboardElements = node.querySelectorAll(`
                                .community-dashboard, .community-container,
                                [class*="community-dashboard"], [class*="community-container"],
                                .card, .dashboard-card, .community-card, .feed-card, .stats-card,
                                .feed, #feed, .community-stats, .top-contributors, .popular-topics,
                                .post, .comment, .question, .answer, .post-container, .comment-container,
                                .question-container, .answer-container, .user-post, .user-comment,
                                .assistant-response, .knowledge-section, .question-section,
                                .community-dashboard-header, .community-dashboard-content,
                                .community-dashboard-sidebar, .community-dashboard-main,
                                .community-dashboard-footer, .assistant-profile, .assistant-header,
                                .assistant-info, .assistant-name, .assistant-description,
                                .empty-state, .no-content, .no-contributors, .no-topics,
                                .tag, .badge, .topic-tag, .popular-topic, .topic-pill, .topic-badge,
                                .machine-learning, .python, .data-science, .neural-networks,
                                .browser-header, .browser-toolbar, .browser-tabs, .browser-navigation,
                                .browser-controls, .browser-address-bar, .browser-buttons,
                                .main-header, .main-navigation, .main-menu, .top-bar, .app-bar, .header-bar,
                                .home-icon, .dashboard-icon, .manage-icon, .companies-icon, .assistants-icon,
                                .directory-icon, .favorites-icon, .community-icon, .feed-tab, .statistics-tab,
                                .contributions-tab, .moderation-tab, .chat-now, .community-dashboard-btn,
                                .white-bg, .bg-white, .background-white,
                                .sidebar, .side-nav, .side-menu, .side-bar, .left-sidebar, .left-nav,
                                .left-menu, .left-bar, .right-sidebar, .right-nav, .right-menu, .right-bar,
                                .sidenav, .side-navigation, .side-panel, .side-column, .nav-sidebar,
                                .nav-side, .menu-sidebar, .menu-side, .sidebar-item, .sidebar-link,
                                .sidebar-nav-item, .sidebar-nav-link, .sidebar-menu-item, .sidebar-menu-link,
                                .side-nav-item, .side-nav-link, .side-menu-item, .side-menu-link,
                                .sidebar-icon, .side-nav-icon, .side-menu-icon, .nav-sidebar-icon,
                                .sidebar-item-icon, .sidebar-link-icon, .sidebar-nav-icon, .sidebar-menu-icon,
                                .home-link, .dashboard-link, .manage-link, .companies-link, .assistants-link,
                                .directory-link, .favorites-link, .community-link,
                                [class*="browser-"], [class*="navigation-"], [class*="header-"],
                                [class*="toolbar-"], [class*="menu-"], [class*="bar-"], [class*="white-"],
                                [class*="sidebar"], [class*="side-nav"], [class*="side-menu"], [class*="side-bar"],
                                [class*="sidenav"], [class*="side-navigation"], [class*="side-panel"],
                                [class*="side-column"], [class*="nav-sidebar"], [class*="nav-side"],
                                [class*="menu-sidebar"], [class*="menu-side"], [class*="sidebar-item"],
                                [class*="sidebar-link"], [class*="sidebar-nav"], [class*="sidebar-menu"],
                                [class*="side-nav-item"], [class*="side-nav-link"], [class*="side-menu-item"],
                                [class*="side-menu-link"], [class*="sidebar-icon"], [class*="side-nav-icon"],
                                [class*="side-menu-icon"], [class*="nav-sidebar-icon"], [class*="sidebar-item-icon"],
                                [class*="sidebar-link-icon"], [class*="sidebar-nav-icon"], [class*="sidebar-menu-icon"],
                                [class*="home-link"], [class*="dashboard-link"], [class*="manage-link"],
                                [class*="companies-link"], [class*="assistants-link"], [class*="directory-link"],
                                [class*="favorites-link"], [class*="community-link"]
                            `);
                            if (dashboardElements.length > 0) {
                                shouldApplyCommunityDashboard = true;
                            }
                        }

                        // Apply styles if needed
                        if (shouldApplyFilters) {
                            applyDarkBackgroundToFilters();
                        }

                        if (shouldApplyCommunityDashboard) {
                            applyDarkBackgroundToCommunityDashboard();
                        }

                        if (shouldApplyFacebookStyle) {
                            applyDarkBackgroundToFacebookStyle();
                        }
                    }
                });
            }
        });
    });

    // Start observing the document body
    observer.observe(document.body, { childList: true, subtree: true });
})();
