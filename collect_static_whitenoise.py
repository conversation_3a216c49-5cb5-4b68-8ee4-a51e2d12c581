"""
<PERSON><PERSON><PERSON> to collect static files using Whitenoise for cPanel deployment.
Run this script after uploading your code to cPanel.
"""
import os
import sys
import subprocess
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set the Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.production_settings')

print("Collecting static files with Whitenoise for cPanel deployment...")
try:
    # Run the collectstatic command
    subprocess.run(
        [sys.executable, "manage.py", "collectstatic", "--noinput"],
        check=True
    )
    print("Static files collected successfully!")
    
    # Create necessary directories
    os.makedirs('logs', exist_ok=True)
    os.makedirs('media/tinymce_uploads', exist_ok=True)
    
    print("Created logs and media directories.")
    
    # Set proper permissions for static files
    print("Setting proper permissions for static files...")
    staticfiles_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'staticfiles')
    
    # Set directory permissions to 755 (rwxr-xr-x)
    for root, dirs, files in os.walk(staticfiles_dir):
        for directory in dirs:
            dir_path = os.path.join(root, directory)
            try:
                os.chmod(dir_path, 0o755)
                print(f"Set 755 permissions on directory: {dir_path}")
            except Exception as e:
                print(f"Error setting permissions on {dir_path}: {e}")
    
        # Set file permissions to 644 (rw-r--r--)
        for file in files:
            file_path = os.path.join(root, file)
            try:
                os.chmod(file_path, 0o644)
                print(f"Set 644 permissions on file: {file_path}")
            except Exception as e:
                print(f"Error setting permissions on {file_path}: {e}")
    
    print("\nNext steps:")
    print("1. Make sure Whitenoise is installed: pip install whitenoise")
    print("2. Restart your application in cPanel")
    print("3. Clear your browser cache and test your site")
    
except subprocess.CalledProcessError as e:
    print(f"Error collecting static files: {e}")
    sys.exit(1)
except Exception as e:
    print(f"Unexpected error: {e}")
    sys.exit(1)
