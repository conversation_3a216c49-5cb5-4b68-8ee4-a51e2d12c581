/**
 * Chat Scroll Fix
 * Ensures chat scrolls properly after content changes
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize scroll fix
    initChatScrollFix();
    
    // Set up mutation observer to handle dynamically added messages
    observeChatBox();
});

/**
 * Initialize chat scroll fix
 */
function initChatScrollFix() {
    console.log('[Chat Scroll Fix] Initializing chat scroll fix');
    
    // Fix chat box scrolling
    fixChatBoxScrolling();
    
    // Add window resize listener to fix scrolling on resize
    window.addEventListener('resize', function() {
        fixChatBoxScrolling();
    });
    
    // Add scroll event listener to ensure smooth scrolling
    const chatBox = document.getElementById('chat-box');
    if (chatBox) {
        chatBox.addEventListener('scroll', function() {
            // Add smooth scrolling class when user scrolls
            chatBox.classList.add('smooth-scroll');
        });
    }
    
    // Scroll to bottom initially
    scrollToBottom();
}

/**
 * Fix chat box scrolling
 */
function fixChatBoxScrolling() {
    const chatBox = document.getElementById('chat-box');
    if (!chatBox) return;
    
    // Ensure chat box has proper overflow
    chatBox.style.overflowY = 'auto';
    chatBox.style.overflowX = 'hidden';
    chatBox.style.webkitOverflowScrolling = 'touch';
    
    // Set proper height
    const viewportHeight = window.innerHeight;
    const headerHeight = 100; // Approximate header height
    const formHeight = 70; // Approximate form height
    const maxHeight = viewportHeight - headerHeight - formHeight;
    
    chatBox.style.maxHeight = `${maxHeight}px`;
    
    // Ensure flex layout
    chatBox.style.display = 'flex';
    chatBox.style.flexDirection = 'column';
    
    // Scroll to bottom
    scrollToBottom();
}

/**
 * Scroll to bottom of chat box
 */
function scrollToBottom() {
    const chatBox = document.getElementById('chat-box');
    if (!chatBox) return;
    
    // Scroll to bottom
    chatBox.scrollTop = chatBox.scrollHeight;
}

/**
 * Set up mutation observer to handle dynamically added messages
 */
function observeChatBox() {
    const chatBox = document.getElementById('chat-box');
    if (!chatBox) return;
    
    // Create mutation observer
    const observer = new MutationObserver(function(mutations) {
        // Scroll to bottom after mutations
        scrollToBottom();
        
        // Process any new tables
        processNewTables(mutations);
    });
    
    // Start observing
    observer.observe(chatBox, {
        childList: true,
        subtree: true
    });
}

/**
 * Process new tables added to the chat
 */
function processNewTables(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // Check if the added node contains tables
                    const tables = node.querySelectorAll('table');
                    if (tables.length > 0) {
                        // Process each table
                        tables.forEach(function(table) {
                            // Ensure table has proper width
                            table.style.width = '100%';
                            table.style.maxWidth = '100%';
                            
                            // Wrap table in a div for scrolling if not already wrapped
                            if (!table.parentNode.classList.contains('table-wrapper')) {
                                const wrapper = document.createElement('div');
                                wrapper.className = 'table-wrapper';
                                table.parentNode.insertBefore(wrapper, table);
                                wrapper.appendChild(table);
                            }
                        });
                        
                        // Scroll to bottom after processing tables
                        scrollToBottom();
                    }
                }
            });
        }
    });
}

// Add global function to scroll to bottom
window.scrollChatToBottom = scrollToBottom;
