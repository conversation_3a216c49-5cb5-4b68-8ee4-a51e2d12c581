import decimal # Import decimal
from django.db import models
from django.db.models import Avg, Sum # Import Avg and Sum
from django.conf import settings # Import settings
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator, MaxValueValidator, MinLengthValidator # Import validators
from accounts.models import Company
from assistants.models import Assistant, Interaction # Import Interaction
from django.core.validators import MinValueValidator # Re-import MinValueValidator


# --- Directory Settings Model ---
class DirectorySettings(models.Model):
    """Singleton model to store directory-wide settings."""
    SCROLL_DIRECTIONS = [('horizontal', 'Horizontal'), ('vertical', 'Vertical')]
    TRANSITION_EFFECTS = [
        ('slide', 'Slide'),
        ('fade', 'Fade'),
        ('cube', 'Cube'),
        ('coverflow', 'Coverflow'),
        ('flip', 'Flip'),
    ]

    featured_scroll_direction = models.CharField(
        max_length=10, choices=SCROLL_DIRECTIONS, default='horizontal',
        help_text="Scroll direction for featured assistants carousel."
    )
    featured_transition_effect = models.CharField(
        max_length=10, choices=TRANSITION_EFFECTS, default='slide',
        help_text="Transition effect for featured assistants carousel."
    )
    featured_visible_count = models.PositiveIntegerField(
        default=1, validators=[MinValueValidator(1)], # Changed default to 1
        help_text="Number of featured assistants visible at a time."
    )
    featured_autoplay = models.BooleanField(
        default=True, help_text="Enable autoplay for featured carousel." # Changed default to True
    )
    featured_autoplay_delay = models.PositiveIntegerField(
        default=5000, validators=[MinValueValidator(1000)],
        help_text="Animation speed in milliseconds. This is how long it takes for the specified number of items (visible count) to cross the screen."
    )
    hide_standard_tier_assistants = models.BooleanField(
        default=False,
        verbose_name="Hide Standard Tier Assistants",
        help_text="If checked, assistants with the 'Standard' tier will not be shown in the public directory."
    )
    hide_standard_tier_companies = models.BooleanField(
        default=False,
        verbose_name="Hide Standard Tier Companies",
        help_text="If checked, companies with the 'Standard' tier will not be shown in the public directory."
    )
    hide_community_assistants = models.BooleanField(
        default=False,
        verbose_name="Hide Community Assistants",
        help_text="If checked, community assistants will not be shown in the public directory."
    )
    hide_standard_tier_community_assistants = models.BooleanField(
        default=False,
        verbose_name="Hide Standard Tier Community Assistants",
        help_text="If checked, community assistants with the 'Standard' tier will not be shown in the public directory."
    )

    class Meta:
        verbose_name = "Directory Settings"
        verbose_name_plural = "Directory Settings"

    def __str__(self):
        return f"Directory Settings (hide_standard_tier_companies={self.hide_standard_tier_companies}, hide_standard_tier_assistants={self.hide_standard_tier_assistants}, hide_community_assistants={self.hide_community_assistants}, hide_standard_tier_community_assistants={self.hide_standard_tier_community_assistants})"

    def save(self, *args, **kwargs):
        # Direct console output for debugging
        print(f"\n\n[DIRECT DEBUG] DirectorySettings.save() called with hide_standard_tier_companies={self.hide_standard_tier_companies}, hide_standard_tier_assistants={self.hide_standard_tier_assistants}, hide_community_assistants={self.hide_community_assistants}, hide_standard_tier_community_assistants={self.hide_standard_tier_community_assistants}\n\n")

        # Get the old values if this is an existing object
        if self.pk:
            try:
                old_obj = self.__class__.objects.get(pk=self.pk)
                if (old_obj.hide_standard_tier_companies != self.hide_standard_tier_companies or
                    old_obj.hide_standard_tier_assistants != self.hide_standard_tier_assistants or
                    old_obj.hide_community_assistants != self.hide_community_assistants or
                    old_obj.hide_standard_tier_community_assistants != self.hide_standard_tier_community_assistants):
                    print(f"\n\n[DIRECT DEBUG] DirectorySettings values changing: hide_standard_tier_companies: {old_obj.hide_standard_tier_companies} -> {self.hide_standard_tier_companies}, hide_standard_tier_assistants: {old_obj.hide_standard_tier_assistants} -> {self.hide_standard_tier_assistants}, hide_community_assistants: {old_obj.hide_community_assistants} -> {self.hide_community_assistants}, hide_standard_tier_community_assistants: {old_obj.hide_standard_tier_community_assistants} -> {self.hide_standard_tier_community_assistants}\n\n")
            except self.__class__.DoesNotExist:
                pass

        super().save(*args, **kwargs)
        print(f"\n\n[DIRECT DEBUG] DirectorySettings.save() completed - pk={self.pk}\n\n")

    @classmethod
    def load(cls):
        # Simple singleton fetcher - ensures one object exists
        obj, created = cls.objects.get_or_create(pk=1)
        print(f"\n\n[DIRECT DEBUG] DirectorySettings.load() called: hide_standard_tier_companies={obj.hide_standard_tier_companies}, hide_standard_tier_assistants={obj.hide_standard_tier_assistants}, hide_community_assistants={obj.hide_community_assistants}, hide_standard_tier_community_assistants={obj.hide_standard_tier_community_assistants}, created={created}\n\n")

        # Get the calling function/file for debugging
        import traceback
        stack = traceback.extract_stack()
        caller = stack[-2]  # The caller of this method
        print(f"\n\n[DIRECT DEBUG] DirectorySettings.load() called from {caller.filename}:{caller.lineno} in {caller.name}\n\n")

        # Force a fresh query to ensure we're getting the latest data
        if not created:
            try:
                # Refresh from database to ensure we have the latest values
                fresh_obj = cls.objects.get(pk=1)
                print(f"\n\n[DIRECT DEBUG] DirectorySettings refreshed: hide_standard_tier_companies={fresh_obj.hide_standard_tier_companies}, hide_standard_tier_assistants={fresh_obj.hide_standard_tier_assistants}, hide_community_assistants={fresh_obj.hide_community_assistants}, hide_standard_tier_community_assistants={fresh_obj.hide_standard_tier_community_assistants}\n\n")

                # Check if values actually changed
                if (fresh_obj.hide_standard_tier_companies != obj.hide_standard_tier_companies or
                    fresh_obj.hide_standard_tier_assistants != obj.hide_standard_tier_assistants or
                    fresh_obj.hide_community_assistants != obj.hide_community_assistants or
                    fresh_obj.hide_standard_tier_community_assistants != obj.hide_standard_tier_community_assistants):
                    print(f"\n\n[DIRECT DEBUG] DirectorySettings values changed after refresh! This indicates a potential caching issue.\n\n")

                return fresh_obj
            except cls.DoesNotExist:
                print("\n\n[DIRECT DEBUG] DirectorySettings refresh failed, using original object\n\n")
                return obj
        return obj

# --- Category Model ---
class CompanyCategory(models.Model):
    """Stores predefined categories for companies."""
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=110, unique=True, blank=True) # Add slug field

    class Meta:
        verbose_name = _('company category')
        verbose_name_plural = _('company categories')
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # Auto-generate slug if blank
        if not self.slug:
            from django.utils.text import slugify
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)


# --- New Model for Favorite Folders ---
class FavoriteFolder(models.Model):
    """Stores user-created folders for organizing saved items."""
    ITEM_TYPES = [
        ('company', 'Company'),
        ('assistant', 'Assistant'),
    ]

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='favorite_folders')
    name = models.CharField(max_length=100)
    item_type = models.CharField(max_length=20, choices=ITEM_TYPES, help_text="Specifies whether this folder is for companies or assistants.")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('favorite folder')
        verbose_name_plural = _('favorite folders')
        ordering = ['name']
        unique_together = [
            ('user', 'name', 'item_type'), # User cannot have two folders with the same name for the same item type
        ]

    def __str__(self):
        return f"{self.user.username}'s {self.get_item_type_display()} Folder: {self.name}"


class CompanyListingManager(models.Manager):
    def visible(self):
        return self.filter(is_listed=True)

class CompanyListing(models.Model):
    company = models.OneToOneField(Company, on_delete=models.CASCADE, related_name='listing')
    is_listed = models.BooleanField(default=True)
    featured = models.BooleanField(default=False)
    description = models.TextField(blank=True)
    website = models.URLField(blank=True)
    social_links = models.JSONField(default=dict, blank=True)
    # categories = models.JSONField(default=list, blank=True) # Replaced JSONField
    categories = models.ManyToManyField(CompanyCategory, blank=True, related_name='company_listings') # Use ManyToManyField
    tags = models.JSONField(default=list, blank=True) # Keep tags as JSON for simplicity for now
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    # Add rating fields
    avg_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0.0)
    total_ratings = models.IntegerField(default=0)

    objects = CompanyListingManager()

    class Meta:
        verbose_name = _('company listing')
        verbose_name_plural = _('company listings')
        ordering = ['-featured', '-created_at']

    def __str__(self):
        return f"{self.company.name} Listing"

    # update_rating_stats method removed - logic moved to view


class AssistantListingManager(models.Manager):
    def visible(self):
        return self.filter(is_listed=True, assistant__is_public=True)

class AssistantListing(models.Model):
    assistant = models.OneToOneField(Assistant, on_delete=models.CASCADE, related_name='listing')
    is_listed = models.BooleanField(default=True)
    # featured = models.BooleanField(default=False) # Removed, tier is now on Assistant model
    short_description = models.TextField(blank=True)
    long_description = models.TextField(blank=True)
    categories = models.JSONField(default=list, blank=True)
    tags = models.JSONField(default=list, blank=True)
    capabilities = models.JSONField(default=list, blank=True)
    avg_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0.0)
    total_ratings = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = AssistantListingManager()

    class Meta:
        verbose_name = _('assistant listing')
        verbose_name_plural = _('assistant listings')
        ordering = ['-avg_rating', '-created_at'] # Adjusted default ordering

    def __str__(self):
        return f"{self.assistant.name} Listing"

    # update_rating_stats method removed - logic moved to view

class SavedItem(models.Model):
    ITEM_TYPES = [
        ('company', 'Company'),
        ('assistant', 'Assistant'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='saved_items')
    item_type = models.CharField(max_length=20, choices=ITEM_TYPES)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True)
    assistant = models.ForeignKey(Assistant, on_delete=models.CASCADE, null=True, blank=True)
    folder = models.ForeignKey(FavoriteFolder, on_delete=models.SET_NULL, null=True, blank=True, related_name='saved_items') # Link to folder
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _('saved item')
        verbose_name_plural = _('saved items')
        ordering = ['-created_at']
        unique_together = [
            ('user', 'company'),
            ('user', 'assistant'),
        ]

    def __str__(self):
        if self.item_type == 'company':
            return f"{self.user.username} - Saved Company: {self.company.name}"
        return f"{self.user.username} - Saved Assistant: {self.assistant.name}"
    def clean(self):
        # Ensure item type matches folder type if folder is assigned
        if self.folder and self.folder.item_type != self.item_type:
            raise ValidationError(_(f"Cannot add a {self.get_item_type_display()} to a {self.folder.get_item_type_display()} folder."))
        if self.item_type == 'company' and not self.company:
            raise ValidationError({'company': _('Company is required for company-type items')})
        if self.item_type == 'assistant' and not self.assistant:
            raise ValidationError({'assistant': _('Assistant is required for assistant-type items')})
        super().clean()

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

# --- New Model for Directory Ratings ---
class DirectoryRating(models.Model):
    """Stores ratings submitted directly from the directory listing for either an Assistant or a Company."""
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='directory_ratings')
    # Allow linking to either Assistant or Company, but not both
    assistant = models.ForeignKey(Assistant, on_delete=models.CASCADE, related_name='directory_ratings', null=True, blank=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='directory_ratings', null=True, blank=True)
    rating = models.IntegerField(
        validators=[
            MinValueValidator(1),
            MaxValueValidator(5)
        ]
    )
    comment = models.TextField(blank=True, help_text="Optional comment about the rating")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('directory rating')
        verbose_name_plural = _('directory ratings')
        ordering = ['-created_at']
        # Ensure a user rates an item (assistant OR company) only once
        unique_together = [
            ('user', 'assistant'),
            ('user', 'company'),
        ]
        constraints = [
            models.CheckConstraint(
                check=(
                    models.Q(assistant__isnull=False, company__isnull=True) |
                    models.Q(assistant__isnull=True, company__isnull=False)
                ),
                name='rating_must_link_to_one_item'
            )
        ]

    def __str__(self):
        if self.assistant:
            return f"{self.user.username}'s rating for Assistant {self.assistant.name}: {self.rating}"
        elif self.company:
            return f"{self.user.username}'s rating for Company {self.company.name}: {self.rating}"
        return f"{self.user.username}'s rating (unlinked)" # Should not happen with constraint

    def save(self, *args, **kwargs):
        # Ensure constraint is checked before saving if DB doesn't support it fully
        if not ((self.assistant is None) ^ (self.company is None)):
             raise ValidationError("Rating must be linked to either an Assistant or a Company, but not both.")
        super().save(*args, **kwargs)
        # After saving a rating, the calling view should handle updating stats
        # (Removed automatic update call from here)
