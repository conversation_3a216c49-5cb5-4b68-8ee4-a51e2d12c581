import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

print("Starting RegistrationLink table creation script...")

# We'll use Django's database connection

# SQL to create the accounts_registrationlink table
registrationlink_sql = """
CREATE TABLE IF NOT EXISTS "accounts_registrationlink" (
    "id" serial NOT NULL PRIMARY KEY,
    "token" uuid NOT NULL UNIQUE,
    "expires_at" timestamp with time zone NULL,
    "max_uses" integer NULL,
    "uses_count" integer NOT NULL,
    "is_active" boolean NOT NULL,
    "notes" varchar(255) NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "qr_code" varchar(100) NULL,
    "company_id" integer NOT NULL REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED,
    "created_by_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
    "intended_group_id" integer NULL REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# SQL to create the accounts_registrationlink_accessible_folders table
accessible_folders_sql = """
CREATE TABLE IF NOT EXISTS "accounts_registrationlink_accessible_folders" (
    "id" serial NOT NULL PRIMARY KEY,
    "registrationlink_id" integer NOT NULL,
    "assistantfolder_id" integer NOT NULL,
    CONSTRAINT "accounts_registrationlink_accessible_folders_registrationlink_id_assistantfolder_id_unique" UNIQUE ("registrationlink_id", "assistantfolder_id"),
    CONSTRAINT "accounts_registrationlink_accessible_folders_registrationlink_id_fkey" FOREIGN KEY ("registrationlink_id") REFERENCES "accounts_registrationlink" ("id") DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT "accounts_registrationlink_accessible_folders_assistantfolder_id_fkey" FOREIGN KEY ("assistantfolder_id") REFERENCES "assistants_assistantfolder" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# Execute the SQL
try:
    with connection.cursor() as cursor:
        print("Creating accounts_registrationlink table...")
        cursor.execute(registrationlink_sql)
        print("RegistrationLink table created successfully!")

        print("Creating accounts_registrationlink_accessible_folders table...")
        cursor.execute(accessible_folders_sql)
        print("RegistrationLink accessible_folders table created successfully!")

    print("Tables created successfully!")
except Exception as e:
    print(f"Error creating tables: {e}")
