import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

# Get all models from the project
from django.apps import apps

def get_table_name(model):
    """Get the database table name for a model."""
    return model._meta.db_table

def check_table_exists(table_name):
    """Check if a table exists in the database."""
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = %s
            );
        """, [table_name])
        return cursor.fetchone()[0]

# Get all models
all_models = apps.get_models()
print(f"Found {len(all_models)} models")

# Check which tables exist
missing_tables = []
existing_tables = []
for model in all_models:
    table_name = get_table_name(model)
    exists = check_table_exists(table_name)
    if not exists:
        missing_tables.append((model.__name__, table_name))
    else:
        existing_tables.append((model.__name__, table_name))

# Print results
print(f"\nExisting tables ({len(existing_tables)}):")
for model_name, table_name in existing_tables:
    print(f"  - {model_name}: {table_name}")

if missing_tables:
    print(f"\nMissing tables ({len(missing_tables)}):")
    for model_name, table_name in missing_tables:
        print(f"  - {model_name}: {table_name}")
else:
    print("\nAll tables exist in the database.")
