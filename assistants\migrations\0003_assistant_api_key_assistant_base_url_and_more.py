# Generated by Django 4.2.21 on 2025-05-22 22:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("assistants", "0002_alter_assistant_model"),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name="assistant",
            name="api_key",
            field=models.CharField(
                blank=True,
                help_text="API key for OpenAI Compatible models",
                max_length=255,
            ),
        ),
        migrations.AddField(
            model_name="assistant",
            name="base_url",
            field=models.URLField(
                blank=True,
                default="https://api.openai.com/v1",
                help_text="Base URL for OpenAI Compatible API",
                max_length=255,
            ),
        ),
        migrations.AddField(
            model_name="assistant",
            name="custom_model_name",
            field=models.CharField(
                blank=True,
                help_text="Model name for OpenAI Compatible API",
                max_length=100,
            ),
        ),
        migrations.AlterField(
            model_name="assistant",
            name="model",
            field=models.Char<PERSON>ield(
                choices=[
                    ("gpt-3.5-turbo", "GPT-3.5 Turbo"),
                    ("gpt-4", "GPT-4"),
                    ("gpt-4-turbo", "GPT-4 Turbo"),
                    ("claude-2", "Claude 2"),
                    ("claude-instant", "Claude Instant"),
                    ("gemini-2.0-flash", "Gemini 2.0 Flash"),
                    ("llama-3.3-70b-versatile", "Llama 3.3 70B Versatile"),
                    ("openai-compatible", "OpenAI Compatible"),
                ],
                default="gpt-3.5-turbo",
                max_length=50,
            ),
        ),
    ]
