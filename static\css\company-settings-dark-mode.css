/**
 * Company Settings Dark Mode CSS
 * Specific dark mode styling for company settings pages with computed values
 */

/* CSS Variables for Dark Mode */
:root {
  --dark-bg-primary: #121212;
  --dark-bg-secondary: #1a1a1a;
  --dark-bg-tertiary: #252525;
  --dark-border-color: #333333;
  --dark-text-primary: #ffffff;
  --dark-text-secondary: #aaaaaa;
  --dark-accent-color: #0077ff;
  --dark-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Company Settings container */
[data-theme="dark"] .container.py-4 {
  background-color: var(--dark-bg-primary) !important;
  color: var(--dark-text-primary) !important;
}

/* Company Settings cards */
[data-theme="dark"] .card {
  background-color: var(--dark-bg-secondary) !important;
  border: 1px solid var(--dark-border-color) !important;
  border-radius: 8px !important;
  box-shadow: var(--dark-shadow) !important;
  color: var(--dark-text-primary) !important;
}

/* Card headers */
[data-theme="dark"] .card-header {
  background-color: var(--dark-bg-tertiary) !important;
  border-bottom: 1px solid var(--dark-border-color) !important;
  color: var(--dark-text-primary) !important;
}

/* Card bodies */
[data-theme="dark"] .card-body {
  background-color: var(--dark-bg-secondary) !important;
  color: var(--dark-text-primary) !important;
}

/* Form elements */
[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
  background-color: var(--dark-bg-tertiary) !important;
  border: 1px solid var(--dark-border-color) !important;
  color: var(--dark-text-primary) !important;
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
  border-color: var(--dark-accent-color) !important;
  box-shadow: 0 0 0 3px rgba(0, 119, 255, 0.25) !important;
}

/* Form labels */
[data-theme="dark"] .form-label {
  color: var(--dark-text-primary) !important;
}

/* Form help text */
[data-theme="dark"] .form-text {
  color: var(--dark-text-secondary) !important;
}

/* Checkboxes */
[data-theme="dark"] .form-check-input {
  background-color: var(--dark-bg-tertiary) !important;
  border: 1px solid var(--dark-border-color) !important;
}

[data-theme="dark"] .form-check-input:checked {
  background-color: var(--dark-accent-color) !important;
  border-color: var(--dark-accent-color) !important;
}

/* Buttons */
[data-theme="dark"] .btn-light {
  background-color: var(--dark-border-color) !important;
  border-color: #444444 !important;
  color: var(--dark-text-primary) !important;
}

[data-theme="dark"] .btn-light:hover {
  background-color: #444444 !important;
  border-color: #555555 !important;
}

/* Directory Categories section */
[data-theme="dark"] #directory-categories {
  background-color: var(--dark-bg-secondary) !important;
  border: 1px solid var(--dark-border-color) !important;
  border-radius: 8px !important;
  padding: 20px !important;
}

/* Advanced Settings section */
[data-theme="dark"] #advanced-settings {
  background-color: var(--dark-bg-secondary) !important;
  border: 1px solid var(--dark-border-color) !important;
  border-radius: 8px !important;
  padding: 20px !important;
}

/* Modals */
[data-theme="dark"] .modal-content {
  background-color: var(--dark-bg-secondary) !important;
  border: 1px solid var(--dark-border-color) !important;
  color: var(--dark-text-primary) !important;
}

[data-theme="dark"] .modal-header {
  border-bottom: 1px solid var(--dark-border-color) !important;
}

[data-theme="dark"] .modal-footer {
  border-top: 1px solid var(--dark-border-color) !important;
}

/* Specific targeting for company settings sections */
[data-theme="dark"] .row.align-items-center.mb-4 {
  background-color: var(--dark-bg-primary) !important;
  color: var(--dark-text-primary) !important;
}

/* Target the specific white sections in the company settings page */
[data-theme="dark"] .bg-light,
[data-theme="dark"] .bg-white,
[data-theme="dark"] .bg-light-subtle,
[data-theme="dark"] [style*="background-color: white"],
[data-theme="dark"] [style*="background-color: #fff"],
[data-theme="dark"] [style*="background-color: rgb(255, 255, 255)"],
[data-theme="dark"] [style*="background-color: rgba(255, 255, 255"] {
  background-color: var(--dark-bg-primary) !important;
  color: var(--dark-text-primary) !important;
}

/* Specific overrides for Bootstrap 5 utility classes */
[data-theme="dark"] .bg-light-subtle {
  background-color: #121212 !important;
}

[data-theme="dark"] .bg-body-secondary {
  background-color: var(--dark-bg-secondary) !important;
}

[data-theme="dark"] .bg-body-tertiary {
  background-color: var(--dark-bg-tertiary) !important;
}

[data-theme="dark"] .text-body-secondary {
  color: var(--dark-text-secondary) !important;
}

[data-theme="dark"] .border-light-subtle {
  border-color: var(--dark-border-color) !important;
}

/* Ensure all cards in company settings have dark backgrounds */
[data-theme="dark"] .container.py-4 .card,
[data-theme="dark"] .container.py-4 .card-header,
[data-theme="dark"] .container.py-4 .card-body {
  background-color: var(--dark-bg-secondary) !important;
  color: var(--dark-text-primary) !important;
}

/* Ensure all form elements in company settings have dark backgrounds */
[data-theme="dark"] .container.py-4 .form-control,
[data-theme="dark"] .container.py-4 .form-select {
  background-color: var(--dark-bg-tertiary) !important;
  color: var(--dark-text-primary) !important;
}

/* Ensure all text in company settings is white */
[data-theme="dark"] .container.py-4 h1,
[data-theme="dark"] .container.py-4 h2,
[data-theme="dark"] .container.py-4 h3,
[data-theme="dark"] .container.py-4 h4,
[data-theme="dark"] .container.py-4 h5,
[data-theme="dark"] .container.py-4 h6,
[data-theme="dark"] .container.py-4 p,
[data-theme="dark"] .container.py-4 span,
[data-theme="dark"] .container.py-4 label {
  color: var(--dark-text-primary) !important;
}

/* Ensure muted text is properly styled */
[data-theme="dark"] .container.py-4 .text-muted {
  color: var(--dark-text-secondary) !important;
}

/* Add specific computed values for elements shown in the screenshot */
[data-theme="dark"] .directory-title {
  color: var(--dark-text-primary) !important;
}

[data-theme="dark"] .directory-settings-card {
  background-color: var(--dark-bg-secondary) !important;
  border-color: var(--dark-border-color) !important;
}

[data-theme="dark"] .card-header.bg-dark {
  background-color: var(--dark-bg-tertiary) !important;
}

/* Checkbox styling with computed values */
[data-theme="dark"] .form-check {
  background-color: transparent !important;
}

[data-theme="dark"] .form-check-label {
  color: var(--dark-text-primary) !important;
}

/* Dropdown menus in settings */
[data-theme="dark"] .dropdown-menu {
  background-color: var(--dark-bg-tertiary) !important;
  border-color: var(--dark-border-color) !important;
}

[data-theme="dark"] .dropdown-item {
  color: var(--dark-text-primary) !important;
}

[data-theme="dark"] .dropdown-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Specific elements from the screenshot */
[data-theme="dark"] .directory-categories {
  background-color: var(--dark-bg-secondary) !important;
  color: var(--dark-text-primary) !important;
}

[data-theme="dark"] textarea.form-control {
  background-color: var(--dark-bg-tertiary) !important;
  color: var(--dark-text-primary) !important;
  border-color: var(--dark-border-color) !important;
}

/* Advanced settings section */
[data-theme="dark"] .advanced-settings {
  background-color: var(--dark-bg-secondary) !important;
  color: var(--dark-text-primary) !important;
}

/* Timezone and language selectors */
[data-theme="dark"] select.form-select {
  background-color: var(--dark-bg-tertiary) !important;
  color: var(--dark-text-primary) !important;
  border-color: var(--dark-border-color) !important;
}

/* Featured checkbox styling */
[data-theme="dark"] .featured-checkbox {
  background-color: var(--dark-bg-tertiary) !important;
}

/* No Change option in select */
[data-theme="dark"] option {
  background-color: var(--dark-bg-tertiary) !important;
  color: var(--dark-text-primary) !important;
}

/* Computed values for specific elements in the screenshot */
[data-theme="dark"] .border-rounded-color {
  border-color: var(--dark-border-color) !important;
}

[data-theme="dark"] .text-dark {
  color: var(--dark-text-primary) !important;
}

/* Ensure code blocks and syntax highlighting are dark */
[data-theme="dark"] pre,
[data-theme="dark"] code {
  background-color: var(--dark-bg-tertiary) !important;
  color: var(--dark-text-primary) !important;
  border-color: var(--dark-border-color) !important;
}

/* Specific styles for the checkbox in the screenshot */
[data-theme="dark"] .form-check-input[type="checkbox"] {
  background-color: var(--dark-bg-tertiary) !important;
  border-color: var(--dark-border-color) !important;
}

[data-theme="dark"] .form-check-input[type="checkbox"]:checked {
  background-color: var(--dark-accent-color) !important;
  border-color: var(--dark-accent-color) !important;
}

/* Style for the "Yes" text next to checkbox */
[data-theme="dark"] .form-check-label.yes-label {
  color: var(--dark-text-primary) !important;
  font-weight: 500;
}

/* Style for the "No Change" option in select */
[data-theme="dark"] .no-change-option {
  color: var(--dark-text-secondary) !important;
  font-style: italic;
}

/* Style for the directory categories section */
[data-theme="dark"] #directory-categories-container {
  background-color: var(--dark-bg-secondary) !important;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid var(--dark-border-color) !important;
}

/* Style for the description textarea */
[data-theme="dark"] textarea#id_description {
  background-color: var(--dark-bg-tertiary) !important;
  color: var(--dark-text-primary) !important;
  border-color: var(--dark-border-color) !important;
  min-height: 100px;
}

/* Computed values for the DevTools panel elements */
[data-theme="dark"] .devtools-panel {
  background-color: var(--dark-bg-primary) !important;
  color: var(--dark-text-primary) !important;
}

[data-theme="dark"] .devtools-code {
  color: #88ccff !important; /* Light blue for code */
}

[data-theme="dark"] .devtools-property {
  color: #ff88aa !important; /* Pink for properties */
}

[data-theme="dark"] .devtools-value {
  color: #aaffaa !important; /* Light green for values */
}
