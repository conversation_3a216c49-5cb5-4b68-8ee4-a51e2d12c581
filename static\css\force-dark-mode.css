/**
 * Force Dark Mode CSS
 * This file ensures dark mode is applied to all elements
 */

/* Force dark mode on html and body */
html, 
body {
  background-color: #121212 !important;
  color: #ffffff !important;
  color-scheme: dark !important;
}

/* Force dark mode data attribute */
html[data-theme],
body[data-theme] {
  --bs-body-color: #f8f9fa !important;
  --bs-body-bg: #121212 !important;
  --bs-primary: #0d6efd !important;
  --bs-secondary: #6c757d !important;
  color-scheme: dark !important;
  data-theme: dark !important;
}

/* Force dark mode on main content */
main {
  background-color: #121212 !important;
  color: #ffffff !important;
}

/* Force dark mode on cards */
.card {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

/* Force dark mode on modals */
.modal-content {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

/* Force dark mode on inputs */
input, 
textarea, 
select, 
.form-control {
  background-color: #2d2d2d !important;
  color: #ffffff !important;
  border-color: #444444 !important;
}

/* Force dark mode on buttons */
.btn:not(.btn-primary):not(.btn-secondary):not(.btn-success):not(.btn-danger):not(.btn-warning):not(.btn-info) {
  background-color: #2d2d2d !important;
  color: #ffffff !important;
  border-color: #444444 !important;
}

/* Force dark mode on tables */
table, th, td {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

/* Force dark mode on navbar */
.navbar {
  background-color: #1a1a1a !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

/* Force dark mode on dropdowns */
.dropdown-menu {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

.dropdown-item {
  color: #ffffff !important;
}

.dropdown-item:hover {
  background-color: #2d2d2d !important;
}

/* Force dark mode on list groups */
.list-group-item {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

/* Force dark mode on alerts */
.alert:not(.alert-primary):not(.alert-secondary):not(.alert-success):not(.alert-danger):not(.alert-warning):not(.alert-info) {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

/* Force dark mode on footer */
footer {
  background-color: #1a1a1a !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

/* Force dark mode on TinyMCE */
.tox-tinymce {
  border-color: #333333 !important;
}

.tox-editor-container {
  background-color: #1e1e1e !important;
}

/* Force dark mode on code blocks */
pre, code {
  background-color: #2d2d2d !important;
  color: #e6e6e6 !important;
  border-color: #444444 !important;
}

/* Force dark mode on horizontal rules */
hr {
  border-color: #333333 !important;
}

/* Force dark mode on badges */
.badge:not(.bg-primary):not(.bg-secondary):not(.bg-success):not(.bg-danger):not(.bg-warning):not(.bg-info) {
  background-color: #2d2d2d !important;
  color: #ffffff !important;
}

/* Force dark mode on pagination */
.pagination .page-link {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

.pagination .page-item.active .page-link {
  background-color: #0d6efd !important;
  border-color: #0d6efd !important;
}

/* Force dark mode on breadcrumbs */
.breadcrumb {
  background-color: #1e1e1e !important;
}

.breadcrumb-item {
  color: #ffffff !important;
}

.breadcrumb-item.active {
  color: #adb5bd !important;
}

/* Force dark mode on tooltips */
.tooltip .tooltip-inner {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
}

/* Force dark mode on popovers */
.popover {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

.popover-header {
  background-color: #2d2d2d !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

/* Force dark mode on progress bars */
.progress {
  background-color: #2d2d2d !important;
}

/* Force dark mode on jumbotron */
.jumbotron {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
}

/* Force dark mode on all icons */
i.bi, i.fa, i.fas, i.far, i.fab {
  color: #ffffff !important;
}

/* Force dark mode on links */
a:not(.btn):not(.nav-link) {
  color: #6ea8fe !important;
}

a:not(.btn):not(.nav-link):hover {
  color: #9ec5fe !important;
}

/* Force dark mode on custom elements */
[class*="bg-light"] {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
}

[class*="text-dark"] {
  color: #ffffff !important;
}

[class*="border-light"] {
  border-color: #333333 !important;
}

/* Force dark mode on any remaining light elements */
.bg-white, .bg-light {
  background-color: #1e1e1e !important;
}

.text-dark {
  color: #ffffff !important;
}

.border-light {
  border-color: #333333 !important;
}
