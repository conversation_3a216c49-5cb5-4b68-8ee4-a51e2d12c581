import decimal
from django.core.management.base import BaseCommand
from django.db import transaction, models
from accounts.models import Company
from assistants.models import Assistant, Interaction # Import Interaction
from directory.models import CompanyListing, AssistantListing

class Command(BaseCommand):
    help = 'Ensures CompanyListing and AssistantListing records exist for all Companies and Assistants.'

    @transaction.atomic
    def handle(self, *args, **options):
        self.stdout.write('Starting listing synchronization...')
        
        # Sync Company Listings
        company_created_count = 0
        company_updated_count = 0
        companies = Company.objects.all()
        self.stdout.write(f'Processing {companies.count()} companies...')
        for company in companies:
            try:
                # Check if info exists, default list_in_directory if not
                list_in_directory = getattr(company, 'info', None) and company.info.list_in_directory
                
                listing, created = CompanyListing.objects.update_or_create(
                    company=company,
                    defaults={
                        'is_listed': list_in_directory,
                        'description': getattr(company.info, 'description', '') if hasattr(company, 'info') else '',
                        'website': getattr(company.info, 'website', '') if hasattr(company, 'info') else '',
                    }
                )
                if created:
                    company_created_count += 1
                else:
                    # Optionally check if update was needed
                    if listing.is_listed != list_in_directory:
                         listing.is_listed = list_in_directory
                         listing.save(update_fields=['is_listed'])
                         company_updated_count += 1
                    # Could add more checks/updates here if needed
                    pass # No other fields to sync by default for now
                    
            except Exception as e:
                self.stderr.write(self.style.ERROR(f'Error processing company {company.name} (ID: {company.id}): {e}'))

        self.stdout.write(self.style.SUCCESS(f'Company sync complete. Created: {company_created_count}, Updated: {company_updated_count}'))

        # Sync Assistant Listings
        assistant_created_count = 0
        assistant_updated_count = 0
        assistants = Assistant.objects.all()
        self.stdout.write(f'Processing {assistants.count()} assistants...')
        for assistant in assistants:
            try:
                listing, created = AssistantListing.objects.update_or_create(
                    assistant=assistant,
                    defaults={
                        'is_listed': assistant.is_public,
                        'short_description': assistant.description,
                        'avg_rating': assistant.interactions.filter(rating__isnull=False).aggregate(models.Avg('rating'))['rating__avg'] or decimal.Decimal('0.0'),
                        'total_ratings': assistant.interactions.filter(rating__isnull=False).count(),
                        # Ensure tags/categories fields exist, default to empty list if creating
                        'tags': [], 
                        'categories': [],
                    }
                )
                if created:
                    assistant_created_count += 1
                else:
                    # If not created, explicitly update rating stats as they might have changed
                    new_avg = assistant.interactions.filter(rating__isnull=False).aggregate(models.Avg('rating'))['rating__avg'] or decimal.Decimal('0.0')
                    new_count = assistant.interactions.filter(rating__isnull=False).count()
                    
                    update_needed = False
                    fields_to_update = []
                    
                    if listing.is_listed != assistant.is_public:
                        listing.is_listed = assistant.is_public
                        fields_to_update.append('is_listed')
                        update_needed = True
                    if listing.short_description != assistant.description:
                        listing.short_description = assistant.description
                        fields_to_update.append('short_description')
                        update_needed = True
                    # Use Decimal comparison for avg_rating
                    # Ensure avg_rating is Decimal before quantize
                    current_avg_decimal = listing.avg_rating if isinstance(listing.avg_rating, decimal.Decimal) else decimal.Decimal(str(listing.avg_rating or '0.0'))
                    if current_avg_decimal.quantize(decimal.Decimal("0.01")) != new_avg.quantize(decimal.Decimal("0.01")):
                        listing.avg_rating = new_avg
                        fields_to_update.append('avg_rating')
                        update_needed = True
                    if listing.total_ratings != new_count:
                        listing.total_ratings = new_count
                        fields_to_update.append('total_ratings')
                        update_needed = True

                    if update_needed:
                        listing.save(update_fields=fields_to_update)
                        assistant_updated_count += 1
            # Corrected indentation for the except block
            except Exception as e:
                self.stderr.write(self.style.ERROR(f'Error processing assistant {assistant.name} (ID: {assistant.id}): {e}'))

        self.stdout.write(self.style.SUCCESS(f'Assistant sync complete. Created: {assistant_created_count}, Updated: {assistant_updated_count}'))
        self.stdout.write(self.style.SUCCESS('Listing synchronization finished.'))
