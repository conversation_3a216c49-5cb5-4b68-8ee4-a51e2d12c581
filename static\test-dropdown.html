<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Category Dropdown Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            color: #333;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .dropdown-container {
            position: relative;
        }
        
        .dropdown-container input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        
        .dropdown-list {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1000;
            display: none;
            max-height: 200px;
            overflow-y: auto;
            background-color: #fff;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .dropdown-list.show {
            display: block;
        }
        
        .dropdown-item {
            padding: 10px;
            cursor: pointer;
        }
        
        .dropdown-item:hover {
            background-color: #f5f5f5;
        }
    </style>
</head>
<body>
    <h1>Category Dropdown Test</h1>
    
    <div class="form-group">
        <label for="industry">Industry</label>
        <div class="dropdown-container industry-dropdown-container">
            <input type="text" id="industry" class="form-control" placeholder="Select or type industry">
            <input type="hidden" name="industry_value" id="industry_value">
            <div class="dropdown-list"></div>
        </div>
    </div>
    
    <div class="form-group">
        <label for="categories">Categories</label>
        <div class="dropdown-container category-dropdown-container">
            <input type="text" id="categories" class="form-control" placeholder="Select or type categories">
            <input type="hidden" name="categories_value" id="categories_value">
            <div class="dropdown-list"></div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log("Category Dropdowns script loaded");
            
            // Global variables
            let categoriesData = {};
            let industryDropdowns = document.querySelectorAll('.industry-dropdown-container');
            let categoryDropdowns = document.querySelectorAll('.category-dropdown-container');
            
            console.log("Found industry dropdowns:", industryDropdowns.length);
            console.log("Found category dropdowns:", categoryDropdowns.length);
            
            // Load categories data
            fetch('/static/json/category.json')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to load categories data');
                    }
                    return response.json();
                })
                .then(data => {
                    categoriesData = data;
                    console.log("Categories loaded:", Object.keys(categoriesData).length);
                    
                    // Initialize dropdowns
                    setupIndustryDropdowns();
                    
                    // Check for pre-selected industries
                    checkPreselectedIndustries();
                })
                .catch(error => {
                    console.error("Error loading categories:", error);
                    // For testing, add some sample data
                    categoriesData = {
                        "Technology": ["Software", "Hardware", "Cloud Computing"],
                        "Healthcare": ["Hospitals", "Pharmaceuticals", "Medical Devices"],
                        "Candy & Soda": ["Candy and other confectionery", "Bottled-canned soft drinks", "Flavoring syrup", "Potato chips", "Manufactured ice"]
                    };
                    console.log("Using sample data instead");
                    setupIndustryDropdowns();
                });
            
            // Set up industry dropdowns
            function setupIndustryDropdowns() {
                industryDropdowns.forEach(container => {
                    const input = container.querySelector('input');
                    const dropdownList = container.querySelector('.dropdown-list');
                    
                    if (!input || !dropdownList) {
                        console.error("Missing input or dropdown list in industry container");
                        return;
                    }
                    
                    // Create industry options
                    const industries = Object.keys(categoriesData);
                    
                    // Clear existing options
                    dropdownList.innerHTML = '';
                    
                    // Add options to dropdown
                    industries.forEach(industry => {
                        const option = document.createElement('div');
                        option.className = 'dropdown-item';
                        option.textContent = industry;
                        
                        option.addEventListener('click', () => {
                            // Set the value
                            input.value = industry;
                            
                            // Hide dropdown
                            dropdownList.style.display = 'none';
                            dropdownList.classList.remove('show');
                            
                            // Update category dropdowns
                            updateCategoryDropdowns(industry);
                        });
                        
                        dropdownList.appendChild(option);
                    });
                    
                    // Show dropdown on focus
                    input.addEventListener('focus', () => {
                        dropdownList.style.display = 'block';
                        dropdownList.classList.add('show');
                    });
                    
                    // Filter options on input
                    input.addEventListener('input', () => {
                        const value = input.value.toLowerCase();
                        
                        // Show dropdown
                        dropdownList.style.display = 'block';
                        dropdownList.classList.add('show');
                        
                        // Filter options
                        const options = dropdownList.querySelectorAll('.dropdown-item');
                        options.forEach(option => {
                            if (option.textContent.toLowerCase().includes(value)) {
                                option.style.display = 'block';
                            } else {
                                option.style.display = 'none';
                            }
                        });
                    });
                    
                    // Hide dropdown when clicking outside
                    document.addEventListener('click', (event) => {
                        if (!container.contains(event.target)) {
                            dropdownList.style.display = 'none';
                            dropdownList.classList.remove('show');
                        }
                    });
                });
            }
            
            // Update all category dropdowns based on selected industry
            function updateCategoryDropdowns(industry) {
                console.log("Updating category dropdowns for industry:", industry);
                
                categoryDropdowns.forEach(container => {
                    const input = container.querySelector('input');
                    const dropdownList = container.querySelector('.dropdown-list');
                    
                    if (!input || !dropdownList) {
                        console.error("Missing input or dropdown list in category container");
                        return;
                    }
                    
                    // Clear existing options
                    dropdownList.innerHTML = '';
                    input.value = '';
                    
                    // Get categories for selected industry
                    const categories = categoriesData[industry] || [];
                    console.log("Categories found:", categories.length);
                    
                    // Add options to dropdown
                    categories.forEach(category => {
                        const option = document.createElement('div');
                        option.className = 'dropdown-item';
                        option.textContent = category;
                        
                        option.addEventListener('click', () => {
                            // Set the value
                            input.value = category;
                            
                            // Hide dropdown
                            dropdownList.style.display = 'none';
                            dropdownList.classList.remove('show');
                        });
                        
                        dropdownList.appendChild(option);
                    });
                    
                    // Show dropdown on focus
                    input.addEventListener('focus', () => {
                        dropdownList.style.display = 'block';
                        dropdownList.classList.add('show');
                    });
                    
                    // Filter options on input
                    input.addEventListener('input', () => {
                        const value = input.value.toLowerCase();
                        
                        // Show dropdown
                        dropdownList.style.display = 'block';
                        dropdownList.classList.add('show');
                        
                        // Filter options
                        const options = dropdownList.querySelectorAll('.dropdown-item');
                        options.forEach(option => {
                            if (option.textContent.toLowerCase().includes(value)) {
                                option.style.display = 'block';
                            } else {
                                option.style.display = 'none';
                            }
                        });
                    });
                    
                    // Hide dropdown when clicking outside
                    document.addEventListener('click', (event) => {
                        if (!container.contains(event.target)) {
                            dropdownList.style.display = 'none';
                            dropdownList.classList.remove('show');
                        }
                    });
                });
            }
            
            // Check for pre-selected industries
            function checkPreselectedIndustries() {
                industryDropdowns.forEach(container => {
                    const input = container.querySelector('input');
                    if (input && input.value) {
                        console.log("Found pre-selected industry:", input.value);
                        updateCategoryDropdowns(input.value);
                    }
                });
            }
        });
    </script>
</body>
</html>
