"""
<PERSON><PERSON><PERSON> to fix common database issues when migrating from SQLite to PostgreSQL.
"""
import os
import django
from django.db import connection
from django.apps import apps

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

def check_table_exists(table_name):
    """Check if a table exists in the database."""
    cursor = connection.cursor()
    cursor.execute("""
    SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = %s
    )
    """, [table_name])
    exists = cursor.fetchone()[0]
    cursor.close()
    return exists

def check_column_exists(table_name, column_name):
    """Check if a column exists in a table."""
    cursor = connection.cursor()
    cursor.execute("""
    SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_name = %s AND column_name = %s
    )
    """, [table_name, column_name])
    exists = cursor.fetchone()[0]
    cursor.close()
    return exists

def create_session_table():
    """Create the django_session table manually if it doesn't exist."""
    if not check_table_exists('django_session'):
        print("Creating django_session table...")
        cursor = connection.cursor()
        cursor.execute("""
        CREATE TABLE django_session (
            session_key varchar(40) NOT NULL PRIMARY KEY,
            session_data text NOT NULL,
            expire_date timestamp with time zone NOT NULL
        );
        """)
        cursor.execute("""
        CREATE INDEX django_session_expire_date_idx ON django_session (expire_date);
        """)
        cursor.close()
        print("django_session table created successfully.")
    else:
        print("django_session table already exists.")

def create_auth_tables():
    """Create auth tables if they don't exist."""
    tables = [
        'auth_group',
        'auth_group_permissions',
        'auth_permission',
        'auth_user',
        'auth_user_groups',
        'auth_user_user_permissions',
    ]
    
    for table in tables:
        if not check_table_exists(table):
            print(f"Table {table} doesn't exist. You should run migrations for the auth app.")
            return False
    
    print("All auth tables exist.")
    return True

def fix_sequence_issues():
    """Fix sequence issues in PostgreSQL."""
    print("Fixing sequence issues...")
    cursor = connection.cursor()
    
    # Get all models with auto-incrementing primary keys
    for model in apps.get_models():
        table_name = model._meta.db_table
        pk_column = model._meta.pk.column
        
        # Check if the primary key is auto-incrementing
        if model._meta.pk.is_auto_field:
            sequence_name = f"{table_name}_{pk_column}_seq"
            
            # Check if the sequence exists
            cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.sequences
                WHERE sequence_name = %s
            )
            """, [sequence_name])
            
            sequence_exists = cursor.fetchone()[0]
            
            if sequence_exists:
                print(f"Fixing sequence for {table_name}.{pk_column}...")
                # Get the maximum value of the primary key
                cursor.execute(f"SELECT MAX({pk_column}) FROM {table_name}")
                max_id = cursor.fetchone()[0]
                
                if max_id is not None:
                    # Set the sequence to the next value after the maximum
                    cursor.execute(f"SELECT setval('{sequence_name}', {max_id})")
                    print(f"Sequence {sequence_name} set to {max_id}")
                else:
                    print(f"Table {table_name} is empty, sequence left at default")
            else:
                print(f"Sequence {sequence_name} doesn't exist, skipping")
    
    cursor.close()
    print("Sequence issues fixed.")

def list_migration_conflicts():
    """List potential migration conflicts."""
    from django.db.migrations.recorder import MigrationRecorder
    
    recorder = MigrationRecorder(connection)
    applied_migrations = recorder.applied_migrations()
    
    # Group migrations by app
    migrations_by_app = {}
    for app, name in applied_migrations:
        if app not in migrations_by_app:
            migrations_by_app[app] = []
        migrations_by_app[app].append(name)
    
    # Check for potential conflicts
    print("Checking for potential migration conflicts...")
    for app, migrations in migrations_by_app.items():
        # Look for merge migrations
        merge_migrations = [m for m in migrations if 'merge' in m]
        if merge_migrations:
            print(f"App '{app}' has merge migrations: {', '.join(merge_migrations)}")
            print("  This could indicate previous migration conflicts.")
        
        # Look for migrations with similar names
        for i, m1 in enumerate(migrations):
            for m2 in migrations[i+1:]:
                if m1 != m2 and (m1 in m2 or m2 in m1):
                    print(f"App '{app}' has similar migration names: {m1}, {m2}")
                    print("  This could indicate potential conflicts.")

def main():
    """Main function to fix database issues."""
    print("Checking for common database issues...")
    
    # Check if essential tables exist
    create_session_table()
    create_auth_tables()
    
    # Fix sequence issues
    fix_sequence_issues()
    
    # List potential migration conflicts
    list_migration_conflicts()
    
    print("Database check completed.")

if __name__ == "__main__":
    main()
