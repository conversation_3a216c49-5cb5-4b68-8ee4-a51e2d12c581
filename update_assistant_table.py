import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

print("Starting Assistant table update script...")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

# SQL to add missing columns to the assistants_assistant table
alter_assistant_sql = """
ALTER TABLE "assistants_assistant" 
ADD COLUMN IF NOT EXISTS "tier" varchar(10) NOT NULL DEFAULT 'standard',
ADD COLUMN IF NOT EXISTS "is_featured" boolean NOT NULL DEFAULT false,
ADD COLUMN IF NOT EXISTS "tier_expiry_date" timestamp with time zone NULL,
ADD COLUMN IF NOT EXISTS "featured_expiry_date" timestamp with time zone NULL,
ADD COLUMN IF NOT EXISTS "tier_change_pending" boolean NOT NULL DEFAULT false,
ADD COLUMN IF NOT EXISTS "featured_request_pending" boolean NOT NULL DEFAULT false,
ADD COLUMN IF NOT EXISTS "requested_tier" varchar(10) NULL,
ADD COLUMN IF NOT EXISTS "requested_tier_duration" integer NULL,
ADD COLUMN IF NOT EXISTS "requested_featured_duration" integer NULL;
"""

# Connect to the database
try:
    print("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(**db_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    
    # Alter the assistants_assistant table
    print("Altering assistants_assistant table...")
    cursor.execute(alter_assistant_sql)
    print("Assistant table updated successfully!")
    
    # Close the connection
    cursor.close()
    conn.close()
    print("Database connection closed.")
    
except Exception as e:
    print(f"Error: {e}")
