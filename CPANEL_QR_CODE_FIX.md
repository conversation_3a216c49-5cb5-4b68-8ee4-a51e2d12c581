# cPanel QR Code Fix Guide

This guide provides instructions for fixing QR code generation issues on cPanel, specifically the issue where the letter "A" is not properly centered in the QR code.

## Problem Description

When deploying the application to cPanel, the QR codes may be generated without the letter "A" in the center, or with the letter "A" positioned incorrectly. This is typically caused by:

1. Missing dependencies (numpy)
2. Font availability issues on cPanel
3. Different vertical offset requirements on cPanel compared to local development

## Solution

We've created several scripts to help diagnose and fix the QR code generation issues on cPanel:

1. `diagnose_qr_code.py` - Diagnoses the issue by testing font loading and QR code generation
2. `fix_qr_code_generation.py` - Interactive script to test different vertical offsets and fix the issue
3. `auto_fix_qr_code.py` - Automatic script that applies a fix with a default offset value

## Step 1: Install Required Dependencies

First, make sure all required dependencies are installed:

```bash
pip install -r requirements.production.txt
```

This will install numpy, which is required for QR code verification and analysis.

## Step 2: Diagnose the Issue

Run the diagnostic script to identify the specific issue:

```bash
python diagnose_qr_code.py
```

This script will:
- Print system information
- Test font loading with different font names
- Generate a test QR code
- Test different vertical offsets

Check the output for clues about what might be causing the issue.

## Step 3: Fix the Issue

### Automatic Fix

For a quick fix, run the automatic fix script:

```bash
python auto_fix_qr_code.py
```

This script will:
- Back up the QR generator file
- Update the vertical offset to a value that works well on cPanel (0)
- Regenerate QR codes for all companies and assistants

### Interactive Fix

If the automatic fix doesn't work, try the interactive fix script:

```bash
python fix_qr_code_generation.py
```

This script will:
- Back up the QR generator file
- Test different vertical offsets
- Ask you to select the best offset value
- Update the vertical offset to the selected value
- Regenerate QR codes for all companies

## Step 4: Verify the Fix

After applying the fix, check the generated QR codes to ensure the letter "A" is properly centered.

You can view the QR codes in the admin interface or directly in the media directory.

## Manual Fix

If the scripts don't work, you can manually fix the issue:

1. Edit `utils/qr_generator.py`
2. Find the line with `QR_LETTER_VERTICAL_OFFSET = -50` (or similar value)
3. Change the value to `0` or another value that works on your cPanel server
4. Save the file
5. Regenerate the QR codes using the Django admin interface or by running:
   ```bash
   python manage.py regenerate_all_qr_codes
   ```

## Troubleshooting

### QR Codes Still Not Generating Correctly

If the QR codes are still not generating correctly after applying the fix:

1. Check the cPanel error logs for any Python errors
2. Verify that the correct fonts are available on the server
3. Try different vertical offset values
4. Make sure numpy is installed correctly

### Font Issues

If the issue is related to font availability:

1. Upload custom fonts to your cPanel server in `~/public_html/fonts`
2. Update the font loading code in `utils/qr_generator.py` to use these custom fonts
3. Regenerate the QR codes

## Technical Details

The QR code generation system uses a vertical offset to ensure the letter "A" is perfectly centered. This offset may need to be different on cPanel compared to local development due to differences in font rendering.

The default value of `-50` works well in most local development environments, but a value of `0` often works better on cPanel servers.

The font loading mechanism has been enhanced to better support cPanel environments by:

1. Adding more Linux font directories to the search path
2. Adding cPanel-specific font directories
3. Adding more font fallbacks (DejaVu Sans, Liberation Sans, etc.)
4. Improving error handling and logging

## Contact Support

If you continue to experience issues with QR code generation on cPanel, please contact support with the following information:

1. The output of the `diagnose_qr_code.py` script
2. The cPanel error logs
3. Screenshots of the incorrectly generated QR codes
