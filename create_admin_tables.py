import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

print("Starting admin tables creation script...")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

# SQL to create the django_admin_log table
admin_log_sql = """
CREATE TABLE IF NOT EXISTS "django_admin_log" (
    "id" serial NOT NULL PRIMARY KEY,
    "action_time" timestamp with time zone NOT NULL,
    "object_id" text NULL,
    "object_repr" varchar(200) NOT NULL,
    "action_flag" smallint NOT NULL CHECK ("action_flag" >= 0),
    "change_message" text NOT NULL,
    "content_type_id" integer NULL REFERENCES "django_content_type" ("id") DEFERRABLE INITIALLY DEFERRED,
    "user_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# Connect to the database
try:
    print("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(**db_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()

    # Create the django_admin_log table
    print("Creating django_admin_log table...")
    cursor.execute(admin_log_sql)
    print("Admin log table created successfully!")

    # Close the connection
    cursor.close()
    conn.close()
    print("Database connection closed.")

except Exception as e:
    print(f"Error: {e}")
