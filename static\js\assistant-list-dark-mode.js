/**
 * Dark Mode JavaScript for Assistant Lists
 * Ensures proper styling of assistant lists in dark mode
 */

// Execute immediately
(function() {
    // Function to update assistant list styling based on theme
    function updateAssistantListStyling() {
        // Check if dark mode is active
        const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';


        if (isDarkMode) {
            // Apply dark mode styles to filter form
            const filterForms = document.querySelectorAll('.filter-form');
            filterForms.forEach(form => {
                form.style.backgroundColor = '#1e1e1e';
                form.style.borderColor = '#333333';
                form.style.color = '#ffffff';
            });

            // Apply dark mode styles to input fields
            const formControls = document.querySelectorAll('.form-control');
            formControls.forEach(input => {
                input.style.backgroundColor = '#333333';
                input.style.borderColor = '#444444';
                input.style.color = '#ffffff';
            });

            // Apply dark mode styles to input group text
            const inputGroupTexts = document.querySelectorAll('.input-group-text');
            inputGroupTexts.forEach(text => {
                text.style.backgroundColor = '#252525';
                text.style.borderColor = '#444444';
                text.style.color = '#ffffff';
            });

            // Apply dark mode styles to select dropdowns
            const formSelects = document.querySelectorAll('.form-select');
            formSelects.forEach(select => {
                select.style.backgroundColor = '#333333';
                select.style.borderColor = '#444444';
                select.style.color = '#ffffff';
                select.style.backgroundImage = "url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e\")";
            });

            // Apply dark mode styles to tier sections
            const tierSections = document.querySelectorAll('.tier-section');
            tierSections.forEach(section => {
                section.style.backgroundColor = '#1e1e1e';
                section.style.borderColor = '#333333';
                section.style.color = '#ffffff';
            });

            // Apply dark mode styles to featured sections
            const featuredSections = document.querySelectorAll('.featured-section');
            featuredSections.forEach(section => {
                section.style.backgroundColor = '#1e1e1e';
                section.style.borderColor = '#333333';
                section.style.color = '#ffffff';
            });

            // Apply dark mode styles to list group items (assistant cards)
            const listGroupItems = document.querySelectorAll('.list-group-item');
            listGroupItems.forEach(item => {
                item.style.backgroundColor = '#252525';
                item.style.borderColor = '#333333';
                item.style.color = '#ffffff';
                item.style.boxShadow = '0 0.125rem 0.25rem rgba(0, 0, 0, 0.2)';
            });

            // Apply dark mode styles to logo containers
            const logoContainers = document.querySelectorAll('.logo-container');
            logoContainers.forEach(container => {
                container.style.backgroundColor = '#333333';
                container.style.borderColor = '#444444';
                container.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.3)';
            });

            // Apply dark mode styles to logo placeholders
            const logoPlaceholders = document.querySelectorAll('.logo-placeholder');
            logoPlaceholders.forEach(placeholder => {
                placeholder.style.backgroundColor = 'rgba(30, 30, 30, 0.5)';
                placeholder.style.color = '#3b7dd8';
            });

            // Apply dark mode styles to text muted
            const textMuteds = document.querySelectorAll('.text-muted');
            textMuteds.forEach(text => {
                text.style.color = '#aaaaaa';
            });

            // Apply dark mode styles to alerts
            const alertLights = document.querySelectorAll('.alert-light');
            alertLights.forEach(alert => {
                alert.style.backgroundColor = '#252525';
                alert.style.borderColor = '#333333';
                alert.style.color = '#ffffff';
            });

            const alertInfos = document.querySelectorAll('.alert-info');
            alertInfos.forEach(alert => {
                alert.style.backgroundColor = '#1a3a57';
                alert.style.borderColor = '#2a5a87';
                alert.style.color = '#ffffff';
            });

            // Apply dark mode styles to badges
            const badgeLights = document.querySelectorAll('.badge.bg-light');
            badgeLights.forEach(badge => {
                badge.style.backgroundColor = '#333333';
                badge.style.color = '#ffffff';
            });

            // Apply dark mode styles to featured carousel
            const featuredCarouselContainers = document.querySelectorAll('.featured-carousel-container');
            featuredCarouselContainers.forEach(container => {
                container.style.backgroundColor = '#1a1a1a';
                container.style.borderColor = '#333333';
            });

            const featuredItemWrappers = document.querySelectorAll('.featured-item-wrapper');
            featuredItemWrappers.forEach(wrapper => {
                wrapper.style.backgroundColor = '#252525';
                wrapper.style.boxShadow = '0 10px 20px rgba(0, 0, 0, 0.3)';
            });

            // Apply dark mode styles to dropdown menus
            const dropdownMenus = document.querySelectorAll('.dropdown-menu');
            dropdownMenus.forEach(menu => {
                menu.style.backgroundColor = '#252525';
                menu.style.borderColor = '#333333';
                menu.style.color = '#ffffff';
            });

            const dropdownItems = document.querySelectorAll('.dropdown-item');
            dropdownItems.forEach(item => {
                item.style.color = '#ffffff';
            });

            const dropdownDividers = document.querySelectorAll('.dropdown-divider');
            dropdownDividers.forEach(divider => {
                divider.style.borderColor = '#444444';
            });

            // Apply dark mode styles to folder filter buttons
            const folderFilterButtons = document.querySelectorAll('.folder-filter-buttons');
            folderFilterButtons.forEach(buttons => {
                buttons.style.borderColor = '#333333';
            });

            // Apply dark mode styles to body if it has bg-light class
            const body = document.querySelector('body.bg-light');
            if (body) {
                body.style.backgroundColor = '#121212';
            }
        } else {
            // Reset styles for light mode
            // This allows the default styles to take effect

            // Reset filter form styles
            const filterForms = document.querySelectorAll('.filter-form');
            filterForms.forEach(form => {
                form.style.backgroundColor = '';
                form.style.borderColor = '';
                form.style.color = '';
            });

            // Reset input field styles
            const formControls = document.querySelectorAll('.form-control');
            formControls.forEach(input => {
                input.style.backgroundColor = '';
                input.style.borderColor = '';
                input.style.color = '';
            });

            // Reset input group text styles
            const inputGroupTexts = document.querySelectorAll('.input-group-text');
            inputGroupTexts.forEach(text => {
                text.style.backgroundColor = '';
                text.style.borderColor = '';
                text.style.color = '';
            });

            // Reset select dropdown styles
            const formSelects = document.querySelectorAll('.form-select');
            formSelects.forEach(select => {
                select.style.backgroundColor = '';
                select.style.borderColor = '';
                select.style.color = '';
                select.style.backgroundImage = '';
            });

            // Reset tier section styles
            const tierSections = document.querySelectorAll('.tier-section');
            tierSections.forEach(section => {
                section.style.backgroundColor = '';
                section.style.borderColor = '';
                section.style.color = '';
            });

            // Reset featured section styles
            const featuredSections = document.querySelectorAll('.featured-section');
            featuredSections.forEach(section => {
                section.style.backgroundColor = '';
                section.style.borderColor = '';
                section.style.color = '';
            });

            // Reset list group item styles
            const listGroupItems = document.querySelectorAll('.list-group-item');
            listGroupItems.forEach(item => {
                item.style.backgroundColor = '';
                item.style.borderColor = '';
                item.style.color = '';
                item.style.boxShadow = '';
            });

            // Reset logo container styles
            const logoContainers = document.querySelectorAll('.logo-container');
            logoContainers.forEach(container => {
                container.style.backgroundColor = '';
                container.style.borderColor = '';
                container.style.boxShadow = '';
            });

            // Reset logo placeholder styles
            const logoPlaceholders = document.querySelectorAll('.logo-placeholder');
            logoPlaceholders.forEach(placeholder => {
                placeholder.style.backgroundColor = '';
                placeholder.style.color = '';
            });

            // Reset text muted styles
            const textMuteds = document.querySelectorAll('.text-muted');
            textMuteds.forEach(text => {
                text.style.color = '';
            });

            // Reset alert styles
            const alertLights = document.querySelectorAll('.alert-light');
            alertLights.forEach(alert => {
                alert.style.backgroundColor = '';
                alert.style.borderColor = '';
                alert.style.color = '';
            });

            const alertInfos = document.querySelectorAll('.alert-info');
            alertInfos.forEach(alert => {
                alert.style.backgroundColor = '';
                alert.style.borderColor = '';
                alert.style.color = '';
            });

            // Reset badge styles
            const badgeLights = document.querySelectorAll('.badge.bg-light');
            badgeLights.forEach(badge => {
                badge.style.backgroundColor = '';
                badge.style.color = '';
            });

            // Reset featured carousel styles
            const featuredCarouselContainers = document.querySelectorAll('.featured-carousel-container');
            featuredCarouselContainers.forEach(container => {
                container.style.backgroundColor = '';
                container.style.borderColor = '';
            });

            const featuredItemWrappers = document.querySelectorAll('.featured-item-wrapper');
            featuredItemWrappers.forEach(wrapper => {
                wrapper.style.backgroundColor = '';
                wrapper.style.boxShadow = '';
            });

            // Reset dropdown menu styles
            const dropdownMenus = document.querySelectorAll('.dropdown-menu');
            dropdownMenus.forEach(menu => {
                menu.style.backgroundColor = '';
                menu.style.borderColor = '';
                menu.style.color = '';
            });

            const dropdownItems = document.querySelectorAll('.dropdown-item');
            dropdownItems.forEach(item => {
                item.style.color = '';
            });

            const dropdownDividers = document.querySelectorAll('.dropdown-divider');
            dropdownDividers.forEach(divider => {
                divider.style.borderColor = '';
            });

            // Reset folder filter button styles
            const folderFilterButtons = document.querySelectorAll('.folder-filter-buttons');
            folderFilterButtons.forEach(buttons => {
                buttons.style.borderColor = '';
            });

            // Reset body styles
            const body = document.querySelector('body.bg-light');
            if (body) {
                body.style.backgroundColor = '';
            }
        }
    }

    // Update styling immediately
    updateAssistantListStyling();

    // Set up an interval to ensure styling is applied, but at a much lower frequency
    setInterval(updateAssistantListStyling, 5000);

    // Listen for theme changes
    document.addEventListener('themeChanged', function(e) {
        updateAssistantListStyling();
    });

    // Execute again when the DOM is fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        updateAssistantListStyling();

        // Create a MutationObserver to watch for changes to the document
        // Use a debounced version to prevent excessive calls
        let timeout;
        const observer = new MutationObserver(function(mutations) {
            // Clear any existing timeout
            if (timeout) {
                clearTimeout(timeout);
            }

            // Set a new timeout to run the function after a delay
            timeout = setTimeout(function() {
                updateAssistantListStyling();
            }, 500);
        });

        // Start observing the document with more specific parameters
        observer.observe(document.body, {
            attributes: true,
            childList: true,
            subtree: false,
            attributeFilter: ['data-theme']
        });
    });
})();
