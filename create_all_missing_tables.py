import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

print("Starting script to create all missing tables...")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

# SQL to create the auth_user_groups table
auth_user_groups_sql = """
CREATE TABLE IF NOT EXISTS "auth_user_groups" (
    "id" serial NOT NULL PRIMARY KEY,
    "user_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
    "group_id" integer NOT NULL REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT "auth_user_groups_user_id_group_id_94350c0c_uniq" UNIQUE ("user_id", "group_id")
);

CREATE INDEX IF NOT EXISTS "auth_user_groups_user_id_6a12ed8b" ON "auth_user_groups" ("user_id");
CREATE INDEX IF NOT EXISTS "auth_user_groups_group_id_97559544" ON "auth_user_groups" ("group_id");
"""

# SQL to create the auth_user_user_permissions table
auth_user_user_permissions_sql = """
CREATE TABLE IF NOT EXISTS "auth_user_user_permissions" (
    "id" serial NOT NULL PRIMARY KEY,
    "user_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
    "permission_id" integer NOT NULL REFERENCES "auth_permission" ("id") DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT "auth_user_user_permissions_user_id_permission_id_14a6b632_uniq" UNIQUE ("user_id", "permission_id")
);

CREATE INDEX IF NOT EXISTS "auth_user_user_permissions_user_id_a95ead1b" ON "auth_user_user_permissions" ("user_id");
CREATE INDEX IF NOT EXISTS "auth_user_user_permissions_permission_id_1fbb5f2c" ON "auth_user_user_permissions" ("permission_id");
"""

# SQL to create the guardian_userobjectpermission table
guardian_userobjectpermission_sql = """
CREATE TABLE IF NOT EXISTS "guardian_userobjectpermission" (
    "id" serial NOT NULL PRIMARY KEY,
    "object_pk" varchar(255) NOT NULL,
    "content_type_id" integer NOT NULL REFERENCES "django_content_type" ("id") DEFERRABLE INITIALLY DEFERRED,
    "permission_id" integer NOT NULL REFERENCES "auth_permission" ("id") DEFERRABLE INITIALLY DEFERRED,
    "user_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT "guardian_userobjectpermission_user_id_permission_id_object_pk_unique" UNIQUE ("user_id", "permission_id", "object_pk")
);

CREATE INDEX IF NOT EXISTS "guardian_userobjectpermission_content_type_id_object_pk_idx" ON "guardian_userobjectpermission" ("content_type_id", "object_pk");
CREATE INDEX IF NOT EXISTS "guardian_userobjectpermission_user_id_idx" ON "guardian_userobjectpermission" ("user_id");
CREATE INDEX IF NOT EXISTS "guardian_userobjectpermission_permission_id_idx" ON "guardian_userobjectpermission" ("permission_id");
"""

# SQL to create the guardian_groupobjectpermission table
guardian_groupobjectpermission_sql = """
CREATE TABLE IF NOT EXISTS "guardian_groupobjectpermission" (
    "id" serial NOT NULL PRIMARY KEY,
    "object_pk" varchar(255) NOT NULL,
    "content_type_id" integer NOT NULL REFERENCES "django_content_type" ("id") DEFERRABLE INITIALLY DEFERRED,
    "permission_id" integer NOT NULL REFERENCES "auth_permission" ("id") DEFERRABLE INITIALLY DEFERRED,
    "group_id" integer NOT NULL REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT "guardian_groupobjectpermission_group_id_permission_id_object_pk_unique" UNIQUE ("group_id", "permission_id", "object_pk")
);

CREATE INDEX IF NOT EXISTS "guardian_groupobjectpermission_content_type_id_object_pk_idx" ON "guardian_groupobjectpermission" ("content_type_id", "object_pk");
CREATE INDEX IF NOT EXISTS "guardian_groupobjectpermission_group_id_idx" ON "guardian_groupobjectpermission" ("group_id");
CREATE INDEX IF NOT EXISTS "guardian_groupobjectpermission_permission_id_idx" ON "guardian_groupobjectpermission" ("permission_id");
"""

# SQL to create the assistants_communitycontext table
communitycontext_sql = """
CREATE TABLE IF NOT EXISTS "assistants_communitycontext" (
    "id" serial NOT NULL PRIMARY KEY,
    "title" varchar(255) NULL,
    "text_content" text NOT NULL,
    "keywords" jsonb NOT NULL,
    "is_active" boolean NOT NULL,
    "times_used" integer NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "updated_at" timestamp with time zone NOT NULL,
    "assistant_id" integer NOT NULL REFERENCES "assistants_assistant" ("id") DEFERRABLE INITIALLY DEFERRED,
    "created_by_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);

CREATE INDEX IF NOT EXISTS "assistants_communitycontext_assistant_id_created_at_idx" ON "assistants_communitycontext" ("assistant_id", "created_at");
CREATE INDEX IF NOT EXISTS "assistants_communitycontext_is_active_idx" ON "assistants_communitycontext" ("is_active");
CREATE INDEX IF NOT EXISTS "assistants_communitycontext_created_by_id_idx" ON "assistants_communitycontext" ("created_by_id");
"""

# SQL to create the assistants_flaggedquestion table
flaggedquestion_sql = """
CREATE TABLE IF NOT EXISTS "assistants_flaggedquestion" (
    "id" serial NOT NULL PRIMARY KEY,
    "question" text NOT NULL,
    "notes" text NOT NULL,
    "is_resolved" boolean NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "resolved_at" timestamp with time zone NULL,
    "assistant_id" integer NOT NULL REFERENCES "assistants_assistant" ("id") DEFERRABLE INITIALLY DEFERRED,
    "created_by_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
    "resolved_by_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);

CREATE INDEX IF NOT EXISTS "assistants_flaggedquestion_assistant_id_created_at_idx" ON "assistants_flaggedquestion" ("assistant_id", "created_at");
CREATE INDEX IF NOT EXISTS "assistants_flaggedquestion_is_resolved_idx" ON "assistants_flaggedquestion" ("is_resolved");
CREATE INDEX IF NOT EXISTS "assistants_flaggedquestion_created_by_id_idx" ON "assistants_flaggedquestion" ("created_by_id");
CREATE INDEX IF NOT EXISTS "assistants_flaggedquestion_resolved_by_id_idx" ON "assistants_flaggedquestion" ("resolved_by_id");
"""

# SQL to create the assistants_contextnotification table
contextnotification_sql = """
CREATE TABLE IF NOT EXISTS "assistants_contextnotification" (
    "id" serial NOT NULL PRIMARY KEY,
    "is_read" boolean NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "context_id" integer NOT NULL REFERENCES "assistants_communitycontext" ("id") DEFERRABLE INITIALLY DEFERRED,
    "flagged_question_id" integer NULL REFERENCES "assistants_flaggedquestion" ("id") DEFERRABLE INITIALLY DEFERRED,
    "user_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);

CREATE INDEX IF NOT EXISTS "assistants_contextnotification_context_id_idx" ON "assistants_contextnotification" ("context_id");
CREATE INDEX IF NOT EXISTS "assistants_contextnotification_flagged_question_id_idx" ON "assistants_contextnotification" ("flagged_question_id");
CREATE INDEX IF NOT EXISTS "assistants_contextnotification_user_id_idx" ON "assistants_contextnotification" ("user_id");
CREATE INDEX IF NOT EXISTS "assistants_contextnotification_user_id_is_read_idx" ON "assistants_contextnotification" ("user_id", "is_read");
"""

# SQL to create the impersonate_impersonationlog table
impersonationlog_sql = """
CREATE TABLE IF NOT EXISTS "impersonate_impersonationlog" (
    "id" serial NOT NULL PRIMARY KEY,
    "session_key" varchar(40) NOT NULL,
    "session_started_at" timestamp with time zone NULL,
    "session_ended_at" timestamp with time zone NULL,
    "impersonator_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
    "impersonating_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);

CREATE INDEX IF NOT EXISTS "impersonate_impersonationlog_impersonator_id_idx" ON "impersonate_impersonationlog" ("impersonator_id");
CREATE INDEX IF NOT EXISTS "impersonate_impersonationlog_impersonating_id_idx" ON "impersonate_impersonationlog" ("impersonating_id");
CREATE INDEX IF NOT EXISTS "impersonate_impersonationlog_session_started_at_idx" ON "impersonate_impersonationlog" ("session_started_at");
"""

# SQL to create the content_content table
content_sql = """
CREATE TABLE IF NOT EXISTS "content_content" (
    "id" serial NOT NULL PRIMARY KEY,
    "title" varchar(200) NOT NULL,
    "slug" varchar(200) NOT NULL,
    "content_type" varchar(20) NOT NULL,
    "body" text NOT NULL,
    "summary" text NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "updated_at" timestamp with time zone NOT NULL,
    "is_public" boolean NOT NULL,
    "is_archived" boolean NOT NULL,
    "assistant_id" integer NULL,
    "author_id" integer NULL,
    "company_id" integer NOT NULL
);

CREATE INDEX IF NOT EXISTS "content_content_company_id_content_type_idx" ON "content_content" ("company_id", "content_type");
CREATE INDEX IF NOT EXISTS "content_content_company_id_is_public_idx" ON "content_content" ("company_id", "is_public");
CREATE INDEX IF NOT EXISTS "content_content_company_id_is_archived_idx" ON "content_content" ("company_id", "is_archived");
"""

# SQL to create the content_contentimage table
contentimage_sql = """
CREATE TABLE IF NOT EXISTS "content_contentimage" (
    "id" serial NOT NULL PRIMARY KEY,
    "image" varchar(100) NOT NULL,
    "alt_text" varchar(200) NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "content_id" integer NOT NULL REFERENCES "content_content" ("id") DEFERRABLE INITIALLY DEFERRED
);

CREATE INDEX IF NOT EXISTS "content_contentimage_content_id_idx" ON "content_contentimage" ("content_id");
"""

# SQL to create the assistants_interaction table
interaction_sql = """
CREATE TABLE IF NOT EXISTS "assistants_interaction" (
    "id" serial NOT NULL PRIMARY KEY,
    "prompt" text NOT NULL,
    "response" text NOT NULL,
    "context" text NOT NULL,
    "rating" integer NULL,
    "duration" double precision NOT NULL,
    "token_count" integer NOT NULL,
    "use_community_context" boolean NOT NULL DEFAULT false,
    "created_at" timestamp with time zone NOT NULL,
    "assistant_id" integer NOT NULL,
    "user_id" integer NULL
);

CREATE INDEX IF NOT EXISTS "assistants_interaction_assistant_id_created_at_idx" ON "assistants_interaction" ("assistant_id", "created_at");
CREATE INDEX IF NOT EXISTS "assistants_interaction_user_id_created_at_idx" ON "assistants_interaction" ("user_id", "created_at");
"""

# SQL to create the accounts_activitylog table
activitylog_sql = """
CREATE TABLE IF NOT EXISTS "accounts_activitylog" (
    "id" serial NOT NULL PRIMARY KEY,
    "activity_type" varchar(50) NOT NULL,
    "description" text NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "metadata" jsonb NOT NULL,
    "company_id" integer NOT NULL REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED,
    "user_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);

CREATE INDEX IF NOT EXISTS "accounts_activitylog_company_id_idx" ON "accounts_activitylog" ("company_id");
CREATE INDEX IF NOT EXISTS "accounts_activitylog_user_id_idx" ON "accounts_activitylog" ("user_id");
CREATE INDEX IF NOT EXISTS "accounts_activitylog_created_at_idx" ON "accounts_activitylog" ("created_at");
CREATE INDEX IF NOT EXISTS "accounts_activitylog_activity_type_idx" ON "accounts_activitylog" ("activity_type");
"""

# Connect to the database
try:
    print("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(**db_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()

    # Create the auth_user_groups table
    print("Creating auth_user_groups table...")
    cursor.execute(auth_user_groups_sql)
    print("auth_user_groups table created successfully!")

    # Create the auth_user_user_permissions table
    print("Creating auth_user_user_permissions table...")
    cursor.execute(auth_user_user_permissions_sql)
    print("auth_user_user_permissions table created successfully!")

    # Create the guardian_userobjectpermission table
    print("Creating guardian_userobjectpermission table...")
    cursor.execute(guardian_userobjectpermission_sql)
    print("guardian_userobjectpermission table created successfully!")

    # Create the guardian_groupobjectpermission table
    print("Creating guardian_groupobjectpermission table...")
    cursor.execute(guardian_groupobjectpermission_sql)
    print("guardian_groupobjectpermission table created successfully!")

    # Create the content_content table
    print("Creating content_content table...")
    cursor.execute(content_sql)
    print("Content table created successfully!")

    # Create the content_contentimage table
    print("Creating content_contentimage table...")
    cursor.execute(contentimage_sql)
    print("ContentImage table created successfully!")

    # Create the impersonate_impersonationlog table
    print("Creating impersonate_impersonationlog table...")
    cursor.execute(impersonationlog_sql)
    print("ImpersonationLog table created successfully!")

    # Create the assistants_interaction table
    print("Creating assistants_interaction table...")
    cursor.execute(interaction_sql)
    print("Interaction table created successfully!")

    # Create the assistants_communitycontext table
    print("Creating assistants_communitycontext table...")
    cursor.execute(communitycontext_sql)
    print("CommunityContext table created successfully!")

    # Create the assistants_flaggedquestion table
    print("Creating assistants_flaggedquestion table...")
    cursor.execute(flaggedquestion_sql)
    print("FlaggedQuestion table created successfully!")

    # Create the assistants_contextnotification table
    print("Creating assistants_contextnotification table...")
    cursor.execute(contextnotification_sql)
    print("ContextNotification table created successfully!")

    # Create the accounts_activitylog table
    print("Creating accounts_activitylog table...")
    cursor.execute(activitylog_sql)
    print("ActivityLog table created successfully!")

    # Close the connection
    cursor.close()
    conn.close()
    print("Database connection closed.")
    print("All missing tables created successfully!")

except Exception as e:
    print(f"Error: {e}")
