from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    help = 'Creates the content_content and content_contentimage tables'

    def handle(self, *args, **options):
        self.stdout.write('Creating content tables...')
        
        # SQL to create the content_content table
        content_sql = """
        CREATE TABLE IF NOT EXISTS "content_content" (
            "id" serial NOT NULL PRIMARY KEY,
            "title" varchar(200) NOT NULL,
            "slug" varchar(200) NOT NULL,
            "content_type" varchar(20) NOT NULL,
            "body" text NOT NULL,
            "summary" text NOT NULL,
            "created_at" timestamp with time zone NOT NULL,
            "updated_at" timestamp with time zone NOT NULL,
            "is_public" boolean NOT NULL,
            "is_archived" boolean NOT NULL,
            "assistant_id" integer NULL REFERENCES "assistants_assistant" ("id") DEFERRABLE INITIALLY DEFERRED,
            "author_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
            "company_id" integer NOT NULL REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED
        );
        """
        
        # SQL to create the content_contentimage table
        contentimage_sql = """
        CREATE TABLE IF NOT EXISTS "content_contentimage" (
            "id" serial NOT NULL PRIMARY KEY,
            "image" varchar(100) NOT NULL,
            "alt_text" varchar(200) NOT NULL,
            "created_at" timestamp with time zone NOT NULL,
            "content_id" integer NOT NULL REFERENCES "content_content" ("id") DEFERRABLE INITIALLY DEFERRED
        );
        """
        
        # SQL to create the content_contentversion table
        contentversion_sql = """
        CREATE TABLE IF NOT EXISTS "content_contentversion" (
            "id" serial NOT NULL PRIMARY KEY,
            "body" text NOT NULL,
            "created_at" timestamp with time zone NOT NULL,
            "change_summary" varchar(200) NOT NULL,
            "content_id" integer NOT NULL REFERENCES "content_content" ("id") DEFERRABLE INITIALLY DEFERRED,
            "edited_by_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
        );
        """
        
        # SQL to create indexes
        indexes_sql = """
        CREATE INDEX IF NOT EXISTS "content_content_company_id_content_type_idx" ON "content_content" ("company_id", "content_type");
        CREATE INDEX IF NOT EXISTS "content_content_company_id_is_public_idx" ON "content_content" ("company_id", "is_public");
        CREATE INDEX IF NOT EXISTS "content_content_company_id_is_archived_idx" ON "content_content" ("company_id", "is_archived");
        CREATE INDEX IF NOT EXISTS "content_contentimage_content_id_idx" ON "content_contentimage" ("content_id");
        CREATE INDEX IF NOT EXISTS "content_contentversion_content_id_idx" ON "content_contentversion" ("content_id");
        """
        
        # Execute the SQL
        with connection.cursor() as cursor:
            self.stdout.write('Creating content_content table...')
            cursor.execute(content_sql)
            self.stdout.write(self.style.SUCCESS('Content table created successfully!'))
            
            self.stdout.write('Creating content_contentimage table...')
            cursor.execute(contentimage_sql)
            self.stdout.write(self.style.SUCCESS('ContentImage table created successfully!'))
            
            self.stdout.write('Creating content_contentversion table...')
            cursor.execute(contentversion_sql)
            self.stdout.write(self.style.SUCCESS('ContentVersion table created successfully!'))
            
            self.stdout.write('Creating indexes...')
            cursor.execute(indexes_sql)
            self.stdout.write(self.style.SUCCESS('Indexes created successfully!'))
        
        self.stdout.write(self.style.SUCCESS('All content tables created successfully!'))
