from django import template
from django.utils.safestring import mark_safe

register = template.Library()

@register.simple_tag
def tinymce_textarea(name, value='', id=None, placeholder='', rows=10, cols=80, classes=''):
    """
    Render a textarea with TinyMCE enabled.

    Usage:
    {% load tinymce_tags %}
    {% tinymce_textarea name="content" value=form.content.value id="id_content" placeholder="Enter content..." %}
    """
    if id is None:
        id = f"id_{name}"

    # Always include the tinymce-editor class
    class_attr = f"tinymce-editor {classes}".strip()

    # Create the HTML with proper escaping for JavaScript
    textarea_html = f'<textarea name="{name}" id="{id}" placeholder="{placeholder}" rows="{rows}" cols="{cols}" class="{class_attr}">{value}</textarea>'

    fallback_html = f'''
    <div id="{id}-fallback" class="tinymce-fallback">
        <div class="alert alert-warning">
            <strong>Note:</strong> The rich text editor failed to load. You can still enter content below:
        </div>
        <textarea name="{name}_fallback" class="form-control" rows="{rows}" placeholder="{placeholder}">{value}</textarea>
    </div>
    '''

    script_html = f'''
    <script>
        document.addEventListener('DOMContentLoaded', function() {{
            setTimeout(function() {{
                if (typeof tinymce === 'undefined' || !tinymce.get('{id}')) {{
                    document.getElementById('{id}-fallback').classList.add('visible');
                }}
            }}, 2000);
        }});
    </script>
    '''

    # Combine all parts
    full_html = textarea_html + fallback_html + script_html

    return mark_safe(full_html)

@register.filter
def add_tinymce_class(field):
    """
    Add the tinymce-editor class to a form field.
    """
    return field.as_widget(attrs={'class': field.css_classes() + ' tinymce-editor'})
