"""
<PERSON><PERSON><PERSON> to fix migrations when tables already exist in the database.
This script will:
1. Delete all migration files
2. Reset the migration history in the database
3. Recreate migrations for all apps
4. Apply migrations with the --fake flag to handle existing tables
"""
import os
import sys
import subprocess
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

def run_command(command):
    """Run a command and return the result."""
    print(f"Running: {' '.join(command)}")
    
    try:
        result = subprocess.run(
            command,
            check=False,  # Don't raise an exception on non-zero exit
            capture_output=True,
            text=True
        )
        
        print(f"Exit code: {result.returncode}")
        if result.stdout:
            print(f"STDOUT: {result.stdout}")
        if result.stderr:
            print(f"STDERR: {result.stderr}")
        
        return result.returncode == 0
    except Exception as e:
        print(f"Error running command: {e}")
        return False

def delete_migrations():
    """Delete all migration files from the project."""
    # List of apps to clean migrations from
    apps = [
        "accounts",
        "assistants",
        "content",
        "directory",
        "site_settings",
        "superadmin",
    ]
    
    for app in apps:
        migrations_dir = os.path.join(app, "migrations")
        
        # Check if the migrations directory exists
        if os.path.exists(migrations_dir):
            print(f"Cleaning migrations from {app}...")
            
            # Get all files in the migrations directory
            for filename in os.listdir(migrations_dir):
                file_path = os.path.join(migrations_dir, filename)
                
                # Delete all Python files except __init__.py
                if filename.endswith('.py') and filename != '__init__.py':
                    try:
                        os.remove(file_path)
                        print(f"  Deleted: {file_path}")
                    except Exception as e:
                        print(f"  Error deleting {file_path}: {e}")
                
                # Delete __pycache__ directory if it exists
                if filename == '__pycache__':
                    try:
                        import shutil
                        shutil.rmtree(file_path)
                        print(f"  Deleted: {file_path}")
                    except Exception as e:
                        print(f"  Error deleting {file_path}: {e}")
            
            # Create __init__.py if it doesn't exist
            init_file = os.path.join(migrations_dir, '__init__.py')
            if not os.path.exists(init_file):
                try:
                    with open(init_file, 'w') as f:
                        pass  # Create an empty file
                    print(f"  Created: {init_file}")
                except Exception as e:
                    print(f"  Error creating {init_file}: {e}")
        else:
            # Create migrations directory if it doesn't exist
            try:
                os.makedirs(migrations_dir)
                print(f"Created migrations directory for {app}")
                
                # Create __init__.py
                with open(os.path.join(migrations_dir, '__init__.py'), 'w') as f:
                    pass  # Create an empty file
                print(f"  Created: {os.path.join(migrations_dir, '__init__.py')}")
            except Exception as e:
                print(f"Error creating migrations directory for {app}: {e}")

    print("\nMigration files have been deleted.")

def reset_migration_history():
    """Reset the migration history in the database."""
    print("Resetting migration history...")
    
    try:
        # Get a cursor to execute SQL
        from django.db import connection
        with connection.cursor() as cursor:
            # Check if the django_migrations table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'django_migrations'
                );
            """)
            table_exists = cursor.fetchone()[0]
            
            if table_exists:
                # Delete all records from django_migrations
                cursor.execute("DELETE FROM django_migrations;")
                print("Migration history has been reset.")
            else:
                print("The django_migrations table does not exist. No action needed.")
    
    except Exception as e:
        print(f"Error resetting migration history: {e}")

def recreate_migrations():
    """Recreate migrations for all apps."""
    # List of apps to create migrations for
    apps = [
        "accounts",
        "assistants",
        "content",
        "directory",
        "site_settings",
        "superadmin",
    ]
    
    print("Creating migrations for all apps...")
    
    # Create migrations for each app
    for app in apps:
        print(f"\nCreating migrations for {app}...")
        success = run_command([sys.executable, "manage.py", "makemigrations", app])
        if not success:
            print(f"Failed to create migrations for {app}.")

def apply_migrations_with_fake():
    """Apply migrations with the --fake flag to handle existing tables."""
    print("\nApplying migrations with --fake flag...")
    
    # First fake the initial migrations for all apps
    print("\nFaking initial migrations for all apps...")
    run_command([sys.executable, "manage.py", "migrate", "--fake-initial"])
    
    # Then fake the remaining migrations
    print("\nFaking remaining migrations...")
    run_command([sys.executable, "manage.py", "migrate", "--fake"])
    
    print("\nMigration process completed.")

def main():
    """Run all migration fix steps."""
    print("=== MIGRATION FIX PROCESS ===")
    print("\nThis process will:")
    print("1. Delete all migration files")
    print("2. Reset the migration history in the database")
    print("3. Recreate migrations for all apps")
    print("4. Apply migrations with the --fake flag to handle existing tables")
    
    # Confirm before proceeding
    confirm = input("\nDo you want to proceed? (y/n): ")
    if confirm.lower() != 'y':
        print("Operation cancelled.")
        return
    
    # Step 1: Delete all migration files
    print("\n=== STEP 1: DELETING MIGRATION FILES ===")
    delete_migrations()
    
    # Step 2: Reset the migration history
    print("\n=== STEP 2: RESETTING MIGRATION HISTORY ===")
    reset_migration_history()
    
    # Step 3: Recreate migrations
    print("\n=== STEP 3: RECREATING MIGRATIONS ===")
    recreate_migrations()
    
    # Step 4: Apply migrations with --fake flag
    print("\n=== STEP 4: APPLYING MIGRATIONS WITH --FAKE FLAG ===")
    apply_migrations_with_fake()
    
    print("\n=== MIGRATION FIX COMPLETED ===")
    print("Your database schema should now be properly set up with clean migrations.")
    print("\nTo verify, run: python manage.py showmigrations")

if __name__ == "__main__":
    main()
