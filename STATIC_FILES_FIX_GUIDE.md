# Static Files Fix Guide for cPanel

This guide provides instructions for fixing static file loading issues in your Django application on cPanel.

## Problem

Static files (CSS, JavaScript, images) are not loading properly in the cPanel environment, resulting in a broken website appearance and functionality.

## Solution

We've made several changes to fix the static file loading issues:

1. **Updated Static Files Settings**:
   - Changed `STATIC_URL` to include a leading slash: `/static/`
   - Switched to the standard Django static files storage
   - Updated static files configuration for better cPanel compatibility

2. **Improved .htaccess Configuration**:
   - Enhanced rules for serving static files
   - Added alternative rules for different cPanel configurations
   - Improved media file handling

3. **Created Production Settings**:
   - Added a dedicated `production_settings.py` file
   - Configured optimal settings for cPanel environment
   - Disabled problematic security settings

4. **Updated WSGI Configuration**:
   - Modified `passenger_wsgi.py` to use production settings
   - Ensured proper Python path configuration

5. **Added Helper Scripts**:
   - `collect_static.py` for collecting static files
   - `fix_permissions.py` for setting correct file permissions

## Implementation Steps

### 1. Update Your cPanel Environment

1. Upload all the modified files to your cPanel environment:
   - `company_assistant/settings.py`
   - `company_assistant/production_settings.py`
   - `.htaccess`
   - `passenger_wsgi.py`
   - `collect_static.py`
   - `fix_permissions.py`

2. Run the permission fixing script:
   ```bash
   python fix_permissions.py
   ```

3. Run the static file collection script:
   ```bash
   python collect_static.py
   ```

4. Restart your application in cPanel:
   - Go to "Setup Python App" and restart your application, or
   - Touch the passenger_wsgi.py file: `touch passenger_wsgi.py`

### 2. Verify the Fix

1. Clear your browser cache completely
2. Access your website and check if static files are loading properly
3. Check browser developer tools (F12) for any remaining 404 errors

## Additional Troubleshooting

If you're still experiencing issues:

### Check Static Files Location

Verify that your static files are in the correct location:
```bash
ls -la staticfiles/
```

### Check Error Logs

Look for errors in the logs:
```bash
tail -f logs/error.log
```

### Try Alternative Static File Serving

If the standard approach doesn't work, you can try:

1. Creating a symbolic link:
```bash
ln -s /home/<USER>/your_project/staticfiles /home/<USER>/public_html/static
```

2. Manually copying static files:
```bash
cp -r staticfiles/* ~/public_html/static/
```

### Check File Permissions

Ensure proper permissions:
```bash
find staticfiles/ -type d -exec chmod 755 {} \;
find staticfiles/ -type f -exec chmod 644 {} \;
```

## Technical Details

### Static Files Configuration

We've updated the static files configuration in `settings.py`:
```python
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [BASE_DIR / 'static']
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'
```

### .htaccess Configuration

We've improved the .htaccess file with better rules for static files:
```apache
# Serve static files directly - improved rule
RewriteCond %{REQUEST_URI} ^/static/
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^(.*)$ $1 [L]

# Alternative static files rule if the above doesn't work
RewriteCond %{REQUEST_URI} ^/static/
RewriteRule ^static/(.*)$ staticfiles/$1 [L]
```

### Production Settings

We've created a dedicated production settings file with optimized configuration for cPanel.

## Conclusion

These changes should resolve the static file loading issues in your cPanel environment. If you continue to experience problems, please check the error logs and consider contacting your hosting provider for assistance.
