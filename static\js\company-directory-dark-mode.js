/**
 * Company Directory Dark Mode Handler
 * Ensures dark mode is properly applied to company directory pages
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a company directory page
    const isCompanyDirectoryPage = document.querySelector('.company-directory-container') ||
                                   document.title.includes('Company Directory') ||
                                   window.location.href.includes('/directory/companies/');

    if (!isCompanyDirectoryPage) {
        return; // Exit if not on a company directory page
    }

    // Load the company directory dark mode CSS
    loadCompanyDirectoryCss();

    // Check if dark mode is active
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';

    // Apply dark mode styles to company directory elements
    applyDarkModeToCompanyDirectory(isDarkMode);

    // Listen for theme changes
    document.addEventListener('themeChanged', function(e) {
        const isDarkMode = e.detail.theme === 'dark';
        applyDarkModeToCompanyDirectory(isDarkMode);
    });

    // Function to load the company directory dark mode CSS
    function loadCompanyDirectoryCss() {
        if (!document.getElementById('company-directory-dark-mode-css')) {
            const link = document.createElement('link');
            link.id = 'company-directory-dark-mode-css';
            link.rel = 'stylesheet';
            link.href = '/static/css/company-directory-dark-mode.css';
            document.head.appendChild(link);
        }
    }

    // Function to apply dark mode to company directory elements
    function applyDarkModeToCompanyDirectory(isDarkMode) {
        if (isDarkMode) {
            // Directly target the form-filter-form element
            const filterForm = document.getElementById('form-filter-form');
            if (filterForm) {
                filterForm.style.backgroundColor = '#1a1a1a';
                filterForm.style.border = '1px solid #333333';
                filterForm.style.borderRadius = '8px';
                filterForm.style.padding = '20px';
                filterForm.style.marginBottom = '20px';
                filterForm.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
            }
            // Apply dark mode to body if it has bg-light class
            const body = document.body;
            if (body.classList.contains('bg-light')) {
                body.style.backgroundColor = '#121212';
                body.style.backgroundImage = 'radial-gradient(circle at 25% 25%, rgba(30, 30, 30, 0.2) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(20, 20, 20, 0.2) 0%, transparent 50%), linear-gradient(to bottom, #121212, #0a0a0a)';
                body.style.backgroundAttachment = 'fixed';
                body.style.color = '#ffffff';
            }

            // Apply dark mode to all elements with bg-light class
            document.querySelectorAll('.bg-light').forEach(el => {
                el.style.backgroundColor = '#121212';
                el.style.color = '#ffffff';
            });

            // Apply dark mode to featured companies section
            document.querySelectorAll('.featured-section, .featured-companies').forEach(section => {
                section.style.backgroundColor = '#1a1a1a';
                section.style.borderRadius = '8px';
                section.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
                section.style.padding = '20px';
                section.style.marginBottom = '30px';
                section.style.border = '1px solid #333333';

                // Style the heading
                const heading = section.querySelector('h2');
                if (heading) {
                    heading.style.color = '#ffffff';
                    heading.style.borderBottom = '1px solid #333333';
                    heading.style.paddingBottom = '10px';
                    heading.style.marginBottom = '20px';
                }
            });

            // Apply dark mode to featured carousel container
            document.querySelectorAll('.featured-carousel-container').forEach(container => {
                container.style.backgroundColor = '#1a1a1a';
                container.style.border = '1px solid #333333';
                container.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
            });

            // Apply dark mode to featured item wrappers
            document.querySelectorAll('.featured-item-wrapper').forEach(wrapper => {
                wrapper.style.backgroundColor = '#252525';
                wrapper.style.border = '1px solid #333333';
                wrapper.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.4)';

                // Style item info
                const itemInfo = wrapper.querySelector('.item-info');
                if (itemInfo) {
                    // Style heading
                    const heading = itemInfo.querySelector('h5');
                    if (heading) {
                        heading.style.color = '#ffffff';
                    }

                    // Style paragraph
                    const paragraph = itemInfo.querySelector('p');
                    if (paragraph) {
                        paragraph.style.color = '#cccccc';
                    }
                }

                // Style logo container
                const logoContainer = wrapper.querySelector('.logo-container');
                if (logoContainer) {
                    logoContainer.style.backgroundColor = '#1a1a1a';
                    logoContainer.style.border = '1px solid #333333';
                }

                // Style logo placeholder
                const logoPlaceholder = wrapper.querySelector('.logo-placeholder');
                if (logoPlaceholder) {
                    logoPlaceholder.style.backgroundColor = '#1a1a1a';
                    logoPlaceholder.style.color = '#0077ff';
                }
            });

            // Apply dark mode to company cards
            document.querySelectorAll('.company-card').forEach(card => {
                card.style.backgroundColor = '#252525';
                card.style.border = '1px solid #333333';
                card.style.borderRadius = '8px';
                card.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
                card.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';
                card.style.marginBottom = '20px';
                card.style.overflow = 'hidden';

                // Add hover effect
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.3)';
                });

                // Add leave effect
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
                });

                // Style logo container
                const logoContainer = card.querySelector('.company-logo-container');
                if (logoContainer) {
                    logoContainer.style.backgroundColor = '#1a1a1a';
                    logoContainer.style.borderBottom = '1px solid #333333';
                    logoContainer.style.padding = '15px';
                    logoContainer.style.textAlign = 'center';
                }

                // Style company name
                const companyName = card.querySelector('.company-name');
                if (companyName) {
                    companyName.style.color = '#ffffff';
                    companyName.style.fontWeight = '600';
                    companyName.style.marginBottom = '10px';
                }

                // Style company description
                const companyDesc = card.querySelector('.company-description');
                if (companyDesc) {
                    companyDesc.style.color = '#cccccc';
                    companyDesc.style.marginBottom = '15px';
                }

                // Style company actions
                const companyActions = card.querySelector('.company-actions');
                if (companyActions) {
                    companyActions.style.borderTop = '1px solid #333333';
                    companyActions.style.padding = '10px 15px';
                    companyActions.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
                }
            });

            // Apply dark mode to search container
            document.querySelectorAll('.company-search-container, .search-bar, .search-container, .company-directory-container .input-group, .filter-form .input-group').forEach(container => {
                container.style.backgroundColor = '#1a1a1a';
                container.style.borderRadius = '8px';
                container.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
                container.style.padding = '20px';
                container.style.marginBottom = '30px';
                container.style.border = '1px solid #333333';

                // Style heading
                const heading = container.querySelector('h3');
                if (heading) {
                    heading.style.color = '#ffffff';
                    heading.style.marginBottom = '15px';
                }

                // Style search input
                const searchInputs = container.querySelectorAll('.search-input, input[type="text"], input[type="search"]');
                searchInputs.forEach(searchInput => {
                    searchInput.style.backgroundColor = '#252525';
                    searchInput.style.border = '1px solid #333333';
                    searchInput.style.color = '#ffffff';
                    searchInput.style.padding = '10px 15px';
                    searchInput.style.borderRadius = '4px';

                    // Add focus effect
                    searchInput.addEventListener('focus', function() {
                        this.style.borderColor = '#0077ff';
                        this.style.boxShadow = '0 0 0 3px rgba(0, 119, 255, 0.25)';
                        this.style.outline = 'none';
                    });

                    // Add blur effect
                    searchInput.addEventListener('blur', function() {
                        this.style.borderColor = '#333333';
                        this.style.boxShadow = 'none';
                    });
                });

                // Style input group text if present
                const inputGroupText = container.querySelector('.input-group-text');
                if (inputGroupText) {
                    inputGroupText.style.backgroundColor = '#252525';
                    inputGroupText.style.border = 'none';
                    inputGroupText.style.borderRight = '1px solid #333333';
                    inputGroupText.style.color = '#ffffff';
                }

                // Style search button
                const searchButtons = container.querySelectorAll('.search-button, button[type="submit"], .btn-search, .input-group .btn, .input-group-append .btn');
                searchButtons.forEach(searchButton => {
                    searchButton.style.backgroundColor = '#0077ff';
                    searchButton.style.border = 'none';
                    searchButton.style.color = '#ffffff';
                    searchButton.style.padding = '10px 20px';
                    searchButton.style.borderRadius = '4px';
                    searchButton.style.fontWeight = '500';

                    // Add hover effect
                    searchButton.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = '#0066dd';
                    });

                    // Add leave effect
                    searchButton.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = '#0077ff';
                    });
                });
            });

            // Apply dark mode to company filters
            document.querySelectorAll('.company-filters, .filter-form, #form-filter-form, form.filter-form, .form-filter-form').forEach(filters => {
                filters.style.backgroundColor = '#1a1a1a';
                filters.style.borderRadius = '8px';
                filters.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
                filters.style.padding = '20px';
                filters.style.marginBottom = '20px';
                filters.style.border = '1px solid #333333';

                // Style headings
                filters.querySelectorAll('h3, h4, h5').forEach(heading => {
                    heading.style.color = '#ffffff';
                    heading.style.marginBottom = '15px';
                });

                // Style icons in headings
                filters.querySelectorAll('h5 i, .h5 i, .bi-funnel-fill, .bi-info-circle').forEach(icon => {
                    icon.style.color = '#0077ff';
                });

                // Style form labels
                filters.querySelectorAll('.form-label').forEach(label => {
                    label.style.color = '#ffffff';
                });

                // Style form controls
                filters.querySelectorAll('.form-control, .form-select, input[type="text"], input[type="search"], select').forEach(control => {
                    control.style.backgroundColor = '#252525';
                    control.style.border = '1px solid #333333';
                    control.style.color = '#ffffff';
                });

                // Style input group text
                filters.querySelectorAll('.input-group-text').forEach(text => {
                    text.style.backgroundColor = '#252525';
                    text.style.border = 'none';
                    text.style.borderRight = '1px solid #333333';
                    text.style.color = '#ffffff';
                });

                // Style buttons
                filters.querySelectorAll('.btn-primary').forEach(button => {
                    button.style.backgroundColor = '#0077ff';
                    button.style.borderColor = '#0077ff';
                    button.style.backgroundImage = 'none';
                    button.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.3)';

                    // Add hover effect
                    button.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = '#0066dd';
                        this.style.transform = 'translateY(-1px)';
                        this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.4)';
                    });

                    // Add leave effect
                    button.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = '#0077ff';
                        this.style.transform = 'translateY(0)';
                        this.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.3)';
                    });
                });

                filters.querySelectorAll('.btn-outline-secondary').forEach(button => {
                    button.style.color = '#cccccc';
                    button.style.borderColor = '#555555';
                });

                // Style help text and muted text
                filters.querySelectorAll('.form-text, .text-muted, .text-secondary, small, p.text-muted').forEach(text => {
                    text.style.color = '#aaaaaa';
                });

                // Fix input group padding if present
                const inputGroup = filters.querySelector('.input-group');
                if (inputGroup) {
                    inputGroup.style.padding = '0';
                    inputGroup.style.marginBottom = '0';
                    inputGroup.style.boxShadow = 'none';
                }
            });

            // Apply dark mode to tier sections
            document.querySelectorAll('.tier-section').forEach(section => {
                section.style.backgroundColor = '#1a1a1a';
                section.style.border = '1px solid #333333';
                section.style.borderRadius = '8px';
                section.style.padding = '20px';
                section.style.marginBottom = '30px';

                // Style headings
                const heading = section.querySelector('h3');
                if (heading) {
                    heading.style.color = '#ffffff';
                    heading.style.borderBottom = '1px solid #333333';
                    heading.style.paddingBottom = '10px';
                    heading.style.marginBottom = '20px';
                }
            });

            // Apply dark mode to list group items and cards
            document.querySelectorAll('.list-group-item, .company-cards-container .card').forEach(item => {
                item.style.backgroundColor = '#252525';
                item.style.border = '1px solid #333333';
                item.style.borderRadius = '8px';
                item.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
                item.style.color = '#ffffff';

                // Style links
                item.querySelectorAll('a').forEach(link => {
                    link.style.color = '#0088ff';
                });
            });

            // Apply dark mode to tier badges
            document.querySelectorAll('.tier-badge').forEach(badge => {
                badge.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.3)';
            });

            // Apply dark mode to alerts
            document.querySelectorAll('.alert-light').forEach(alert => {
                alert.style.backgroundColor = '#1a1a1a';
                alert.style.borderColor = '#333333';
                alert.style.color = '#ffffff';
            });

            // Apply dark mode to badges
            document.querySelectorAll('.badge').forEach(badge => {
                badge.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.2)';
            });

            // Apply dark mode to like and favorite buttons
            document.querySelectorAll('.like-button, .favorite-button, .btn-like, .btn-favorite, .btn-circle').forEach(button => {
                button.style.backgroundColor = '#252525';
                button.style.border = '1px solid #333333';
                button.style.color = '#ffffff';
                button.style.borderRadius = '50%';
                button.style.width = '36px';
                button.style.height = '36px';
                button.style.display = 'flex';
                button.style.alignItems = 'center';
                button.style.justifyContent = 'center';
                button.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.3)';
                button.style.transition = 'all 0.2s ease';

                // Check if button is active
                if (button.classList.contains('active')) {
                    button.style.backgroundColor = '#0077ff';
                    button.style.borderColor = '#0077ff';
                }

                // Add hover effect
                button.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('active')) {
                        this.style.backgroundColor = '#333333';
                    }
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.4)';
                });

                // Add leave effect
                button.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        this.style.backgroundColor = '#252525';
                    }
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.3)';
                });

                // Style icon inside button
                const icon = button.querySelector('i');
                if (icon) {
                    icon.style.fontSize = '16px';
                }
            });

            // Apply dark mode to tables
            document.querySelectorAll('.table').forEach(table => {
                table.style.color = '#ffffff';
                table.style.backgroundColor = '#1a1a1a';
                table.style.borderRadius = '8px';
                table.style.overflow = 'hidden';
                table.style.border = '1px solid #333333';

                // Style table headers
                table.querySelectorAll('thead th').forEach(th => {
                    th.style.backgroundColor = '#252525';
                    th.style.color = '#ffffff';
                    th.style.borderBottom = '2px solid #333333';
                    th.style.padding = '12px 15px';
                    th.style.fontWeight = '600';
                });

                // Style table rows
                table.querySelectorAll('tbody tr').forEach((tr, index) => {
                    tr.style.borderBottom = '1px solid #333333';

                    // Add striped effect
                    if (index % 2 === 0) {
                        tr.style.backgroundColor = '#1d1d1d';
                    } else {
                        tr.style.backgroundColor = '#1a1a1a';
                    }

                    // Add hover effect
                    tr.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = '#252525';
                    });

                    tr.addEventListener('mouseleave', function() {
                        if (index % 2 === 0) {
                            this.style.backgroundColor = '#1d1d1d';
                        } else {
                            this.style.backgroundColor = '#1a1a1a';
                        }
                    });
                });

                // Style table cells
                table.querySelectorAll('tbody td').forEach(td => {
                    td.style.padding = '12px 15px';
                    td.style.color = '#cccccc';
                    td.style.borderTop = '1px solid #333333';
                });
            });

            // Apply dark mode to modals
            document.querySelectorAll('.modal-content').forEach(modal => {
                modal.style.backgroundColor = '#1a1a1a';
                modal.style.border = '1px solid #333333';
                modal.style.borderRadius = '8px';
                modal.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.3)';

                // Style modal header
                const modalHeader = modal.querySelector('.modal-header');
                if (modalHeader) {
                    modalHeader.style.borderBottom = '1px solid #333333';
                    modalHeader.style.padding = '15px 20px';

                    // Style modal title
                    const modalTitle = modalHeader.querySelector('.modal-title');
                    if (modalTitle) {
                        modalTitle.style.color = '#ffffff';
                        modalTitle.style.fontWeight = '600';
                    }

                    // Style close button
                    const closeButton = modalHeader.querySelector('.close');
                    if (closeButton) {
                        closeButton.style.color = '#ffffff';
                        closeButton.style.opacity = '0.8';

                        // Add hover effect
                        closeButton.addEventListener('mouseenter', function() {
                            this.style.opacity = '1';
                        });

                        closeButton.addEventListener('mouseleave', function() {
                            this.style.opacity = '0.8';
                        });
                    }
                }

                // Style modal body
                const modalBody = modal.querySelector('.modal-body');
                if (modalBody) {
                    modalBody.style.padding = '20px';
                    modalBody.style.color = '#cccccc';
                }

                // Style modal footer
                const modalFooter = modal.querySelector('.modal-footer');
                if (modalFooter) {
                    modalFooter.style.borderTop = '1px solid #333333';
                    modalFooter.style.padding = '15px 20px';
                }
            });
        } else {
            // Reset styles for light mode
            document.querySelectorAll(`
                body.bg-light,
                .bg-light,
                .featured-companies,
                .featured-section,
                .featured-carousel-container,
                .featured-item-wrapper,
                .item-info,
                .logo-container,
                .logo-placeholder,
                .company-card,
                .company-logo-container,
                .company-name,
                .company-description,
                .company-actions,
                .company-search-container,
                .search-input,
                .search-button,
                .company-filters,
                .filter-form,
                .form-control,
                .form-select,
                input[type="text"],
                input[type="search"],
                select,
                .input-group-text,
                .btn-primary,
                .btn-outline-secondary,
                .tier-section,
                .list-group-item,
                .company-cards-container .card,
                .tier-badge,
                .alert-light,
                .badge,
                .table,
                .table thead th,
                .table tbody tr,
                .table tbody td,
                .modal-content,
                .modal-header,
                .modal-title,
                .modal-body,
                .modal-footer,
                .close,
                .search-bar,
                .search-container,
                .like-button,
                .favorite-button,
                .btn-like,
                .btn-favorite,
                .btn-circle,
                .company-logo,
                .featured-logo,
                img.logo
            `).forEach(el => {
                el.style.backgroundColor = '';
                el.style.backgroundImage = '';
                el.style.backgroundAttachment = '';
                el.style.background = '';
                el.style.border = '';
                el.style.borderColor = '';
                el.style.borderBottom = '';
                el.style.borderTop = '';
                el.style.borderRadius = '';
                el.style.boxShadow = '';
                el.style.color = '';
                el.style.transform = '';
                el.style.transition = '';
                el.style.padding = '';
                el.style.margin = '';
                el.style.marginBottom = '';
                el.style.textAlign = '';
                el.style.fontWeight = '';
                el.style.outline = '';

                // Remove event listeners by cloning and replacing
                const newEl = el.cloneNode(true);
                el.parentNode.replaceChild(newEl, el);
            });
        }
    }

    // Apply dark mode immediately if needed
    if (isDarkMode) {
        applyDarkModeToCompanyDirectory(true);
    }

    // Set up a mutation observer to apply styles to new elements
    const observer = new MutationObserver(function() {
        const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
        if (isDarkMode) {
            applyDarkModeToCompanyDirectory(true);

            // Additional check for the filter form with delay to catch any dynamic elements
            setTimeout(function() {
                const filterForm = document.getElementById('form-filter-form');
                if (filterForm) {
                    filterForm.style.backgroundColor = '#1a1a1a';
                    filterForm.style.border = '1px solid #333333';
                    filterForm.style.borderRadius = '8px';
                    filterForm.style.padding = '20px';
                    filterForm.style.marginBottom = '20px';
                    filterForm.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
                }

                // Also try to find by class or other attributes
                document.querySelectorAll('form.filter-form, #form-filter-form, .form-filter-form').forEach(form => {
                    form.style.backgroundColor = '#1a1a1a';
                    form.style.border = '1px solid #333333';
                    form.style.borderRadius = '8px';
                    form.style.padding = '20px';
                    form.style.marginBottom = '20px';
                    form.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
                });
            }, 500);
        }
    });

    observer.observe(document.body, { childList: true, subtree: true });

    // Also set up a timer to periodically check for the filter form
    if (document.documentElement.getAttribute('data-theme') === 'dark') {
        setInterval(function() {
            const filterForm = document.getElementById('form-filter-form');
            if (filterForm) {
                filterForm.style.backgroundColor = '#1a1a1a';
                filterForm.style.border = '1px solid #333333';
                filterForm.style.borderRadius = '8px';
                filterForm.style.padding = '20px';
                filterForm.style.marginBottom = '20px';
                filterForm.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
            }
        }, 2000);
    }
});
