"""
<PERSON><PERSON><PERSON> to fix the database schema and migrations for the assistants app.
"""
import os
import django
import psycopg2
from django.conf import settings

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

def fix_database():
    """Fix the database schema and migrations."""
    print("Starting database fix...")
    
    # Get database connection info from settings
    db_settings = settings.DATABASES['default']
    
    # Connect to the database
    try:
        conn = psycopg2.connect(
            dbname=db_settings['NAME'],
            user=db_settings['USER'],
            password=db_settings['PASSWORD'],
            host=db_settings['HOST'],
            port=db_settings['PORT']
        )
        conn.autocommit = True
        cursor = conn.cursor()
        print("Connected to the database.")
    except Exception as e:
        print(f"Error connecting to the database: {e}")
        return
    
    # Check if the columns exist
    columns_to_check = [
        'featured_autoplay',
        'featured_autoplay_delay',
        'featured_scroll_direction',
        'featured_transition_effect',
        'featured_visible_count',
        'is_featured'
    ]
    
    for column in columns_to_check:
        try:
            cursor.execute("""
                SELECT 1
                FROM information_schema.columns
                WHERE table_name = 'assistants_assistant'
                AND column_name = %s
            """, (column,))
            
            if cursor.fetchone():
                print(f"Column '{column}' already exists in assistants_assistant.")
            else:
                print(f"Column '{column}' does not exist in assistants_assistant. Adding it...")
                
                # Add the column with appropriate default value
                if column == 'featured_autoplay' or column == 'is_featured':
                    cursor.execute(f"ALTER TABLE assistants_assistant ADD COLUMN {column} boolean DEFAULT false")
                elif column == 'featured_autoplay_delay':
                    cursor.execute(f"ALTER TABLE assistants_assistant ADD COLUMN {column} integer DEFAULT 5000")
                elif column == 'featured_visible_count':
                    cursor.execute(f"ALTER TABLE assistants_assistant ADD COLUMN {column} integer DEFAULT 3")
                elif column == 'featured_scroll_direction':
                    cursor.execute(f"ALTER TABLE assistants_assistant ADD COLUMN {column} varchar(10) DEFAULT 'horizontal'")
                elif column == 'featured_transition_effect':
                    cursor.execute(f"ALTER TABLE assistants_assistant ADD COLUMN {column} varchar(10) DEFAULT 'slide'")
                
                print(f"Column '{column}' added to assistants_assistant.")
        except Exception as e:
            print(f"Error checking/adding column '{column}': {e}")
    
    # Mark migrations as applied
    migrations_to_mark = [
        ('assistants', '0009_add_featured_settings'),
        ('assistants', '0010_merge_20250402_0632')
    ]
    
    for app, name in migrations_to_mark:
        try:
            cursor.execute("""
                SELECT 1
                FROM django_migrations
                WHERE app = %s AND name = %s
            """, (app, name))
            
            if cursor.fetchone():
                print(f"Migration '{app}.{name}' is already applied.")
            else:
                cursor.execute("""
                    INSERT INTO django_migrations (app, name, applied)
                    VALUES (%s, %s, NOW())
                """, (app, name))
                
                print(f"Migration '{app}.{name}' marked as applied.")
        except Exception as e:
            print(f"Error marking migration '{app}.{name}' as applied: {e}")
    
    # Close the database connection
    cursor.close()
    conn.close()
    
    print("Database fix completed.")
    print("\nNext steps:")
    print("1. Run 'python manage.py migrate' to apply the remaining migrations.")

if __name__ == "__main__":
    fix_database()
