from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    help = 'Checks and fixes the accounts_registrationlink table structure'

    def handle(self, *args, **options):
        self.stdout.write('Checking accounts_registrationlink table structure...')

        # Check if the table exists
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = 'accounts_registrationlink'
                );
            """)
            table_exists = cursor.fetchone()[0]

            if table_exists:
                self.stdout.write(self.style.SUCCESS('The accounts_registrationlink table exists!'))
            else:
                self.stdout.write(self.style.WARNING('The accounts_registrationlink table does NOT exist!'))

                # Create the table
                self.stdout.write('Creating accounts_registrationlink table...')
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS "accounts_registrationlink" (
                        "id" serial NOT NULL PRIMARY KEY,
                        "token" uuid NOT NULL UNIQUE,
                        "expires_at" timestamp with time zone NULL,
                        "max_uses" integer NULL,
                        "uses_count" integer NOT NULL,
                        "is_active" boolean NOT NULL,
                        "notes" varchar(255) NOT NULL,
                        "created_at" timestamp with time zone NOT NULL,
                        "qr_code" varchar(100) NULL,
                        "company_id" integer NOT NULL REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED,
                        "created_by_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
                        "intended_group_id" integer NULL REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED
                    );
                """)
                self.stdout.write(self.style.SUCCESS('Table created successfully!'))

            # Check if the many-to-many table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = 'accounts_registrationlink_accessible_folders'
                );
            """)
            m2m_table_exists = cursor.fetchone()[0]

            if m2m_table_exists:
                self.stdout.write(self.style.SUCCESS('The accounts_registrationlink_accessible_folders table exists!'))

                # Check if the table has the correct structure
                try:
                    cursor.execute("""
                        SELECT "registrationlink_id", "assistantfolder_id"
                        FROM "accounts_registrationlink_accessible_folders"
                        LIMIT 0;
                    """)
                    self.stdout.write(self.style.SUCCESS('The table has the correct structure!'))
                except Exception as e:
                    self.stdout.write(self.style.WARNING(f'Error checking table structure: {e}'))
                    self.stdout.write('The table might not have the correct structure.')
            else:
                self.stdout.write(self.style.WARNING('The accounts_registrationlink_accessible_folders table does NOT exist!'))

                # Create the many-to-many table
                self.stdout.write('Creating accounts_registrationlink_accessible_folders table...')
                try:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS "accounts_registrationlink_accessible_folders" (
                            "id" serial NOT NULL PRIMARY KEY,
                            "registrationlink_id" integer NOT NULL,
                            "assistantfolder_id" integer NOT NULL,
                            CONSTRAINT "accounts_registrationlink_accessible_folders_registrationlink_id_assistantfolder_id_unique" UNIQUE ("registrationlink_id", "assistantfolder_id")
                        );
                    """)

                    # Add foreign key constraints separately
                    cursor.execute("""
                        ALTER TABLE "accounts_registrationlink_accessible_folders"
                        ADD CONSTRAINT "accounts_registrationlink_accessible_folders_registrationlink_id_fkey"
                        FOREIGN KEY ("registrationlink_id") REFERENCES "accounts_registrationlink" ("id") DEFERRABLE INITIALLY DEFERRED;
                    """)

                    cursor.execute("""
                        ALTER TABLE "accounts_registrationlink_accessible_folders"
                        ADD CONSTRAINT "accounts_registrationlink_accessible_folders_assistantfolder_id_fkey"
                        FOREIGN KEY ("assistantfolder_id") REFERENCES "assistants_assistantfolder" ("id") DEFERRABLE INITIALLY DEFERRED;
                    """)

                    self.stdout.write(self.style.SUCCESS('Table created successfully!'))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error creating table: {e}'))
                    self.stdout.write('Trying to continue...')

        self.stdout.write(self.style.SUCCESS('Table check and fix completed successfully!'))
