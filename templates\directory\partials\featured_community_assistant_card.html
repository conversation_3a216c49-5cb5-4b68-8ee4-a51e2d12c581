{% load static account_tags rating_tags %}

{# Featured Community Assistant Card - Optimized for carousel display #}
<div class="list-group-item position-relative directory-card" data-assistant-id="{{ assistant.id }}">
    {# Show Featured badge #}
    {% if assistant.is_featured %}
    <span class="badge bg-success position-absolute top-0 start-0 m-2" style="z-index: 10; font-size: 0.7em; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <i class="bi bi-star-fill me-1"></i>Featured
    </span>
    {% endif %}

    {# Tier badges are not shown in the featured section #}

    <div class="row g-3 text-center">
        {# Logo #}
        <div class="col-12 d-flex justify-content-center">
            <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="text-decoration-none">
                <div class="logo-container">
                    {% with logo_url=assistant.get_logo_url %}
                        {% if logo_url %}
                            <img src="{{ logo_url }}" alt="{{ assistant.name }} logo">
                        {% else %}
                            <i class="bi bi-robot logo-placeholder"></i>
                        {% endif %}
                    {% endwith %}
                </div>
            </a>
        </div>

        {# Assistant Name and Company #}
        <div class="col-12">
            <h6 class="mb-1">
                <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="text-decoration-none text-body">
                    {{ assistant.name }}
                </a>
            </h6>
            <p class="mb-2 text-muted small">
                <i class="bi bi-building me-1"></i>{{ assistant.company.name }}
            </p>
            <div class="mb-2">
                <span class="badge bg-secondary tag-badge community-badge">Community</span>
                {% if assistant.linked_company %}
                <span class="badge bg-info tag-badge">Linked to {{ assistant.linked_company.name }}</span>
                {% endif %}
            </div>
        </div>

        {# Rating and Like Button #}
        <div class="col-12 d-flex justify-content-center align-items-center">
            {# Rating Display #}
            {% if assistant.avg_rating > 0 %}
                <div class="rating-display-container me-2" id="rating-display-{{ assistant.id }}">
                    {% render_stars assistant.avg_rating assistant.total_ratings %}
                </div>
            {% else %}
                <div class="small text-muted fst-italic me-2" id="no-rating-placeholder-{{ assistant.id }}">(No ratings)</div>
            {% endif %}

            {# Favorite Button #}
            {% if user.is_authenticated %}
                <button
                    class="like-button btn btn-sm p-0 {% if assistant.id in saved_assistant_ids %}text-danger{% else %}text-secondary{% endif %}"
                    data-item-id="{{ assistant.id }}"
                    data-item-type="assistant"
                    title="{% if assistant.id in saved_assistant_ids %}Unlike{% else %}Like{% endif %}"
                    style="background: none; border: none; cursor: pointer; line-height: 1;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-heart-fill" viewBox="0 0 16 16" style="pointer-events: none;">
                        <path fill-rule="evenodd" d="M8 1.314C12.438-3.248 23.534 4.735 8 15-7.534 4.736 3.562-3.248 8 1.314"/>
                    </svg>
                </button>
            {% endif %}
        </div>
    </div>
</div>
