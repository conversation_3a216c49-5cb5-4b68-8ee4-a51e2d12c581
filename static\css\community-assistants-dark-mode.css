/**
 * Dark Mode Styling for Community Assistants Page
 * Includes styling for filter form and folder options modal
 */

/* Direct fix for the form with white padding in the screenshot */
[data-theme="dark"] form[class*="filter"],
[data-theme="dark"] div[class*="filter"],
[data-theme="dark"] *[class*="filter-form"],
[data-theme="dark"] *[id*="filter-form"] {
  background-color: #121212 !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  padding: 20px !important;
  margin-bottom: 20px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

/* Target the form-filter-form element directly */
[data-theme="dark"] form.form-filter-form,
[data-theme="dark"] #form-filter-form,
[data-theme="dark"] .form-filter-form {
  background-color: #121212 !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  padding: 20px !important;
  margin-bottom: 20px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

/* Target the search input container */
[data-theme="dark"] form.form-filter-form .input-group,
[data-theme="dark"] #form-filter-form .input-group,
[data-theme="dark"] .form-filter-form .input-group {
  background-color: transparent !important;
  border: none !important;
}

/* Target the search input field */
[data-theme="dark"] form.form-filter-form .form-control,
[data-theme="dark"] #form-filter-form .form-control,
[data-theme="dark"] .form-filter-form .form-control {
  background-color: #252525 !important;
  border-color: #444444 !important;
  color: #ffffff !important;
}

/* Target the search button */
[data-theme="dark"] form.form-filter-form .btn-primary,
[data-theme="dark"] #form-filter-form .btn-primary,
[data-theme="dark"] .form-filter-form .btn-primary {
  background-color: #0077ff !important;
  border-color: #0066dd !important;
  color: #ffffff !important;
}

/* Target the search icon container */
[data-theme="dark"] form.form-filter-form .input-group-text,
[data-theme="dark"] #form-filter-form .input-group-text,
[data-theme="dark"] .form-filter-form .input-group-text {
  background-color: #252525 !important;
  border-color: #444444 !important;
  color: #ffffff !important;
}

/* Fix for the white padding in the filter form */
[data-theme="dark"] .filter-form,
[data-theme="dark"] form.filter-form {
  background-color: #121212 !important;
  padding: 20px !important;
  border-radius: 8px !important;
  border: 1px solid #333333 !important;
}

/* Extremely specific selector for the filter form with white padding */
[data-theme="dark"] form[id="form-filter-form"],
[data-theme="dark"] form#form-filter-form,
[data-theme="dark"] #form-filter-form,
[data-theme="dark"] .form-filter-form,
[data-theme="dark"] form.filter-form,
[data-theme="dark"] div > form#form-filter-form,
[data-theme="dark"] div > #form-filter-form,
[data-theme="dark"] body form#form-filter-form {
  background-color: #121212 !important;
  background: #121212 !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  padding: 20px !important;
  margin-bottom: 20px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

/* Direct fix for the specific form in the screenshot */
[data-theme="dark"] form[id="form-filter-form"],
[data-theme="dark"] form.form-filter-form,
[data-theme="dark"] #form-filter-form,
[data-theme="dark"] .form-filter-form {
  background-color: #121212 !important;
  background: #121212 !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  padding: 20px !important;
  margin-bottom: 20px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

/* Dark mode styling for the folder options modal */
[data-theme="dark"] #folderOptionsModal .modal-content {
  background-color: #1e1e1e !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5) !important;
}

[data-theme="dark"] #folderOptionsModal .modal-header {
  background-color: #252525 !important;
  border-bottom: 1px solid #333333 !important;
  color: #ffffff !important;
}

[data-theme="dark"] #folderOptionsModal .modal-title {
  color: #ffffff !important;
  font-weight: 600 !important;
}

[data-theme="dark"] #folderOptionsModal .modal-body {
  color: #e0e0e0 !important;
  background-color: #1e1e1e !important;
}

[data-theme="dark"] #folderOptionsModal .list-group-item {
  background-color: #2a2a2a !important;
  border-color: #333333 !important;
  color: #e0e0e0 !important;
  transition: all 0.2s ease !important;
}

[data-theme="dark"] #folderOptionsModal .list-group-item:hover {
  background-color: #333333 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] #folderOptionsModal .folder-list-container {
  background-color: #252525 !important;
  border: 1px solid #444444 !important;
  border-radius: 0.25rem !important;
  scrollbar-width: thin !important;
  scrollbar-color: #444444 #252525 !important;
}

[data-theme="dark"] #folderOptionsModal .folder-list-container::-webkit-scrollbar {
  width: 8px !important;
}

[data-theme="dark"] #folderOptionsModal .folder-list-container::-webkit-scrollbar-track {
  background: #252525 !important;
}

[data-theme="dark"] #folderOptionsModal .folder-list-container::-webkit-scrollbar-thumb {
  background-color: #444444 !important;
  border-radius: 4px !important;
}

[data-theme="dark"] #folderOptionsModal .form-control {
  background-color: #252525 !important;
  border-color: #444444 !important;
  color: #ffffff !important;
  box-shadow: none !important;
}

[data-theme="dark"] #folderOptionsModal .form-control:focus {
  border-color: #0077ff !important;
  box-shadow: 0 0 0 0.25rem rgba(0, 119, 255, 0.25) !important;
}

[data-theme="dark"] #folderOptionsModal .input-group-text {
  background-color: #252525 !important;
  border-color: #444444 !important;
  color: #ffffff !important;
}

[data-theme="dark"] #folderOptionsModal .btn-primary {
  background-color: #0077ff !important;
  border-color: #0066dd !important;
  color: #ffffff !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
  padding: 0.6rem 1.2rem !important;
  font-weight: 500 !important;
}

[data-theme="dark"] #folderOptionsModal .btn-primary:hover {
  background-color: #0066dd !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15) !important;
}

[data-theme="dark"] #folderOptionsModal .btn-success {
  background-color: #28a745 !important;
  border-color: #218838 !important;
  color: #ffffff !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

[data-theme="dark"] #folderOptionsModal .btn-success:hover {
  background-color: #218838 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15) !important;
}

[data-theme="dark"] #folderOptionsModal .btn-outline-secondary {
  border-color: #444444 !important;
  color: #e0e0e0 !important;
  background-color: transparent !important;
  transition: all 0.2s ease !important;
  padding: 0.6rem 1.2rem !important;
}

[data-theme="dark"] #folderOptionsModal .btn-outline-secondary:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: #666666 !important;
}

/* Quick save section styling */
[data-theme="dark"] #folderOptionsModal .quick-save-section {
  margin-top: 1rem !important;
}

[data-theme="dark"] #folderOptionsModal .quick-save-section .btn {
  padding: 0.75rem 1.5rem !important;
  font-size: 1.05rem !important;
}

/* Folder modal content container */
[data-theme="dark"] #folderOptionsModal .folder-modal-content {
  display: flex !important;
  flex-direction: column !important;
}

/* Folder list container */
[data-theme="dark"] #folderOptionsModal .folder-list-section {
  margin-bottom: 1.5rem !important;
}

[data-theme="dark"] #folderOptionsModal .text-muted {
  color: #aaaaaa !important;
}

[data-theme="dark"] #folderOptionsModal hr {
  border-color: #333333 !important;
  opacity: 0.5 !important;
}

[data-theme="dark"] #folderOptionsModal .alert-danger {
  background-color: rgba(220, 53, 69, 0.2) !important;
  border-color: #dc3545 !important;
  color: #f8d7da !important;
}

[data-theme="dark"] #folderOptionsModal strong {
  color: #0077ff !important;
  font-weight: 600 !important;
}

/* Heart icon styling for like buttons - white for unliked, pink for liked */
/* ALL heart icons are white by default in dark mode */
[data-theme="dark"] .like-button i,
[data-theme="dark"] .btn-like i,
[data-theme="dark"] .btn-favorite i,
[data-theme="dark"] .favorite-button i,
[data-theme="dark"] .like-button svg,
[data-theme="dark"] .btn-like svg,
[data-theme="dark"] .btn-favorite svg,
[data-theme="dark"] .favorite-button svg,
[data-theme="dark"] .like-button .bi-heart,
[data-theme="dark"] .btn-like .bi-heart,
[data-theme="dark"] .btn-favorite .bi-heart,
[data-theme="dark"] .favorite-button .bi-heart,
[data-theme="dark"] .like-button .bi-heart-fill,
[data-theme="dark"] .btn-like .bi-heart-fill,
[data-theme="dark"] .btn-favorite .bi-heart-fill,
[data-theme="dark"] .favorite-button .bi-heart-fill {
  color: #ffffff !important;
  fill: #ffffff !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5)) !important;
}

/* ONLY liked hearts should be pink/red in dark mode */
[data-theme="dark"] .like-button.text-danger i,
[data-theme="dark"] .btn-like.text-danger i,
[data-theme="dark"] .btn-favorite.text-danger i,
[data-theme="dark"] .favorite-button.text-danger i,
[data-theme="dark"] .like-button.text-danger svg,
[data-theme="dark"] .btn-like.text-danger svg,
[data-theme="dark"] .btn-favorite.text-danger svg,
[data-theme="dark"] .favorite-button.text-danger svg,
[data-theme="dark"] .like-button.text-danger .bi-heart,
[data-theme="dark"] .btn-like.text-danger .bi-heart,
[data-theme="dark"] .btn-favorite.text-danger .bi-heart,
[data-theme="dark"] .favorite-button.text-danger .bi-heart,
[data-theme="dark"] .like-button.text-danger .bi-heart-fill,
[data-theme="dark"] .btn-like.text-danger .bi-heart-fill,
[data-theme="dark"] .btn-favorite.text-danger .bi-heart-fill,
[data-theme="dark"] .favorite-button.text-danger .bi-heart-fill {
  color: #ff3366 !important;
  fill: #ff3366 !important;
  filter: drop-shadow(0 0 3px rgba(255, 51, 102, 0.5)) !important;
}