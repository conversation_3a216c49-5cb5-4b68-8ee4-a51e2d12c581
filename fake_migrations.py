"""
Script to fake migrations that are causing issues.
"""
import os
import sys
import django
from django.core.management import call_command

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

def check_migration_status():
    """Check the status of migrations for the assistants app."""
    print("Checking migration status for assistants app...")
    try:
        call_command('showmigrations', 'assistants')
        return True
    except Exception as e:
        print(f"Error checking migration status: {e}")
        return False

def fake_migration(app, migration):
    """Fake a specific migration."""
    print(f"Faking migration {app}.{migration}...")
    try:
        call_command('migrate', app, migration, fake=True)
        print(f"Migration {app}.{migration} faked successfully.")
        return True
    except Exception as e:
        print(f"Error faking migration {app}.{migration}: {e}")
        return False

def main():
    """Main function to fake migrations."""
    print("Starting migration fix...")
    
    # Check migration status
    check_migration_status()
    
    # Fake the problematic migrations
    migrations_to_fake = [
        ('assistants', '0039_alter_assistant_is_active'),
    ]
    
    for app, migration in migrations_to_fake:
        if not fake_migration(app, migration):
            print(f"Failed to fake migration {app}.{migration}. Continuing...")
    
    # Check migration status again
    check_migration_status()
    
    print("\nNext steps:")
    print("1. Run 'python manage.py migrate' to apply the remaining migrations")
    print("2. If you still encounter issues, try 'python manage.py migrate --fake-initial'")

if __name__ == "__main__":
    main()
