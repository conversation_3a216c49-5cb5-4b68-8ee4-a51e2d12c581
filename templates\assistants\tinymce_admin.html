{% load static %}
<!-- TinyMCE implementation using Django admin's TinyMCE -->
<script src="{% url 'admin:jsi18n' %}"></script>
<script src="{% static 'admin/js/vendor/jquery/jquery.min.js' %}"></script>
<script src="{% static 'admin/js/jquery.init.js' %}"></script>
<script src="{% static 'tinymce/tinymce.min.js' %}"></script>

<script>
// Initialize TinyMCE directly
document.addEventListener('DOMContentLoaded', function() {
    console.log("Initializing TinyMCE from admin approach");
    
    if (typeof tinymce !== 'undefined') {
        tinymce.init({
            selector: '#id_text_content',
            height: 300,
            menubar: false,
            plugins: [
                'advlist autolink lists link image charmap print preview anchor',
                'searchreplace visualblocks code fullscreen',
                'insertdatetime media table paste code help wordcount'
            ],
            toolbar: 'undo redo | formatselect | bold italic backcolor | ' +
                    'alignleft aligncenter alignright alignjustify | ' +
                    'bullist numlist outdent indent | removeformat | help',
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }'
        });
    } else {
        console.error("TinyMCE not available");
        document.getElementById('tinymce-fallback').style.display = 'block';
    }
});
</script>
