import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

# SQL to create the directory_directorysettings table
sql = """
CREATE TABLE IF NOT EXISTS "directory_directorysettings" (
    "id" serial NOT NULL PRIMARY KEY,
    "featured_scroll_direction" varchar(10) NOT NULL,
    "featured_transition_effect" varchar(10) NOT NULL,
    "featured_visible_count" integer NOT NULL,
    "featured_autoplay" boolean NOT NULL,
    "featured_autoplay_delay" integer NOT NULL,
    "hide_standard_tier_assistants" boolean NOT NULL,
    "hide_standard_tier_companies" boolean NOT NULL
);
"""

# Execute the SQL
with connection.cursor() as cursor:
    cursor.execute(sql)

# Insert the default settings
sql_insert = """
INSERT INTO directory_directorysettings (
    id, 
    featured_scroll_direction, 
    featured_transition_effect, 
    featured_visible_count, 
    featured_autoplay, 
    featured_autoplay_delay, 
    hide_standard_tier_assistants, 
    hide_standard_tier_companies
)
VALUES (
    1, 
    'horizontal', 
    'slide', 
    1, 
    true, 
    5000, 
    false, 
    false
)
ON CONFLICT (id) DO NOTHING;
"""

with connection.cursor() as cursor:
    cursor.execute(sql_insert)

print("Directory settings table created successfully!")
