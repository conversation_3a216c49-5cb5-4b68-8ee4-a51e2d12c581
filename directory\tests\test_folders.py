import uuid
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError

from accounts.models import Company
from assistants.models import Assistant, AssistantFolder
from directory.models import FavoriteFolder, SavedItem

User = get_user_model()

class FavoriteFolderModelTests(TestCase):
    """Tests for the FavoriteFolder model."""

    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='foldertest',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create test company
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.user
        )

        # Create test assistant
        self.assistant = Assistant.objects.create(
            name='Test Assistant',
            company=self.company,
            assistant_type='general',
            is_public=True,
            is_active=True
        )

    def test_favorite_folder_creation(self):
        """Test creating a favorite folder."""
        folder = FavoriteFolder.objects.create(
            user=self.user,
            name='Test Folder',
            item_type='assistant'
        )

        self.assertEqual(folder.name, 'Test Folder')
        self.assertEqual(folder.item_type, 'assistant')
        self.assertEqual(folder.user, self.user)

    def test_favorite_folder_unique_constraint(self):
        """Test that a user cannot have two folders with the same name for the same item type."""
        # Create first folder
        FavoriteFolder.objects.create(
            user=self.user,
            name='Test Folder',
            item_type='assistant'
        )

        # Try to create another folder with the same name and item type
        with self.assertRaises(Exception):
            FavoriteFolder.objects.create(
                user=self.user,
                name='Test Folder',
                item_type='assistant'
            )

    def test_favorite_folder_different_types(self):
        """Test that a user can have folders with the same name for different item types."""
        # Create assistant folder
        folder1 = FavoriteFolder.objects.create(
            user=self.user,
            name='Test Folder',
            item_type='assistant'
        )

        # Create company folder with the same name
        folder2 = FavoriteFolder.objects.create(
            user=self.user,
            name='Test Folder',
            item_type='company'
        )

        self.assertEqual(folder1.name, folder2.name)
        self.assertNotEqual(folder1.item_type, folder2.item_type)

    def test_saved_item_with_folder(self):
        """Test saving an item to a folder."""
        # Create folder
        folder = FavoriteFolder.objects.create(
            user=self.user,
            name='Test Folder',
            item_type='assistant'
        )

        # Save assistant to folder
        saved_item = SavedItem.objects.create(
            user=self.user,
            item_type='assistant',
            assistant=self.assistant,
            folder=folder
        )

        self.assertEqual(saved_item.folder, folder)
        self.assertEqual(saved_item.assistant, self.assistant)

    def test_saved_item_folder_type_validation(self):
        """Test that an item can only be saved to a folder of the same type."""
        # Create company folder
        company_folder = FavoriteFolder.objects.create(
            user=self.user,
            name='Company Folder',
            item_type='company'
        )

        # Try to save assistant to company folder
        saved_item = SavedItem(
            user=self.user,
            item_type='assistant',
            assistant=self.assistant,
            folder=company_folder
        )

        # Should raise validation error
        with self.assertRaises(ValidationError):
            saved_item.full_clean()


class FavoriteFolderViewTests(TestCase):
    """Tests for the favorite folder views."""

    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='folderviewtest',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create test company
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.user
        )

        # Create test assistant
        self.assistant = Assistant.objects.create(
            name='Test Assistant',
            company=self.company,
            assistant_type='general',
            is_public=True,
            is_active=True
        )

        # Create test client and login
        self.client = Client()
        self.client.login(username='folderviewtest', password='testpass123')

    def test_create_folder_and_save(self):
        """Test creating a folder and saving an item to it."""
        response = self.client.post(
            reverse('directory:create_folder_and_save'),
            {
                'item_type': 'assistant',
                'item_id': self.assistant.id,
                'folder_name': 'New Test Folder'
            }
        )

        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], 'success')

        # Check folder was created
        folder = FavoriteFolder.objects.get(name='New Test Folder', user=self.user)
        self.assertEqual(folder.item_type, 'assistant')

        # Check item was saved to folder
        saved_item = SavedItem.objects.get(user=self.user, assistant=self.assistant)
        self.assertEqual(saved_item.folder, folder)

    def test_save_item_no_folder(self):
        """Test saving an item without a folder."""
        response = self.client.post(
            reverse('directory:save_item_no_folder'),
            {
                'item_type': 'assistant',
                'item_id': self.assistant.id
            }
        )

        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], 'success')

        # Check item was saved without a folder
        saved_item = SavedItem.objects.get(user=self.user, assistant=self.assistant)
        self.assertIsNone(saved_item.folder)

    def test_delete_favorite_folder(self):
        """Test deleting a favorite folder."""
        # Create folder
        folder = FavoriteFolder.objects.create(
            user=self.user,
            name='Folder to Delete',
            item_type='assistant'
        )

        # Save item to folder
        SavedItem.objects.create(
            user=self.user,
            item_type='assistant',
            assistant=self.assistant,
            folder=folder
        )

        response = self.client.post(
            reverse('directory:delete_favorite_folder', args=[folder.id])
        )

        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], 'success')

        # Check folder was deleted
        self.assertFalse(FavoriteFolder.objects.filter(id=folder.id).exists())

        # Check saved item still exists but folder is null
        saved_item = SavedItem.objects.get(user=self.user, assistant=self.assistant)
        self.assertIsNone(saved_item.folder)

    def test_list_folders_for_item_type(self):
        """Test listing folders for a specific item type."""
        # Create assistant folders
        folder1 = FavoriteFolder.objects.create(
            user=self.user,
            name='Assistant Folder 1',
            item_type='assistant'
        )
        folder2 = FavoriteFolder.objects.create(
            user=self.user,
            name='Assistant Folder 2',
            item_type='assistant'
        )

        # Create company folder (should not be returned)
        FavoriteFolder.objects.create(
            user=self.user,
            name='Company Folder',
            item_type='company'
        )

        response = self.client.get(
            reverse('directory:list_folders_for_item_type'),
            {'item_type': 'assistant'}
        )

        # Check response
        self.assertEqual(response.status_code, 200)
        folders = response.json()['folders']
        self.assertEqual(len(folders), 2)
        folder_ids = [f['id'] for f in folders]
        self.assertIn(folder1.id, folder_ids)
        self.assertIn(folder2.id, folder_ids)

    def test_my_favorites_view(self):
        """Test the my favorites view."""
        # Create folders
        assistant_folder = FavoriteFolder.objects.create(
            user=self.user,
            name='Assistant Folder',
            item_type='assistant'
        )
        company_folder = FavoriteFolder.objects.create(
            user=self.user,
            name='Company Folder',
            item_type='company'
        )

        # Save assistant to folder
        SavedItem.objects.create(
            user=self.user,
            item_type='assistant',
            assistant=self.assistant,
            folder=assistant_folder
        )

        # Save company to folder
        SavedItem.objects.create(
            user=self.user,
            item_type='company',
            company=self.company,
            folder=company_folder
        )

        response = self.client.get(reverse('directory:my_favorites'))

        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Assistant Folder')
        self.assertContains(response, 'Company Folder')
        self.assertContains(response, 'Test Assistant')
        self.assertContains(response, 'Test Company')

    def test_add_item_to_folder(self):
        """Test adding an item to an existing folder."""
        # Create folder
        folder = FavoriteFolder.objects.create(
            user=self.user,
            name='Existing Folder',
            item_type='assistant'
        )

        response = self.client.post(
            reverse('directory:add_item_to_folder'),
            {
                'item_type': 'assistant',
                'item_id': self.assistant.id,
                'folder_id': folder.id
            }
        )

        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], 'success')

        # Check item was saved to folder
        saved_item = SavedItem.objects.get(user=self.user, assistant=self.assistant)
        self.assertEqual(saved_item.folder, folder)
