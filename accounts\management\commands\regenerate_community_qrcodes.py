from django.core.management.base import BaseCommand
from django.urls import reverse
from accounts.models import Company
from utils.qr_generator import generate_model_qr_code
import time

class Command(BaseCommand):
    help = 'Regenerate QR codes for all community entities'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force regeneration of QR codes even if they already exist',
        )
        parser.add_argument(
            '--id',
            type=int,
            help='Regenerate QR code for a specific community ID',
        )

    def handle(self, *args, **options):
        force = options.get('force', False)
        specific_id = options.get('id')

        # Build the query
        query = {'entity_type': 'community'}
        if not force:
            query['qr_code'] = ''  # Only get communities without QR codes

        # If specific ID is provided, only process that community
        if specific_id:
            query = {'id': specific_id, 'entity_type': 'community'}
            self.stdout.write(f"Processing only community with ID: {specific_id}")

        # Get the communities
        communities = Company.objects.filter(**query)
        total = communities.count()

        self.stdout.write(f"Found {total} communities that need QR codes")

        # Process each community
        success_count = 0
        fail_count = 0

        for i, community in enumerate(communities, 1):
            self.stdout.write(f"Processing {i}/{total}: {community.name} (ID: {community.id})")

            # Try multiple attempts for each community
            max_attempts = 3
            success = False

            for attempt in range(max_attempts):
                try:
                    # Try to use the company detail URL
                    try:
                        url_path = reverse('accounts:company_detail', kwargs={'slug': community.slug})
                    except Exception as e:
                        # Fallback to direct URL if reverse fails
                        url_path = f"/accounts/company/{community.id}/"
                        self.stdout.write(self.style.WARNING(
                            f"Using fallback URL path for {community.name}: {url_path}"
                        ))

                    success = generate_model_qr_code(community, url_path, field_name='qr_code')

                    if success:
                        # Save the community with the new QR code
                        community.save(update_fields=['qr_code'])
                        self.stdout.write(self.style.SUCCESS(
                            f"Successfully generated QR code for {community.name} (attempt {attempt+1})"
                        ))
                        success_count += 1
                        break
                    else:
                        self.stdout.write(self.style.WARNING(
                            f"Failed to generate QR code for {community.name} (attempt {attempt+1})"
                        ))
                        # Small delay before retry
                        time.sleep(0.5)
                except Exception as e:
                    self.stdout.write(self.style.ERROR(
                        f"Error generating QR code for {community.name}: {str(e)}"
                    ))
                    # Continue to next attempt

            if not success:
                fail_count += 1

        # Print summary
        self.stdout.write("\nSummary:")
        self.stdout.write(self.style.SUCCESS(f"Successfully generated QR codes for {success_count} communities"))
        if fail_count > 0:
            self.stdout.write(self.style.ERROR(f"Failed to generate QR codes for {fail_count} communities"))
        else:
            self.stdout.write(self.style.SUCCESS("All QR codes were generated successfully"))
