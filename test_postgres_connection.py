"""
Simple script to test PostgreSQL connection.
"""
import psycopg2
import sys

def test_connection(database, user, password, host='localhost', port='5432'):
    """Test connection to PostgreSQL database."""
    print(f"Testing connection to PostgreSQL database '{database}' on {host}:{port}...")
    try:
        # Connect to PostgreSQL
        conn = psycopg2.connect(
            dbname=database,
            user=user,
            password=password,
            host=host,
            port=port
        )
        
        # Create a cursor
        cursor = conn.cursor()
        
        # Execute a simple query
        cursor.execute("SELECT version();")
        
        # Fetch the result
        version = cursor.fetchone()[0]
        print(f"Connection successful!")
        print(f"PostgreSQL version: {version}")
        
        # Close the cursor and connection
        cursor.close()
        conn.close()
        
        return True
    except Exception as e:
        print(f"Connection failed: {e}")
        return False

if __name__ == "__main__":
    # Get connection parameters from command line arguments or use defaults
    database = sys.argv[1] if len(sys.argv) > 1 else 'postgres'
    user = sys.argv[2] if len(sys.argv) > 2 else 'postgres'
    password = sys.argv[3] if len(sys.argv) > 3 else 'M@kerere1'  # Using the password from your local_settings.py
    host = sys.argv[4] if len(sys.argv) > 4 else 'localhost'
    port = sys.argv[5] if len(sys.argv) > 5 else '5432'
    
    # Test connection
    success = test_connection(database, user, password, host, port)
    
    # Exit with appropriate status code
    sys.exit(0 if success else 1)
