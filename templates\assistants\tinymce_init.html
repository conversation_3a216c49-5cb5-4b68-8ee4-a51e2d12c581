{% load static %}
<script src="{% static 'tinymce/tinymce.min.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize TinyMCE for static fields
    if (typeof tinymce !== 'undefined') {
        tinymce.init({
            selector: '#id_description, #id_system_prompt, #id_greeting_message, #id_extra_context',
            height: 300,
            width: 'auto',
            menubar: true,
            skin: 'oxide-dark', // Use dark skin
            content_css: [
                '/static/css/tinymce-dark-theme.css', // Our custom CSS for the content
                'dark' // Use TinyMCE's built-in dark content CSS
            ],
            plugins: 'advlist autolink lists link image charmap print preview anchor ' +
                    'searchreplace visualblocks code fullscreen ' +
                    'insertdatetime media table paste code help wordcount imagetools ' +
                    'emoticons hr pagebreak nonbreaking toc textpattern codesample ' +
                    'quickbars directionality visualchars template save importcss',
            toolbar1: 'formatselect fontselect fontsizeselect styleselect | ' +
                     'bold italic underline strikethrough subscript superscript | forecolor backcolor | ' +
                     'alignleft aligncenter alignright alignjustify | ' +
                     'bullist numlist outdent indent | ltr rtl',
            toolbar2: 'undo redo | cut copy paste pastetext | searchreplace | ' +
                     'link unlink image media table emoticons charmap | ' +
                     'hr pagebreak nonbreaking template | removeformat code | fullscreen preview | help',
            toolbar3: 'insertdatetime | visualchars visualblocks | codesample blockquote cite | save print',
            // Hide HTML elements in the edit interface
            hidden_input: false,
            element_format: 'html',
            entity_encoding: 'raw',
            convert_fonts_to_spans: false,
            verify_html: false,
            visual: false,
            images_upload_url: '{% url "assistants:tinymce-upload" %}',
            file_picker_types: 'image',
            automatic_uploads: true,
            images_reuse_filename: false,
            paste_data_images: true,
            file_picker_callback: function (callback, value, meta) {
                if (meta.filetype === 'image') {
                    var input = document.createElement('input');
                    input.setAttribute('type', 'file');
                    input.setAttribute('accept', 'image/*');
                    input.onchange = function () {
                        var file = this.files[0];
                        var formData = new FormData();
                        formData.append('file', file);

                        // Get CSRF token from cookie
                        function getCookie(name) {
                            let cookieValue = null;
                            if (document.cookie && document.cookie !== '') {
                                const cookies = document.cookie.split(';');
                                for (let i = 0; i < cookies.length; i++) {
                                    const cookie = cookies[i].trim();
                                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                                        break;
                                    }
                                }
                            }
                            return cookieValue;
                        }
                        const csrftoken = getCookie('csrftoken');

                        fetch('{% url "assistants:tinymce-upload" %}', {
                            method: 'POST',
                            body: formData,
                            credentials: 'same-origin',
                            headers: {
                                'X-CSRFToken': csrftoken
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.location) {
                                callback(data.location, { alt: file.name });
                            } else {
                                alert('Image upload failed.');
                            }
                        })
                        .catch(() => alert('Image upload failed.'));
                    };
                    input.click();
                }
            },
            content_style: `
                body { font-family:Helvetica,Arial,sans-serif; font-size:14px; }
                img { max-width: 100%; height: auto; }
                img.img-fluid { max-width: 100%; height: auto; }
                img.float-left { float: left; margin-right: 1rem; margin-bottom: 0.5rem; }
                img.float-right { float: right; margin-left: 1rem; margin-bottom: 0.5rem; }
                img.mx-auto.d-block { display: block; margin-left: auto; margin-right: auto; }
                figure.image { display: inline-block; margin: 0; }
                figure.image figcaption { font-size: 0.8em; color: #555; text-align: center; }
                table { border-collapse: collapse; width: 100%; }
                table td, table th { border: 1px solid #ddd; padding: 8px; }
                blockquote { border-left: 3px solid #ccc; margin-left: 1.5em; padding-left: 1em; }
                pre { background-color: #f5f5f5; padding: 1em; border-radius: 3px; }
                .mce-content-body [data-mce-selected="inline-boundary"] { background-color: #b4d7ff; }
            `,
            statusbar: true,
            image_advtab: true,
            image_title: true,
            // Enable image resizing
            image_dimensions: true,
            image_caption: true,
            image_class_list: [
                {title: 'None', value: ''},
                {title: 'Responsive', value: 'img-fluid'},
                {title: 'Left Aligned', value: 'float-left mr-3'},
                {title: 'Right Aligned', value: 'float-right ml-3'},
                {title: 'Centered', value: 'mx-auto d-block'}
            ],
            // Enable drag and resize of images
            resize_img_proportional: true,
            object_resizing: 'img,table',
            resize: true,
            setup: function (editor) {
                // Create word count warning element
                const wordCountLimit = 500;
                let wordCountWarning = null;

                // Function to check word count and display warning
                function checkWordCount() {
                    const content = editor.getContent({format: 'text'});
                    const wordCount = content.split(/\s+/).filter(Boolean).length;

                    if (!wordCountWarning) {
                        // Create warning element if it doesn't exist
                        wordCountWarning = document.createElement('p');
                        wordCountWarning.className = 'word-count-warning form-text small mt-1';
                        const editorContainer = editor.getContainer();
                        if (editorContainer && editorContainer.parentNode) {
                            editorContainer.parentNode.insertBefore(wordCountWarning, editorContainer.nextSibling);
                        }
                    }

                    // Update warning text
                    wordCountWarning.textContent = `${wordCount}/${wordCountLimit} words`;

                    // Add warning styling if over limit
                    if (wordCount > wordCountLimit) {
                        wordCountWarning.classList.add('text-danger');
                        wordCountWarning.classList.remove('text-muted');
                    } else {
                        wordCountWarning.classList.remove('text-danger');
                        wordCountWarning.classList.add('text-muted');
                    }
                }

                editor.on('change keyup paste', function () {
                    editor.save();
                    // Also update the textarea directly
                    const textarea = document.getElementById(editor.id);
                    if (textarea) {
                        textarea.value = editor.getContent();
                    }

                    // Check word count
                    checkWordCount();
                });

                editor.on('init', function(evt) {
                    console.log(`TinyMCE Static Init Event: Editor #${evt.target.id} initialized successfully.`);
                    // Force the editor to show its content
                    const textarea = document.getElementById(editor.id);
                    if (textarea && textarea.value) {
                        editor.setContent(textarea.value);
                        console.log(`Set content for editor #${editor.id} from textarea value`);
                    }

                    // Initial word count check
                    checkWordCount();
                });

                editor.on('error', function(e) {
                    console.error(`TinyMCE Static Error Event for #${editor.id}:`, e);
                });
            }
        });
    }
});
</script>
