"""
<PERSON><PERSON><PERSON> to fix the NavigationItem migration issue.
This script will:
1. Create the NavigationItem model in the database state
2. Fake the migrations that are causing issues
"""
import os
import sys
import django
from django.core.management import call_command
from django.db import connection

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

def check_migration_status():
    """Check the status of migrations for the assistants app."""
    print("Checking migration status for assistants app...")
    try:
        call_command('showmigrations', 'assistants')
        return True
    except Exception as e:
        print(f"Error checking migration status: {e}")
        return False

def fake_migration(app, migration):
    """Fake a specific migration."""
    print(f"Faking migration {app}.{migration}...")
    try:
        call_command('migrate', app, migration, fake=True)
        print(f"Migration {app}.{migration} faked successfully.")
        return True
    except Exception as e:
        print(f"Error faking migration {app}.{migration}: {e}")
        return False

def create_navigationitem_model():
    """Create the NavigationItem model in the database."""
    print("Creating NavigationItem model in the database...")
    try:
        with connection.cursor() as cursor:
            # Check if the table already exists
            cursor.execute("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.tables
                WHERE table_name = 'assistants_navigationitem'
            );
            """)
            table_exists = cursor.fetchone()[0]

            if table_exists:
                print("Table assistants_navigationitem already exists.")
                return True

            # Create the table
            cursor.execute("""
            CREATE TABLE assistants_navigationitem (
                id SERIAL PRIMARY KEY,
                label VARCHAR(100) NOT NULL,
                unique_id VARCHAR(100) NOT NULL,
                order INTEGER NOT NULL,
                visible BOOLEAN NOT NULL,
                section_type VARCHAR(20) NOT NULL,
                gallery JSONB NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE NOT NULL,
                updated_at TIMESTAMP WITH TIME ZONE NOT NULL,
                assistant_id INTEGER NOT NULL REFERENCES assistants_assistant(id) ON DELETE CASCADE
            );
            """)

            # Create indexes
            cursor.execute("""
            CREATE INDEX assistants_navigationitem_assistant_id ON assistants_navigationitem(assistant_id);
            """)

            # Create unique constraint
            cursor.execute("""
            CREATE UNIQUE INDEX assistants_navigationitem_assistant_id_label_uniq ON assistants_navigationitem(assistant_id, label);
            """)

            print("Table assistants_navigationitem created successfully.")
            return True
    except Exception as e:
        print(f"Error creating NavigationItem model: {e}")
        return False

def fix_migration_record():
    """Fix the migration record in django_migrations table."""
    print("Fixing migration record in django_migrations table...")
    try:
        with connection.cursor() as cursor:
            # Check if the migration is already recorded
            cursor.execute("""
            SELECT EXISTS (
                SELECT 1 FROM django_migrations
                WHERE app = 'assistants' AND name = '0020_navigationitem'
            );
            """)
            migration_exists = cursor.fetchone()[0]

            if migration_exists:
                print("Migration assistants.0020_navigationitem already recorded.")
                return True

            # Insert the migration record
            cursor.execute("""
            INSERT INTO django_migrations (app, name, applied)
            VALUES ('assistants', '0020_navigationitem', NOW());
            """)

            print("Migration record for assistants.0020_navigationitem added successfully.")
            return True
    except Exception as e:
        print(f"Error fixing migration record: {e}")
        return False

def main():
    """Main function to fix the NavigationItem migration issue."""
    print("Starting NavigationItem migration fix...")

    # Check migration status
    check_migration_status()

    # Create the NavigationItem model in the database
    if not create_navigationitem_model():
        print("Failed to create NavigationItem model. Aborting.")
        return

    # Fix the migration record
    if not fix_migration_record():
        print("Failed to fix migration record. Aborting.")
        return

    # Fake the problematic migrations
    migrations_to_fake = [
        ('assistants', '0020_navigationitem'),
        ('assistants', '0021_remove_assistant_nav_config_and_more'),
        ('assistants', '0022_remove_navigationitem_entry_count'),
        ('assistants', '0030_navigationitem_gallery')
    ]

    for app, migration in migrations_to_fake:
        if not fake_migration(app, migration):
            print(f"Failed to fake migration {app}.{migration}. Continuing...")

    # Check migration status again
    check_migration_status()

    print("\nNext steps:")
    print("1. Run 'python manage.py migrate' to apply the remaining migrations")
    print("2. If you still encounter issues, try 'python manage.py migrate --fake-initial'")

if __name__ == "__main__":
    main()
