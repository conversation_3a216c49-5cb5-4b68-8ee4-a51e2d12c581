"""
Cache settings for the company_assistant project.
This module provides cache configurations for different environments.
"""

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Default cache timeout (24 hours in seconds)
CACHE_TIMEOUT = 86400

# Cache configurations for different environments
CACHE_CONFIGS = {
    # Local memory cache (default for development)
    'locmem': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'company-assistant-cache',
        'TIMEOUT': CACHE_TIMEOUT,
    },
    
    # File-based cache (good for cPanel without Redis)
    'file': {
        'BACKEND': 'django.core.cache.backends.filebased.FileBasedCache',
        'LOCATION': os.path.join(BASE_DIR, 'cache'),
        'TIMEOUT': CACHE_TIMEOUT,
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
            'CULL_FREQUENCY': 3,  # Fraction of entries to cull when max is reached
        },
    },
    
    # Redis cache (best for production)
    'redis': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': os.getenv('REDIS_URL', 'redis://127.0.0.1:6379/1'),
        'TIMEOUT': CACHE_TIMEOUT,
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'IGNORE_EXCEPTIONS': True,
        },
    },
    
    # Database cache (alternative for cPanel)
    'db': {
        'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
        'LOCATION': 'django_cache',
        'TIMEOUT': CACHE_TIMEOUT,
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
            'CULL_FREQUENCY': 3,
        },
    },
    
    # Dummy cache (for testing)
    'dummy': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    },
}

# Function to get the appropriate cache configuration
def get_cache_config(cache_type=None):
    """
    Get the appropriate cache configuration based on the environment.
    
    Args:
        cache_type (str): The type of cache to use. If None, will try to determine from environment.
    
    Returns:
        dict: The cache configuration dictionary
    """
    # If cache_type is specified, use that
    if cache_type and cache_type in CACHE_CONFIGS:
        return CACHE_CONFIGS[cache_type]
    
    # Try to determine from environment
    cache_type = os.getenv('CACHE_TYPE', '').lower()
    if cache_type in CACHE_CONFIGS:
        return CACHE_CONFIGS[cache_type]
    
    # Check if Redis is available
    redis_url = os.getenv('REDIS_URL')
    if redis_url:
        return CACHE_CONFIGS['redis']
    
    # Default to file-based cache for cPanel environments
    if os.getenv('CPANEL_ENV') or 'PASSENGER_WSGI' in os.environ:
        return CACHE_CONFIGS['file']
    
    # Default to locmem for development
    return CACHE_CONFIGS['locmem']

# Default cache configuration
DEFAULT_CACHE_CONFIG = get_cache_config()

# Cache configuration for session tokens
SESSION_TOKEN_CACHE_CONFIG = {
    'BACKEND': DEFAULT_CACHE_CONFIG['BACKEND'],
    'LOCATION': DEFAULT_CACHE_CONFIG.get('LOCATION', '') + '_tokens',
    'TIMEOUT': CACHE_TIMEOUT,
    'OPTIONS': DEFAULT_CACHE_CONFIG.get('OPTIONS', {}),
}
