import os
import django
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

def check_table_structure(table_name):
    """Check the structure of a database table."""
    print(f"\nChecking structure of table: {table_name}")
    
    with connection.cursor() as cursor:
        # Get column information
        cursor.execute(f"""
            SELECT column_name, data_type, character_maximum_length, is_nullable
            FROM information_schema.columns
            WHERE table_name = '{table_name}'
            ORDER BY ordinal_position;
        """)
        
        columns = cursor.fetchall()
        
        if not columns:
            print(f"Table '{table_name}' does not exist or has no columns.")
            return
        
        print(f"Found {len(columns)} columns:")
        for col in columns:
            col_name, data_type, max_length, nullable = col
            max_length_str = f"({max_length})" if max_length else ""
            nullable_str = "NULL" if nullable == "YES" else "NOT NULL"
            print(f"  - {col_name}: {data_type}{max_length_str} {nullable_str}")

def check_migration_status():
    """Check the status of Django migrations."""
    print("\nChecking migration status:")
    
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT app, name, applied
            FROM django_migrations
            WHERE app = 'accounts'
            ORDER BY applied;
        """)
        
        migrations = cursor.fetchall()
        
        if not migrations:
            print("No migrations found for the 'accounts' app.")
            return
        
        print(f"Found {len(migrations)} migrations:")
        for migration in migrations:
            app, name, applied = migration
            print(f"  - {app}.{name}: Applied on {applied}")

if __name__ == "__main__":
    # Check the structure of the CompanyInformation table
    check_table_structure("accounts_companyinformation")
    
    # Check migration status
    check_migration_status()
    
    print("\nDatabase check complete.")
