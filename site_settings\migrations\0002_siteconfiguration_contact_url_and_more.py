# Generated by Django 5.2.1 on 2025-05-20 12:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("site_settings", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="siteconfiguration",
            name="contact_url",
            field=models.URLField(
                blank=True,
                help_text="URL for the 'Contact' links in the header and footer. If not set, the default contact page will be used.",
                max_length=255,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="siteconfiguration",
            name="learn_ai_url",
            field=models.URLField(
                blank=True,
                help_text="URL for the 'Learn AI' button on the homepage.",
                max_length=255,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="siteconfiguration",
            name="site_name",
            field=models.CharField(default="24seven Platform", max_length=255),
        ),
    ]
