"""
Basic test script to check if the application is working correctly.
"""

import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import models
from django.contrib.auth.models import User
from accounts.models import Company
from assistants.models import Assistant

def test_basic_models():
    """Test basic model functionality."""
    print("Testing basic model functionality...")

    try:
        # Count users
        user_count = User.objects.count()
        print(f"Found {user_count} users")

        # Count companies
        company_count = Company.objects.count()
        print(f"Found {company_count} companies")

        # Count assistants
        assistant_count = Assistant.objects.count()
        print(f"Found {assistant_count} assistants")

        print("Basic model test completed successfully!")
        return True
    except Exception as e:
        print(f"Error in test_basic_models: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_all_basic_tests():
    """Run all basic tests."""
    print("Running all basic tests...")

    results = []
    results.append(test_basic_models())

    # Return True only if all tests passed
    return all(results)

if __name__ == "__main__":
    run_all_basic_tests()
