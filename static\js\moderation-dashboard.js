/**
 * Moderation Dashboard JavaScript
 * Handles interactive functionality for the community assistant moderation dashboard
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Handle card hover effects
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.classList.add('shadow');
        });
        card.addEventListener('mouseleave', function() {
            this.classList.remove('shadow');
        });
    });

    // Handle form submissions with confirmation
    const confirmForms = document.querySelectorAll('form[data-confirm]');
    confirmForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const confirmMessage = this.getAttribute('data-confirm');
            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return false;
            }
        });
    });

    // Handle dynamic badge colors
    const badges = document.querySelectorAll('.badge');
    badges.forEach(badge => {
        // Get the text content and convert to lowercase for class names
        const text = badge.textContent.trim().toLowerCase().replace(/\s+/g, '-');
        
        // Add appropriate classes if not already present
        if (!badge.classList.contains(`bg-${text}-subtle`)) {
            badge.classList.add(`bg-${text}-subtle`);
        }
        if (!badge.classList.contains(`text-${text}`)) {
            badge.classList.add(`text-${text}`);
        }
    });

    // Handle filter form submissions
    const filterSelects = document.querySelectorAll('select[onchange="this.form.submit()"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });

    // Handle modal data passing
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('show.bs.modal', function(event) {
            // Get the button that triggered the modal
            const button = event.relatedTarget;
            
            // Extract data attributes
            const dataAttributes = button.dataset;
            
            // Find all elements in the modal with data-target attributes
            const targetElements = modal.querySelectorAll('[data-target]');
            
            // For each target element, set its value/content based on the button's data
            targetElements.forEach(element => {
                const targetAttribute = element.getAttribute('data-target');
                if (dataAttributes[targetAttribute]) {
                    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                        element.value = dataAttributes[targetAttribute];
                    } else {
                        element.textContent = dataAttributes[targetAttribute];
                    }
                }
            });
        });
    });

    // Handle tabs with URL hash
    const hash = window.location.hash;
    if (hash) {
        const tabId = hash.replace('#', '');
        const tab = document.querySelector(`a[href="#${tabId}"]`);
        if (tab) {
            const bsTab = new bootstrap.Tab(tab);
            bsTab.show();
        }
    }

    // Update URL hash when tab is shown
    const tabLinks = document.querySelectorAll('a[data-bs-toggle="tab"]');
    tabLinks.forEach(tabLink => {
        tabLink.addEventListener('shown.bs.tab', function(event) {
            const id = event.target.getAttribute('href').replace('#', '');
            window.history.replaceState(null, null, `#${id}`);
        });
    });
});
