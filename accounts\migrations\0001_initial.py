# Generated by Django 5.2.1 on 2025-05-20 05:56

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="RegistrationLink",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "token",
                    models.UUIDField(default=uuid.uuid4, editable=False, unique=True),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Optional date/time when this link expires.",
                        null=True,
                    ),
                ),
                (
                    "max_uses",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Optional limit on the number of times this link can be used.",
                        null=True,
                    ),
                ),
                ("uses_count", models.PositiveIntegerField(default=0, editable=False)),
                ("is_active", models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                (
                    "notes",
                    models.CharField(
                        blank=True,
                        help_text="Optional notes for the creator about this link's purpose.",
                        max_length=255,
                    ),
                ),
                (
                    "qr_code",
                    models.ImageField(
                        blank=True,
                        help_text="QR code for this registration link.",
                        null=True,
                        upload_to="registration_link_qrcodes/",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "Registration Link",
                "verbose_name_plural": "Registration Links",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "avatar",
                    models.ImageField(
                        blank=True,
                        help_text="User profile picture.",
                        null=True,
                        upload_to="user_avatars/",
                    ),
                ),
                (
                    "bio",
                    models.TextField(
                        blank=True, help_text="A short description about the user."
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Company",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("slug", models.SlugField(max_length=200, unique=True)),
                (
                    "entity_type",
                    models.CharField(
                        choices=[("company", "Company"), ("community", "Community")],
                        default="company",
                        help_text="Whether this is a company or a community",
                        max_length=20,
                    ),
                ),
                (
                    "qr_code",
                    models.ImageField(
                        blank=True, null=True, upload_to="company_qrcodes/"
                    ),
                ),
                (
                    "tier",
                    models.CharField(
                        choices=[
                            ("Gold", "Gold"),
                            ("Silver", "Silver"),
                            ("Bronze", "Bronze"),
                            ("Standard", "Standard"),
                        ],
                        db_index=True,
                        default="Standard",
                        help_text="Directory display tier for this company.",
                        max_length=10,
                    ),
                ),
                (
                    "is_featured",
                    models.BooleanField(
                        db_index=True,
                        default=False,
                        help_text="Mark this company to appear in the featured section on the directory.",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        db_index=True,
                        default=False,
                        help_text="Whether the company or community is approved and active on the platform.",
                    ),
                ),
                (
                    "requested_tier",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Gold", "Gold"),
                            ("Silver", "Silver"),
                            ("Bronze", "Bronze"),
                            ("Standard", "Standard"),
                        ],
                        help_text="Tier requested by the company owner, pending superadmin approval.",
                        max_length=10,
                        null=True,
                    ),
                ),
                (
                    "tier_change_pending",
                    models.BooleanField(
                        db_index=True,
                        default=False,
                        help_text="Indicates if a tier change request is pending approval.",
                    ),
                ),
                (
                    "tier_expiry_date",
                    models.DateTimeField(
                        blank=True,
                        help_text="Date and time when the current tier expires (if applicable).",
                        null=True,
                    ),
                ),
                (
                    "requested_tier_duration",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("monthly", "Monthly"),
                            ("quarterly", "Quarterly"),
                            ("annually", "Annually"),
                        ],
                        help_text="Duration requested for the tier upgrade.",
                        max_length=10,
                        null=True,
                    ),
                ),
                (
                    "featured_request_pending",
                    models.BooleanField(
                        db_index=True,
                        default=False,
                        help_text="Indicates if a request to feature this company is pending approval.",
                    ),
                ),
                (
                    "featured_expiry_date",
                    models.DateTimeField(
                        blank=True,
                        help_text="Date and time when the featured status expires (if applicable).",
                        null=True,
                    ),
                ),
                (
                    "requested_featured_duration",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("monthly", "Monthly"),
                            ("quarterly", "Quarterly"),
                            ("annually", "Annually"),
                        ],
                        help_text="Duration requested for the featured status.",
                        max_length=10,
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="owned_companies",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Companies",
                "permissions": [
                    ("change_company_settings", "Can change company settings"),
                    ("manage_billing", "Can manage billing"),
                    ("delete_company_object", "Can delete company"),
                    ("manage_directory_listing", "Can manage directory listing"),
                    ("manage_members", "Can add/remove/change roles of members"),
                    (
                        "manage_invites_links",
                        "Can manage invitations and registration links",
                    ),
                    ("view_company_activity", "Can view company activity log"),
                    ("add_assistantfolder", "Can add assistant folders to the company"),
                    (
                        "change_assistantfolder",
                        "Can change assistant folders within the company",
                    ),
                    (
                        "delete_assistantfolder",
                        "Can delete assistant folders within the company",
                    ),
                    (
                        "view_assistantfolder",
                        "Can view assistant folders within the company",
                    ),
                    (
                        "manage_folder_access",
                        "Can manage user access to specific folders",
                    ),
                    ("change_membership_role", "Can change member roles"),
                    (
                        "manage_company_assistants",
                        "Can manage assistants within the company",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="ActivityLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "activity_type",
                    models.CharField(
                        choices=[
                            ("user_joined", "User Joined"),
                            ("invitation_sent", "Invitation Sent"),
                            ("invitation_accepted", "Invitation Accepted"),
                            ("invitation_cancelled", "Invitation Cancelled"),
                            ("invitation_resent", "Invitation Resent"),
                            ("member_removed", "Member Removed"),
                            ("role_changed", "Member Role Changed"),
                            ("folder_access_changed", "Folder Access Changed"),
                            ("settings_changed", "Settings Changed"),
                            ("assistant_created", "Assistant Created"),
                            ("assistant_updated", "Assistant Updated"),
                            ("assistant_deleted", "Assistant Deleted"),
                            ("folder_created", "Folder Created"),
                            ("folder_updated", "Folder Updated"),
                            ("folder_deleted", "Folder Deleted"),
                        ],
                        max_length=50,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="activities",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="activity_logs",
                        to="accounts.company",
                    ),
                ),
            ],
            options={
                "verbose_name": "Activity Log",
                "verbose_name_plural": "Activity Logs",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="CompanyInformation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("mission", models.TextField(blank=True)),
                ("description", models.TextField(blank=True)),
                ("website", models.URLField(blank=True)),
                ("contact_email", models.EmailField(blank=True, max_length=254)),
                ("contact_phone", models.CharField(blank=True, max_length=50)),
                ("timezone", models.CharField(default="UTC", max_length=50)),
                ("language", models.CharField(default="en", max_length=10)),
                ("address_line1", models.CharField(blank=True, max_length=255)),
                ("address_line2", models.CharField(blank=True, max_length=255)),
                ("city", models.CharField(blank=True, max_length=100)),
                ("postal_code", models.CharField(blank=True, max_length=20)),
                ("country", models.CharField(blank=True, max_length=100)),
                (
                    "logo",
                    models.ImageField(
                        blank=True, null=True, upload_to="company_logos/"
                    ),
                ),
                ("industry", models.CharField(blank=True, max_length=100)),
                ("size", models.CharField(blank=True, max_length=50)),
                ("founded", models.PositiveIntegerField(blank=True, null=True)),
                ("linkedin", models.URLField(blank=True)),
                ("twitter", models.URLField(blank=True)),
                ("facebook", models.URLField(blank=True)),
                ("custom_domain", models.CharField(blank=True, max_length=255)),
                ("list_in_directory", models.BooleanField(default=True)),
                (
                    "company",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="info",
                        to="accounts.company",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="CompanyInvitation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.EmailField(max_length=254)),
                ("token", models.CharField(max_length=100, unique=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("accepted", "Accepted"),
                            ("declined", "Declined"),
                            ("expired", "Expired"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("invited_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField()),
                ("accepted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="invitations",
                        to="accounts.company",
                    ),
                ),
                (
                    "invited_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="sent_invitations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-invited_at"],
            },
        ),
        migrations.CreateModel(
            name="Membership",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date_joined", models.DateTimeField(auto_now_add=True)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="memberships",
                        to="accounts.company",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="memberships",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["company__name", "user__username"],
            },
        ),
    ]
