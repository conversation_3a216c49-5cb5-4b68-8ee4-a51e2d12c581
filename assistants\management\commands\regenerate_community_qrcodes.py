from django.core.management.base import BaseCommand
from django.urls import reverse
from assistants.models import Assistant
from utils.qr_generator import generate_model_qr_code
import time
import logging
import os

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Regenerate QR codes for all community assistants to ensure they have the "A" logo'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force regeneration of QR codes even if they already exist',
        )

    def handle(self, **options):
        force = options.get('force', False)

        # Build the query
        query = {'assistant_type': 'community'}
        if not force:
            # Only process assistants with existing QR codes
            query['qr_code__isnull'] = False

        # Get the community assistants
        assistants = Assistant.objects.filter(**query)
        total = assistants.count()

        self.stdout.write(f"Found {total} community assistants that need QR code regeneration")

        # Print some debug info
        for assistant in assistants[:3]:  # Just show first 3 for brevity
            self.stdout.write(f"Debug - Assistant: {assistant.name} (ID: {assistant.id}, Type: {assistant.assistant_type})")
            if assistant.qr_code:
                self.stdout.write(f"Debug - QR code: {assistant.qr_code.url}")

        # Process each assistant
        success_count = 0
        fail_count = 0

        for i, assistant in enumerate(assistants, 1):
            self.stdout.write(f"Processing {i}/{total}: {assistant.name} (ID: {assistant.id})")

            # Multiple attempts for reliability
            max_attempts = 3
            success = False

            for attempt in range(max_attempts):
                try:
                    # Use the assistant's public chat URL for the QR code
                    url_path = reverse('assistants:assistant_chat', kwargs={'slug': assistant.slug})

                    # Generate QR code with the "A" logo
                    success = generate_model_qr_code(assistant, url_path, field_name='qr_code', letter="A")

                    if success:
                        # Save the assistant with the new QR code
                        assistant.save(update_fields=['qr_code'])
                        self.stdout.write(self.style.SUCCESS(
                            f"Successfully regenerated QR code for {assistant.name} (attempt {attempt+1})"
                        ))
                        success_count += 1
                        break
                    else:
                        self.stdout.write(self.style.WARNING(
                            f"Failed to regenerate QR code for {assistant.name} (attempt {attempt+1})"
                        ))
                        # Small delay before retry
                        time.sleep(0.5)
                except Exception as e:
                    self.stdout.write(self.style.ERROR(
                        f"Error regenerating QR code for {assistant.name}: {str(e)}"
                    ))
                    # Continue to next attempt

            if not success:
                fail_count += 1

        self.stdout.write(f"\nCommunity Assistant QR Code Summary:")
        self.stdout.write(f"Successfully regenerated QR codes for {success_count} community assistants")
        if fail_count > 0:
            self.stdout.write(self.style.WARNING(
                f"Failed to regenerate QR codes for {fail_count} community assistants"
            ))
