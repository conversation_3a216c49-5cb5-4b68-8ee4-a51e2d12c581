from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate # Import authenticate
from django.contrib.auth.decorators import login_required
from django.contrib.auth.views import LoginView # Import LoginView
from django.contrib import messages
from django.utils.crypto import get_random_string
from django.utils import timezone
from datetime import timedelta # Import timedelta
from django.urls import reverse, NoReverseMatch, reverse_lazy # Import reverse_lazy
from django.http import JsonResponse, HttpResponseForbidden, HttpResponse, Http404
from django.core.exceptions import PermissionDenied, ObjectDoesNotExist # Import ObjectDoesNotExist
from django.db.models import Q
from django.views.decorators.http import require_POST, require_GET
from django.contrib.sites.models import Site
from django.contrib.sessions.models import Session # Import Session model
from django.db.models import Avg, Sum, Count, Q # Import aggregation functions and Q
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger # Import Paginator
import qrcode # Import qrcode library
from io import BytesIO # Import BytesIO for image generation
import datetime # Import datetime
from collections import defaultdict # Import defaultdict for grouping
from itertools import groupby # Import groupby
from django.db.models import F # Import F for ordering
from django.contrib.auth import get_user_model # Import get_user_model
# Removed PostgreSQL specific search tools import

from .forms import (
    CustomUserCreationForm, CompanyCreationForm,
    CompanySettingsForm, CompanyDirectorySettingsForm, # Added CompanyDirectorySettingsForm
    TeamInvitationForm, UserProfileForm, # Replaced UserSettingsForm with UserProfileForm
    RegistrationLinkForm
)
# TODO: Define a UserPreferencesForm in forms.py if needed
from django.contrib.auth.models import Permission, User, Group # Import Permission, User, and Group
from guardian.shortcuts import assign_perm # Import assign_perm
# Corrected import: Removed Role
from .models import Company, CompanyInvitation as TeamInvitation, ActivityLog, Membership, RegistrationLink, CompanyInformation
from assistants.models import Assistant, Interaction, AssistantFolder # Import Assistant model and AssistantFolder
from directory.models import SavedItem, CompanyListing # Import SavedItem AND CompanyListing
from .utils import generate_company_qr, generate_qr_code # Import QR code generation functions


# Custom Login View to always redirect to home
class CustomLoginView(LoginView):
    def get_success_url(self):
        # Always redirect to the 'home' page regardless of 'next' parameter
        return reverse_lazy('home')

# Permission lists (Consider moving to a constants file)
ADMIN_PERMS_COMPANY = [
    'accounts.change_company_settings', 'accounts.manage_billing',
    'accounts.manage_directory_listing', 'accounts.manage_members',
    'accounts.manage_invites_links', 'accounts.view_company_activity',
    'accounts.manage_folder_access', 'accounts.change_membership_role',
    'assistants.add_assistantfolder', 'assistants.change_assistantfolder',
    'assistants.delete_assistantfolder', 'assistants.view_assistantfolder',
    'assistants.add_assistant', 'assistants.change_assistant',
    'assistants.delete_assistant', 'assistants.view_assistant_usage',
    'assistants.view_assistant_analytics', 'assistants.access_all_private',
    'assistants.create_assistant_token',
    'accounts.view_company', # Added permission to view company details/team page
]
MEMBER_PERMS_COMPANY = [
    'accounts.view_company_activity', 'assistants.view_assistantfolder',
    'assistants.add_assistant', 'assistants.change_assistant',
    'assistants.delete_assistant', 'assistants.view_assistant_usage',
]
VIEWER_PERMS_COMPANY = [
    # 'accounts.view_company_activity', # Removed - Viewers should not see dashboard/activity
    'assistants.view_assistantfolder',
    'assistants.view_assistant', # Added - Viewers should be able to view assistants
    'assistants.view_assistant_usage',
    'assistants.view_assistant_analytics',
]
FOLDER_ACCESS_PERMS = [ # Permissions granted on specific folders
    'assistants.view_assistantfolder', 'assistants.change_assistantfolder',
    'assistants.view_assistant', 'assistants.change_assistant', 'assistants.delete_assistant',
]


def register(request):
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            # Explicitly set the backend attribute before logging in
            user.backend = 'django.contrib.auth.backends.ModelBackend'
            login(request, user)
            token = request.session.get('registration_link_token')
            if token:
                try:
                    # Use select_related('company', 'intended_group') now
                    link = RegistrationLink.objects.select_related('company', 'intended_group').get(token=token)

                    if link.is_valid:
                        company = link.company
                        if not Membership.objects.filter(user=user, company=company).exists():
                            # Create membership first
                            Membership.objects.create(user=user, company=company)

                            # --- Assign Group and Permissions based on link.intended_group ---
                            intended_group = link.intended_group
                            if intended_group:
                                # Assign the user to the intended group
                                user.groups.add(intended_group)
                                print(f"[Register View] Added user {user.username} to group '{intended_group.name}' for company {company.name}")

                                # Assign object-level permissions based on the group
                                perms_to_assign = []
                                if intended_group.name == 'Admin': perms_to_assign = ADMIN_PERMS_COMPANY
                                elif intended_group.name == 'Member': perms_to_assign = MEMBER_PERMS_COMPANY
                                elif intended_group.name == 'Viewer': perms_to_assign = VIEWER_PERMS_COMPANY
                                elif intended_group.name == 'Company Guests': perms_to_assign = VIEWER_PERMS_COMPANY # Treat Company Guests like Viewers for permissions

                                print(f"[Register View] Assigning {len(perms_to_assign)} company permissions to {user.username} for company {company.name} based on group '{intended_group.name}'")
                                for perm_codename in perms_to_assign:
                                    try: assign_perm(perm_codename, user, company)
                                    except Permission.DoesNotExist: print(f"  Warning: Company Permission '{perm_codename}' not found. Skipping.")
                                    except Exception as e: print(f"  Error assigning company perm '{perm_codename}': {e}")

                                # --- Assign Folder Permissions based on link.accessible_folders ---
                                LINK_FOLDER_PERMS = ['assistants.view_assistantfolder', 'assistants.view_assistant']
                                selected_folders = link.accessible_folders.all()
                                if selected_folders:
                                    print(f"[Register View] Assigning folder permissions for {len(selected_folders)} folders to {user.username}")
                                    for folder in selected_folders:
                                        for perm_codename in LINK_FOLDER_PERMS:
                                            try:
                                                assign_perm(perm_codename, user, folder)
                                                print(f"  Assigned '{perm_codename}' for folder '{folder.name}'")
                                            except Permission.DoesNotExist: print(f"  Warning: Folder Permission '{perm_codename}' not found. Skipping.")
                                            except Exception as e: print(f"  Error assigning folder perm '{perm_codename}' for folder '{folder.name}': {e}")
                                # --- End Folder Permission Logic ---

                                # Log activity with role name
                                ActivityLog.objects.create(
                                    company=company, user=user,
                                    activity_type=ActivityLog.TYPE_USER_JOINED,
                                    description=f"{user.username} joined via registration link as '{intended_group.name}'."
                                )
                                messages.success(request, f"You have successfully joined {company.name} as a {intended_group.name}!")
                            else:
                                # Fallback if no group is set on the link
                                ActivityLog.objects.create(
                                    company=company, user=user,
                                    activity_type=ActivityLog.TYPE_USER_JOINED,
                                    description=f"{user.username} joined via registration link (no specific role assigned)."
                                )
                                messages.warning(request, f"You have joined {company.name}, but no specific role was assigned via the link.")

                            # Increment link use after processing
                            link.increment_use()
                            request.session['active_company_id'] = str(company.id)
                        del request.session['registration_link_token']
                        return redirect('home') # Changed from 'accounts:dashboard'
                    else:
                         messages.warning(request, "The registration link used is no longer valid.")
                         if 'registration_link_token' in request.session:
                             del request.session['registration_link_token']
                except RegistrationLink.DoesNotExist:
                    messages.error(request, "Error processing registration link. Please contact support.")
                    if 'registration_link_token' in request.session:
                        del request.session['registration_link_token']
            return redirect('accounts:company_create')
    else:
        form = CustomUserCreationForm()
    return render(request, 'accounts/register.html', {'form': form})

@login_required
def dashboard(request):
    user = request.user
    active_company_id = request.session.get('active_company_id')
    company = None
    if active_company_id:
        try:
            company = Company.objects.filter(
                Q(pk=active_company_id, owner=user) |
                Q(pk=active_company_id, memberships__user=user)
            ).distinct().get()
        except Company.DoesNotExist:
            if 'active_company_id' in request.session: del request.session['active_company_id']
            company = None
    if not company:
        company = user.owned_companies.first()
        if not company:
            first_membership = user.memberships.select_related('company').first()
            if first_membership:
                company = first_membership.company
    if not company:
        return redirect('accounts:company_create')
    if str(company.id) != active_company_id:
     request.session['active_company_id'] = str(company.id)

    # Check if user has permission to view the dashboard (Owner or someone with activity view permission)
    if company.owner != request.user and not request.user.has_perm('accounts.view_company_activity', company):
         raise PermissionDenied("You do not have permission to view this dashboard.")

    period = request.GET.get('period', 'all')
    now = timezone.now()
    start_date = None
    if period == 'day': start_date = now - timedelta(days=1)
    elif period == 'week': start_date = now - timedelta(weeks=1)
    elif period == 'month': start_date = now - timedelta(days=30)

    assistant_interactions_qs = Interaction.objects.filter(assistant__company=company)
    if start_date:
        assistant_interactions_qs = assistant_interactions_qs.filter(created_at__gte=start_date)
    aggregated_analytics = assistant_interactions_qs.aggregate(
        total_interactions=Count('id'), average_rating=Avg('rating'), total_tokens=Sum('token_count')
    )
    dashboard_analytics = {
        'total_assistant_interactions': aggregated_analytics.get('total_interactions') or 0,
        'average_assistant_rating': aggregated_analytics.get('average_rating'),
        'total_assistant_tokens': aggregated_analytics.get('total_tokens') or 0,
    }
    # Get community assistants if this is a community entity
    community_assistants = []
    if hasattr(company, 'entity_type') and company.entity_type == 'community':
        from assistants.models import Assistant
        community_assistants = Assistant.objects.filter(
            company=company,
            assistant_type=Assistant.TYPE_COMMUNITY,
            is_active=True
        )

    context = {
        'company': company,
        'active_assistants': company.assistants.filter(is_active=True).count(),
        'total_activity_log_entries': ActivityLog.objects.filter(company=company).count(),
        'recent_activity': ActivityLog.objects.filter(company=company).order_by('-created_at')[:10],
        'company_qr_code_url': company.qr_code.url if company.qr_code else None,
        'dashboard_analytics': dashboard_analytics,
        'selected_period': period,
        'active_company': company,
        'pending_invitations': TeamInvitation.objects.filter(company=company, status=TeamInvitation.STATUS_PENDING).count(),
        'total_content': 0, # Placeholder
        'active_members': company.memberships.filter(user__is_active=True).count() if hasattr(company, 'memberships') else 0,
        'total_members': company.memberships.count() if hasattr(company, 'memberships') else 0,
        'community_assistants': community_assistants,
    }
    can_manage_directory_settings = user.has_perm('accounts.change_company_settings', company) or user.is_superuser
    context['can_manage_directory_settings'] = can_manage_directory_settings

    # Get recent contexts and flagged questions for community dashboard
    if hasattr(company, 'entity_type') and company.entity_type == 'community' and community_assistants:
        from assistants.models import CommunityContext, FlaggedQuestion
        from assistants.forms import SimpleCommunityContextForm
        # Get recent contexts
        recent_contexts = CommunityContext.objects.filter(
            assistant=community_assistants[0],
            is_active=True
        ).order_by('-created_at')[:5]
        context['recent_contexts'] = recent_contexts
        context['total_contexts'] = CommunityContext.objects.filter(
            assistant=community_assistants[0],
            is_active=True
        ).count()

        # Get flagged questions
        flagged_questions = FlaggedQuestion.objects.filter(
            assistant=community_assistants[0],
            is_resolved=False
        ).order_by('-created_at')[:5]
        context['flagged_questions'] = flagged_questions

        # Add the simplified context form
        context['context_form'] = SimpleCommunityContextForm()

        # Use community dashboard template for community entities
        return render(request, 'accounts/community_dashboard.html', context)

    # Use regular dashboard template for company entities
    return render(request, 'accounts/dashboard.html', context)

@login_required
def company_create(request):
    # Get entity type from query parameter
    entity_type = request.GET.get('type', 'company')
    if entity_type not in ['company', 'community']:
        entity_type = 'company'  # Default to company if invalid type

    print(f"[DEBUG] Company Create View - Method: {request.method}, Entity Type: {entity_type}")

    if request.method == 'POST':
        print(f"[DEBUG] POST Data: {request.POST}")

        # Get the entity type from the form data
        form_entity_type = request.POST.get('entity_type', entity_type)
        print(f"[DEBUG] Form entity type: {form_entity_type}")

        # Create form with POST data and user
        form = CompanyCreationForm(request.POST, user=request.user)
        print(f"[DEBUG] Form is bound: {form.is_bound}")

        # Check if the form is valid
        if form.is_valid():
            try:
                print(f"[DEBUG] Form is valid. Cleaned data: {form.cleaned_data}")

                # Save the company
                company = form.save()
                request.session['active_company_id'] = str(company.id)
                print(f"[DEBUG] Company created successfully: {company.name} (ID: {company.id})")

                # Ensure company information is properly initialized
                company_info = CompanyInformation.objects.get(company=company)
                print(f"[DEBUG] Company info: Contact Phone='{company_info.contact_phone}', Contact Email='{company_info.contact_email}', Industry='{company_info.industry}'")

                # Store company information in session for persistence
                # Store all relevant fields for better synchronization
                request.session['company_mission'] = company_info.mission
                request.session['company_description'] = company_info.description
                request.session['company_website'] = company_info.website
                request.session['company_contact_phone'] = company_info.contact_phone
                request.session['company_contact_email'] = company_info.contact_email
                request.session['company_industry'] = company_info.industry
                request.session['company_size'] = company_info.size
                request.session['company_timezone'] = company_info.timezone
                request.session['company_language'] = company_info.language
                request.session['company_founded'] = company_info.founded

                # Store categories if available
                from directory.models import CompanyListing
                try:
                    listing = CompanyListing.objects.get(company=company)
                    categories = listing.categories.all()
                    categories_text = ', '.join([category.name for category in categories])
                    request.session['company_categories'] = categories_text
                except CompanyListing.DoesNotExist:
                    pass

                # If this is a community entity, automatically create a community assistant
                if company.entity_type == 'community':
                    from assistants.models import Assistant
                    from django.utils.text import slugify

                    try:
                        # Create a community assistant
                        assistant = Assistant.objects.create(
                            name=f"{company.name} Assistant",
                            slug=slugify(f"{company.name}-assistant"),
                            description=company.info.mission if hasattr(company, 'info') and company.info else '',
                            assistant_type=Assistant.TYPE_COMMUNITY,
                            company=company,
                            created_by=request.user,
                            system_prompt=f"You are a community assistant for {company.name}. You use community-contributed context to provide accurate answers."
                        )
                        print(f"[DEBUG] Community assistant created: {assistant.name}")
                    except Exception as assistant_error:
                        print(f"[ERROR] Error creating community assistant: {str(assistant_error)}")
                        # Continue even if assistant creation fails - we can create it later

                    # Add success message for community creation
                    messages.success(request, f"Your community '{company.name}' has been created and is pending approval. You'll be notified once it's approved.")
                else:
                    # Add success message for company creation
                    messages.success(request, f"Your company '{company.name}' has been created and is pending approval. You'll be notified once it's approved.")

                # Redirect to home page
                return redirect('home')

            except Exception as e:
                # Log the error
                print(f"[ERROR] Exception during company creation: {str(e)}")
                # Add error message
                messages.error(request, f"An error occurred while creating your {form_entity_type}. Please try again.")
        else:
            # Log form validation errors
            print(f"[DEBUG] Form is invalid. Errors: {form.errors}")
            print(f"[DEBUG] Non-field errors: {form.non_field_errors()}")

            # Debug specific field errors
            for field_name, field_errors in form.errors.items():
                print(f"[DEBUG] Field '{field_name}' errors: {field_errors}")

            # Add specific error messages based on validation errors
            if 'contact_phone' in form.errors:
                messages.error(request, "Please provide a valid contact phone number.")
            elif 'name' in form.errors and 'A company with this name already exists.' in str(form.errors['name']):
                messages.error(request, "A company with this name already exists. Please choose a different name.")
            elif 'timezone' in form.errors:
                messages.error(request, "Please select a timezone.")
            elif 'language' in form.errors:
                messages.error(request, "Please select a language.")
            else:
                entity_display = "community" if form_entity_type == "community" else "company"
                messages.error(request, f"Please correct the errors in the form before creating your {entity_display}.")
    else:
        # Initialize form with entity_type from query parameter and default values
        initial_data = {
            'entity_type': entity_type,
            'timezone': 'UTC',
            'language': 'en',
        }
        form = CompanyCreationForm(initial=initial_data, user=request.user)

        # Set the categories queryset to be ordered by name
        from directory.models import CompanyCategory
        categories = CompanyCategory.objects.all().order_by('name')
        form.fields['categories'].queryset = categories

        print(f"[DEBUG] GET request - initialized form with entity_type: {entity_type}")

    return render(request, 'accounts/company_create.html', {'form': form})

@login_required
def company_detail(request, company_id):
    # Moved debug print earlier
    print(f"DEBUG: Entering company_detail view for company {company_id}")
    company = get_object_or_404(Company, id=company_id)
    print(f"DEBUG: Fetched company: {company.name}") # DEBUG

    if not request.user.has_perm('accounts.view_company', company) and request.user != company.owner:
         raise PermissionDenied("You do not have permission to view this company.")

    # Check if company is favorited by the current user
    is_favorited = False
    if request.user.is_authenticated:
        is_favorited = SavedItem.objects.filter(user=request.user, company=company, item_type='company').exists()

    # Get or create company listing for ratings
    from directory.models import CompanyListing
    company_listing, created = CompanyListing.objects.get_or_create(
        company=company,
        defaults={
            'is_listed': True,
            'short_description': company.info.description if hasattr(company, 'info') and company.info else '',
            'avg_rating': 0.0,
            'total_ratings': 0,
            'tags': [],
            'categories': []
        }
    )

    # --- Fetch and Sort Folders ---
    # Fetch folders and order them by the 'order' field, then by 'name'
    folders_qs = company.assistant_folders.all().order_by('order', 'name')
    print(f"DEBUG: Fetched folders sorted by order: {folders_qs}") # DEBUG

    # --- Fetch Public Assistants ---
    public_assistants_qs = company.assistants.filter(is_public=True, is_active=True).select_related('folder')

    # --- Apply Folder Filter (if any) ---
    folder_id_filter = request.GET.get('folder_id')
    filtered_assistants_qs = public_assistants_qs # Start with all public
    target_folder_obj = None
    if folder_id_filter:
        if folder_id_filter == 'unassigned':
            filtered_assistants_qs = filtered_assistants_qs.filter(folder__isnull=True)
        else:
            try:
                # Ensure the requested folder belongs to the current company
                target_folder_obj = get_object_or_404(AssistantFolder, pk=int(folder_id_filter), company=company)
                filtered_assistants_qs = filtered_assistants_qs.filter(folder=target_folder_obj)
            except (ValueError, TypeError, AssistantFolder.DoesNotExist):
                folder_id_filter = None # Reset filter state if invalid
                pass # Keep filtered_assistants_qs as is (all public)

    # --- Group Assistants Manually Based on Sorted Folders ---
    # Order assistants primarily by name for consistency within groups
    final_assistants_list = list(filtered_assistants_qs.order_by('name'))
    print(f"DEBUG: Final list of public assistants count: {len(final_assistants_list)}") # DEBUG

    grouped_assistants_list = []

    # 1. Get Assistants for each folder (in the ASCENDING sorted order)
    if not folder_id_filter or folder_id_filter == 'unassigned': # Show all folders if no filter or unassigned
        for folder in folders_qs:
            folder_assistants = [a for a in final_assistants_list if a.folder_id == folder.id]
            if folder_assistants:
                grouped_assistants_list.append((folder, folder_assistants))
    elif target_folder_obj: # Only show the selected folder
         folder_assistants = [a for a in final_assistants_list if a.folder_id == target_folder_obj.id]
         if folder_assistants:
             grouped_assistants_list.append((target_folder_obj, folder_assistants))

    # 2. Get Unassigned Assistants and add them LAST
    if not target_folder_obj or folder_id_filter == 'unassigned':
        unassigned_assistants = [a for a in final_assistants_list if a.folder is None]
        if unassigned_assistants:
            grouped_assistants_list.append((None, unassigned_assistants))

    print(f"DEBUG: Grouped assistants list length: {len(grouped_assistants_list)}") # DEBUG

    context = {
        'company': company,
        'activities': ActivityLog.objects.filter(company=company).order_by('-created_at')[:50],
        'is_favorited': is_favorited,
        'folders': folders_qs, # Pass sorted folders for filter links
        'grouped_assistants_list': grouped_assistants_list, # Pass correctly grouped/sorted public assistants
        'selected_folder_id': folder_id_filter, # Pass selected folder ID (string or None)
        'active_conversations': 0, # Placeholder
        'total_content': 0, # Placeholder - replace with actual calculation if needed
        'company_listing': company_listing, # Pass company listing for ratings
    }
    print(f"DEBUG: company_detail context prepared:") # DEBUG
    print(f"  Folders: {context['folders']}") # DEBUG
    print(f"  Selected Folder ID: {context['selected_folder_id']}") # DEBUG
    return render(request, 'accounts/company_detail.html', context)


@login_required
def company_settings(request, company_id):
    company = get_object_or_404(Company, id=company_id)
    # Prioritize owner check: If user is owner, grant access. Otherwise, check permission.
    if request.user != company.owner and not request.user.has_perm('accounts.change_company_settings', company):
        raise PermissionDenied("You do not have permission to edit these settings.")

    # Redirect to community settings if this is a community entity
    if hasattr(company, 'entity_type') and company.entity_type == 'community':
        return redirect('accounts:community_settings', company_id=company_id)

    # Get or create company information
    company_info, created_info = CompanyInformation.objects.get_or_create(company=company)

    # Debug information about company_info
    print(f"DEBUG - Company Info Object: {company_info}")
    print(f"DEBUG - Company Info Fields: Mission='{company_info.mission}', Website='{company_info.website}', Industry='{company_info.industry}', Size='{company_info.size}', Founded='{company_info.founded}'")

    # Always initialize company information with default values if any field is empty
    updated = False

    if not company_info.mission:
        company_info.mission = "We want to help people and businesses do more with less effort by using smart AI assistants that learn and adapt to your needs."
        updated = True

    if not company_info.website:
        company_info.website = "https://24seven.com"
        updated = True

    if not company_info.industry:
        company_info.industry = "Technology"
        updated = True

    if not company_info.size:
        company_info.size = "10-50"
        updated = True

    if not company_info.founded:
        company_info.founded = 2020
        updated = True

    # Ensure all fields are properly set from session if available
    # Contact information
    if not company_info.contact_phone:
        contact_phone = request.session.get('company_contact_phone')
        if contact_phone:
            company_info.contact_phone = contact_phone
            updated = True

    if not company_info.contact_email:
        contact_email = request.session.get('company_contact_email')
        if contact_email:
            company_info.contact_email = contact_email
            updated = True

    # Basic information
    if not company_info.mission:
        mission = request.session.get('company_mission')
        if mission:
            company_info.mission = mission
            updated = True

    if not company_info.description:
        description = request.session.get('company_description')
        if description:
            company_info.description = description
            updated = True

    if not company_info.website:
        website = request.session.get('company_website')
        if website:
            company_info.website = website
            updated = True

    if not company_info.industry:
        industry = request.session.get('company_industry')
        if industry:
            company_info.industry = industry
            updated = True

    if not company_info.size:
        size = request.session.get('company_size')
        if size:
            company_info.size = size
            updated = True

    # Settings
    if not company_info.timezone:
        timezone_value = request.session.get('company_timezone')
        if timezone_value:
            company_info.timezone = timezone_value
            updated = True

    if not company_info.language:
        language = request.session.get('company_language')
        if language:
            company_info.language = language
            updated = True

    if not company_info.founded:
        founded = request.session.get('company_founded')
        if founded:
            company_info.founded = founded
            updated = True

    if updated:
        company_info.save()
        print(f"DEBUG - Updated company info with default values: {company_info.__dict__}")

    listing, created_listing = CompanyListing.objects.get_or_create(company=company) # Assuming CompanyListing exists

    if request.method == 'POST':
        current_year = datetime.date.today().year
        form = CompanySettingsForm(request.POST, request.FILES, instance=company_info, current_year=current_year)
        dir_form = CompanyDirectorySettingsForm(request.POST, instance=company) # Uses new request fields
        if form.is_valid() and dir_form.is_valid():
            # Print form data before saving
            print(f"DEBUG - Form data before save: {form.cleaned_data}")

            company_info = form.save()

            # Print company info after saving
            print(f"DEBUG - Company info after save: Mission='{company_info.mission}', Website='{company_info.website}', Industry='{company_info.industry}'")

            # Save company information to session for persistence
            request.session['company_mission'] = company_info.mission
            request.session['company_website'] = company_info.website
            request.session['company_industry'] = company_info.industry

            # Process categories using utility function
            from .utils import process_categories
            process_categories(request, company, form)

            # Print session data
            print(f"DEBUG - Session data: Mission='{request.session.get('company_mission')}', Website='{request.session.get('company_website')}', Industry='{request.session.get('company_industry')}'")

            # Explicitly handle list_in_directory field to ensure it's properly saved
            list_in_directory_value = form.cleaned_data.get('list_in_directory')
            print(f"--- DEBUG: list_in_directory value in form.cleaned_data: {list_in_directory_value}")
            company_info.list_in_directory = list_in_directory_value
            company_info.save()

            # Verify the data was saved to the database
            saved_info = CompanyInformation.objects.get(company=company)
            print(f"DEBUG - Saved to database: Mission='{saved_info.mission}', Website='{saved_info.website}', Industry='{saved_info.industry}'")

            # Get submitted values from the *new* request fields in dir_form
            requested_new_tier = dir_form.cleaned_data.get('request_new_tier')
            requested_featured_status = dir_form.cleaned_data.get('request_featured_status')
            submitted_tier_duration = dir_form.cleaned_data.get('requested_tier_duration')
            submitted_featured_duration = dir_form.cleaned_data.get('requested_featured_duration')

            # Get current approved values
            current_tier = company.tier
            current_featured = company.is_featured

            # --- Tier Logic ---
            tier_updated = False
            if requested_new_tier and requested_new_tier != current_tier:
                # Requesting an upgrade (Standard is excluded from choices)
                company.requested_tier = requested_new_tier
                company.requested_tier_duration = submitted_tier_duration or Company.DURATION_MONTHLY # Default duration
                company.tier_change_pending = True
                tier_updated = False # Main tier field is NOT updated
                messages.info(request, f"Tier change to '{requested_new_tier}' requested for company '{company.name}'. Pending superadmin approval.")
            # No explicit downgrade to Standard via this form anymore
            elif not requested_new_tier and company.tier_change_pending:
                 # User selected "No Change" but a request was pending - clear the request
                 company.requested_tier = None
                 company.tier_change_pending = False
                 company.requested_tier_duration = None
                 tier_updated = False # No change to main tier field

            # --- Featured Logic ---
            featured_updated = False
            if requested_featured_status and not current_featured:
                # Requesting to feature
                company.requested_featured_duration = submitted_featured_duration or Company.DURATION_MONTHLY # Default duration
                company.featured_request_pending = True
                featured_updated = False # Main featured field is NOT updated
                messages.info(request, f"Featured status requested for company '{company.name}'. Pending superadmin approval.")
            elif not requested_featured_status and current_featured:
                 # Requesting to un-feature - apply immediately
                 company.is_featured = False
                 company.featured_request_pending = False
                 company.featured_expiry_date = None
                 company.requested_featured_duration = None
                 featured_updated = True
                 messages.success(request, f"Featured status removed for company '{company.name}'.")
            elif requested_featured_status == current_featured and company.featured_request_pending:
                 # Request matches current status, but a request was pending - clear the request
                 company.featured_request_pending = False
                 company.requested_featured_duration = None
                 featured_updated = False # No change to main featured field

            # --- Save the Company Instance ---
            update_fields = [
                # Fields managed by CompanySettingsForm are saved via form.save() above
                # Only save fields related to tier/feature requests here
                'requested_tier', 'tier_change_pending', 'requested_tier_duration', 'tier_expiry_date',
                'featured_request_pending', 'requested_featured_duration', 'featured_expiry_date'
            ]
            if tier_updated: # Only happens on explicit downgrade to Standard (handled elsewhere now)
                update_fields.append('tier')
            if featured_updated: # Only happens on explicit un-feature request
                update_fields.append('is_featured')

            # Save only the specified fields for the Company model
            company.save(update_fields=update_fields)

            # Handle categories for listing
            from directory.models import CompanyListing, CompanyCategory
            categories_text = form.cleaned_data.get('categories')
            if categories_text:
                listing, created = CompanyListing.objects.get_or_create(company=company)

                # Clear existing categories
                listing.categories.clear()

                # Create or get category objects and add them to the listing
                category_names = [name.strip() for name in categories_text.split(',') if name.strip()]
                for name in category_names:
                    category, _ = CompanyCategory.objects.get_or_create(name=name)
                    listing.categories.add(category)
            messages.success(request, 'Company settings updated successfully.')
            return redirect('accounts:company_settings', company_id=company.id)
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        current_year = datetime.date.today().year
        form = CompanySettingsForm(instance=company_info, current_year=current_year)
        dir_form = CompanyDirectorySettingsForm(instance=company)

        # Debug information
        print(f"DEBUG - Company Info: {company_info.__dict__}")
        print(f"DEBUG - Form initial data: {form.initial}")

        # Use utility function to handle categories from session
        from .utils import handle_categories_from_session
        form = handle_categories_from_session(request, company, form)

    context = {
        'company': company, 'form': form, 'dir_form': dir_form, 'current_year': datetime.date.today().year
    }
    return render(request, 'accounts/company_settings.html', context)

@login_required
@require_POST
def company_delete(request, company_id):
    """Deletes a company if the requesting user is the owner."""
    company = get_object_or_404(Company, id=company_id)

    # Security check: Only the owner can delete the company
    if request.user != company.owner:
        messages.error(request, "You do not have permission to delete this company.")
        # Redirect back to settings or dashboard, settings seems more appropriate
        return redirect('accounts:company_settings', company_id=company.id)

    company_name = company.name # Get name for message before deleting
    company.delete()

    # Clear the active company ID from the session if it was the one deleted
    if request.session.get('active_company_id') == str(company_id):
        del request.session['active_company_id']

    messages.success(request, f"Company '{company_name}' and all associated data have been permanently deleted.")
    # Redirect to dashboard after successful deletion
    return redirect('accounts:dashboard')

@login_required
def company_team(request, company_id):
    company = get_object_or_404(Company.objects.prefetch_related('memberships__user'), id=company_id)
    if not request.user.has_perm('accounts.view_company', company) and request.user != company.owner:
         raise PermissionDenied("You do not have permission to view this company team page.")
    can_manage_team = request.user.has_perm('accounts.manage_members', company)
    can_manage_links_invites = request.user.has_perm('accounts.manage_invites_links', company)
    invite_form = None
    reg_link_form = None
    if can_manage_links_invites:
        if request.method == 'POST':
            if 'submit_invite_form' in request.POST:
                invite_form = TeamInvitationForm(request.POST)
                if invite_form.is_valid():
                    invitations = invite_form.save(company=company, invited_by=request.user)
                    # --- Impersonation Logging ---
                    log_user = request.real_user if request.is_impersonate else request.user
                    log_description = f"Sent {len(invitations)} invitation(s)."
                    if request.is_impersonate:
                        log_description += f" (Superadmin '{request.real_user.username}' impersonating '{request.user.username}')"
                    ActivityLog.objects.create(
                        company=company, user=log_user, activity_type=ActivityLog.TYPE_INVITATION_SENT,
                        description=log_description
                    )
                    # --- End Impersonation Logging ---
                    messages.success(request, f'Sent {len(invitations)} invitation(s).')
                    return redirect('accounts:company_team', company_id=company.id)
            elif 'submit_reg_link_form' in request.POST:
                # Pass the company object here
                reg_link_form = RegistrationLinkForm(request.POST, company=company)
                if reg_link_form.is_valid():
                    # Call the form's save method, passing the required arguments.
                    # The form's save method handles assigning company, created_by, and saving.
                    link = reg_link_form.save(company=company, created_by=request.user)
                    try:
                        # Log activity, ensuring link.intended_group exists before accessing its name
                        role_name = link.intended_group.name if link.intended_group else "Unknown Role"
                        # --- Impersonation Logging ---
                        log_user = request.real_user if request.is_impersonate else request.user
                        log_description = f"Created registration link for role '{role_name}'."
                        if request.is_impersonate:
                            log_description += f" (Superadmin '{request.real_user.username}' impersonating '{request.user.username}')"
                        ActivityLog.objects.create(
                            company=company, user=log_user, activity_type='registration_link_created',
                            description=log_description
                        )
                        # --- End Impersonation Logging ---
                    except Exception as e: print(f"Error logging registration_link_created: {e}")
                    messages.success(request, 'Registration link created successfully.')
                    return redirect('accounts:company_team', company_id=company.id)
            # Handle case where reg link form was submitted but invalid
            elif 'submit_reg_link_form' in request.POST:
                # If the reg link form was submitted but invalid, reg_link_form is already set
                # Initialize invite_form if it wasn't the one submitted
                if invite_form is None: invite_form = TeamInvitationForm()
            # Handle case where invite form was submitted but invalid
            elif 'submit_invite_form' in request.POST:
                 # If the invite form was submitted but invalid, invite_form is already set
                 # Initialize reg_link_form if it wasn't the one submitted
                 if reg_link_form is None: reg_link_form = RegistrationLinkForm(company=company)

        # --- End POST handling ---
        # --- GET request or failed POST ---
        # Ensure forms are initialized if not already set by POST handling
        if invite_form is None: invite_form = TeamInvitationForm()
        if reg_link_form is None:
             reg_link_form = RegistrationLinkForm(company=company) # Pass company for GET

    # --- Fetch and Filter Data ---
    # Get filter parameters from GET request
    search_query = request.GET.get('q', '').strip()
    group_filter = request.GET.get('group', '')

    # Base queryset for members (excluding owner)
    memberships_qs = company.memberships.exclude(user=company.owner).select_related('user').order_by('user__username')

    # Apply search filter
    if search_query:
        memberships_qs = memberships_qs.filter(
            Q(user__username__icontains=search_query) |
            Q(user__first_name__icontains=search_query) |
            Q(user__last_name__icontains=search_query) |
            Q(user__email__icontains=search_query)
        )

    # Apply group filter
    selected_group = None
    if group_filter:
        try:
            # Ensure the group exists before filtering
            selected_group = Group.objects.get(name=group_filter)
            memberships_qs = memberships_qs.filter(user__groups=selected_group)
        except Group.DoesNotExist:
            group_filter = '' # Reset filter if group name is invalid

    # Fetch other data
    pending_invitations = TeamInvitation.objects.filter(company=company, status=TeamInvitation.STATUS_PENDING)
    registration_links = company.registration_links.select_related('created_by', 'intended_group').order_by('-created_at')

    # Get available groups for the filter dropdown (consider caching this)
    # Only include groups relevant to company roles
    relevant_group_names = ["Company Administrators", "Company Members", "Company Guests"]
    available_groups = Group.objects.filter(name__in=relevant_group_names).order_by('name')

    context = {
        'company': company,
        'owner': company.owner,
        'invite_form': invite_form,
        'reg_link_form': reg_link_form,
        'memberships': memberships_qs, # Pass the filtered queryset
        'pending_invitations': pending_invitations,
        'registration_links': registration_links,
        'can_manage_team': can_manage_team,
        'can_manage_links_invites': can_manage_links_invites,
        'search_query': search_query, # Pass filter values back to template
        'group_filter': group_filter,
        'available_groups': available_groups, # Pass groups for dropdown
    }
    return render(request, 'accounts/company_team.html', context)

@login_required
def company_switch(request):
    user = request.user
    owned_company_ids = user.owned_companies.values_list('id', flat=True)
    member_company_ids = user.memberships.values_list('company_id', flat=True)
    accessible_company_ids = set(owned_company_ids) | set(member_company_ids)
    user_companies = Company.objects.filter(id__in=accessible_company_ids).order_by('name')
    if request.method == 'POST':
        company_id = request.POST.get('company_id')
        if company_id and user_companies.filter(id=company_id).exists():
            request.session['active_company_id'] = company_id
            return redirect('home') # Changed from 'accounts:dashboard'
    context = { 'companies': user_companies, 'active_company_id': request.session.get('active_company_id') }
    return render(request, 'accounts/company_switch.html', context)

@login_required
@require_POST
def invite_member(request, company_id):
    company = get_object_or_404(Company, id=company_id)
    if not request.user.has_perm('accounts.manage_invites_links', company):
        return JsonResponse({'status': 'error', 'message': 'Permission denied.'}, status=403)
    form = TeamInvitationForm(request.POST)
    if form.is_valid():
        invitations = form.save(company=company, invited_by=request.user)
        # --- Impersonation Logging ---
        log_user = request.real_user if request.is_impersonate else request.user
        log_description = f"Sent {len(invitations)} invitation(s) via AJAX."
        if request.is_impersonate:
            log_description += f" (Superadmin '{request.real_user.username}' impersonating '{request.user.username}')"
        ActivityLog.objects.create(
            company=company, user=log_user, activity_type=ActivityLog.TYPE_INVITATION_SENT,
            description=log_description
        )
        # --- End Impersonation Logging ---
        return JsonResponse({'status': 'success', 'message': f'Sent {len(invitations)} invitation(s).'})
    return JsonResponse({'status': 'error', 'errors': form.errors}, status=400)

@login_required
@require_POST
def remove_member(request, company_id, user_id): # Added user_id parameter
    company = get_object_or_404(Company, id=company_id)
    if not request.user.has_perm('accounts.manage_members', company):
        # Check if it's an AJAX request before returning JSON
        is_ajax = request.headers.get('x-requested-with') == 'XMLHttpRequest'
        if is_ajax:
            return JsonResponse({'status': 'error', 'message': 'Permission denied.'}, status=403)
        else:
            messages.error(request, 'Permission denied.')
            return redirect('accounts:company_team', company_id=company_id) # Redirect for non-AJAX

    # Fetch membership using company_id and user_id
    membership_to_remove = get_object_or_404(Membership, company_id=company_id, user_id=user_id)
    user_to_remove = membership_to_remove.user # Or simply get_object_or_404(User, id=user_id) if needed elsewhere

    if user_to_remove == company.owner:
        is_ajax = request.headers.get('x-requested-with') == 'XMLHttpRequest'
        if is_ajax:
            return JsonResponse({'status': 'error', 'message': 'Cannot remove the company owner.'}, status=400)
        else:
            messages.error(request, 'Cannot remove the company owner.')
            return redirect('accounts:company_team', company_id=company_id)

    if user_to_remove == request.user:
        is_ajax = request.headers.get('x-requested-with') == 'XMLHttpRequest'
        if is_ajax:
             return JsonResponse({'status': 'error', 'message': 'Cannot remove yourself.'}, status=400)
        else:
            messages.error(request, 'Cannot remove yourself.')
            return redirect('accounts:company_team', company_id=company_id)

    removed_username = user_to_remove.username
    membership_to_remove.delete()
    try:
        # --- Impersonation Logging ---
        log_user = request.real_user if request.is_impersonate else request.user
        log_description = f"Removed member {removed_username}."
        if request.is_impersonate:
            log_description += f" (Superadmin '{request.real_user.username}' impersonating '{request.user.username}')"
        ActivityLog.objects.create(
            company=company, user=log_user, activity_type='member_removed',
            description=log_description
        )
        # --- End Impersonation Logging ---
    except Exception as e: print(f"Error logging member_removed: {e}")

    # Always redirect back to the team page after removal for standard form submission
    messages.success(request, f"Member {removed_username} removed successfully.")
    return redirect('accounts:company_team', company_id=company_id)

@login_required
@require_POST
def cancel_invitation(request, company_id, invitation_id):
    company = get_object_or_404(Company, id=company_id)
    if not request.user.has_perm('accounts.manage_invites_links', company):
         return JsonResponse({'status': 'error', 'message': 'Permission denied.'}, status=403)
    invitation = get_object_or_404(TeamInvitation, id=invitation_id, company=company)
    invited_email = invitation.email
    invitation.delete()
    try:
        # --- Impersonation Logging ---
        log_user = request.real_user if request.is_impersonate else request.user
        log_description = f"Cancelled invitation for {invited_email}."
        if request.is_impersonate:
            log_description += f" (Superadmin '{request.real_user.username}' impersonating '{request.user.username}')"
        ActivityLog.objects.create(
            company=company, user=log_user, activity_type='invitation_cancelled',
            description=log_description
        )
        # --- End Impersonation Logging ---
    except Exception as e: print(f"Error logging invitation_cancelled: {e}")
    return JsonResponse({'status': 'success', 'message': 'Invitation cancelled successfully'})

@login_required
@require_POST
def resend_invitation(request, company_id, invitation_id):
    company = get_object_or_404(Company, id=company_id)
    if not request.user.has_perm('accounts.manage_invites_links', company):
         return JsonResponse({'status': 'error', 'message': 'Permission denied.'}, status=403)
    invitation = get_object_or_404(TeamInvitation, id=invitation_id, company=company)
    # invitation.send_invitation() # Actual email sending logic needed here
    try:
        # --- Impersonation Logging ---
        log_user = request.real_user if request.is_impersonate else request.user
        log_description = f"Resent invitation for {invitation.email}."
        if request.is_impersonate:
            log_description += f" (Superadmin '{request.real_user.username}' impersonating '{request.user.username}')"
        ActivityLog.objects.create(
            company=company, user=log_user, activity_type='invitation_resent',
            description=log_description
        )
        # --- End Impersonation Logging ---
    except Exception as e: print(f"Error logging invitation_resent: {e}")
    messages.info(request, f"Placeholder: Invitation for {invitation.email} would be resent.")
    return JsonResponse({'status': 'success', 'message': 'Invitation resent successfully'})

def accept_invitation(request, token):
    invitation = get_object_or_404(TeamInvitation, token=token)
    if request.user.is_authenticated:
        if invitation.is_expired():
            messages.error(request, 'This invitation has expired.')
            return redirect('home')
        if invitation.status != TeamInvitation.STATUS_PENDING:
            messages.warning(request, 'This invitation has already been responded to.')
            return redirect('home')
        company = invitation.company
        user = request.user
        if Membership.objects.filter(user=user, company=company).exists():
            messages.warning(request, f'You are already a member of {company.name}.')
            invitation.status = TeamInvitation.STATUS_DECLINED
            invitation.save()
            return redirect('home') # Changed from 'accounts:dashboard'

        # Determine intended role name (still relies on old FK temporarily)
        intended_role_name = "Member"
        # Need to handle potential AttributeError if invitation.role is None after migration
        try:
            # This 'invitation.role' might fail after migration 0010
            if invitation.role:
                intended_role_name = invitation.role.name
        except AttributeError: # Catch if .role doesn't exist anymore
             pass # Keep default 'Member'

        perms_to_assign = []
        if intended_role_name == 'Admin': perms_to_assign = ADMIN_PERMS_COMPANY
        elif intended_role_name == 'Member': perms_to_assign = MEMBER_PERMS_COMPANY
        elif intended_role_name == 'Viewer': perms_to_assign = VIEWER_PERMS_COMPANY
        else: perms_to_assign = MEMBER_PERMS_COMPANY; intended_role_name = "Member"

        # Create membership (role FK is gone)
        membership = Membership.objects.create(user=user, company=company)

        # --- Assign User to Group ---
        target_group_name = None
        if intended_role_name == 'Admin': target_group_name = 'Company Administrators' # Assuming this group exists
        elif intended_role_name == 'Member': target_group_name = 'Company Members'
        elif intended_role_name == 'Viewer': target_group_name = 'Company Guests' # Map Viewer role to Company Guests group
        else: target_group_name = 'Company Members' # Default to Member group

        if target_group_name:
            try:
                group_to_assign = Group.objects.get(name=target_group_name)
                user.groups.add(group_to_assign)
                print(f"Added user {user.username} to group '{group_to_assign.name}'")
            except Group.DoesNotExist:
                print(f"Warning: Group '{target_group_name}' not found. User not added to group.")
            except Exception as e:
                print(f"Error adding user {user.username} to group '{target_group_name}': {e}")
        # --- End Assign User to Group ---

        print(f"Assigning {len(perms_to_assign)} permissions to {user.username} for company {company.name} as {intended_role_name}")
        for perm_codename in perms_to_assign:
            try: assign_perm(perm_codename, user, company)
            except Permission.DoesNotExist: print(f"  Warning: Permission '{perm_codename}' not found. Skipping.")
            except Exception as e: print(f"  Error assigning perm '{perm_codename}': {e}")

        # Note: The Membership object no longer has a 'role' field to pass here.
        # The old Role object reference (invitation.role) is also gone.
        # If Membership creation needs the old Role temporarily for FK constraint before migration removes the field,
        # this needs careful handling. Assuming the migration removing the FK runs *after* this code change.
        # If issues arise, we might need to temporarily fetch the Role object by name if needed.

        invitation.status = TeamInvitation.STATUS_ACCEPTED
        invitation.accepted_at = timezone.now()
        invitation.save()
        ActivityLog.objects.create(
            company=company, user=user, activity_type=ActivityLog.TYPE_INVITATION_ACCEPTED,
            description=f"{user.username} accepted invitation and joined." # Removed role name reference
        )
        messages.success(request, f'Welcome to {invitation.company.name}!')
        request.session['active_company_id'] = str(company.id)
        return redirect('home') # Changed from 'accounts:dashboard'
    else:
        request.session['invitation_token'] = str(token)
        messages.info(request, f"Please register or log in to join {company.name}.")
        return redirect('accounts:register')

@login_required
def community_settings(request, company_id):
    company = get_object_or_404(Company, id=company_id)
    # Prioritize owner check: If user is owner, grant access. Otherwise, check permission.
    if request.user != company.owner and not request.user.has_perm('accounts.change_company_settings', company):
        raise PermissionDenied("You do not have permission to edit these settings.")

    # Redirect to company settings if this is not a community entity
    if not hasattr(company, 'entity_type') or company.entity_type != 'community':
        return redirect('accounts:company_settings', company_id=company_id)

    # Get community assistants
    from assistants.models import Assistant
    community_assistants = Assistant.objects.filter(
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY,
        is_active=True
    )

    company_info, created_info = CompanyInformation.objects.get_or_create(company=company)

    if request.method == 'POST':
        form = CompanySettingsForm(request.POST, request.FILES, instance=company_info, current_year=datetime.date.today().year)
        if form.is_valid():
            company_info = form.save()

            # Handle categories for listing using utility function
            from .utils import process_categories
            process_categories(request, company, form)

            # Explicitly handle list_in_directory field to ensure it's properly saved
            list_in_directory_value = form.cleaned_data.get('list_in_directory')
            print(f"--- DEBUG: Community list_in_directory value in form.cleaned_data: {list_in_directory_value}")
            company_info.list_in_directory = list_in_directory_value
            company_info.save()

            messages.success(request, 'Community settings updated successfully.')
            return redirect('accounts:community_settings', company_id=company.id)
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = CompanySettingsForm(instance=company_info, current_year=datetime.date.today().year)

        # Use utility function to handle categories from session
        from .utils import handle_categories_from_session
        form = handle_categories_from_session(request, company, form)

    context = {
        'company': company,
        'form': form,
        'community_assistants': community_assistants,
        'company_qr_code_url': company.qr_code.url if company.qr_code else None,
    }
    return render(request, 'accounts/community_settings.html', context)


@login_required
def user_settings(request):
    # Instantiate forms
    # Pass request.user as instance for UserProfileForm
    profile_form = UserProfileForm(instance=request.user)
    # TODO: Instantiate preferences_form here if created
    # preferences_form = UserPreferencesForm(instance=request.user.profile or request.user) # Adjust instance based on where prefs are stored

    if request.method == 'POST':
        form_type = request.POST.get('form_type')

        if form_type == 'profile':
            # Pass request.FILES to handle avatar upload
            profile_form = UserProfileForm(request.POST, request.FILES, instance=request.user)
            if profile_form.is_valid():
                profile_form.save()
                messages.success(request, 'Your profile information has been updated.')
                return redirect('accounts:user_settings') # Redirect back to settings page
            else:
                 messages.error(request, 'Please correct the errors in the profile form.')
        elif form_type == 'preferences':
            # TODO: Process preferences_form here
            # preferences_form = UserPreferencesForm(request.POST, instance=...)
            # if preferences_form.is_valid():
            #     preferences_form.save()
            #     messages.success(request, 'Your preferences have been updated.')
            #     return redirect('accounts:user_settings')
            # else:
            #     messages.error(request, 'Please correct the errors in the preferences form.')
            pass # Placeholder for preferences form handling

    # GET request or failed POST
    active_sessions_count = Session.objects.filter(expire_date__gte=timezone.now()).count() # Consider filtering by user if needed
    context = {
        'form': profile_form, # Pass profile_form as 'form' for now to match template
        # 'preferences_form': preferences_form, # Pass preferences form when implemented
        'active_sessions': active_sessions_count
    }
    return render(request, 'accounts/user_settings.html', context)

@login_required
@require_POST
def logout_all(request):
    messages.info(request, "Placeholder: All other sessions would be logged out.")
    return redirect('accounts:user_settings')

@login_required
@require_POST
def delete_account(request):
    messages.warning(request, "Placeholder: Account deletion not implemented.")
    return redirect('home')

def public_company_detail_view(request, slug):
    company = get_object_or_404(Company.objects.prefetch_related('info', 'assistants__folder', 'assistant_folders'), slug=slug)

    # --- Fetch and Sort Folders ---
    folders_qs = company.assistant_folders.all().order_by('order', 'name')

    # --- Fetch Public Assistants ---
    public_assistants_qs = company.assistants.filter(is_public=True, is_active=True).select_related('folder', 'listing')
    print(f"DEBUG [Public Detail]: Initial public assistants count: {public_assistants_qs.count()}") # DEBUG PRINT 1

    # --- Apply Filters (Using new query parameter names) ---
    q_name = request.GET.get('q_name', '')
    q_category = request.GET.get('q_category', '')
    q_tag = request.GET.get('q_tag', '')
    folder_id_filter = request.GET.get('folder_id', '') # Keep as string for comparison

    filtered_assistants_qs = public_assistants_qs # Start with all public

    if q_name:
        # Use Q object to search name OR description (SQLite compatible)
        filtered_assistants_qs = filtered_assistants_qs.filter(
            Q(name__icontains=q_name) | Q(description__icontains=q_name)
        )
        print(f"DEBUG [Public Detail]: Applied simple search for '{q_name}' in name/description") # DEBUG
    if q_category:
        # Assumes categories are stored in the related AssistantListing model's JSONField
        # Adjust if category storage is different
        filtered_assistants_qs = filtered_assistants_qs.filter(listing__categories__icontains=q_category)
    if q_tag:
        # Assumes tags are stored in the related AssistantListing model's JSONField
        filtered_assistants_qs = filtered_assistants_qs.filter(listing__tags__icontains=q_tag)

    target_folder_obj = None
    if folder_id_filter:
        if folder_id_filter == 'unassigned':
            filtered_assistants_qs = filtered_assistants_qs.filter(folder__isnull=True)
        else:
            # Validate folder_id is a valid integer and belongs to the company
            try:
                folder_id_int = int(folder_id_filter)
                # Check against the sorted folders_qs
                target_folder_obj = folders_qs.get(id=folder_id_int) # Use get() for single object
                filtered_assistants_qs = filtered_assistants_qs.filter(folder_id=folder_id_int)
            except (ValueError, TypeError, AssistantFolder.DoesNotExist):
                 folder_id_filter = '' # Reset if invalid folder for this company

    print(f"DEBUG [Public Detail]: Filtered assistants count: {filtered_assistants_qs.count()}") # DEBUG PRINT 2

    # --- Group Assistants Manually Based on Sorted Folders ---
    # Order assistants primarily by name (ranking is not available in SQLite)
    final_assistants_list = list(filtered_assistants_qs.order_by('name'))
    print(f"DEBUG [Public Detail]: Ordering by name ASC.") # DEBUG

    print(f"DEBUG [Public Detail]: Final assistants list (before grouping): {[(a.name, a.folder.name if a.folder else None) for a in final_assistants_list]}") # DEBUG PRINT 3

    grouped_assistants_list = []

    # 1. Get Assistants for each folder (in the ASCENDING sorted order)
    if not folder_id_filter or folder_id_filter == 'unassigned': # Show all folders if no filter or unassigned
        for folder in folders_qs:
            folder_assistants = [a for a in final_assistants_list if a.folder_id == folder.id]
            if folder_assistants:
                grouped_assistants_list.append((folder, folder_assistants))
    elif target_folder_obj: # Only show the selected folder
         folder_assistants = [a for a in final_assistants_list if a.folder_id == target_folder_obj.id]
         if folder_assistants:
             grouped_assistants_list.append((target_folder_obj, folder_assistants))

    # 2. Get Unassigned Assistants and add them LAST
    if not target_folder_obj or folder_id_filter == 'unassigned':
        unassigned_assistants = [a for a in final_assistants_list if a.folder is None]
        if unassigned_assistants:
            grouped_assistants_list.append((None, unassigned_assistants))

    print(f"DEBUG [Public Detail]: Final grouped_assistants_list length: {len(grouped_assistants_list)}") # DEBUG PRINT 4
    # Optional: Print the structure if needed
    # print(f"DEBUG [Public Detail]: Final grouped_assistants_list structure: {[(f.name if f else 'None', [a.name for a in assistants]) for f, assistants in grouped_assistants_list]}")


    # Get saved assistant IDs for the current user (if authenticated)
    # Check against ALL filtered assistants, not just a paginated subset
    saved_assistant_ids = set()
    if request.user.is_authenticated:
        saved_assistant_ids = set(
            SavedItem.objects.filter(
                user=request.user,
                item_type='assistant',
                assistant_id__in=[a.id for a in final_assistants_list] # Check against all relevant assistants
            ).values_list('assistant_id', flat=True)
        )

    context = {
        'company': company,
        'folders': folders_qs, # Pass sorted folders for filter links
        'grouped_assistants_list': grouped_assistants_list, # Pass correctly grouped list
        'q_name': q_name, # Pass filter values back to template
        'q_category': q_category,
        'q_tag': q_tag,
        'selected_folder_id': folder_id_filter, # String or empty
        'saved_assistant_ids': saved_assistant_ids,
        # Removed pagination context ('page_obj', 'grouped_assistants')
        'company_qr_code_url': company.qr_code.url if company.qr_code else None, # Add QR code URL
    }
    # --- DEBUG PRINT for QR Code ---
    print(f"DEBUG [Public Detail View] Company: {company.name} (ID: {company.id})")
    print(f"DEBUG [Public Detail View] company.qr_code object: {company.qr_code}")
    qr_url_debug = None
    if company.qr_code:
        try:
            qr_url_debug = company.qr_code.url
        except Exception as e:
            qr_url_debug = f"Error getting URL: {e}"
    print(f"DEBUG [Public Detail View] company.qr_code.url: {qr_url_debug}")
    print(f"DEBUG [Public Detail View] Context company_qr_code_url: {context.get('company_qr_code_url')}")
    # --- END DEBUG PRINT ---
    return render(request, 'accounts/public_company_detail.html', context)

def join_via_link(request, token):
    try:
        # Use select_related('company', 'intended_group') now
        link = RegistrationLink.objects.select_related('company', 'intended_group').get(token=token)
    except RegistrationLink.DoesNotExist:
        messages.error(request, "Invalid or expired registration link.")
        return redirect('home')
    if not link.is_valid:
        messages.error(request, "This registration link is no longer valid.")
        return redirect('home')
    company = link.company
    if request.user.is_authenticated:
        user = request.user
        if Membership.objects.filter(user=user, company=company).exists():
            messages.warning(request, f"You are already a member of {company.name}.")
            return redirect('home') # Changed from 'accounts:dashboard'

        # --- Assign Group and Permissions based on link.intended_group ---
        intended_group = link.intended_group
        if not intended_group:
            # Fallback if no group is set on the link (shouldn't happen if form requires it)
            messages.error(request, "Registration link is missing role information. Please contact the administrator.")
            return redirect('home')

        # Create membership
        membership = Membership.objects.create(user=user, company=company)

        # Assign the user to the intended group
        user.groups.add(intended_group)
        print(f"Added user {user.username} to group '{intended_group.name}' for company {company.name}")

        # Assign object-level permissions based on the group (using django-guardian)
        # Fetch permissions associated with the group (you might need a helper function or define perms here)
        # This example assumes you have a way to map Group names to permission sets
        perms_to_assign = []
        if intended_group.name == 'Admin': # Example mapping
            perms_to_assign = ADMIN_PERMS_COMPANY
        elif intended_group.name == 'Member':
            perms_to_assign = MEMBER_PERMS_COMPANY
        elif intended_group.name == 'Viewer':
             perms_to_assign = VIEWER_PERMS_COMPANY
        # Add more role mappings as needed

        print(f"Assigning {len(perms_to_assign)} company permissions to {user.username} for company {company.name} based on group '{intended_group.name}'")
        for perm_codename in perms_to_assign:
            try: assign_perm(perm_codename, user, company)
            except Permission.DoesNotExist: print(f"  Warning: Company Permission '{perm_codename}' not found. Skipping.")
            except Exception as e: print(f"  Error assigning company perm '{perm_codename}': {e}")

        # --- Assign Folder Permissions based on link.accessible_folders ---
        LINK_FOLDER_PERMS = ['assistants.view_assistantfolder', 'assistants.view_assistant']
        selected_folders = link.accessible_folders.all()
        if selected_folders:
            print(f"Assigning folder permissions for {len(selected_folders)} folders to {user.username}")
            for folder in selected_folders:
                for perm_codename in LINK_FOLDER_PERMS:
                    try:
                        assign_perm(perm_codename, user, folder)
                        print(f"  Assigned '{perm_codename}' for folder '{folder.name}'")
                    except Permission.DoesNotExist:
                        print(f"  Warning: Folder Permission '{perm_codename}' not found. Skipping.")
                    except Exception as e:
                        print(f"  Error assigning folder perm '{perm_codename}' for folder '{folder.name}': {e}")
        # --- End Folder Permission Logic ---

        # Increment link use after permissions are assigned
        link.increment_use()

        ActivityLog.objects.create(
            company=company, user=user, activity_type=ActivityLog.TYPE_USER_JOINED,
            description=f"{user.username} joined via registration link as '{intended_group.name}'." # Log the role
        )
        messages.success(request, f"You have successfully joined {company.name} as a {intended_group.name}!")
        request.session['active_company_id'] = str(company.id)

        # Conditional redirect based on role
        if intended_group.name == 'Viewer': # Check if the role is 'Viewer'
            return redirect('directory:company_list') # Redirect Viewers to the public directory
        else:
            return redirect('home') # Changed from 'accounts:dashboard'
    else:
        request.session['registration_link_token'] = str(token)
        messages.info(request, f"Please register or log in to join {company.name}.")
        return redirect('accounts:register')

@login_required
@require_GET
def registration_link_qr_code(request, token):
    try: link = RegistrationLink.objects.get(token=token)
    except RegistrationLink.DoesNotExist: raise Http404("Registration link not found.")
    if not request.user.has_perm('accounts.manage_invites_links', link.company):
         raise PermissionDenied("You do not have permission to view this QR code.")
    try: join_url = request.build_absolute_uri(link.get_absolute_url())
    except NoReverseMatch: return HttpResponse("Error generating QR code URL.", status=500)
    qr = qrcode.QRCode(version=1, error_correction=qrcode.constants.ERROR_CORRECT_L, box_size=6, border=4)
    qr.add_data(join_url)
    qr.make(fit=True)
    img = qr.make_image(fill_color="black", back_color="white")
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    buffer.seek(0)
    response = HttpResponse(buffer.getvalue(), content_type='image/png')
    buffer.close()
    return response

@login_required
def generate_company_qr_code(request, company_id):
    """Generate or regenerate QR code for a company."""
    from utils.qr_generator import generate_model_qr_code

    # Get the company
    company = get_object_or_404(Company, id=company_id)

    # Check permissions
    if not request.user.has_perm('accounts.change_company_settings', company) and company.owner != request.user:
        return JsonResponse({'status': 'error', 'message': 'Permission denied'}, status=403)

    try:
        # Generate the QR code using the company's detail URL
        url_path = reverse('accounts:company_detail', kwargs={'slug': company.slug})
        success = generate_model_qr_code(company, url_path, field_name='qr_code')

        if success:
            # Save the company with the new QR code
            company.save(update_fields=['qr_code'])
            return JsonResponse({
                'status': 'success',
                'message': 'QR code generated successfully',
                'qr_code_url': company.qr_code.url
            })
        else:
            return JsonResponse({
                'status': 'error',
                'message': 'Failed to generate QR code'
            }, status=500)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Error generating QR code: {str(e)}'
        }, status=500)

@login_required
@require_POST
def deactivate_registration_link(request, token):
    """Deactivates a registration link."""
    link = get_object_or_404(RegistrationLink, token=token)
    company = link.company # Get company from the link

    # Permission check
    if not request.user.has_perm('accounts.manage_invites_links', company):
        messages.error(request, 'Permission denied.')
        # Redirect to team page or dashboard depending on context, team page seems safer
        return redirect('accounts:company_team', company_id=company.id)

    link.is_active = False
    link.save(update_fields=['is_active'])
    messages.success(request, f"Registration link deactivated successfully.")
    # Log activity
    try:
        # --- Impersonation Logging ---
        log_user = request.real_user if request.is_impersonate else request.user
        log_description = f"Deactivated registration link (Token ending: ...{str(token)[-6:]})."
        if request.is_impersonate:
            log_description += f" (Superadmin '{request.real_user.username}' impersonating '{request.user.username}')"
        ActivityLog.objects.create(
            company=company, user=log_user, activity_type='registration_link_deactivated',
            description=log_description
        )
        # --- End Impersonation Logging ---
    except Exception as e:
        print(f"Error logging registration_link_deactivated: {e}")
    return redirect('accounts:company_team', company_id=company.id)


@login_required
@require_POST
def transfer_ownership(request, company_id):
    """Transfers ownership of a company to another user."""
    company = get_object_or_404(Company, id=company_id)
    User = get_user_model()

    # Security check: Only the current owner can initiate a transfer
    if request.user != company.owner:
        messages.error(request, "Only the company owner can transfer ownership.")
        return redirect('accounts:company_settings', company_id=company_id)

    new_owner_id = request.POST.get('new_owner')
    if not new_owner_id:
        messages.error(request, "No new owner selected.")
        return redirect('accounts:company_settings', company_id=company_id)

    try:
        new_owner = User.objects.get(id=new_owner_id)
    except User.DoesNotExist:
        messages.error(request, "Selected new owner does not exist.")
        return redirect('accounts:company_settings', company_id=company_id)

    # Ensure the new owner is actually a member of the company (optional but recommended)
    if not company.memberships.filter(user=new_owner).exists() and company.owner != new_owner:
         # Add the new owner as a member if they aren't already (consider default role/perms)
         # This might need more complex logic depending on your membership/permission setup
         Membership.objects.create(user=new_owner, company=company)
         # Optionally assign default permissions/group here
         messages.info(request, f"User {new_owner.username} was added as a member before ownership transfer.")


    # --- Perform the transfer ---
    old_owner = company.owner
    company.owner = new_owner
    company.save(update_fields=['owner'])

    # --- Adjust Permissions/Groups (Crucial Step!) ---
    # 1. Remove old owner's admin/owner-specific permissions/groups for this company.
    #    (Be careful not to remove permissions they might have via other means).
    #    This requires knowledge of your specific permission setup (e.g., Guardian groups/perms).
    #    Example (using Guardian, assuming an 'Admin' group or specific perms):
    #    remove_perm('accounts.change_company_settings', old_owner, company) # Example
    #    old_owner.groups.remove(Group.objects.get(name='Company Administrators')) # Example

    # 2. Grant new owner the necessary admin/owner permissions/groups.
    #    assign_perm('accounts.change_company_settings', new_owner, company) # Example
    #    new_owner.groups.add(Group.objects.get(name='Company Administrators')) # Example

    # --- Log the activity ---
    ActivityLog.objects.create(
        company=company,
        user=request.user, # The user performing the action (old owner)
        activity_type='ownership_transferred',
        description=f"Ownership transferred from {old_owner.username} to {new_owner.username}."
    )

    messages.success(request, f"Ownership of {company.name} successfully transferred to {new_owner.username}.")
    # Redirect to a relevant page, maybe the team page or dashboard
    return redirect('accounts:company_team', company_id=company.id)


@login_required
@require_POST
def delete_registration_link(request, token):
    """Deletes a registration link."""
    link = get_object_or_404(RegistrationLink, token=token)
    company = link.company # Get company from the link

    # Permission check
    if not request.user.has_perm('accounts.manage_invites_links', company):
        messages.error(request, 'Permission denied.')
        return redirect('accounts:company_team', company_id=company.id)

    link_token_suffix = str(token)[-6:] # Get suffix before deleting
    link.delete()
    messages.success(request, f"Registration link deleted successfully.")
    # Log activity
    try:
        # --- Impersonation Logging ---
        log_user = request.real_user if request.is_impersonate else request.user
        log_description = f"Deleted registration link (Token ending: ...{link_token_suffix})."
        if request.is_impersonate:
            log_description += f" (Superadmin '{request.real_user.username}' impersonating '{request.user.username}')"
        ActivityLog.objects.create(
            company=company, user=log_user, activity_type='registration_link_deleted',
            description=log_description
        )
        # --- End Impersonation Logging ---
    except Exception as e:
        print(f"Error logging registration_link_deleted: {e}")
    return redirect('accounts:company_team', company_id=company.id)
