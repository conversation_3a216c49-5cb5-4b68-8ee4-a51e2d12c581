"""
<PERSON><PERSON><PERSON> to completely reset the PostgreSQL database and start fresh.
This is useful when you want to start with a clean database.
"""
import os
import sys
import subprocess
import psycopg2
from psycopg2 import sql
from django.conf import settings

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
import django
django.setup()

# Get database connection info from settings
db_settings = settings.DATABASES['default']

def reset_database():
    """Drop and recreate the database."""
    print("Connecting to PostgreSQL database...")
    try:
        # Connect to PostgreSQL (to postgres database)
        conn_params = {
            'dbname': 'postgres',  # Connect to postgres database
            'user': db_settings['USER'],
            'host': db_settings['HOST'],
            'port': db_settings['PORT']
        }
        
        # Only add password if it's not empty
        if db_settings['PASSWORD']:
            conn_params['password'] = db_settings['PASSWORD']
            
        conn = psycopg2.connect(**conn_params)
        conn.autocommit = True  # Important for dropping/creating databases
        cursor = conn.cursor()
        
        db_name = db_settings['NAME']
        
        # Check if database exists
        cursor.execute(
            sql.SQL("SELECT 1 FROM pg_database WHERE datname = %s"),
            [db_name]
        )
        exists = cursor.fetchone()
        
        if exists:
            # Terminate all connections to the database
            print(f"Terminating all connections to '{db_name}'...")
            cursor.execute(
                sql.SQL("""
                SELECT pg_terminate_backend(pg_stat_activity.pid)
                FROM pg_stat_activity
                WHERE pg_stat_activity.datname = %s
                AND pid <> pg_backend_pid()
                """),
                [db_name]
            )
            
            # Drop the database
            print(f"Dropping database '{db_name}'...")
            cursor.execute(
                sql.SQL("DROP DATABASE IF EXISTS {}").format(sql.Identifier(db_name))
            )
            print(f"Database '{db_name}' dropped.")
        
        # Create the database
        print(f"Creating database '{db_name}'...")
        cursor.execute(
            sql.SQL("CREATE DATABASE {}").format(sql.Identifier(db_name))
        )
        print(f"Database '{db_name}' created successfully.")
        
        cursor.close()
        conn.close()
        
        return True
    except Exception as e:
        print(f"Error resetting database: {e}")
        return False

def run_migrations():
    """Run Django migrations."""
    print("\nRunning migrations...")
    try:
        result = subprocess.run(
            [sys.executable, "manage.py", "migrate"],
            check=True,
            capture_output=True,
            text=True
        )
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error running migrations: {e}")
        print(e.stdout)
        print(e.stderr)
        return False

def create_superuser():
    """Create a Django superuser."""
    print("\nCreating superuser...")
    try:
        from django.contrib.auth.models import User
        if not User.objects.filter(username='admin').exists():
            User.objects.create_superuser('admin', '<EMAIL>', 'admin')
            print("Superuser 'admin' created with password 'admin'.")
        else:
            print("Superuser 'admin' already exists.")
        return True
    except Exception as e:
        print(f"Error creating superuser: {e}")
        return False

def main():
    """Main function to reset the database and run migrations."""
    print("PostgreSQL Fresh Start Script")
    print("=============================")
    
    confirm = input("This will COMPLETELY RESET your database. All data will be lost. Continue? (y/n): ").strip().lower()
    if confirm != 'y':
        print("Operation cancelled.")
        return
    
    # Reset the database
    if reset_database():
        # Run migrations
        if run_migrations():
            # Create superuser
            create_superuser()
            
            print("\nFresh start completed successfully!")
            print("\nNext steps:")
            print("1. Run the development server: python manage.py runserver")
            print("2. Log in with username 'admin' and password 'admin'")
            print("3. Change the admin password immediately after logging in")
        else:
            print("\nFailed to run migrations.")
    else:
        print("\nFailed to reset the database.")

if __name__ == "__main__":
    main()
