/**
 * Modern Assistant <PERSON><PERSON> CSS
 * Provides enhanced, modern styling for buttons on the manage assistants page
 */

/* Base button styling for all buttons in the assistant list */
.assistant-list-modern-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 8px !important;
  padding: 0.5rem 0.75rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1),
              0 1px 3px rgba(0, 0, 0, 0.08),
              inset 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
  border: none !important;
  font-weight: 500 !important;
  letter-spacing: 0.02em !important;
  text-transform: none !important;
  min-width: 36px !important;
  min-height: 36px !important;
  z-index: 1 !important;
}

/* Hover effect for all buttons */
.assistant-list-modern-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 7px 14px rgba(0, 0, 0, 0.15),
              0 3px 6px rgba(0, 0, 0, 0.1),
              inset 0 0 0 1px rgba(255, 255, 255, 0.2) !important;
}

/* Active/pressed state for all buttons */
.assistant-list-modern-btn:active {
  transform: translateY(1px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1),
              inset 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
}

/* Focus state for all buttons */
.assistant-list-modern-btn:focus {
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5),
              0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

/* Chat button - Primary blue with gradient */
.btn-outline-primary.assistant-list-modern-btn {
  background: linear-gradient(135deg, #0066ff, #0052cc) !important;
  color: white !important;
  border: none !important;
}

.btn-outline-primary.assistant-list-modern-btn:hover {
  background: linear-gradient(135deg, #0077ff, #0066dd) !important;
}

/* Settings button - Dark gray with subtle gradient */
.btn-outline-secondary.assistant-list-modern-btn {
  background: linear-gradient(135deg, #4a5568, #2d3748) !important;
  color: white !important;
  border: none !important;
}

.btn-outline-secondary.assistant-list-modern-btn:hover {
  background: linear-gradient(135deg, #4a5568, #1a202c) !important;
}

/* Analytics button - Info blue with gradient */
.btn-outline-info.assistant-list-modern-btn {
  background: linear-gradient(135deg, #3498db, #2980b9) !important;
  color: white !important;
  border: none !important;
}

.btn-outline-info.assistant-list-modern-btn:hover {
  background: linear-gradient(135deg, #3498db, #2471a3) !important;
}

/* Public chat button - Success green with gradient */
.btn-outline-success.assistant-list-modern-btn {
  background: linear-gradient(135deg, #48bb78, #38a169) !important;
  color: white !important;
  border: none !important;
}

.btn-outline-success.assistant-list-modern-btn:hover {
  background: linear-gradient(135deg, #48bb78, #2f855a) !important;
}

/* Rate button - Warning yellow/orange with gradient */
.btn-outline-warning.assistant-list-modern-btn {
  background: linear-gradient(135deg, #f6ad55, #ed8936) !important;
  color: white !important;
  border: none !important;
}

.btn-outline-warning.assistant-list-modern-btn:hover {
  background: linear-gradient(135deg, #f6ad55, #dd6b20) !important;
}

/* Delete button - Danger red with gradient */
.btn-outline-danger.assistant-list-modern-btn {
  background: linear-gradient(135deg, #f56565, #e53e3e) !important;
  color: white !important;
  border: none !important;
}

.btn-outline-danger.assistant-list-modern-btn:hover {
  background: linear-gradient(135deg, #f56565, #c53030) !important;
}

/* Icon styling within buttons */
.assistant-list-modern-btn i {
  font-size: 1rem !important;
  margin: 0 !important;
  padding: 0 !important;
  line-height: 1 !important;
  display: block !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* Add folder button styling */
.js-trigger-create-folder-modal.assistant-list-modern-btn {
  background: linear-gradient(135deg, #6b46c1, #553c9a) !important;
  color: white !important;
  border: none !important;
  padding: 0.5rem 1rem !important;
  font-size: 0.875rem !important;
}

.js-trigger-create-folder-modal.assistant-list-modern-btn:hover {
  background: linear-gradient(135deg, #6b46c1, #44337a) !important;
}

/* Create new assistant button styling */
.btn-primary.assistant-list-modern-btn {
  background: linear-gradient(135deg, #3182ce, #2b6cb0) !important;
  color: white !important;
  border: none !important;
  padding: 0.75rem 1.5rem !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  letter-spacing: 0.03em !important;
}

.btn-primary.assistant-list-modern-btn:hover {
  background: linear-gradient(135deg, #3182ce, #2c5282) !important;
}

/* Filter buttons styling */
.btn-primary.assistant-list-modern-btn[type="submit"] {
  background: linear-gradient(135deg, #4299e1, #3182ce) !important;
  padding: 0.5rem 1rem !important;
}

.btn-outline-secondary.assistant-list-modern-btn[href*="list"] {
  background: linear-gradient(135deg, #718096, #4a5568) !important;
  padding: 0.5rem 1rem !important;
}

/* Dark mode adjustments */
[data-theme="dark"] .assistant-list-modern-btn {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2),
              0 1px 3px rgba(0, 0, 0, 0.15),
              inset 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
}

[data-theme="dark"] .assistant-list-modern-btn:hover {
  box-shadow: 0 7px 14px rgba(0, 0, 0, 0.25),
              0 3px 6px rgba(0, 0, 0, 0.2),
              inset 0 0 0 1px rgba(255, 255, 255, 0.1),
              0 0 15px rgba(66, 153, 225, 0.4) !important;
}

/* Dark mode specific button colors */
[data-theme="dark"] .btn-outline-primary.assistant-list-modern-btn {
  background: linear-gradient(135deg, #0077ff, #0055dd) !important;
}

[data-theme="dark"] .btn-outline-secondary.assistant-list-modern-btn {
  background: linear-gradient(135deg, #4a5568, #2d3748) !important;
}

[data-theme="dark"] .btn-outline-info.assistant-list-modern-btn {
  background: linear-gradient(135deg, #38b2ac, #2c7a7b) !important;
}

[data-theme="dark"] .btn-outline-success.assistant-list-modern-btn {
  background: linear-gradient(135deg, #48bb78, #2f855a) !important;
}

[data-theme="dark"] .btn-outline-warning.assistant-list-modern-btn {
  background: linear-gradient(135deg, #ed8936, #c05621) !important;
}

[data-theme="dark"] .btn-outline-danger.assistant-list-modern-btn {
  background: linear-gradient(135deg, #f56565, #c53030) !important;
}

/* Enhanced glow effects for dark mode */
[data-theme="dark"] .btn-outline-primary.assistant-list-modern-btn:hover {
  box-shadow: 0 7px 14px rgba(0, 0, 0, 0.25),
              0 3px 6px rgba(0, 0, 0, 0.2),
              inset 0 0 0 1px rgba(255, 255, 255, 0.1),
              0 0 15px rgba(0, 119, 255, 0.5) !important;
}

[data-theme="dark"] .btn-outline-danger.assistant-list-modern-btn:hover {
  box-shadow: 0 7px 14px rgba(0, 0, 0, 0.25),
              0 3px 6px rgba(0, 0, 0, 0.2),
              inset 0 0 0 1px rgba(255, 255, 255, 0.1),
              0 0 15px rgba(245, 101, 101, 0.5) !important;
}

/* Button group styling */
.btn-group .assistant-list-modern-btn {
  border-radius: 0 !important;
  margin: 0 1px !important;
}

.btn-group .assistant-list-modern-btn:first-child {
  border-top-left-radius: 8px !important;
  border-bottom-left-radius: 8px !important;
}

.btn-group .assistant-list-modern-btn:last-child {
  border-top-right-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
}

/* Dropdown button styling */
.dropdown .assistant-list-modern-btn.dropdown-toggle {
  padding-right: 1rem !important;
}

.dropdown .assistant-list-modern-btn.dropdown-toggle::after {
  margin-left: 0.5rem !important;
}

/* Dropdown menu styling */
.dropdown-menu {
  border-radius: 8px !important;
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1),
              0 4px 6px rgba(0, 0, 0, 0.05) !important;
  border: none !important;
  padding: 0.5rem 0 !important;
  margin-top: 0.5rem !important;
}

.dropdown-item {
  padding: 0.5rem 1rem !important;
  transition: all 0.2s ease !important;
}

.dropdown-item:hover {
  background-color: rgba(66, 153, 225, 0.1) !important;
}

/* Add a subtle glow effect to buttons */
.assistant-list-modern-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.assistant-list-modern-btn:hover::before {
  opacity: 1;
}

/* Enhanced 3D effect for buttons */
.assistant-list-modern-btn {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Add subtle text shadow for better readability */
.assistant-list-modern-btn {
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

/* Add glass morphism effect */
.assistant-list-modern-btn {
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

/* Add subtle border glow on hover */
.assistant-list-modern-btn:hover {
  box-shadow: 0 7px 14px rgba(0, 0, 0, 0.15),
              0 3px 6px rgba(0, 0, 0, 0.1),
              inset 0 0 0 1px rgba(255, 255, 255, 0.2),
              0 0 15px rgba(66, 153, 225, 0.3) !important;
}
