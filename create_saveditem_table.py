import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

print("Starting SavedItem table creation script...")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

# SQL to create the directory_saveditem table
saveditem_sql = """
CREATE TABLE IF NOT EXISTS "directory_saveditem" (
    "id" serial NOT NULL PRIMARY KEY,
    "created_at" timestamp with time zone NOT NULL,
    "item_type" varchar(20) NOT NULL,
    "assistant_id" integer NULL REFERENCES "assistants_assistant" ("id") DEFERRABLE INITIALLY DEFERRED,
    "company_id" integer NULL REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED,
    "folder_id" integer NULL REFERENCES "directory_favoritefolder" ("id") <PERSON>F<PERSON><PERSON>BLE INITIALLY DEFERRED,
    "user_id" integer NOT NULL REFERENCES "auth_user" ("id") <PERSON><PERSON><PERSON>RABLE INITIALLY DEFERRED,
    CONSTRAINT "directory_saveditem_user_id_assistant_id_unique" UNIQUE ("user_id", "assistant_id"),
    CONSTRAINT "directory_saveditem_user_id_company_id_unique" UNIQUE ("user_id", "company_id")
);
"""

# SQL to create the directory_favoritefolder table (in case it doesn't exist)
favoritefolder_sql = """
CREATE TABLE IF NOT EXISTS "directory_favoritefolder" (
    "id" serial NOT NULL PRIMARY KEY,
    "name" varchar(100) NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "updated_at" timestamp with time zone NOT NULL,
    "user_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# Connect to the database
try:
    print("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(**db_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    
    # Create the directory_favoritefolder table first (since it's referenced by directory_saveditem)
    print("Creating directory_favoritefolder table...")
    cursor.execute(favoritefolder_sql)
    print("FavoriteFolder table created successfully!")
    
    # Create the directory_saveditem table
    print("Creating directory_saveditem table...")
    cursor.execute(saveditem_sql)
    print("SavedItem table created successfully!")
    
    # Close the connection
    cursor.close()
    conn.close()
    print("Database connection closed.")
    
except Exception as e:
    print(f"Error: {e}")
