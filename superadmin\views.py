from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy
from django.views.generic import TemplateView, ListView, View
from django.contrib.auth.mixins import UserPassesTestMixin
from django.contrib.auth.decorators import user_passes_test
from django.utils.decorators import method_decorator
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType
import json
import sys
import django
from datetime import datetime, timedelta, time
from django.conf import settings
from django.http import JsonResponse
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.db.models import Q
from django.db import connection
from guardian.shortcuts import assign_perm, get_perms


from accounts.models import Company
from accounts.permissions import OWNER_PERMS_COMPANY
from assistants.models import Assistant

# Decorator for checking superuser status
superuser_required = method_decorator(user_passes_test(lambda u: u.is_superuser), name='dispatch')

@superuser_required
class DashboardView(TemplateView):
    """
    Displays the main superadmin dashboard with system information and key metrics.
    """
    template_name = 'superadmin/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _("Superadmin Dashboard")

        # Pending approvals and changes
        context['pending_company_approvals'] = Company.objects.filter(is_active=False, entity_type='company').count()
        context['pending_community_approvals'] = Company.objects.filter(is_active=False, entity_type='community').count()
        context['pending_company_tier_changes'] = Company.objects.filter(tier_change_pending=True).count()
        context['pending_assistant_tier_changes'] = Assistant.objects.filter(tier_change_pending=True).count()
        context['community_assistants_count'] = Assistant.objects.filter(assistant_type=Assistant.TYPE_COMMUNITY).count()

        # Add missing featured request counts
        context['pending_company_featured_requests'] = Company.objects.filter(featured_request_pending=True).count()
        context['pending_assistant_featured_requests'] = Assistant.objects.filter(featured_request_pending=True).count()

        # Add total pending notifications count for easy reference
        context['total_pending_notifications'] = (
            context['pending_company_approvals'] +
            context['pending_community_approvals'] +
            context['pending_company_tier_changes'] +
            context['pending_assistant_tier_changes'] +
            context['pending_company_featured_requests'] +
            context['pending_assistant_featured_requests']
        )

        # System information
        context['django_version'] = django.get_version()
        context['python_version'] = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"

        # Database information
        db_engine = connection.vendor
        if db_engine == 'sqlite':
            db_name = 'SQLite'
        elif db_engine == 'postgresql':
            db_name = 'PostgreSQL'
        elif db_engine == 'mysql':
            db_name = 'MySQL'
        else:
            db_name = db_engine.capitalize()
        context['database_engine'] = db_name

        # Total counts
        User = get_user_model()
        context['total_companies'] = Company.objects.count()
        context['total_assistants'] = Assistant.objects.count()
        context['total_users'] = User.objects.count()

        return context

@superuser_required
class CompanyListView(ListView):
    """
    Displays a list of all companies with filtering options.
    """
    model = Company
    template_name = 'superadmin/company_list.html'
    context_object_name = 'companies'
    paginate_by = 25 # Optional: Add pagination

    def get_queryset(self):
        queryset = super().get_queryset().select_related('owner').order_by('-created_at')

        # Filtering & Searching
        status = self.request.GET.get('status')
        tier_pending = self.request.GET.get('tier_pending')
        featured = self.request.GET.get('featured') # Get featured filter
        featured_pending = self.request.GET.get('featured_pending') # Get new filter
        entity_type = self.request.GET.get('entity_type', 'company') # Default to company if not specified
        query = self.request.GET.get('q') # Get search query

        # Always filter by entity_type (default to 'company' if not specified)
        queryset = queryset.filter(entity_type=entity_type)

        if query:
            # Search by company name OR owner username
            queryset = queryset.filter(
                Q(name__icontains=query) | Q(owner__username__icontains=query)
            )

        if status == 'inactive':
            queryset = queryset.filter(is_active=False)
        elif status == 'active':
             queryset = queryset.filter(is_active=True)

        if entity_type == 'company':
            queryset = queryset.filter(entity_type='company')
        elif entity_type == 'community':
            queryset = queryset.filter(entity_type='community')

        if tier_pending == 'true':
            queryset = queryset.filter(tier_change_pending=True)

        if featured == 'true':
            queryset = queryset.filter(is_featured=True)
        elif featured == 'false':
            queryset = queryset.filter(is_featured=False)

        if featured_pending == 'true': # Apply new filter
            queryset = queryset.filter(featured_request_pending=True)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get entity type (default to 'company')
        entity_type = self.request.GET.get('entity_type', 'company')

        # Set title and entity_type_display based on entity_type
        if entity_type == 'community':
            context['title'] = _("Manage Communities")
            context['entity_type_display'] = _("Communities")
        else:
            context['title'] = _("Manage Companies")
            context['entity_type_display'] = _("Companies")

        # Pass filter values back to template for display/form persistence
        context['filter_status'] = self.request.GET.get('status', '')
        context['filter_tier_pending'] = self.request.GET.get('tier_pending', '')
        context['filter_featured'] = self.request.GET.get('featured', '') # Pass featured filter back
        context['filter_featured_pending'] = self.request.GET.get('featured_pending', '') # Pass new filter back
        context['filter_entity_type'] = entity_type # Pass entity_type filter back
        context['filter_q'] = self.request.GET.get('q', '') # Pass search query back
        return context

@superuser_required
class CompanyActivateToggleView(View):
    """
    Toggles the is_active status of a Company via POST request.
    """
    http_method_names = ['post'] # Only allow POST

    def post(self, request, *args, **kwargs):
        company = get_object_or_404(Company, pk=kwargs.get('pk'))
        company.is_active = not company.is_active
        company.save(update_fields=['is_active'])

        if company.is_active:
            messages.success(request, _("Company '{name}' activated successfully.").format(name=company.name))
        else:
            messages.warning(request, _("Company '{name}' deactivated.").format(name=company.name))

        # Redirect back to the company list, potentially preserving filters
        redirect_url = reverse_lazy('superadmin:company_list')
        query_params = request.GET.urlencode() # Get original query params
        if query_params:
            redirect_url += f'?{query_params}'

        return redirect(redirect_url)

@superuser_required
class CompanyRejectFeaturedView(View):
    """
    Rejects a pending featured request for a Company via POST.
    """
    http_method_names = ['post']

    def post(self, request, *args, **kwargs):
        company = get_object_or_404(Company, pk=kwargs.get('pk'), featured_request_pending=True)
        company.featured_request_pending = False
        company.requested_featured_duration = None # Clear requested duration
        company.save(update_fields=['featured_request_pending', 'requested_featured_duration'])

        messages.warning(request, _("Featured request for company '{name}' rejected.").format(name=company.name))

        redirect_url = reverse_lazy('superadmin:company_list')
        query_params = request.GET.urlencode()
        if query_params:
            redirect_url += f'?{query_params}'
        return redirect(redirect_url)

@superuser_required
class CompanyApproveTierView(View): # Corrected class name
    """
    Approves a pending tier change request for a Company via POST.
    """
    http_method_names = ['post']

    def post(self, request, *args, **kwargs):
        company = get_object_or_404(Company, pk=kwargs.get('pk'), tier_change_pending=True)
        approved_tier = company.requested_tier
        duration = company.requested_tier_duration

        expiry_date = None
        if duration:
            now = timezone.now()
            if duration == Company.DURATION_MONTHLY:
                expiry_date = now + timedelta(days=30) # Approximate
            elif duration == Company.DURATION_QUARTERLY:
                expiry_date = now + timedelta(days=90) # Approximate
            elif duration == Company.DURATION_ANNUALLY:
                expiry_date = now + timedelta(days=365) # Approximate

        company.tier = approved_tier
        company.tier_expiry_date = expiry_date
        company.requested_tier = None
        company.tier_change_pending = False
        company.requested_tier_duration = None # Clear requested duration
        company.save(update_fields=['tier', 'requested_tier', 'tier_change_pending', 'tier_expiry_date', 'requested_tier_duration'])

        expiry_msg = f" until {expiry_date.strftime('%Y-%m-%d')}" if expiry_date else ""
        messages.success(request, _("Tier change for '{name}' to '{tier}' approved{expiry}.").format(name=company.name, tier=company.get_tier_display(), expiry=expiry_msg))

        redirect_url = reverse_lazy('superadmin:company_list')
        query_params = request.GET.urlencode()
        if query_params:
            redirect_url += f'?{query_params}'
        return redirect(redirect_url)

@superuser_required
class CompanyApproveFeaturedView(View):
    """
    Approves a pending featured request for a Company via POST.
    """
    http_method_names = ['post']

    def post(self, request, *args, **kwargs):
        company = get_object_or_404(Company, pk=kwargs.get('pk'), featured_request_pending=True)
        duration = company.requested_featured_duration

        expiry_date = None
        if duration:
            now = timezone.now() # Need timezone import
            if duration == Company.DURATION_MONTHLY:
                expiry_date = now + timedelta(days=30)
            elif duration == Company.DURATION_QUARTERLY:
                expiry_date = now + timedelta(days=90)
            elif duration == Company.DURATION_ANNUALLY:
                expiry_date = now + timedelta(days=365)

        company.is_featured = True
        company.featured_expiry_date = expiry_date
        company.featured_request_pending = False
        company.requested_featured_duration = None # Clear requested duration
        company.save(update_fields=['is_featured', 'featured_request_pending', 'featured_expiry_date', 'requested_featured_duration'])

        expiry_msg = f" until {expiry_date.strftime('%Y-%m-%d')}" if expiry_date else ""
        messages.success(request, _("Company '{name}' marked as featured{expiry}.").format(name=company.name, expiry=expiry_msg))

        redirect_url = reverse_lazy('superadmin:company_list')
        query_params = request.GET.urlencode()
        if query_params:
            redirect_url += f'?{query_params}'
        return redirect(redirect_url)

@superuser_required
class AssistantApproveFeaturedView(View):
    """
    Approves a pending featured request for an Assistant via POST.
    """
    http_method_names = ['post']

    def post(self, request, *args, **kwargs):
        assistant = get_object_or_404(Assistant, pk=kwargs.get('pk'), featured_request_pending=True)
        duration = assistant.requested_featured_duration

        expiry_date = None
        if duration:
            now = timezone.now() # Need timezone import
            if duration == Assistant.DURATION_MONTHLY:
                expiry_date = now + timedelta(days=30)
            elif duration == Assistant.DURATION_QUARTERLY:
                expiry_date = now + timedelta(days=90)
            elif duration == Assistant.DURATION_ANNUALLY:
                expiry_date = now + timedelta(days=365)

        assistant.is_featured = True
        assistant.featured_expiry_date = expiry_date
        assistant.featured_request_pending = False
        assistant.requested_featured_duration = None # Clear requested duration
        assistant.save(update_fields=['is_featured', 'featured_request_pending', 'featured_expiry_date', 'requested_featured_duration'])

        expiry_msg = f" until {expiry_date.strftime('%Y-%m-%d')}" if expiry_date else ""
        messages.success(request, _("Assistant '{name}' marked as featured{expiry}.").format(name=assistant.name, expiry=expiry_msg))

        redirect_url = reverse_lazy('superadmin:assistant_list')
        query_params = request.GET.urlencode()
        if query_params:
            redirect_url += f'?{query_params}'
        return redirect(redirect_url)


@superuser_required
class CompanyRejectTierView(View):
    """
    Rejects a pending tier change request for a Company via POST.
    """
    http_method_names = ['post']

    def post(self, request, *args, **kwargs):
        company = get_object_or_404(Company, pk=kwargs.get('pk'), tier_change_pending=True)
        rejected_tier = company.get_requested_tier_display() # Get display name before clearing
        company.requested_tier = None
        company.tier_change_pending = False
        company.save(update_fields=['requested_tier', 'tier_change_pending'])

        messages.warning(request, _("Tier change request for '{name}' (to '{tier}') rejected.").format(name=company.name, tier=rejected_tier))

        redirect_url = reverse_lazy('superadmin:company_list')
        query_params = request.GET.urlencode()
        if query_params:
            redirect_url += f'?{query_params}'
        return redirect(redirect_url)

@superuser_required
class CompanyFeaturedToggleView(View):
    """
    Toggles the is_featured status of a Company via POST request.
    """
    http_method_names = ['post']

    def post(self, request, *args, **kwargs):
        company = get_object_or_404(Company, pk=kwargs.get('pk'))
        company.is_featured = not company.is_featured
        company.save(update_fields=['is_featured'])

        if company.is_featured:
            messages.success(request, _("Company '{name}' marked as featured.").format(name=company.name))
        else:
            messages.info(request, _("Company '{name}' removed from featured.").format(name=company.name))

        redirect_url = reverse_lazy('superadmin:company_list')
        query_params = request.GET.urlencode()
        if query_params:
            redirect_url += f'?{query_params}'
        return redirect(redirect_url)

@superuser_required
class CompanyImpersonateView(View):
    """
    Initiates impersonation of a company owner (user) via POST request.
    Redirects to the django-impersonate start URL.

    Before impersonation, ensures the user has all necessary permissions.
    """
    http_method_names = ['post']

    def post(self, request, *args, **kwargs):
        user_pk_to_impersonate = kwargs.get('user_pk')

        # Get the user to impersonate
        User = get_user_model()
        try:
            user_to_impersonate = User.objects.get(pk=user_pk_to_impersonate)
        except User.DoesNotExist:
            messages.error(request, _("User not found."))
            return redirect('superadmin:company_list')

        # Check if the user is a company owner
        from accounts.models import Company
        from accounts.permissions import OWNER_PERMS_COMPANY
        from guardian.shortcuts import assign_perm, get_perms

        # Get companies owned by this user
        companies = Company.objects.filter(owner=user_to_impersonate)

        if not companies.exists():
            messages.warning(request, _("User is not a company owner. Impersonation may not work as expected."))
        else:
            # Ensure the user has all necessary permissions for their companies
            for company in companies:
                # Get current permissions
                current_perms = get_perms(user_to_impersonate, company)

                # Assign any missing permissions
                for perm_string in OWNER_PERMS_COMPANY:
                    try:
                        # Split the permission string into app_label and codename
                        app_label, codename = perm_string.split('.')

                        # Handle permissions differently based on app_label
                        if app_label == 'accounts':
                            # For accounts app, use the Company model
                            model_name = 'company'
                        elif app_label == 'assistants':
                            # For assistants app, use the Assistant model
                            model_name = 'assistant'
                        else:
                            # For other apps, use a default model
                            model_name = 'company'

                        try:
                            # Get the content type for the app and model
                            content_type = ContentType.objects.get(app_label=app_label, model=model_name)

                            # Check if the permission exists
                            try:
                                perm_obj = Permission.objects.get(content_type=content_type, codename=codename)

                                # For accounts permissions, assign to the company
                                if app_label == 'accounts':
                                    if codename not in current_perms:
                                        assign_perm(codename, user_to_impersonate, company)

                                # For assistants permissions, we need to assign them to all assistants owned by the company
                                elif app_label == 'assistants':
                                    from assistants.models import Assistant
                                    assistants = Assistant.objects.filter(company=company)
                                    for assistant in assistants:
                                        try:
                                            assign_perm(codename, user_to_impersonate, assistant)
                                        except Exception as e:
                                            messages.warning(request, _(f"Error assigning assistant permission '{codename}' for assistant '{assistant.name}': {e}"))

                                    if not assistants.exists():
                                        messages.info(request, _(f"No assistants found for company '{company.name}'"))

                            except Permission.DoesNotExist:
                                messages.warning(request, _(f"Permission '{perm_string}' does not exist in database"))
                                continue

                        except ContentType.DoesNotExist:
                            messages.warning(request, _(f"Content type for app '{app_label}' and model '{model_name}' does not exist"))
                            continue

                    except Exception as e:
                        messages.warning(request, _(f"Error assigning permission '{perm_string}': {e}"))

            # Check if any account permissions are missing
            account_perms = [p.split('.')[1] for p in OWNER_PERMS_COMPANY if p.startswith('accounts.')]
            missing_account_perms = set(account_perms) - set(current_perms)

            if missing_account_perms:
                messages.warning(request, _("Some account permissions could not be assigned for user {username}.").format(username=user_to_impersonate.username))
            else:
                messages.success(request, _("Owner permissions verified for user {username}.").format(username=user_to_impersonate.username))

        # Redirect to django-impersonate
        impersonate_url = reverse_lazy('impersonate-start', args=[user_pk_to_impersonate])
        return redirect(impersonate_url)

@superuser_required
class AssistantListView(ListView):
    """
    Displays a list of all assistants platform-wide with filtering options.
    """
    model = Assistant
    template_name = 'superadmin/assistant_list.html'
    context_object_name = 'assistants'
    paginate_by = 25

    def get_queryset(self):
        # Exclude community assistants
        queryset = super().get_queryset().exclude(assistant_type=Assistant.TYPE_COMMUNITY).select_related('company', 'created_by').order_by('-created_at')

        # Filtering
        company_pk = self.request.GET.get('company')
        status = self.request.GET.get('status')
        tier_pending = self.request.GET.get('tier_pending')
        featured = self.request.GET.get('featured')
        featured_pending = self.request.GET.get('featured_pending') # Get new filter
        query = self.request.GET.get('q') # Get search query

        if query:
            # Search by assistant name
            queryset = queryset.filter(name__icontains=query)

        if company_pk:
             queryset = queryset.filter(company__pk=company_pk)

        if status == 'inactive':
            queryset = queryset.filter(is_active=False)
        elif status == 'active':
             queryset = queryset.filter(is_active=True)

        if tier_pending == 'true':
            queryset = queryset.filter(tier_change_pending=True)

        if featured == 'true':
            queryset = queryset.filter(is_featured=True)
        elif featured == 'false':
            queryset = queryset.filter(is_featured=False)

        if featured_pending == 'true': # Apply new filter
            queryset = queryset.filter(featured_request_pending=True)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _("Manage Assistants")
        # Pass filter values back
        context['filter_company'] = self.request.GET.get('company', '')
        context['filter_status'] = self.request.GET.get('status', '')
        context['filter_tier_pending'] = self.request.GET.get('tier_pending', '')
        context['filter_featured'] = self.request.GET.get('featured', '')
        context['filter_featured_pending'] = self.request.GET.get('featured_pending', '') # Pass new filter back
        context['filter_q'] = self.request.GET.get('q', '') # Pass search query back
        # Provide companies for dropdown filter
        context['companies'] = Company.objects.order_by('name')
        return context

@superuser_required
class AssistantActivateToggleView(View):
    """
    Toggles the is_active status of an Assistant via POST request.
    """
    http_method_names = ['post']

    def post(self, request, *args, **kwargs):
        assistant = get_object_or_404(Assistant, pk=kwargs.get('pk'))
        assistant.is_active = not assistant.is_active
        assistant.save(update_fields=['is_active'])

        if assistant.is_active:
            messages.success(request, _("Assistant '{name}' activated successfully.").format(name=assistant.name))
        else:
            messages.warning(request, _("Assistant '{name}' deactivated.").format(name=assistant.name))

        # Redirect to the appropriate list based on assistant type
        if assistant.assistant_type == Assistant.TYPE_COMMUNITY:
            redirect_url = reverse_lazy('superadmin:community_assistant_list')
        else:
            redirect_url = reverse_lazy('superadmin:assistant_list')

        query_params = request.GET.urlencode()
        if query_params:
            redirect_url += f'?{query_params}'
        return redirect(redirect_url)

@superuser_required
class AssistantApproveTierView(View):
    """
    Approves a pending tier change request for an Assistant via POST.
    """
    http_method_names = ['post']

    def post(self, request, *args, **kwargs):
        assistant = get_object_or_404(Assistant, pk=kwargs.get('pk'), tier_change_pending=True)
        approved_tier = assistant.requested_tier
        duration = assistant.requested_tier_duration

        expiry_date = None
        if duration:
            now = timezone.now() # Need timezone import
            if duration == Assistant.DURATION_MONTHLY:
                expiry_date = now + timedelta(days=30) # Approximate
            elif duration == Assistant.DURATION_QUARTERLY:
                expiry_date = now + timedelta(days=90) # Approximate
            elif duration == Assistant.DURATION_ANNUALLY:
                expiry_date = now + timedelta(days=365) # Approximate

        assistant.tier = approved_tier
        assistant.tier_expiry_date = expiry_date
        assistant.requested_tier = None
        assistant.tier_change_pending = False
        assistant.requested_tier_duration = None # Clear requested duration
        assistant.save(update_fields=['tier', 'requested_tier', 'tier_change_pending', 'tier_expiry_date', 'requested_tier_duration'])

        expiry_msg = f" until {expiry_date.strftime('%Y-%m-%d')}" if expiry_date else ""
        messages.success(request, _("Tier change for assistant '{name}' to '{tier}' approved{expiry}.").format(name=assistant.name, tier=assistant.get_tier_display(), expiry=expiry_msg))

        # Redirect to the appropriate list based on assistant type
        if assistant.assistant_type == Assistant.TYPE_COMMUNITY:
            redirect_url = reverse_lazy('superadmin:community_assistant_list')
        else:
            redirect_url = reverse_lazy('superadmin:assistant_list')

        query_params = request.GET.urlencode()
        if query_params:
            redirect_url += f'?{query_params}'
        return redirect(redirect_url)

@superuser_required
class AssistantRejectFeaturedView(View):
    """
    Rejects a pending featured request for an Assistant via POST.
    """
    http_method_names = ['post']

    def post(self, request, *args, **kwargs):
        assistant = get_object_or_404(Assistant, pk=kwargs.get('pk'), featured_request_pending=True)
        assistant.featured_request_pending = False
        assistant.requested_featured_duration = None # Clear requested duration
        assistant.save(update_fields=['featured_request_pending', 'requested_featured_duration'])

        messages.warning(request, _("Featured request for assistant '{name}' rejected.").format(name=assistant.name))

        # Redirect to the appropriate list based on assistant type
        if assistant.assistant_type == Assistant.TYPE_COMMUNITY:
            redirect_url = reverse_lazy('superadmin:community_assistant_list')
        else:
            redirect_url = reverse_lazy('superadmin:assistant_list')

        query_params = request.GET.urlencode()
        if query_params:
            redirect_url += f'?{query_params}'
        return redirect(redirect_url)

@superuser_required
class AssistantRejectTierView(View):
    """
    Rejects a pending tier change request for an Assistant via POST.
    """
    http_method_names = ['post']

    def post(self, request, *args, **kwargs):
        assistant = get_object_or_404(Assistant, pk=kwargs.get('pk'), tier_change_pending=True)
        rejected_tier = assistant.get_requested_tier_display() # Get display name before clearing
        assistant.requested_tier = None
        assistant.tier_change_pending = False
        assistant.requested_tier_duration = None # Clear requested duration
        assistant.save(update_fields=['requested_tier', 'tier_change_pending', 'requested_tier_duration'])

        messages.warning(request, _("Tier change request for assistant '{name}' (to '{tier}') rejected.").format(name=assistant.name, tier=rejected_tier))

        # Redirect to the appropriate list based on assistant type
        if assistant.assistant_type == Assistant.TYPE_COMMUNITY:
            redirect_url = reverse_lazy('superadmin:community_assistant_list')
        else:
            redirect_url = reverse_lazy('superadmin:assistant_list')

        query_params = request.GET.urlencode()
        if query_params:
            redirect_url += f'?{query_params}'
        return redirect(redirect_url)

@superuser_required
class AssistantFeaturedToggleView(View):
    """
    Toggles the is_featured status of an Assistant via POST request.
    """
    http_method_names = ['post']

    def post(self, request, *args, **kwargs):
        assistant = get_object_or_404(Assistant, pk=kwargs.get('pk'))
        assistant.is_featured = not assistant.is_featured
        assistant.save(update_fields=['is_featured'])

        if assistant.is_featured:
            messages.success(request, _("Assistant '{name}' marked as featured.").format(name=assistant.name))
        else:
            messages.info(request, _("Assistant '{name}' removed from featured.").format(name=assistant.name))

        # Redirect to the appropriate list based on assistant type
        if assistant.assistant_type == Assistant.TYPE_COMMUNITY:
            redirect_url = reverse_lazy('superadmin:community_assistant_list')
        else:
            redirect_url = reverse_lazy('superadmin:assistant_list')

        query_params = request.GET.urlencode()
        if query_params:
            redirect_url += f'?{query_params}'
        return redirect(redirect_url)

@superuser_required
class AssistantUpdateExpiryView(View):
    """
    Handles AJAX requests to update tier_expiry_date or featured_expiry_date
    for an Assistant.
    """
    http_method_names = ['post']

    def post(self, request, *args, **kwargs):
        assistant_pk = kwargs.get('pk')
        assistant = get_object_or_404(Assistant, pk=assistant_pk)

        try:
            data = json.loads(request.body)
            field_name = data.get('field')
            new_value_str = data.get('value') # Expects YYYY-MM-DD or empty string

            if field_name not in ['tier_expiry_date', 'featured_expiry_date']:
                return JsonResponse({'status': 'error', 'message': 'Invalid field name.'}, status=400)

            new_date = None
            if new_value_str:
                try:
                    # Attempt to parse the date string
                    new_date = datetime.strptime(new_value_str, '%Y-%m-%d').date() # Use datetime.strptime
                    # Make it timezone-aware if your project uses timezone support
                    # This assumes settings.TIME_ZONE is set correctly
                    # If using USE_TZ=False, you might not need this part
                    if settings.USE_TZ:
                         # For simplicity, setting time to midnight in the current time zone
                         # Adjust if specific time logic is needed
                         new_datetime = timezone.make_aware(datetime.combine(new_date, datetime.min.time())) # Use datetime.min.time()
                         new_date = new_datetime # Store the datetime object
                    else:
                         # If not using TZ, store as naive date/datetime as appropriate for the model field
                         # Assuming DateTimeField, combine with min time
                         new_date = datetime.combine(new_date, datetime.min.time()) # Use datetime.min.time()

                except ValueError:
                    return JsonResponse({'status': 'error', 'message': 'Invalid date format. Use YYYY-MM-DD.'}, status=400)

            # Update the specific field
            setattr(assistant, field_name, new_date)
            assistant.save(update_fields=[field_name])

            # Include the redirect URL in the response based on assistant type
            if assistant.assistant_type == Assistant.TYPE_COMMUNITY:
                redirect_url = reverse_lazy('superadmin:community_assistant_list')
            else:
                redirect_url = reverse_lazy('superadmin:assistant_list')

            return JsonResponse({
                'status': 'success',
                'message': f'{field_name} updated successfully.',
                'redirect_url': str(redirect_url)
            })

        except json.JSONDecodeError:
            return JsonResponse({'status': 'error', 'message': 'Invalid JSON data.'}, status=400)
        except Exception as e:
            # Log the error for debugging
            print(f"Error in AssistantUpdateExpiryView: {e}")
            return JsonResponse({'status': 'error', 'message': 'An unexpected server error occurred.'}, status=500)

@superuser_required
class CompanyUpdateExpiryView(View):
    """
    Handles AJAX requests to update tier_expiry_date or featured_expiry_date
    for a Company.
    """
    http_method_names = ['post']

    def post(self, request, *args, **kwargs):
        company_pk = kwargs.get('pk')
        company = get_object_or_404(Company, pk=company_pk)

        try:
            data = json.loads(request.body)
            field_name = data.get('field')
            new_value_str = data.get('value') # Expects YYYY-MM-DD or empty string

            if field_name not in ['tier_expiry_date', 'featured_expiry_date']:
                return JsonResponse({'status': 'error', 'message': 'Invalid field name.'}, status=400)

            new_date = None
            if new_value_str:
                try:
                    # Attempt to parse the date string
                    new_date = datetime.strptime(new_value_str, '%Y-%m-%d').date() # Use datetime.strptime
                    # Make it timezone-aware if your project uses timezone support
                    if settings.USE_TZ:
                         new_datetime = timezone.make_aware(datetime.combine(new_date, datetime.min.time())) # Use datetime.min.time()
                         new_date = new_datetime # Store the datetime object
                    else:
                         new_date = datetime.combine(new_date, datetime.min.time()) # Use datetime.min.time()

                except ValueError:
                    return JsonResponse({'status': 'error', 'message': 'Invalid date format. Use YYYY-MM-DD.'}, status=400)

            # Update the specific field
            setattr(company, field_name, new_date)
            company.save(update_fields=[field_name])

            return JsonResponse({'status': 'success', 'message': f'{field_name} updated successfully.'})

        except json.JSONDecodeError:
            return JsonResponse({'status': 'error', 'message': 'Invalid JSON data.'}, status=400)
        except Exception as e:
            # Log the error for debugging
            print(f"Error in CompanyUpdateExpiryView: {e}")
            return JsonResponse({'status': 'error', 'message': 'An unexpected server error occurred.'}, status=500)

@superuser_required
class CompanySetStandardTierView(View):
    """
    Sets a Company's tier back to Standard via POST request.
    """
    http_method_names = ['post']

    def post(self, request, *args, **kwargs):
        company = get_object_or_404(Company, pk=kwargs.get('pk'))

        # Only proceed if the tier is not already Standard
        if company.tier != Company.TIER_STANDARD:
            company.tier = Company.TIER_STANDARD
            company.tier_expiry_date = None
            company.requested_tier = None
            company.tier_change_pending = False
            company.requested_tier_duration = None
            company.save(update_fields=[
                'tier', 'tier_expiry_date', 'requested_tier',
                'tier_change_pending', 'requested_tier_duration'
            ])
            messages.success(request, _("Company '{name}' tier set to Standard.").format(name=company.name))
        else:
            messages.info(request, _("Company '{name}' is already Standard tier.").format(name=company.name))

        redirect_url = reverse_lazy('superadmin:company_list')
        query_params = request.GET.urlencode()
        if query_params:
            redirect_url += f'?{query_params}'
        return redirect(redirect_url)

@superuser_required
class AssistantSetStandardTierView(View):
    """
    Sets an Assistant's tier back to Standard via POST request.
    """
    http_method_names = ['post']

    def post(self, request, *args, **kwargs):
        assistant = get_object_or_404(Assistant, pk=kwargs.get('pk'))

        # Only proceed if the tier is not already Standard
        if assistant.tier != Assistant.TIER_STANDARD:
            assistant.tier = Assistant.TIER_STANDARD
            assistant.tier_expiry_date = None
            assistant.requested_tier = None
            assistant.tier_change_pending = False
            assistant.requested_tier_duration = None
            assistant.save(update_fields=[
                'tier', 'tier_expiry_date', 'requested_tier',
                'tier_change_pending', 'requested_tier_duration'
            ])
            messages.success(request, _("Assistant '{name}' tier set to Standard.").format(name=assistant.name))
        else:
             messages.info(request, _("Assistant '{name}' is already Standard tier.").format(name=assistant.name))

        # Redirect to the appropriate list based on assistant type
        if assistant.assistant_type == Assistant.TYPE_COMMUNITY:
            redirect_url = reverse_lazy('superadmin:community_assistant_list')
        else:
            redirect_url = reverse_lazy('superadmin:assistant_list')

        query_params = request.GET.urlencode()
        if query_params:
            redirect_url += f'?{query_params}'
        return redirect(redirect_url)


# TODO: Modify accounts/views.py and assistants/views.py to handle tier requests

@superuser_required
class CommunityAssistantListView(ListView):
    """
    Displays a list of all community assistants platform-wide with filtering options.
    """
    model = Assistant
    template_name = 'superadmin/community_assistant_list.html'
    context_object_name = 'assistants'
    paginate_by = 25

    def get_queryset(self):
        # Only get community assistants
        queryset = super().get_queryset().filter(assistant_type=Assistant.TYPE_COMMUNITY)
        queryset = queryset.select_related('company', 'created_by', 'linked_company').order_by('-created_at')

        # Filtering
        company_pk = self.request.GET.get('company')
        linked_company_pk = self.request.GET.get('linked_company')
        status = self.request.GET.get('status')
        tier_pending = self.request.GET.get('tier_pending')
        featured = self.request.GET.get('featured')
        featured_pending = self.request.GET.get('featured_pending')
        query = self.request.GET.get('q')

        if query:
            # Search by assistant name, description, or company name
            queryset = queryset.filter(
                Q(name__icontains=query) |
                Q(description__icontains=query) |
                Q(company__name__icontains=query)
            )

        if company_pk:
            queryset = queryset.filter(company__pk=company_pk)

        if linked_company_pk:
            queryset = queryset.filter(linked_company__pk=linked_company_pk)

        if status == 'inactive':
            queryset = queryset.filter(is_active=False)
        elif status == 'active':
            queryset = queryset.filter(is_active=True)

        if tier_pending == 'true':
            queryset = queryset.filter(tier_change_pending=True)

        if featured == 'true':
            queryset = queryset.filter(is_featured=True)
        elif featured == 'false':
            queryset = queryset.filter(is_featured=False)

        if featured_pending == 'true':
            queryset = queryset.filter(featured_request_pending=True)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _("Manage Community Assistants")
        # Pass filter values back
        context['filter_company'] = self.request.GET.get('company', '')
        context['filter_linked_company'] = self.request.GET.get('linked_company', '')
        context['filter_status'] = self.request.GET.get('status', '')
        context['filter_tier_pending'] = self.request.GET.get('tier_pending', '')
        context['filter_featured'] = self.request.GET.get('featured', '')
        context['filter_featured_pending'] = self.request.GET.get('featured_pending', '')
        context['filter_q'] = self.request.GET.get('q', '')
        # Provide companies for dropdown filter
        context['companies'] = Company.objects.order_by('name')
        return context
