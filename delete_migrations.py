"""
<PERSON><PERSON><PERSON> to delete all migration files from the project.
This will remove all migration files except for __init__.py files.
"""
import os
import shutil

def delete_migrations():
    """Delete all migration files from the project."""
    # List of apps to clean migrations from
    apps = [
        "accounts",
        "assistants",
        "content",
        "directory",
        "site_settings",
        "superadmin",
    ]
    
    for app in apps:
        migrations_dir = os.path.join(app, "migrations")
        
        # Check if the migrations directory exists
        if os.path.exists(migrations_dir):
            print(f"Cleaning migrations from {app}...")
            
            # Get all files in the migrations directory
            for filename in os.listdir(migrations_dir):
                file_path = os.path.join(migrations_dir, filename)
                
                # Delete all Python files except __init__.py
                if filename.endswith('.py') and filename != '__init__.py':
                    try:
                        os.remove(file_path)
                        print(f"  Deleted: {file_path}")
                    except Exception as e:
                        print(f"  Error deleting {file_path}: {e}")
                
                # Delete __pycache__ directory if it exists
                if filename == '__pycache__':
                    try:
                        shutil.rmtree(file_path)
                        print(f"  Deleted: {file_path}")
                    except Exception as e:
                        print(f"  Error deleting {file_path}: {e}")
            
            # Create __init__.py if it doesn't exist
            init_file = os.path.join(migrations_dir, '__init__.py')
            if not os.path.exists(init_file):
                try:
                    with open(init_file, 'w') as f:
                        pass  # Create an empty file
                    print(f"  Created: {init_file}")
                except Exception as e:
                    print(f"  Error creating {init_file}: {e}")
        else:
            # Create migrations directory if it doesn't exist
            try:
                os.makedirs(migrations_dir)
                print(f"Created migrations directory for {app}")
                
                # Create __init__.py
                with open(os.path.join(migrations_dir, '__init__.py'), 'w') as f:
                    pass  # Create an empty file
                print(f"  Created: {os.path.join(migrations_dir, '__init__.py')}")
            except Exception as e:
                print(f"Error creating migrations directory for {app}: {e}")

    print("\nMigration files have been deleted.")
    print("Next steps:")
    print("1. Reset the migration history in the database")
    print("2. Create new migrations with 'python manage.py makemigrations'")
    print("3. Apply the new migrations with 'python manage.py migrate'")

if __name__ == "__main__":
    # Confirm before proceeding
    confirm = input("This will delete all migration files. Are you sure? (y/n): ")
    if confirm.lower() == 'y':
        delete_migrations()
    else:
        print("Operation cancelled.")
