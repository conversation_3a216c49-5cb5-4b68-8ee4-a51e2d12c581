# Permissions and Roles Summary

## Overview

The application uses Django's permission system along with django-guardian for object-level permissions. There are four main roles in the system:

1. **Owner**: The user who created the company
2. **Administrator**: Users with administrative privileges for a company
3. **Member**: Regular users of a company
4. **Viewer**: Users with limited view-only access to a company

## Role Permissions

### Owner Permissions

The owner has the highest level of permissions and can perform all actions within their company:

- **Company Management**:
  - Can change company settings (`accounts.change_company_settings`)
  - Can manage billing (`accounts.manage_billing`)
  - Can manage directory listing (`accounts.manage_directory_listing`)
  - Can manage members (`accounts.manage_members`)
  - Can manage invitations and registration links (`accounts.manage_invites_links`)
  - Can view company activity (`accounts.view_company_activity`)
  - Can delete the company (`accounts.delete_company_object`)

- **Assistant Management**:
  - Can manage company assistants (`accounts.manage_company_assistants`)
  - Can view, change, and delete assistants (`assistants.view_assistant`, `assistants.change_assistant`, `assistants.delete_assistant`)
  - Can view assistant usage and analytics (`assistants.view_assistant_usage`, `assistants.view_assistant_analytics`)

- **Folder Management**:
  - Can add, change, view, and delete assistant folders (`accounts.add_assistantfolder`, `accounts.change_assistantfolder`, `accounts.view_assistantfolder`, `accounts.delete_assistantfolder`)
  - Can manage folder access (`accounts.manage_folder_access`)

### Administrator Permissions

Administrators have extensive permissions but cannot delete the company:

- **Company Management**:
  - Can manage company assistants (`accounts.manage_company_assistants`)
  - Cannot change company settings or delete the company

- **Assistant Management**:
  - Can view, change, and delete assistants (`assistants.view_assistant`, `assistants.change_assistant`, `assistants.delete_assistant`)
  - Can view assistant usage and analytics (`assistants.view_assistant_usage`, `assistants.view_assistant_analytics`)

### Member Permissions

Members have permissions to work with assistants but cannot manage the company:

- **Company Management**:
  - Can manage company assistants (`accounts.manage_company_assistants`)
  - Cannot change company settings or delete the company

- **Assistant Management**:
  - Can view, change, and delete assistants (`assistants.view_assistant`, `assistants.change_assistant`, `assistants.delete_assistant`)
  - Can view assistant usage and analytics (`assistants.view_assistant_usage`, `assistants.view_assistant_analytics`)

### Viewer Permissions

Viewers have very limited permissions:

- **Company Management**:
  - No company management permissions

- **Assistant Management**:
  - No assistant management permissions
  - Cannot view, change, or delete assistants

## Permission Assignment

Permissions are assigned in several ways:

1. **Company Creation**: When a company is created, the owner is automatically assigned all owner permissions for that company.

2. **Assistant Creation**: When an assistant is created, permissions are assigned to the Company Administrators and Company Members groups.

3. **Group Membership**: Users are assigned to groups (Company Administrators, Company Members, Company Guests) which determine their role-based permissions.

## Issues and Recommendations

Based on the test results, there are a few issues with the permission system:

1. **Owner Assistant Permissions**: The owner doesn't automatically get direct permissions on assistants. This needs to be fixed by:
   - Modifying the assistant creation signal to assign permissions to the company owner
   - Adding a management command to ensure all owners have the correct permissions on existing assistants

2. **Permission Warnings**: There are warnings about missing permissions during owner assignment. This indicates that some permissions defined in `OWNER_PERMS_COMPANY` don't exist in the database. This should be fixed by:
   - Ensuring all permissions are properly created during migrations
   - Updating the `OWNER_PERMS_COMPANY` list to match the actual permissions in the database

3. **Viewer Permissions**: Viewers currently have no permissions at all. If they should be able to view assistants but not modify them, the following permissions should be added:
   - `assistants.view_assistant` for viewing assistants
   - `assistants.view_assistant_usage` and `assistants.view_assistant_analytics` if they should see usage and analytics

## Conclusion

The permission system is working correctly for the most part, with clear separation between the different roles. The main issue is with the owner not automatically getting direct permissions on assistants, which can be fixed by modifying the assistant creation signal.
