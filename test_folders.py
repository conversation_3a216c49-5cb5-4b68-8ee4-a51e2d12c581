"""
Test script for folder functionality.
This script tests both FavoriteFolder and AssistantFolder functionality
without relying on Django's test framework.
"""

import os
import sys
import uuid
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from accounts.models import Company
from assistants.models import Assistant, AssistantFolder
from directory.models import FavoriteFolder, SavedItem

User = get_user_model()

def test_favorite_folder():
    """Test FavoriteFolder functionality."""
    print("\n=== Testing FavoriteFolder ===")

    # Create test user
    username = f"foldertest_{uuid.uuid4().hex[:8]}"
    user = User.objects.create_user(
        username=username,
        email=f"{username}@test.com",
        password="testpass123"
    )
    print(f"Created test user: {username}")

    # Create test company
    company_name = f"Test Company {uuid.uuid4().hex[:8]}"
    company = Company.objects.create(
        name=company_name,
        owner=user
    )
    print(f"Created test company: {company_name}")

    # Create test assistant
    assistant_name = f"Test Assistant {uuid.uuid4().hex[:8]}"
    assistant = Assistant.objects.create(
        name=assistant_name,
        company=company,
        assistant_type='general',
        is_public=True,
        is_active=True
    )
    print(f"Created test assistant: {assistant_name}")

    # Test 1: Create favorite folder
    folder_name = f"Test Folder {uuid.uuid4().hex[:8]}"
    folder = FavoriteFolder.objects.create(
        user=user,
        name=folder_name,
        item_type='assistant'
    )
    print(f"Test 1: Created folder: {folder_name}")
    assert folder.name == folder_name
    assert folder.item_type == 'assistant'
    assert folder.user == user

    # Test 2: Create another folder with different type
    folder2_name = f"Test Folder {uuid.uuid4().hex[:8]}"
    folder2 = FavoriteFolder.objects.create(
        user=user,
        name=folder2_name,
        item_type='company'
    )
    print(f"Test 2: Created folder with different type: {folder2_name}")
    assert folder2.name == folder2_name
    assert folder2.item_type == 'company'

    # Test 3: Save assistant to folder
    saved_item = SavedItem.objects.create(
        user=user,
        item_type='assistant',
        assistant=assistant,
        folder=folder
    )
    print(f"Test 3: Saved assistant to folder")
    assert saved_item.folder == folder
    assert saved_item.assistant == assistant

    # Test 4: Try to save assistant to company folder (should fail validation)
    try:
        invalid_item = SavedItem(
            user=user,
            item_type='assistant',
            assistant=assistant,
            folder=folder2
        )
        invalid_item.full_clean()  # This should raise ValidationError
        assert False, "ValidationError not raised"
    except ValidationError:
        print("Test 4: Validation correctly prevented saving assistant to company folder")

    # Test 5: Save company to company folder
    company_saved_item = SavedItem.objects.create(
        user=user,
        item_type='company',
        company=company,
        folder=folder2
    )
    print(f"Test 5: Saved company to company folder")
    assert company_saved_item.folder == folder2
    assert company_saved_item.company == company

    # Clean up
    saved_item.delete()
    company_saved_item.delete()
    folder.delete()
    folder2.delete()
    assistant.delete()
    company.delete()
    user.delete()
    print("Cleaned up test data")

    return True

def test_assistant_folder():
    """Test AssistantFolder functionality."""
    print("\n=== Testing AssistantFolder ===")

    # Create test user
    username = f"asst_foldertest_{uuid.uuid4().hex[:8]}"
    user = User.objects.create_user(
        username=username,
        email=f"{username}@test.com",
        password="testpass123"
    )
    print(f"Created test user: {username}")

    # Create test company
    company_name = f"Test Company {uuid.uuid4().hex[:8]}"
    company = Company.objects.create(
        name=company_name,
        owner=user
    )
    print(f"Created test company: {company_name}")

    # Test 1: Create assistant folder
    folder_name = f"Test Folder {uuid.uuid4().hex[:8]}"
    folder = AssistantFolder.objects.create(
        name=folder_name,
        company=company
    )
    print(f"Test 1: Created folder: {folder_name}")
    assert folder.name == folder_name
    assert folder.company == company

    # Test 2: Create folders with different orders
    folder_a = AssistantFolder.objects.create(
        name="A Folder",
        company=company,
        order=2
    )
    folder_b = AssistantFolder.objects.create(
        name="B Folder",
        company=company,
        order=1
    )
    print("Test 2: Created folders with different orders")

    # Test ordering
    folders = list(AssistantFolder.objects.filter(company=company).order_by('order', 'name'))
    # Print the folders for debugging
    print(f"Folders in order: {[(f.name, f.order) for f in folders]}")

    # Find folders by name instead of relying on position
    folder_a_pos = next((i for i, f in enumerate(folders) if f.name == "A Folder"), -1)
    folder_b_pos = next((i for i, f in enumerate(folders) if f.name == "B Folder"), -1)

    assert folder_b_pos < folder_a_pos, "Folder B (order 1) should come before Folder A (order 2)"
    print("Test 2: Verified folder ordering")

    # Test 3: Create assistant in folder
    assistant_name = f"Test Assistant {uuid.uuid4().hex[:8]}"
    assistant = Assistant.objects.create(
        name=assistant_name,
        company=company,
        assistant_type='general',
        folder=folder
    )
    print(f"Test 3: Created assistant in folder: {assistant_name}")
    assert assistant.folder == folder
    assert folder.assistants.count() == 1
    assert folder.assistants.first() == assistant

    # Test 4: Move assistant to another folder
    assistant.folder = folder_a
    assistant.save()
    print("Test 4: Moved assistant to another folder")
    assert assistant.folder == folder_a
    assert folder_a.assistants.count() == 1
    assert folder.assistants.count() == 0

    # Test 5: Remove assistant from folder
    assistant.folder = None
    assistant.save()
    print("Test 5: Removed assistant from folder")
    assert assistant.folder is None
    assert folder_a.assistants.count() == 0

    # Clean up
    assistant.delete()
    folder.delete()
    folder_a.delete()
    folder_b.delete()
    company.delete()
    user.delete()
    print("Cleaned up test data")

    return True

if __name__ == "__main__":
    success = True

    try:
        favorite_folder_success = test_favorite_folder()
        success = success and favorite_folder_success
    except Exception as e:
        print(f"FavoriteFolder test failed: {e}")
        success = False

    try:
        assistant_folder_success = test_assistant_folder()
        success = success and assistant_folder_success
    except Exception as e:
        print(f"AssistantFolder test failed: {e}")
        success = False

    if success:
        print("\n✅ All folder tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some folder tests failed!")
        sys.exit(1)
