"""
Test script to verify that permissions and roles are working correctly.
"""

import os
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import django
django.setup()

import uuid
from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from guardian.shortcuts import get_perms, get_perms_for_model, assign_perm

from accounts.models import Company, Membership
from assistants.models import Assistant
from accounts.permissions import OWNER_PERMS_COMPANY

# Enable debug output
print("Starting permission tests...")

def create_test_users_and_company():
    """Create test users with different roles and a test company."""
    print("\n=== Creating Test Users and Company ===")

    # Create test users
    owner_username = f"owner_{uuid.uuid4().hex[:8]}"
    admin_username = f"admin_{uuid.uuid4().hex[:8]}"
    member_username = f"member_{uuid.uuid4().hex[:8]}"
    viewer_username = f"viewer_{uuid.uuid4().hex[:8]}"

    owner = User.objects.create_user(username=owner_username, password="Test123!")
    admin = User.objects.create_user(username=admin_username, password="Test123!")
    member = User.objects.create_user(username=member_username, password="Test123!")
    viewer = User.objects.create_user(username=viewer_username, password="Test123!")

    print(f"Created users: {owner_username}, {admin_username}, {member_username}, {viewer_username}")

    # Create test company
    company_name = f"Test Company {uuid.uuid4().hex[:8]}"
    company = Company.objects.create(
        name=company_name,
        owner=owner
    )
    print(f"Created company: {company_name} (ID: {company.id})")

    # Create company information
    from accounts.models import CompanyInformation
    CompanyInformation.objects.create(
        company=company,
        mission="Test mission",
        description="Test description",
        website="https://example.com",
        contact_email="<EMAIL>"
    )

    # Add users to company with different roles
    # The owner is automatically added as a member by the signal

    # Add admin to company
    admin_membership = Membership.objects.create(user=admin, company=company)
    admin_group = Group.objects.get(name='Company Administrators')
    admin.groups.add(admin_group)

    # Add member to company
    member_membership = Membership.objects.create(user=member, company=company)
    member_group = Group.objects.get(name='Company Members')
    member.groups.add(member_group)

    # Add viewer to company
    viewer_membership = Membership.objects.create(user=viewer, company=company)
    viewer_group = Group.objects.get(name='Company Guests')
    viewer.groups.add(viewer_group)

    print(f"Added users to company with different roles")

    return owner, admin, member, viewer, company

def create_test_assistant(company):
    """Create a test assistant for the company."""
    print("\n=== Creating Test Assistant ===")

    assistant_name = f"Test Assistant {uuid.uuid4().hex[:8]}"
    assistant = Assistant.objects.create(
        name=assistant_name,
        company=company,
        assistant_type=Assistant.TYPE_GENERAL,
        system_prompt='You are a helpful assistant.',
        is_public=False,
        is_active=True
    )

    print(f"Created assistant: {assistant_name} (ID: {assistant.id})")

    # Manually assign permissions to the owner for this assistant
    owner = company.owner
    if owner:
        print(f"Manually assigning assistant permissions to owner {owner.username}")
        assistant_perms = [
            'assistants.view_assistant',
            'assistants.change_assistant',
            'assistants.delete_assistant',
            'assistants.view_assistant_usage',
            'assistants.view_assistant_analytics'
        ]
        for perm in assistant_perms:
            try:
                assign_perm(perm, owner, assistant)
                print(f"  Assigned '{perm}' to owner for assistant")
            except Exception as e:
                print(f"  Error assigning '{perm}' to owner: {e}")

    return assistant

def test_owner_permissions(owner, company, assistant):
    """Test that the owner has all the expected permissions."""
    print("\n=== Testing Owner Permissions ===")

    # Get all permissions for the owner on the company
    company_perms = get_perms(owner, company)
    print(f"Owner permissions on company: {company_perms}")

    # Get all permissions for the owner on the assistant
    assistant_perms = get_perms(owner, assistant)
    print(f"Owner permissions on assistant: {assistant_perms}")

    # Check if the owner has all the expected company permissions
    missing_company_perms = set(OWNER_PERMS_COMPANY) - set(company_perms)
    if missing_company_perms:
        print(f"WARNING: Owner is missing the following company permissions: {missing_company_perms}")
    else:
        print(f"SUCCESS: Owner has all expected company permissions")

    # Check specific company permissions
    can_manage_company = owner.has_perm('accounts.change_company_settings', company)
    can_manage_assistants = owner.has_perm('accounts.manage_company_assistants', company)
    can_delete_company = owner.has_perm('accounts.delete_company_object', company)

    print(f"Owner can manage company: {can_manage_company}")
    print(f"Owner can manage assistants: {can_manage_assistants}")
    print(f"Owner can delete company: {can_delete_company}")

    # Check assistant permissions
    can_view_assistant = owner.has_perm('assistants.view_assistant', assistant)
    can_change_assistant = owner.has_perm('assistants.change_assistant', assistant)
    can_delete_assistant = owner.has_perm('assistants.delete_assistant', assistant)
    can_view_usage = owner.has_perm('assistants.view_assistant_usage', assistant)
    can_view_analytics = owner.has_perm('assistants.view_assistant_analytics', assistant)

    print(f"Owner can view assistant: {can_view_assistant}")
    print(f"Owner can change assistant: {can_change_assistant}")
    print(f"Owner can delete assistant: {can_delete_assistant}")
    print(f"Owner can view assistant usage: {can_view_usage}")
    print(f"Owner can view assistant analytics: {can_view_analytics}")

def test_admin_permissions(admin, company, assistant):
    """Test that the admin has the expected permissions."""
    print("\n=== Testing Admin Permissions ===")

    # Get all permissions for the admin on the company
    company_perms = get_perms(admin, company)
    print(f"Admin permissions on company: {company_perms}")

    # Get all permissions for the admin on the assistant
    assistant_perms = get_perms(admin, assistant)
    print(f"Admin permissions on assistant: {assistant_perms}")

    # Check specific company permissions
    can_manage_company = admin.has_perm('accounts.change_company_settings', company)
    can_manage_assistants = admin.has_perm('accounts.manage_company_assistants', company)
    can_delete_company = admin.has_perm('accounts.delete_company_object', company)

    print(f"Admin can manage company: {can_manage_company}")
    print(f"Admin can manage assistants: {can_manage_assistants}")
    print(f"Admin can delete company: {can_delete_company}")

    # Check assistant permissions
    can_view_assistant = admin.has_perm('assistants.view_assistant', assistant)
    can_change_assistant = admin.has_perm('assistants.change_assistant', assistant)
    can_delete_assistant = admin.has_perm('assistants.delete_assistant', assistant)
    can_view_usage = admin.has_perm('assistants.view_assistant_usage', assistant)
    can_view_analytics = admin.has_perm('assistants.view_assistant_analytics', assistant)

    print(f"Admin can view assistant: {can_view_assistant}")
    print(f"Admin can change assistant: {can_change_assistant}")
    print(f"Admin can delete assistant: {can_delete_assistant}")
    print(f"Admin can view assistant usage: {can_view_usage}")
    print(f"Admin can view assistant analytics: {can_view_analytics}")

def test_member_permissions(member, company, assistant):
    """Test that the member has the expected permissions."""
    print("\n=== Testing Member Permissions ===")

    # Get all permissions for the member on the company
    company_perms = get_perms(member, company)
    print(f"Member permissions on company: {company_perms}")

    # Get all permissions for the member on the assistant
    assistant_perms = get_perms(member, assistant)
    print(f"Member permissions on assistant: {assistant_perms}")

    # Check specific company permissions
    can_manage_company = member.has_perm('accounts.change_company_settings', company)
    can_manage_assistants = member.has_perm('accounts.manage_company_assistants', company)
    can_delete_company = member.has_perm('accounts.delete_company_object', company)

    print(f"Member can manage company: {can_manage_company}")
    print(f"Member can manage assistants: {can_manage_assistants}")
    print(f"Member can delete company: {can_delete_company}")

    # Check assistant permissions
    can_view_assistant = member.has_perm('assistants.view_assistant', assistant)
    can_change_assistant = member.has_perm('assistants.change_assistant', assistant)
    can_delete_assistant = member.has_perm('assistants.delete_assistant', assistant)
    can_view_usage = member.has_perm('assistants.view_assistant_usage', assistant)
    can_view_analytics = member.has_perm('assistants.view_assistant_analytics', assistant)

    print(f"Member can view assistant: {can_view_assistant}")
    print(f"Member can change assistant: {can_change_assistant}")
    print(f"Member can delete assistant: {can_delete_assistant}")
    print(f"Member can view assistant usage: {can_view_usage}")
    print(f"Member can view assistant analytics: {can_view_analytics}")

def test_viewer_permissions(viewer, company, assistant):
    """Test that the viewer has the expected permissions."""
    print("\n=== Testing Viewer Permissions ===")

    # Get all permissions for the viewer on the company
    company_perms = get_perms(viewer, company)
    print(f"Viewer permissions on company: {company_perms}")

    # Get all permissions for the viewer on the assistant
    assistant_perms = get_perms(viewer, assistant)
    print(f"Viewer permissions on assistant: {assistant_perms}")

    # Check specific company permissions
    can_manage_company = viewer.has_perm('accounts.change_company_settings', company)
    can_manage_assistants = viewer.has_perm('accounts.manage_company_assistants', company)
    can_delete_company = viewer.has_perm('accounts.delete_company_object', company)

    print(f"Viewer can manage company: {can_manage_company}")
    print(f"Viewer can manage assistants: {can_manage_assistants}")
    print(f"Viewer can delete company: {can_delete_company}")

    # Check assistant permissions
    can_view_assistant = viewer.has_perm('assistants.view_assistant', assistant)
    can_change_assistant = viewer.has_perm('assistants.change_assistant', assistant)
    can_delete_assistant = viewer.has_perm('assistants.delete_assistant', assistant)
    can_view_usage = viewer.has_perm('assistants.view_assistant_usage', assistant)
    can_view_analytics = viewer.has_perm('assistants.view_assistant_analytics', assistant)

    print(f"Viewer can view assistant: {can_view_assistant}")
    print(f"Viewer can change assistant: {can_change_assistant}")
    print(f"Viewer can delete assistant: {can_delete_assistant}")
    print(f"Viewer can view assistant usage: {can_view_usage}")
    print(f"Viewer can view assistant analytics: {can_view_analytics}")

def run_permission_tests():
    """Run all permission tests."""
    print("\n=== Running Permission Tests ===")

    # Create test users and company
    owner, admin, member, viewer, company = create_test_users_and_company()

    # Create test assistant
    assistant = create_test_assistant(company)

    # Test permissions for each role
    test_owner_permissions(owner, company, assistant)
    test_admin_permissions(admin, company, assistant)
    test_member_permissions(member, company, assistant)
    test_viewer_permissions(viewer, company, assistant)

    # Clean up
    print("\n=== Cleaning Up ===")
    assistant.delete()
    company.delete()
    owner.delete()
    admin.delete()
    member.delete()
    viewer.delete()
    print("Test users, company, and assistant deleted")

if __name__ == "__main__":
    run_permission_tests()
