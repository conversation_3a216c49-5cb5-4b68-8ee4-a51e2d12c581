from .models import Assistant

def community_assistants_context(request):
    """
    Adds community assistants to the template context.
    - community_assistants: QuerySet of all community assistants the user has access to.
    """
    context = {
        'community_assistants': Assistant.objects.none()  # Default to empty queryset
    }
    
    if request.user.is_authenticated:
        # For superusers, show all community assistants
        if request.user.is_superuser:
            context['community_assistants'] = Assistant.objects.filter(
                assistant_type=Assistant.TYPE_COMMUNITY,
                is_active=True
            ).order_by('name')
        else:
            # For regular users, show public community assistants and those from companies they belong to
            from django.db.models import Q
            context['community_assistants'] = Assistant.objects.filter(
                Q(assistant_type=Assistant.TYPE_COMMUNITY) &
                (
                    Q(is_public=True) |  # Public assistants
                    Q(company__owner=request.user) |  # User owns the company
                    Q(company__memberships__user=request.user)  # User is a member of the company
                )
            ).filter(
                is_active=True
            ).distinct().order_by('name')
    
    return context
