chat/:1976 Uncaught SyntaxError: Unexpected token '}' (at chat/:1976:5)
chat/:3479 Uncaught ReferenceError: assistantId is not defined
    at chat/:3479:49
(anonymous) @ chat/:3479
chat/:5562 Uncaught SyntaxError: Unexpected token '}' (at chat/:5562:5)
chat/:7061 Uncaught SyntaxError: Identifier 'rateAssistantBaseUrl' has already been declared (at chat/:7061:9)
chat/:7071 Uncaught SyntaxError: Identifier 'currentModalItemId' has already been declared (at chat/:7071:9)
VM997 rating-modal.js:6 Rating modal initialized successfully
chat/:3536 Found 1 like buttons on the page
chat/:4584 [DEBUG] Base Layout: DOMContentLoaded event fired
rating-modal.js:6 Rating modal initialized successfully
global-tinymce.js:17 Initializing Global TinyMCE
global-tinymce.js:28 Found 0 textareas to initialize with TinyMCE
global-tinymce.js:17 Initializing Global TinyMCE
global-tinymce.js:28 Found 0 textareas to initialize with TinyMCE
chat/:1110 Sidebar toggle function called
chat/:1116 Sidebar found, current state: inactive
chat/:1154 Sidebar shown
chat/:3917 ===== DEBUGGING CONTENT BLOCKS =====
chat/:3923 Total content blocks found: 3
chat/:3929 Computed style display value: none
chat/:3930 Computed style visibility value: visible
chat/:3936 Block 1: nav-section-products
chat/:3937   Element: <div class=​"nav-section-content-block" id=​"nav-section-products" style=​"display:​ none;​ visibility:​ visible;​ opacity:​ 1;​" data-nav-label=​"Products">​…​</div>​
chat/:3938   Display (style): none
chat/:3939   Display (computed): none
chat/:3940   Visibility (style): visible
chat/:3941   Visibility (computed): visible
chat/:3942   Content length: 892
chat/:3936 Block 2: nav-section-home
chat/:3937   Element: <div class=​"nav-section-content-block" id=​"nav-section-home" style=​"display:​ none;​ visibility:​ visible;​ opacity:​ 1;​" data-nav-label=​"Contact Us">​…​</div>​
chat/:3938   Display (style): none
chat/:3939   Display (computed): none
chat/:3940   Visibility (style): visible
chat/:3941   Visibility (computed): visible
chat/:3942   Content length: 898
chat/:3936 Block 3: nav-section-about-us
chat/:3937   Element: <div class=​"nav-section-content-block" id=​"nav-section-about-us" style=​"display:​ none;​ visibility:​ visible;​ opacity:​ 1;​" data-nav-label=​"About Us">​…​</div>​
chat/:3938   Display (style): none
chat/:3939   Display (computed): none
chat/:3940   Visibility (style): visible
chat/:3941   Visibility (computed): visible
chat/:3942   Content length: 892
chat/:3945 
Nav buttons:
chat/:3947 Button 1: Products
chat/:3948   Section ID: products
chat/:3949   Item ID: 19
chat/:3947 Button 2: Contact Us
chat/:3948   Section ID: home
chat/:3949   Item ID: 17
chat/:3947 Button 3: About Us
chat/:3948   Section ID: about-us
chat/:3949   Item ID: 18
chat/:4285 Manually showing Products content
chat/:4300 Found Products button: <button type=​"button" class=​"list-group-item list-group-item-action nav-button" data-section-id=​"products" data-item-id=​"19" onclick=​"showContentBlockById('products')​;​ return false;​" style=​"font-family:​ -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;​ font-size:​ 0.9rem;​ font-weight:​ 450;​ color:​ #333333;​ padding:​ 0.8rem 1rem;​ border-color:​ rgba(0, 0, 0, 0.05)​;​ background-color:​ #f8f9fa;​ transition:​ all 0.2s ease-in-out;​ border-radius:​ 0.375rem;​ margin-bottom:​ 0.25rem;​ display:​ flex;​ align-items:​ center;​ cursor:​ pointer;​">​…​</button>​flex
chat/:4344 Created new content display area: <div id=​"content-display-area" class=​"content-display-area mt-4 p-4 bg-white rounded shadow-sm" style=​"display:​ block;​ visibility:​ visible;​ opacity:​ 1;​ background-color:​ rgb(255, 255, 255)​;​ border:​ 1px solid rgba(0, 0, 0, 0.1)​;​ border-radius:​ 0.5rem;​ box-shadow:​ rgba(0, 0, 0, 0.1)​ 0px 2px 10px;​ padding:​ 1.5rem;​ margin-bottom:​ 2rem;​ position:​ relative;​ z-index:​ 100;​ width:​ 100%;​">​…​</div>​
chat/:4411 Products content displayed manually
chat/:4039 Direct Products display
chat/:4113 Direct Products display complete
chat/:3955 ===== DOM STRUCTURE INSPECTION =====
chat/:3959 Chat box: <div class=​"chat-box " id=​"chat-box" style=​"position:​ relative;​ top:​ 0;​ background-color:​ transparent;​ padding:​ 0.5rem;​ border:​ none;​ margin-bottom:​ 0.5rem;​">​…​</div>​flex
chat/:3963 Initial display area: <div id=​"initial-display-area" class=​"text-center mb-4 " style=​"display:​ none;​">​…​</div>​
chat/:3965   Display: none
chat/:3966   Visibility: visible
chat/:3967   Children: 3
chat/:3972 Nav section content: <div id=​"nav-section-content" class=​"mt-3" style=​"display:​ block;​ visibility:​ visible;​ opacity:​ 1;​">​…​</div>​
chat/:3974   Display: block
chat/:3975   Visibility: visible
chat/:3976   Children: 3
chat/:3980   Content blocks found: 3
chat/:3982   Block 1: <div class=​"nav-section-content-block" id=​"nav-section-products" style=​"display:​ none;​ visibility:​ visible;​ opacity:​ 1;​" data-nav-label=​"Products">​…​</div>​
chat/:3983     ID: nav-section-products
chat/:3984     Display: none
chat/:3985     Visibility: visible
chat/:3986     Content length: 892
chat/:3982   Block 2: <div class=​"nav-section-content-block" id=​"nav-section-home" style=​"display:​ none;​ visibility:​ visible;​ opacity:​ 1;​" data-nav-label=​"Contact Us">​…​</div>​
chat/:3983     ID: nav-section-home
chat/:3984     Display: none
chat/:3985     Visibility: visible
chat/:3986     Content length: 898
chat/:3982   Block 3: <div class=​"nav-section-content-block" id=​"nav-section-about-us" style=​"display:​ none;​ visibility:​ visible;​ opacity:​ 1;​" data-nav-label=​"About Us">​…​</div>​
chat/:3983     ID: nav-section-about-us
chat/:3984     Display: none
chat/:3985     Visibility: visible
chat/:3986     Content length: 892
chat/:3992 Content display area: <div id=​"content-display-area" class=​"content-display-area mt-4 p-4 bg-white rounded shadow-sm" style=​"display:​ block;​ visibility:​ visible;​ opacity:​ 1;​ background-color:​ rgb(255, 255, 255)​;​ border:​ 1px solid rgba(0, 0, 0, 0.1)​;​ border-radius:​ 0.5rem;​ box-shadow:​ rgba(0, 0, 0, 0.1)​ 0px 2px 10px;​ padding:​ 1.5rem;​ margin-bottom:​ 2rem;​ position:​ relative;​ z-index:​ 100;​ width:​ 100%;​">​…​</div>​
chat/:3994   Display: block
chat/:3995   Visibility: visible
chat/:3996   Z-index: 100
chat/:3997   Position: relative
chat/:3998   Content: 
            <div class="content-header"><h3>Products</h3><small class="text-muted">Last updated: 7:05:11 AM</small></div>
            <hr>
            <div class="content-body">
                <h3>Products</h3>
                <p>This is the content for the Products section.</p>
                <p>You can add more information about Products here.</p>
                <hr>
                <p class="text-muted small">Note: This is placeholder content. The actual content for this section needs to be added.</p>
            </div>
            <div class="text-end mt-4">
                <button type="button" class="btn btn-outline-secondary btn-sm back-to-chat-btn">
                    <i class="bi bi-chat-dots me-1"></i> Back to Chat
                </button>
            </div>
        
chat/:4002 
Checking for elements with display:none !important...
chat/:4015 Hidden elements with IDs: (37) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
chat/:3917 ===== DEBUGGING CONTENT BLOCKS =====
chat/:3923 Total content blocks found: 3
chat/:3929 Computed style display value: none
chat/:3930 Computed style visibility value: visible
chat/:3936 Block 1: nav-section-products
chat/:3937   Element: <div class=​"nav-section-content-block" id=​"nav-section-products" style=​"display:​ none;​ visibility:​ visible;​ opacity:​ 1;​" data-nav-label=​"Products">​…​</div>​
chat/:3938   Display (style): none
chat/:3939   Display (computed): none
chat/:3940   Visibility (style): visible
chat/:3941   Visibility (computed): visible
chat/:3942   Content length: 892
chat/:3936 Block 2: nav-section-home
chat/:3937   Element: <div class=​"nav-section-content-block" id=​"nav-section-home" style=​"display:​ none;​ visibility:​ visible;​ opacity:​ 1;​" data-nav-label=​"Contact Us">​…​</div>​
chat/:3938   Display (style): none
chat/:3939   Display (computed): none
chat/:3940   Visibility (style): visible
chat/:3941   Visibility (computed): visible
chat/:3942   Content length: 898
chat/:3936 Block 3: nav-section-about-us
chat/:3937   Element: <div class=​"nav-section-content-block" id=​"nav-section-about-us" style=​"display:​ none;​ visibility:​ visible;​ opacity:​ 1;​" data-nav-label=​"About Us">​…​</div>​
chat/:3938   Display (style): none
chat/:3939   Display (computed): none
chat/:3940   Visibility (style): visible
chat/:3941   Visibility (computed): visible
chat/:3942   Content length: 892
chat/:3945 
Nav buttons:
chat/:3947 Button 1: Products
chat/:3948   Section ID: products
chat/:3949   Item ID: 19
chat/:3947 Button 2: Contact Us
chat/:3948   Section ID: home
chat/:3949   Item ID: 17
chat/:3947 Button 3: About Us
chat/:3948   Section ID: about-us
chat/:3949   Item ID: 18
chat/:4179 Directly showing content block for section: products
chat/:4280 Content block for section products shown successfully
chat/:4179 Directly showing content block for section: home
chat/:4280 Content block for section home shown successfully
chat/:4179 Directly showing content block for section: about-us
chat/:4280 Content block for section about-us shown successfully
