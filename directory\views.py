import json
import decimal
from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView
from django.http import JsonResponse, HttpResponseBadRequest, HttpResponseForbidden, HttpResponse
from django.views.decorators.http import require_POST, require_GET
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db import IntegrityError, transaction # Import transaction
from django.db.models import Q, Prefetch, Avg, Sum
from django.core.paginator import Paginator
from django.shortcuts import render, get_object_or_404, redirect # Added redirect
from django.views.generic import ListView
from django.http import JsonResponse, HttpResponseBadRequest, HttpResponseForbidden, HttpResponse
from django.views.decorators.http import require_POST, require_GET
from django.contrib.auth.decorators import login_required, user_passes_test # Added user_passes_test
from django.contrib.admin.views.decorators import staff_member_required # Re-added staff_member_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db import IntegrityError, transaction # Import transaction
from django.db.models import Q, Prefetch, Avg, Sum, F, Case, When, Value, IntegerField # Added F, Case, When, Value, IntegerField
from django.core.paginator import Paginator
from django.template.loader import render_to_string
from django.utils import timezone
from django.contrib import messages # Added messages
# Removed HttpResponseForbidden import
# Re-added DirectorySettings import
from .models import CompanyListing, AssistantListing, SavedItem, DirectoryRating, FavoriteFolder, DirectorySettings
from .forms import DirectorySettingsForm # Import the new form
from accounts.models import Company
# Removed has_company_permission import
from assistants.models import Assistant, Interaction

def search_view(request):
    """
    Handles searching for companies or assistants based on query parameters.
    Renders the main search results page.
    """
    query = request.GET.get('q', '').strip()
    search_type = request.GET.get('type', 'assistant')
    page_number = request.GET.get('page', 1)

    results = []
    is_company_search = search_type == 'company'

    # Load directory settings to check if standard tier should be hidden
    settings_obj = DirectorySettings.load()

    if query:
        if is_company_search:
            # First get all matching companies
            company_qs = Company.objects.filter(
                Q(name__icontains=query) | Q(info__description__icontains=query),
                is_active=True,  # Ensure company is active
                info__list_in_directory=True  # Only show companies that are set to be listed in directory
            ).select_related('info').distinct()

            # Apply standard tier filtering if enabled
            if settings_obj.hide_standard_tier_companies:
                import logging
                logger = logging.getLogger('directory')
                logger.debug(f"Search view: Applying hide_standard_tier_companies filter")

                # Count before filtering
                total_before = company_qs.count()
                standard_tier_count = company_qs.filter(tier=Company.TIER_STANDARD).count()
                featured_count = company_qs.filter(is_featured=True).count()
                logger.debug(f"Before filtering: {total_before} total companies, {standard_tier_count} standard tier, {featured_count} featured")

                # Get featured companies (these should always be shown)
                featured_companies = company_qs.filter(is_featured=True)

                # Get non-standard tier companies
                non_standard_companies = company_qs.exclude(tier=Company.TIER_STANDARD)

                # Combine featured and non-standard (avoiding duplicates)
                company_qs = (featured_companies | non_standard_companies).distinct()

                # Count after filtering
                logger.debug(f"After filtering: {company_qs.count()} companies remain")

            paginator = Paginator(company_qs, 10)
            page_obj = paginator.get_page(page_number)
            results = page_obj.object_list
        else:
            # First get all matching assistants
            assistant_qs = Assistant.objects.filter(
                Q(name__icontains=query) | Q(description__icontains=query),
                is_public=True,  # Only show public assistants
                is_active=True
            ).select_related('company').distinct()

            # Apply standard tier filtering if enabled
            if settings_obj.hide_standard_tier_assistants:
                # Get featured assistants (these should always be shown)
                featured_assistants = assistant_qs.filter(is_featured=True)

                # Get non-standard tier assistants
                non_standard_assistants = assistant_qs.exclude(tier=Assistant.TIER_STANDARD)

                # Combine featured and non-standard (avoiding duplicates)
                assistant_qs = (featured_assistants | non_standard_assistants).distinct()

            paginator = Paginator(assistant_qs, 10)
            page_obj = paginator.get_page(page_number)
            results = page_obj.object_list
    else:
        paginator = Paginator([], 10)
        page_obj = paginator.get_page(page_number)

    context = {
        'query': query,
        'search_type': search_type,
        'is_company_search': is_company_search,
        'results': results,
        'page_obj': page_obj,
    }

    if request.headers.get('HX-Request'):
        template_name = 'directory/partials/search_results.html'
    else:
        template_name = 'search.html'

    return render(request, template_name, context)


# --- Directory Listing Views ---

class CompanyDirectoryListView(ListView):
    """Displays a list of publicly listed companies."""
    model = CompanyListing
    template_name = 'directory/company_list.html'
    paginate_by = 10  # Default to 10 items per page

    def get_queryset(self):
        # Initial filtering based on GET params, applied to all listings
        # Start with CompanyListing, then filter based on related Company and CompanyInformation

        # Debug: Print all companies first
        all_companies = Company.objects.all()
        print(f"\n[DEBUG] Total companies in database: {all_companies.count()}")

        # Debug: Print active companies
        active_companies = Company.objects.filter(is_active=True)
        print(f"[DEBUG] Active companies: {active_companies.count()}")
        for company in active_companies:
            print(f"[DEBUG] Active company: {company.name} (ID: {company.id})")

        # Debug: Print companies with CompanyInformation
        companies_with_info = Company.objects.filter(info__isnull=False)
        print(f"[DEBUG] Companies with CompanyInformation: {companies_with_info.count()}")

        # Debug: Print companies set to be listed in directory
        public_companies = Company.objects.filter(
            is_active=True,
            info__list_in_directory=True
        )
        print(f"[DEBUG] Public companies (list_in_directory=True): {public_companies.count()}")
        for company in public_companies:
            print(f"[DEBUG] Public company: {company.name} (ID: {company.id})")

        # Debug: Print companies with CompanyListing
        companies_with_listing = CompanyListing.objects.all()
        print(f"[DEBUG] Companies with CompanyListing: {companies_with_listing.count()}")

        # Start the actual filtering
        queryset = CompanyListing.objects.select_related(
            'company',
            'company__info'
        ).filter(
            company__is_active=True,                 # Company must be active
            company__info__list_in_directory=True,   # Company must be set to list in directory
            is_listed=True                           # CompanyListing itself must be marked as listed
        )

        # Debug: Print the filtered queryset
        print(f"[DEBUG] After initial filtering: {queryset.count()} companies")
        for listing in queryset:
            print(f"[DEBUG] Filtered company: {listing.company.name} (ID: {listing.company.id})")

        # Only exclude companies that are communities (entity_type='community')
        # instead of excluding companies that have community assistants
        queryset = queryset.exclude(
            company__entity_type='community'
        )

        # Debug: Print after excluding communities
        print(f"[DEBUG] After excluding communities: {queryset.count()} companies")

        # Apply user filters from GET parameters
        self.q_name = self.request.GET.get('q_name', '').strip()
        self.q_category = self.request.GET.get('q_category', '').strip()
        self.q_tag = self.request.GET.get('q_tag', '').strip()
        self.q_industry = self.request.GET.get('q_industry', '').strip()

        if self.q_name:
            queryset = queryset.filter(
                Q(company__name__icontains=self.q_name) |
                Q(company__info__description__icontains=self.q_name)
            )
        if self.q_category:
            # Filter on the categories in CompanyListing
            queryset = queryset.filter(categories__name__icontains=self.q_category)
        if self.q_tag:
            queryset = queryset.filter(tags__contains=self.q_tag)
        if self.q_industry:
            queryset = queryset.filter(company__info__industry__icontains=self.q_industry)

        # Return the base filtered queryset. Tier filtering happens in get_context_data.
        return queryset.distinct()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        settings_obj = DirectorySettings.load()

        # Log the settings
        print(f"[DEBUG] DirectorySettings loaded: hide_standard_tier_companies={settings_obj.hide_standard_tier_companies}, hide_standard_tier_assistants={settings_obj.hide_standard_tier_assistants}")

        # Get the full filtered list (by user search, not tier yet)
        all_listings = list(context['object_list'])

        # --- Identify Featured Companies ---
        # Get featured companies directly from the database
        # Filter out companies with community assistants
        featured_companies = Company.objects.filter(
            info__list_in_directory=True,
            is_featured=True,
            is_active=True
        ).exclude(
            assistants__assistant_type=Assistant.TYPE_COMMUNITY
        ).select_related('info').order_by('-created_at')

        print(f"[DEBUG] Direct query found {featured_companies.count()} featured companies")

        # Create a list to store featured company listings
        featured_listings = []

        # For each featured company, get or create its listing
        for company in featured_companies:
            try:
                listing = CompanyListing.objects.get(company=company)
                print(f"[DEBUG] Found existing listing for featured company: {company.name}")
            except CompanyListing.DoesNotExist:
                # Create a new listing for this company
                description = company.info.description if hasattr(company, 'info') and company.info else ''
                listing = CompanyListing.objects.create(
                    company=company,
                    is_listed=True,
                    featured=True,  # Mark as featured
                    description=description,
                    avg_rating=decimal.Decimal('0.0'),
                    total_ratings=0,
                    tags=[],
                    categories=[]
                )
                print(f"[DEBUG] Created new listing for featured company: {company.name}")

            # Add the listing to our featured listings list
            featured_listings.append(listing)

            # Make sure this listing is in the main list
            if listing not in all_listings:
                all_listings.append(listing)

        # Debug: Print final featured companies list
        print(f"[DEBUG] Final featured companies count: {len(featured_listings)}")
        for listing in featured_listings:
            print(f"[DEBUG] - {listing.company.name} (ID: {listing.company.id}, Featured: {listing.company.is_featured}, Has Logo: {bool(listing.company.info.logo)}")

        # --- Prepare Main List (Apply Tier Filter Here) ---
        # Start with all listings (which now includes any featured companies that weren't in the original list)
        # IMPORTANT: We keep featured items in the main list so they appear in both places
        all_listings_for_main_list = all_listings

        # --- Sorting Logic for Main List ---
        sort_by = self.request.GET.get('sort_by', 'tier')

        if sort_by == 'tier':
            tier_rank_map = {
                Company.TIER_GOLD: 1,
                Company.TIER_SILVER: 2,
                Company.TIER_BRONZE: 3,
                Company.TIER_STANDARD: 4,
            }
            all_listings_for_main_list.sort(
                key=lambda x: (tier_rank_map.get(x.company.tier, 5), -x.created_at.timestamp())
            )
        elif sort_by == 'latest':
            all_listings_for_main_list.sort(key=lambda x: x.created_at, reverse=True)
        elif sort_by == 'name':
            all_listings_for_main_list.sort(key=lambda x: x.company.name.lower())
        elif sort_by == 'rating':
            all_listings_for_main_list.sort(key=lambda x: x.avg_rating if x.avg_rating is not None else decimal.Decimal('-Infinity'), reverse=True)

        # --- Apply Tier Filter AFTER Sorting (to the list for pagination) ---
        # First, check if there are any standard tier companies
        standard_tier_companies = [lst for lst in all_listings_for_main_list if lst.company.tier == Company.TIER_STANDARD]
        print(f"[DEBUG] Found {len(standard_tier_companies)} standard tier companies")
        for lst in standard_tier_companies:
            print(f"[DEBUG] - Standard Tier Company: {lst.company.name}, Featured: {lst.company.is_featured}")

        # Now apply the filter if the setting is enabled
        if settings_obj.hide_standard_tier_companies:
            import logging
            logger = logging.getLogger('directory')
            logger.debug(f"CompanyDirectoryListView: hide_standard_tier_companies is enabled, filtering out standard tier companies")

            # Get the current settings object directly from the database to verify
            fresh_settings = DirectorySettings.objects.get(pk=1)
            logger.debug(f"Fresh settings check: hide_standard_tier_companies={fresh_settings.hide_standard_tier_companies}")

            # Count before filtering
            total_before = len(all_listings_for_main_list)
            standard_tier_count = len([lst for lst in all_listings_for_main_list if lst.company.tier == Company.TIER_STANDARD])
            featured_count = len([lst for lst in all_listings_for_main_list if lst.company.is_featured])
            logger.debug(f"Before filtering: {total_before} total companies, {standard_tier_count} standard tier, {featured_count} featured")

            # Exclude standard tier items *unless* they are also featured
            # Apply this filter to the already sorted list
            # Corrected List Comprehension
            filtered_listings = [
                lst for lst in all_listings_for_main_list
                if lst.company.is_featured or lst.company.tier != Company.TIER_STANDARD
            ]

            # Debug the filtering logic
            logger.debug(f"Filtering details:")
            for lst in all_listings_for_main_list:
                should_include = lst.company.is_featured or lst.company.tier != Company.TIER_STANDARD
                logger.debug(f"- Company: {lst.company.name}, Tier: {lst.company.tier}, Featured: {lst.company.is_featured}, Include: {should_include}")

            # Apply the filter
            all_listings_for_main_list = filtered_listings

            logger.debug(f"After tier filtering: {len(all_listings_for_main_list)} companies remain")
            # Log the companies that remain after filtering
            for lst in all_listings_for_main_list:
                logger.debug(f"- Company: {lst.company.name}, Tier: {lst.company.tier}, Featured: {lst.company.is_featured}")
        else:
            import logging
            logger = logging.getLogger('directory')
            logger.debug(f"hide_standard_tier_companies is disabled, showing all companies")
        # If the setting is disabled, show all companies regardless of tier

        # --- Pagination for Main List ---
        # Get items per page from request, default to self.paginate_by
        items_per_page = self.request.GET.get('items_per_page')
        try:
            items_per_page = int(items_per_page) if items_per_page else self.paginate_by
            # Limit to valid options: 10, 25, 50, 100
            if items_per_page not in [10, 25, 50, 100]:
                items_per_page = self.paginate_by
        except ValueError:
            items_per_page = self.paginate_by

        # Create a new paginator with the filtered list
        paginator = Paginator(all_listings_for_main_list, items_per_page)
        page_number = self.request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        print(f"[DEBUG] Pagination: {len(all_listings_for_main_list)} total items, page {page_obj.number} of {paginator.num_pages}")
        print(f"[DEBUG] Page object has {len(page_obj.object_list)} items")

        # --- Update Context ---
        context['featured_company_listings'] = featured_listings
        context['page_obj'] = page_obj
        context['is_paginated'] = page_obj.has_other_pages()
        context['object_list'] = page_obj.object_list  # This is what the template uses
        context['company_listings'] = page_obj.object_list  # This is also used in some templates

        # Add all companies to context for debugging
        context['all_companies'] = Company.objects.filter(is_active=True)

        context['q_name'] = self.q_name
        context['q_category'] = self.q_category
        context['q_tag'] = self.q_tag
        context['q_industry'] = self.q_industry
        context['sort_by'] = sort_by

        # Add directory settings to context for featured carousel and filtering
        directory_settings_dict = {
            'featured_scroll_direction': settings_obj.featured_scroll_direction,
            'featured_transition_effect': settings_obj.featured_transition_effect,
            'featured_visible_count': settings_obj.featured_visible_count,
            'featured_autoplay': settings_obj.featured_autoplay,
            'featured_autoplay_delay': settings_obj.featured_autoplay_delay,
        }
        context['directory_settings_dict'] = directory_settings_dict

        # Add tier filtering settings to context
        context['hide_standard_tier_companies'] = settings_obj.hide_standard_tier_companies

        # Add direct debug output to the terminal
        print(f"\n\n[DIRECT DEBUG] CompanyDirectoryListView final context: hide_standard_tier_companies={context['hide_standard_tier_companies']}\n\n")

        if self.request.user.is_authenticated:
            saved_company_ids = set(
                SavedItem.objects.filter(
                    user=self.request.user,
                    item_type='company',
                    company__isnull=False
                ).values_list('company_id', flat=True)
            )
            context['saved_company_ids'] = saved_company_ids
        else:
            context['saved_company_ids'] = set()
        return context

class AssistantDirectoryListView(ListView):
    """Displays a list of publicly listed assistants, separating featured."""
    model = AssistantListing
    template_name = 'directory/assistant_list.html'
    paginate_by = 10  # Default to 10 items per page

    def get_queryset(self):
        # Base queryset - DO NOT filter by tier here
        queryset = AssistantListing.objects.select_related(
            'assistant',
            'assistant__company'
        ).filter(
            is_listed=True,
            assistant__is_public=True
        )

        # Exclude community assistants from the regular assistant list
        # We'll handle them separately in get_context_data
        queryset = queryset.exclude(assistant__assistant_type=Assistant.TYPE_COMMUNITY)

        # Apply user filters from GET parameters
        self.q_name = self.request.GET.get('q_name', '').strip()
        self.q_company = self.request.GET.get('q_company', '').strip()
        self.q_category = self.request.GET.get('q_category', '').strip()
        self.q_tag = self.request.GET.get('q_tag', '').strip()

        if self.q_name:
            queryset = queryset.filter(
                Q(assistant__name__icontains=self.q_name) |
                Q(assistant__description__icontains=self.q_name)
            )
        if self.q_company:
            queryset = queryset.filter(assistant__company__name__icontains=self.q_company)
        if self.q_category:
            queryset = queryset.filter(categories__contains=self.q_category)
        if self.q_tag:
            queryset = queryset.filter(tags__contains=self.q_tag)

        # Return the base filtered queryset. Tier filtering happens in get_context_data.
        return queryset.distinct()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        settings_obj = DirectorySettings.load() # Load settings

        # Log the settings
        print(f"[DEBUG] AssistantDirectoryListView: DirectorySettings loaded: hide_standard_tier_companies={settings_obj.hide_standard_tier_companies}, hide_standard_tier_assistants={settings_obj.hide_standard_tier_assistants}")

        # Get the full filtered list (by user search, not tier yet)
        all_listings = list(context['object_list'])

        # --- Identify Featured Assistants (but don't remove them from main list) ---
        # Filter out community assistants from featured list
        featured_listings = [
            lst for lst in all_listings if lst.assistant.is_featured and lst.assistant.assistant_type != Assistant.TYPE_COMMUNITY
        ]
        featured_listings.sort(key=lambda x: x.created_at, reverse=True)

        # --- Prepare Main List (Apply Tier Filter Here) ---
        # Start with all listings, then filter based on settings
        # IMPORTANT: We keep featured items in the main list so they appear in both places
        all_listings_for_main_list = all_listings # Keep all for sorting first

        # --- Sorting Logic for Main List ---
        sort_by = self.request.GET.get('sort_by', 'tier')

        if sort_by == 'tier':
            tier_rank_map = {
                Assistant.TIER_GOLD: 1,
                Assistant.TIER_SILVER: 2,
                Assistant.TIER_BRONZE: 3,
                Assistant.TIER_STANDARD: 4,
            }
            all_listings_for_main_list.sort(
                key=lambda x: (tier_rank_map.get(x.assistant.tier, 5), -x.created_at.timestamp())
            )
        elif sort_by == 'latest':
            all_listings_for_main_list.sort(key=lambda x: x.created_at, reverse=True)
        elif sort_by == 'name':
            all_listings_for_main_list.sort(key=lambda x: x.assistant.name.lower())
        elif sort_by == 'rating':
            all_listings_for_main_list.sort(key=lambda x: x.avg_rating if x.avg_rating is not None else decimal.Decimal('-Infinity'), reverse=True)

        # --- Apply Tier Filter AFTER Sorting (to the list for pagination) ---
        # Only apply the tier filter if the setting is enabled

        # Count standard tier assistants before filtering
        import logging
        logger = logging.getLogger('directory')

        standard_tier_assistants = [lst for lst in all_listings_for_main_list if lst.assistant.tier == Assistant.TIER_STANDARD]
        logger.debug(f"AssistantDirectoryListView: Found {len(standard_tier_assistants)} standard tier assistants")
        for lst in standard_tier_assistants:
            logger.debug(f"- Standard Tier Assistant: {lst.assistant.name}, Featured: {lst.assistant.is_featured}")

        if settings_obj.hide_standard_tier_assistants:
            logger.debug(f"hide_standard_tier_assistants is enabled, filtering out standard tier assistants")

            # Get the current settings object directly from the database to verify
            fresh_settings = DirectorySettings.objects.get(pk=1)
            logger.debug(f"Fresh settings check: hide_standard_tier_assistants={fresh_settings.hide_standard_tier_assistants}")

            # Count before filtering
            total_before = len(all_listings_for_main_list)
            featured_count = len([lst for lst in all_listings_for_main_list if lst.assistant.is_featured])
            logger.debug(f"Before filtering: {total_before} total assistants, {len(standard_tier_assistants)} standard tier, {featured_count} featured")

            # Debug the filtering logic
            logger.debug(f"Filtering details:")
            for lst in all_listings_for_main_list:
                should_include = lst.assistant.is_featured or lst.assistant.tier != Assistant.TIER_STANDARD
                logger.debug(f"- Assistant: {lst.assistant.name}, Tier: {lst.assistant.tier}, Featured: {lst.assistant.is_featured}, Include: {should_include}")

            # Exclude standard tier items *unless* they are also featured
            # Corrected List Comprehension
            filtered_listings = [
                lst for lst in all_listings_for_main_list
                if lst.assistant.is_featured or lst.assistant.tier != Assistant.TIER_STANDARD
            ]

            # Apply the filter
            all_listings_for_main_list = filtered_listings

            logger.debug(f"After tier filtering: {len(all_listings_for_main_list)} assistants remain")
            # Log the assistants that remain after filtering
            for lst in all_listings_for_main_list:
                logger.debug(f"- Assistant: {lst.assistant.name}, Tier: {lst.assistant.tier}, Featured: {lst.assistant.is_featured}")
        else:
            logger.debug(f"hide_standard_tier_assistants is disabled, showing all assistants")

        # --- Pagination for Main List ---
        # Get items per page from request, default to self.paginate_by
        items_per_page = self.request.GET.get('items_per_page')
        try:
            items_per_page = int(items_per_page) if items_per_page else self.paginate_by
            # Limit to valid options: 10, 25, 50, 100
            if items_per_page not in [10, 25, 50, 100]:
                items_per_page = self.paginate_by
        except ValueError:
            items_per_page = self.paginate_by

        # Create a new paginator with the filtered list
        paginator = Paginator(all_listings_for_main_list, items_per_page)
        page_number = self.request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        print(f"[DEBUG] Pagination: {len(all_listings_for_main_list)} total items, page {page_obj.number} of {paginator.num_pages}")
        print(f"[DEBUG] Page object has {len(page_obj.object_list)} items")

        # --- Update Context ---
        context['featured_listings'] = featured_listings
        context['page_obj'] = page_obj
        context['is_paginated'] = page_obj.has_other_pages()
        context['object_list'] = page_obj.object_list  # This is what the template uses
        context['assistant_listings'] = page_obj.object_list  # This is also used in some templates

        context['q_name'] = self.q_name
        context['q_company'] = self.q_company
        context['q_category'] = self.q_category
        context['q_tag'] = self.q_tag
        context['sort_by'] = sort_by

        directory_settings_dict = {
            'featured_scroll_direction': settings_obj.featured_scroll_direction,
            'featured_transition_effect': settings_obj.featured_transition_effect,
            'featured_visible_count': settings_obj.featured_visible_count,
            'featured_autoplay': settings_obj.featured_autoplay,
            'featured_autoplay_delay': settings_obj.featured_autoplay_delay,
        }
        context['directory_settings_dict'] = directory_settings_dict

        # Add tier filtering settings to context
        context['hide_standard_tier_assistants'] = settings_obj.hide_standard_tier_assistants
        context['hide_standard_tier_community_assistants'] = settings_obj.hide_standard_tier_community_assistants

        # Community assistants are no longer included in this view
        # They have their own dedicated page at /assistant/community/

        # Add direct debug output to the terminal
        print(f"\n\n[DIRECT DEBUG] AssistantDirectoryListView final context: hide_standard_tier_assistants={context['hide_standard_tier_assistants']}, hide_standard_tier_community_assistants={context['hide_standard_tier_community_assistants']}\n\n")

        if self.request.user.is_authenticated:
            saved_assistant_ids = set(
                SavedItem.objects.filter(
                    user=self.request.user,
                    item_type='assistant',
                    assistant__isnull=False
                ).values_list('assistant_id', flat=True)
            )
            context['saved_assistant_ids'] = saved_assistant_ids
        else:
            context['saved_assistant_ids'] = set()
        return context


# --- Favorite/Saved Item Views ---

@login_required
@require_POST # Might change depending on final UI interaction (e.g., GET for initial options)
def toggle_saved_item(request):
    """
    Handles the initial interaction when a user clicks the 'favorite' (heart) icon.
    If the item is already saved (in any folder or none), it unfavorites it.
    If the item is NOT saved, it returns options to save directly, create a folder,
    or add to an existing folder. (Actual saving handled by other views).

    Expects POST data: 'item_type' ('company' or 'assistant') and 'item_id'.
    Returns JSON or an HTML partial (e.g., for HTMX).
    """
    item_type = request.POST.get('item_type')
    item_id = request.POST.get('item_id')
    user = request.user

    if not item_type or not item_id:
        return JsonResponse({'status': 'error', 'message': 'Missing parameters'}, status=400)
    try:
        item_id = int(item_id)
    except ValueError:
        return JsonResponse({'status': 'error', 'message': 'Invalid item ID'}, status=400)

    # Log the request for debugging
    print(f"Toggle saved item request: item_type={item_type}, item_id={item_id}, user={user.username}")

    instance = None
    saved_item = None
    try:
        if item_type == 'company':
            try:
                instance = Company.objects.get(pk=item_id)
            except Company.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': f'Company with ID {item_id} not found'}, status=404)
            saved_item = SavedItem.objects.filter(user=user, company=instance).first()
        elif item_type == 'assistant':
            try:
                instance = Assistant.objects.get(pk=item_id)
            except Assistant.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': f'Assistant with ID {item_id} not found'}, status=404)
            saved_item = SavedItem.objects.filter(user=user, assistant=instance).first()
        else:
            return JsonResponse({'status': 'error', 'message': 'Invalid item type'}, status=400)

        if saved_item:
            # Item is already saved, so unfavorite it
            saved_item.delete()
            # TODO: Return response indicating unfavorited (e.g., updated heart icon state)
            # For now, return simple JSON
            return JsonResponse({'status': 'success', 'action': 'unfavorited', 'saved': False})
        else:
            # Item is not saved, return options
            folders = FavoriteFolder.objects.filter(user=user, item_type=item_type).order_by('name')
            # TODO: Return an HTML partial or JSON with options
            # This will likely be rendered into a modal/dropdown by frontend JS/HTMX
            # Example JSON structure:
            return JsonResponse({
                'status': 'options',
                'item_id': item_id,
                'item_type': item_type,
                'item_name': instance.name, # Assuming instance has a 'name' attribute
                'folders': list(folders.values('id', 'name')),
                'saved': False # Indicate it's not currently saved
            })

    except (Company.DoesNotExist, Assistant.DoesNotExist):
         return JsonResponse({'status': 'error', 'message': 'Item not found'}, status=404)
    except Exception as e:
        # Log the error e
        return JsonResponse({'status': 'error', 'message': 'An unexpected error occurred'}, status=500)


@login_required
@require_POST
@transaction.atomic
def create_folder(request):
    """ Creates a new folder without saving an item to it. """
    item_type = request.POST.get('item_type')
    folder_name = request.POST.get('name', '').strip()
    user = request.user

    if not all([item_type, folder_name]):
        return JsonResponse({'status': 'error', 'message': 'Missing parameters (item_type, name)'}, status=400)
    if item_type not in ['company', 'assistant']:
        return JsonResponse({'status': 'error', 'message': 'Invalid item type'}, status=400)

    # Log the request for debugging
    print(f"Create folder request: item_type={item_type}, folder_name={folder_name}, user={user.username}")

    try:
        # Check if a folder with this name already exists for this user and item type
        if FavoriteFolder.objects.filter(user=user, name=folder_name, item_type=item_type).exists():
            return JsonResponse({'status': 'error', 'message': f'A folder named "{folder_name}" already exists for this item type.'}, status=400)

        # Create the folder
        folder = FavoriteFolder.objects.create(
            user=user,
            name=folder_name,
            item_type=item_type
        )
        print(f"Created new folder: {folder.name} (ID: {folder.id})")

        # Return success
        return JsonResponse({
            'status': 'success',
            'message': 'Folder created successfully',
            'folder': {
                'id': folder.id,
                'name': folder.name,
                'item_type': folder.item_type
            }
        })
    except Exception as e:
        print(f"Error creating folder: {e}")
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)


@login_required
@require_POST
@transaction.atomic
def create_folder_and_save(request):
    """ Creates a new folder and saves the item into it. """
    item_type = request.POST.get('item_type')
    item_id = request.POST.get('item_id')
    folder_name = request.POST.get('folder_name', '').strip()
    user = request.user

    if not all([item_type, item_id, folder_name]):
        return JsonResponse({'status': 'error', 'message': 'Missing parameters (item_type, item_id, folder_name)'}, status=400)
    if item_type not in ['company', 'assistant']:
        return JsonResponse({'status': 'error', 'message': 'Invalid item type'}, status=400)
    try:
        item_id = int(item_id)
    except ValueError:
        return JsonResponse({'status': 'error', 'message': 'Invalid item ID'}, status=400)

    # Log the request for debugging
    print(f"Create folder and save request: item_type={item_type}, item_id={item_id}, folder_name={folder_name}, user={user.username}")

    try:
        # 1. Create or get the folder
        folder, created = FavoriteFolder.objects.get_or_create(
            user=user,
            name=folder_name,
            item_type=item_type
        )
        print(f"{'Created new' if created else 'Using existing'} folder: {folder.name} (ID: {folder.id})")

        # 2. Create the SavedItem linked to the folder
        defaults = {'item_type': item_type, 'folder': folder}
        saved_item = None
        if item_type == 'company':
            try:
                instance = Company.objects.get(pk=item_id)
            except Company.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': f'Company with ID {item_id} not found'}, status=404)
            # Use update_or_create to handle potential race conditions or re-saving
            saved_item, created_item = SavedItem.objects.update_or_create(
                user=user, company=instance, defaults=defaults
            )
            print(f"Saved company {instance.name} (ID: {instance.id}) to folder '{folder.name}'")
        elif item_type == 'assistant':
            try:
                instance = Assistant.objects.get(pk=item_id)
            except Assistant.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': f'Assistant with ID {item_id} not found'}, status=404)
            saved_item, created_item = SavedItem.objects.update_or_create(
                user=user, assistant=instance, defaults=defaults
            )
            print(f"Saved assistant {instance.name} (ID: {instance.id}, type: {instance.assistant_type}) to folder '{folder.name}'")

        # 3. Return success
        return JsonResponse({
            'status': 'success',
            'saved': True,
            'action': 'saved_in_new_folder',
            'folder_id': folder.id,
            'folder_name': folder.name
        })

    except (Company.DoesNotExist, Assistant.DoesNotExist):
        return JsonResponse({'status': 'error', 'message': 'Item not found'}, status=404)
    except IntegrityError as e:
         # This might happen if unique_together constraint fails unexpectedly
         return JsonResponse({'status': 'error', 'message': f'Database error: {e}'}, status=400)
    except Exception as e:
        # Log error e
        return JsonResponse({'status': 'error', 'message': 'An unexpected error occurred'}, status=500)


@login_required
@require_POST
@transaction.atomic
def add_item_to_folder(request):
    """ Saves an item into an existing folder. """
    item_type = request.POST.get('item_type')
    item_id = request.POST.get('item_id')
    folder_id = request.POST.get('folder_id')
    user = request.user

    if not all([item_type, item_id, folder_id]):
        return JsonResponse({'status': 'error', 'message': 'Missing parameters (item_type, item_id, folder_id)'}, status=400)
    if item_type not in ['company', 'assistant']:
        return JsonResponse({'status': 'error', 'message': 'Invalid item type'}, status=400)
    try:
        item_id = int(item_id)
        folder_id = int(folder_id)
    except ValueError:
        return JsonResponse({'status': 'error', 'message': 'Invalid item or folder ID'}, status=400)

    # Log the request for debugging
    print(f"Add item to folder request: item_type={item_type}, item_id={item_id}, folder_id={folder_id}, user={user.username}")

    try:
        # 1. Get the folder (ensure it belongs to the user and matches item type)
        try:
            folder = FavoriteFolder.objects.get(pk=folder_id, user=user, item_type=item_type)
            print(f"Found folder: {folder.name} (ID: {folder.id})")
        except FavoriteFolder.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': f'Folder with ID {folder_id} not found or not accessible'}, status=404)

        # 2. Create the SavedItem linked to the folder
        defaults = {'item_type': item_type, 'folder': folder}
        saved_item = None
        if item_type == 'company':
            try:
                instance = Company.objects.get(pk=item_id)
            except Company.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': f'Company with ID {item_id} not found'}, status=404)
            saved_item, created_item = SavedItem.objects.update_or_create(
                user=user, company=instance, defaults=defaults
            )
            print(f"Saved company {instance.name} (ID: {instance.id}) to folder '{folder.name}'")
        elif item_type == 'assistant':
            try:
                instance = Assistant.objects.get(pk=item_id)
            except Assistant.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': f'Assistant with ID {item_id} not found'}, status=404)
            saved_item, created_item = SavedItem.objects.update_or_create(
                user=user, assistant=instance, defaults=defaults
            )
            print(f"Saved assistant {instance.name} (ID: {instance.id}, type: {instance.assistant_type}) to folder '{folder.name}'")

        # 3. Return success
        return JsonResponse({
            'status': 'success',
            'saved': True,
            'action': 'saved_in_folder',
            'folder_id': folder.id,
            'folder_name': folder.name
        })

    except (Company.DoesNotExist, Assistant.DoesNotExist):
        return JsonResponse({'status': 'error', 'message': 'Item not found'}, status=404)
    except FavoriteFolder.DoesNotExist:
         return JsonResponse({'status': 'error', 'message': 'Folder not found or invalid for this item type'}, status=404)
    except IntegrityError as e:
         return JsonResponse({'status': 'error', 'message': f'Database error: {e}'}, status=400)
    except Exception as e:
        # Log error e
        return JsonResponse({'status': 'error', 'message': 'An unexpected error occurred'}, status=500)


@login_required
@require_POST
@transaction.atomic
def save_item_no_folder(request):
    """ Saves an item without assigning it to a folder. """
    item_type = request.POST.get('item_type')
    item_id = request.POST.get('item_id')
    user = request.user

    if not all([item_type, item_id]):
        return JsonResponse({'status': 'error', 'message': 'Missing parameters (item_type, item_id)'}, status=400)
    if item_type not in ['company', 'assistant']:
        return JsonResponse({'status': 'error', 'message': 'Invalid item type'}, status=400)
    try:
        item_id = int(item_id)
    except ValueError:
        return JsonResponse({'status': 'error', 'message': 'Invalid item ID'}, status=400)

    # Log the request for debugging
    print(f"Save item without folder request: item_type={item_type}, item_id={item_id}, user={user.username}")

    try:
        # 1. Create SavedItem with folder=None
        defaults = {'item_type': item_type, 'folder': None} # Explicitly set folder to None
        saved_item = None
        if item_type == 'company':
            try:
                instance = Company.objects.get(pk=item_id)
            except Company.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': f'Company with ID {item_id} not found'}, status=404)
            saved_item, created_item = SavedItem.objects.update_or_create(
                user=user, company=instance, defaults=defaults
            )
            print(f"Saved company {instance.name} (ID: {instance.id}) to favorites without folder")
        elif item_type == 'assistant':
            try:
                instance = Assistant.objects.get(pk=item_id)
            except Assistant.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': f'Assistant with ID {item_id} not found'}, status=404)
            saved_item, created_item = SavedItem.objects.update_or_create(
                user=user, assistant=instance, defaults=defaults
            )
            print(f"Saved assistant {instance.name} (ID: {instance.id}, type: {instance.assistant_type}) to favorites without folder")

        # 3. Return success
        return JsonResponse({
            'status': 'success',
            'saved': True,
            'action': 'saved_no_folder',
            'folder_id': None
        })

    except (Company.DoesNotExist, Assistant.DoesNotExist):
        return JsonResponse({'status': 'error', 'message': 'Item not found'}, status=404)
    except IntegrityError as e:
         return JsonResponse({'status': 'error', 'message': f'Database error: {e}'}, status=400)
    except Exception as e:
        # Log error e
        return JsonResponse({'status': 'error', 'message': 'An unexpected error occurred'}, status=500)


@login_required
@require_GET # Or POST if it modifies state (e.g., creates a folder if needed)
def list_folders_for_item_type(request):
    """ Returns a list of folders for a given item type (for dropdowns). """
    item_type = request.GET.get('item_type')
    user = request.user
    if item_type not in ['company', 'assistant']:
        return JsonResponse({'status': 'error', 'message': 'Invalid item type'}, status=400)

    folders = FavoriteFolder.objects.filter(user=user, item_type=item_type).order_by('name')
    # Could return JSON or an HTML partial
    return JsonResponse({'folders': list(folders.values('id', 'name'))})


@login_required
@require_POST
@transaction.atomic
def edit_favorite_folder(request, folder_id):
    """ Edits the name of a favorite folder. """
    folder = get_object_or_404(FavoriteFolder, pk=folder_id, user=request.user)
    new_name = request.POST.get('name', '').strip()

    if not new_name:
        return JsonResponse({'status': 'error', 'message': 'Folder name cannot be empty.'}, status=400)

    # Check if another folder with the same name and type already exists for the user
    if FavoriteFolder.objects.filter(
        user=request.user,
        name=new_name,
        item_type=folder.item_type
    ).exclude(pk=folder_id).exists():
        return JsonResponse({'status': 'error', 'message': f'A folder named "{new_name}" already exists for this item type.'}, status=400)

    try:
        folder.name = new_name
        folder.save()
        return JsonResponse({'status': 'success', 'message': 'Folder renamed successfully.', 'new_name': new_name})
    except Exception as e:
        # Log error e
        return JsonResponse({'status': 'error', 'message': 'An unexpected error occurred while renaming.'}, status=500)


@login_required
@require_POST
@transaction.atomic
def delete_favorite_folder(request, folder_id):
    """ Deletes a favorite folder. SavedItems within will have their folder set to NULL. """
    folder = get_object_or_404(FavoriteFolder, pk=folder_id, user=request.user)

    try:
        folder_name = folder.name # Get name before deleting
        folder.delete()
        # SavedItems' folder field is automatically set to NULL due to on_delete=models.SET_NULL
        return JsonResponse({'status': 'success', 'message': f'Folder "{folder_name}" deleted successfully.'})
    except Exception as e:
        # Log error e
        return JsonResponse({'status': 'error', 'message': 'An unexpected error occurred while deleting.'}, status=500)

@login_required
@require_GET
def filter_favorites_by_folder(request):
    """
    Filters favorite items by folder_id and item_type for an AJAX request.
    Returns an HTML partial containing the list of items.
    """
    import logging
    logger = logging.getLogger(__name__)

    logger.info("filter_favorites_by_folder called")
    logger.info(f"Request GET params: {request.GET}")

    folder_id_str = request.GET.get('folder_id')
    item_type = request.GET.get('item_type')
    user = request.user

    logger.info(f"Processing request - folder_id: {folder_id_str}, item_type: {item_type}, user: {user.username}")

    if not item_type or item_type not in ['assistant', 'community_assistant', 'company']:
        logger.error(f"Invalid or missing item_type: {item_type}")
        return HttpResponseBadRequest("Invalid or missing item_type.")
    if not folder_id_str:
        logger.error("Missing folder_id")
        return HttpResponseBadRequest("Missing folder_id.")

    base_queryset = SavedItem.objects.filter(user=user)
    logger.info(f"Initial queryset count: {base_queryset.count()}")
    folder = None

    if item_type == 'assistant':
        # For regular assistants, filter by assistant type
        logger.info("Filtering for regular assistants")
        base_queryset = base_queryset.filter(
            assistant__isnull=False,
            item_type='assistant'
        ).exclude(
            assistant__assistant_type=Assistant.TYPE_COMMUNITY
        ).select_related('assistant', 'assistant__company', 'assistant__listing')
        logger.info(f"After filtering for regular assistants, count: {base_queryset.count()}")
        logger.info(f"Assistant.TYPE_COMMUNITY value: {Assistant.TYPE_COMMUNITY}")
        item_type_display_plural = "assistants"
    elif item_type == 'community_assistant':
        # For community assistants, filter by assistant type
        logger.info("Filtering for community assistants")
        base_queryset = base_queryset.filter(
            assistant__isnull=False,
            item_type='assistant',
            assistant__assistant_type=Assistant.TYPE_COMMUNITY
        ).select_related('assistant', 'assistant__company', 'assistant__listing')
        logger.info(f"After filtering for community assistants, count: {base_queryset.count()}")
        logger.info(f"Assistant.TYPE_COMMUNITY value: {Assistant.TYPE_COMMUNITY}")
        item_type_display_plural = "community assistants"
    elif item_type == 'company':
        logger.info("Filtering for companies")
        base_queryset = base_queryset.filter(
            company__isnull=False,
            item_type='company'
        ).select_related('company', 'company__info', 'company__listing')
        logger.info(f"After filtering for companies, count: {base_queryset.count()}")
        item_type_display_plural = "companies"
    else:
        # Should be caught by the initial check, but as a safeguard
        logger.error(f"Invalid item_type specified: {item_type}")
        return HttpResponseBadRequest("Invalid item_type specified.")

    if folder_id_str == 'all':
        logger.info("Filtering for 'all' folder")
        items = base_queryset.order_by('-created_at')
    elif folder_id_str == 'uncategorized':
        logger.info("Filtering for 'uncategorized' folder")
        # Get items that are not in any folder
        items = base_queryset.filter(folder__isnull=True).order_by('-created_at')
        logger.info(f"Uncategorized items count: {items.count()}")
    else:
        try:
            folder_id = int(folder_id_str)
            logger.info(f"Filtering for specific folder ID: {folder_id}")

            # Check if folder exists
            folder_exists = FavoriteFolder.objects.filter(id=folder_id, user=user).exists()
            logger.info(f"Folder exists: {folder_exists}")

            # Check if item_type is in the model
            has_item_type = hasattr(FavoriteFolder, 'item_type')
            logger.info(f"FavoriteFolder has item_type field: {has_item_type}")

            if has_item_type:
                folder = get_object_or_404(FavoriteFolder, id=folder_id, user=user, item_type=item_type)
            else:
                folder = get_object_or_404(FavoriteFolder, id=folder_id, user=user)

            logger.info(f"Found folder: {folder.name}")

            items = base_queryset.filter(folder=folder).order_by('-created_at')
            logger.info(f"Items count after folder filtering: {items.count()}")

            # Debug the SQL query
            logger.info(f"SQL Query: {items.query}")

        except ValueError:
            logger.error(f"Invalid folder_id format: {folder_id_str}")
            return HttpResponseBadRequest("Invalid folder_id format.")
        except FavoriteFolder.DoesNotExist:
            logger.error(f"Folder not found or not accessible: {folder_id_str}")
            return HttpResponseBadRequest("Folder not found or not accessible.")
        except Exception as e:
            logger.error(f"Error retrieving folder: {str(e)}")
            return HttpResponseBadRequest(f"Error retrieving folder: {str(e)}")

    # Render the partial template with the filtered items
    context = {
        'items': items,
        'item_type': item_type,
        'item_type_display_plural': item_type_display_plural,
        'folder_id': folder_id_str,
        'folder': folder, # Will be None for 'all' or 'uncategorized'
        'user': request.user # For any user-specific rendering in partials
    }

    logger.info(f"Final items count: {items.count()}")
    logger.info(f"Rendering template with context keys: {context.keys()}")

    try:
        html_response = render(request, 'directory/partials/_filtered_favorite_items_list.html', context)
        logger.info(f"Successfully rendered template, content length: {len(html_response.content)}")
        return html_response
    except Exception as e:
        logger.error(f"Error rendering template: {str(e)}")
        return HttpResponseBadRequest(f"Error rendering template: {str(e)}")

class MyFavoritesListView(LoginRequiredMixin, ListView):
    """Displays the current user's saved companies and assistants."""
    model = SavedItem
    template_name = 'directory/my_favorites.html'
    # context_object_name = 'saved_items' # Let get_context_data handle specific names
    paginate_by = 10  # Default to 10 items per page

    def get_queryset(self):
        # This method filters based on the ACTIVE tab and its filters BEFORE pagination
        user = self.request.user

        # Get items per page from request, default to self.paginate_by
        items_per_page = self.request.GET.get('items_per_page')
        try:
            items_per_page = int(items_per_page) if items_per_page else self.paginate_by
            # Limit to valid options: 10, 25, 50, 100
            if items_per_page not in [10, 25, 50, 100]:
                items_per_page = self.paginate_by
            self.paginate_by = items_per_page
        except ValueError:
            pass

        # Include prefetch for folder
        queryset = SavedItem.objects.filter(user=user).select_related(
            'company__info',
            'company__listing',
            'assistant__company',
            'assistant__listing',
            'folder' # Select related folder
        ) # Remove default ordering here, apply based on tab

        # Get filter parameters and active tab
        q_name = self.request.GET.get('q_name', '').strip()
        q_company = self.request.GET.get('q_company', '').strip() # Specific to assistant filter
        q_category = self.request.GET.get('q_category', '').strip()
        q_tag = self.request.GET.get('q_tag', '').strip()
        active_tab = self.request.GET.get('tab', 'assistants')
        sort_by = self.request.GET.get('sort_by', 'date_added') # Default sort by date added (was 'tier' in main lists)

        # Apply filters based on the active tab
        if active_tab == 'assistants':
            # Filter for regular assistants (excluding community assistants)
            queryset = queryset.filter(
                item_type='assistant'
            ).exclude(
                assistant__assistant_type=Assistant.TYPE_COMMUNITY
            )
            if q_name:
                # Search name OR description
                queryset = queryset.filter(
                    Q(assistant__name__icontains=q_name) |
                    Q(assistant__description__icontains=q_name)
                )
            if q_company:
                queryset = queryset.filter(assistant__company__name__icontains=q_company)
            if q_category:
                queryset = queryset.filter(assistant__listing__categories__contains=q_category, assistant__listing__isnull=False)
            if q_tag:
                queryset = queryset.filter(assistant__listing__tags__contains=q_tag, assistant__listing__isnull=False)
        elif active_tab == 'community_assistants':
            queryset = queryset.filter(item_type='assistant', assistant__assistant_type=Assistant.TYPE_COMMUNITY)
            if q_name:
                # Search name OR description
                queryset = queryset.filter(
                    Q(assistant__name__icontains=q_name) |
                    Q(assistant__description__icontains=q_name)
                )
            if q_company:
                queryset = queryset.filter(assistant__company__name__icontains=q_company)
            if q_category:
                queryset = queryset.filter(assistant__listing__categories__contains=q_category, assistant__listing__isnull=False)
            if q_tag:
                queryset = queryset.filter(assistant__listing__tags__contains=q_tag, assistant__listing__isnull=False)
        elif active_tab == 'companies':
            queryset = queryset.filter(item_type='company')
            if q_name:
                 # Search name OR description
                queryset = queryset.filter(
                    Q(company__name__icontains=q_name) |
                    Q(company__info__description__icontains=q_name)
                )
            if q_category:
                queryset = queryset.filter(company__listing__categories__contains=q_category, company__listing__isnull=False)
            if q_tag:
                queryset = queryset.filter(company__listing__tags__contains=q_tag, company__listing__isnull=False)
            # q_company filter doesn't apply here

        # Apply ordering based on the active tab and sort_by parameter AFTER filtering
        # Define tier rank map for sorting
        tier_rank_map = {
            Assistant.TIER_GOLD: 1, Assistant.TIER_SILVER: 2, Assistant.TIER_BRONZE: 3, Assistant.TIER_STANDARD: 4,
            Company.TIER_GOLD: 1, Company.TIER_SILVER: 2, Company.TIER_BRONZE: 3, Company.TIER_STANDARD: 4,
        }

        if active_tab == 'assistants' or active_tab == 'community_assistants':
            if sort_by == 'name':
                queryset = queryset.order_by('assistant__name')
            elif sort_by == 'rating':
                # Order by listing rating descending (nulls last)
                queryset = queryset.order_by(F('assistant__listing__avg_rating').desc(nulls_last=True))
            elif sort_by == 'tier':
                 # Use Case/When for custom tier ordering (requires Django 3.2+)
                 # For simplicity, we might need to sort in Python if complex tier logic is needed
                 # Or rely on a pre-calculated field if performance is critical.
                 # Basic tier sort (alphabetical by tier display name might work if names are consistent)
                 # Let's try sorting by the tier value itself, handling None
                 queryset = queryset.order_by(
                     Case(
                         When(assistant__tier=Assistant.TIER_GOLD, then=Value(1)),
                         When(assistant__tier=Assistant.TIER_SILVER, then=Value(2)),
                         When(assistant__tier=Assistant.TIER_BRONZE, then=Value(3)),
                         When(assistant__tier=Assistant.TIER_STANDARD, then=Value(4)),
                         default=Value(5), # Put None/Other last
                         output_field=IntegerField(),
                     ),
                     '-created_at' # Secondary sort by date added within tier
                 )
            else: # Default to date_added (SavedItem creation date)
                queryset = queryset.order_by('-created_at') # Newest first
        elif active_tab == 'companies':
            if sort_by == 'name':
                queryset = queryset.order_by('company__name')
            elif sort_by == 'rating':
                 queryset = queryset.order_by(F('company__listing__avg_rating').desc(nulls_last=True))
            elif sort_by == 'tier':
                 queryset = queryset.order_by(
                     Case(
                         When(company__tier=Company.TIER_GOLD, then=Value(1)),
                         When(company__tier=Company.TIER_SILVER, then=Value(2)),
                         When(company__tier=Company.TIER_BRONZE, then=Value(3)),
                         When(company__tier=Company.TIER_STANDARD, then=Value(4)),
                         default=Value(5),
                         output_field=IntegerField(),
                     ),
                     '-created_at'
                 )
            else: # Default to date_added
                queryset = queryset.order_by('-created_at') # Newest first

        return queryset.distinct()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        active_tab = self.request.GET.get('tab', 'assistants') # Default to assistants

        # Fetch all folders for the user, grouped by type
        all_folders = FavoriteFolder.objects.filter(user=user).order_by('item_type', 'name')
        context['company_folders'] = all_folders.filter(item_type='company')
        context['assistant_folders'] = all_folders.filter(item_type='assistant')
        context['community_assistant_folders'] = all_folders.filter(item_type='assistant')  # Use same folders for community assistants

        # The main queryset (object_list) is already filtered for the active tab by get_queryset
        # We need to group these items by folder (including None for items without a folder)

        items_in_folders = {}
        items_without_folder = []

        # Separate items based on the active tab and group by folder
        target_items = context['object_list'] # Already filtered and paginated for the active tab

        for item in target_items:
            folder_id = item.folder_id if item.folder else None
            if folder_id:
                if folder_id not in items_in_folders:
                    # Store the folder object itself for easy access in the template
                    items_in_folders[folder_id] = {'folder': item.folder, 'items': []}
                items_in_folders[folder_id]['items'].append(item)
            else:
                items_without_folder.append(item)

        # Assign grouped items based on the active tab
        if active_tab == 'assistants':
            context['assistant_items_in_folders'] = items_in_folders
            context['assistant_items_without_folder'] = items_without_folder
            # Initialize empty community assistant items
            context['community_assistant_items_in_folders'] = {}
            context['community_assistant_items_without_folder'] = []
            # Fetch non-paginated, non-filtered company items for the other tab (grouped), ordered alphabetically
            company_items_all = SavedItem.objects.filter(user=user, item_type='company').select_related(
                'company__info', 'company__listing', 'folder'
            ).order_by('company__name') # Order by company name
            context['company_items_in_folders'] = {}
            context['company_items_without_folder'] = []
            for item in company_items_all:
                 folder_id = item.folder_id if item.folder else None
                 if folder_id:
                     if folder_id not in context['company_items_in_folders']:
                         context['company_items_in_folders'][folder_id] = {'folder': item.folder, 'items': []}
                     context['company_items_in_folders'][folder_id]['items'].append(item)
                 else:
                     context['company_items_without_folder'].append(item)

            # Fetch community assistants for the community tab
            community_assistant_items_all = SavedItem.objects.filter(
                user=user,
                item_type='assistant',
                assistant__assistant_type=Assistant.TYPE_COMMUNITY
            ).select_related('assistant__company', 'assistant__listing', 'folder').order_by('assistant__name')

            for item in community_assistant_items_all:
                folder_id = item.folder_id if item.folder else None
                if folder_id:
                    if folder_id not in context['community_assistant_items_in_folders']:
                        context['community_assistant_items_in_folders'][folder_id] = {'folder': item.folder, 'items': []}
                    context['community_assistant_items_in_folders'][folder_id]['items'].append(item)
                else:
                    context['community_assistant_items_without_folder'].append(item)

        elif active_tab == 'community_assistants':
            context['community_assistant_items_in_folders'] = items_in_folders
            context['community_assistant_items_without_folder'] = items_without_folder
            # Initialize empty regular assistant items
            context['assistant_items_in_folders'] = {}
            context['assistant_items_without_folder'] = []
            # Fetch non-paginated, non-filtered company items for the other tab (grouped), ordered alphabetically
            company_items_all = SavedItem.objects.filter(user=user, item_type='company').select_related(
                'company__info', 'company__listing', 'folder'
            ).order_by('company__name') # Order by company name
            context['company_items_in_folders'] = {}
            context['company_items_without_folder'] = []
            for item in company_items_all:
                 folder_id = item.folder_id if item.folder else None
                 if folder_id:
                     if folder_id not in context['company_items_in_folders']:
                         context['company_items_in_folders'][folder_id] = {'folder': item.folder, 'items': []}
                     context['company_items_in_folders'][folder_id]['items'].append(item)
                 else:
                     context['company_items_without_folder'].append(item)

            # Fetch regular assistants for the assistants tab
            regular_assistant_items_all = SavedItem.objects.filter(
                user=user,
                item_type='assistant',
                assistant__assistant_type__in=[Assistant.TYPE_GENERAL, Assistant.TYPE_SUPPORT]
            ).select_related('assistant__company', 'assistant__listing', 'folder').order_by('assistant__name')

            for item in regular_assistant_items_all:
                folder_id = item.folder_id if item.folder else None
                if folder_id:
                    if folder_id not in context['assistant_items_in_folders']:
                        context['assistant_items_in_folders'][folder_id] = {'folder': item.folder, 'items': []}
                    context['assistant_items_in_folders'][folder_id]['items'].append(item)
                else:
                    context['assistant_items_without_folder'].append(item)

        else: # active_tab == 'companies'
            context['company_items_in_folders'] = items_in_folders
            context['company_items_without_folder'] = items_without_folder
            # Initialize empty community assistant items
            context['community_assistant_items_in_folders'] = {}
            context['community_assistant_items_without_folder'] = []

            # Fetch regular assistants for the assistants tab
            regular_assistant_items_all = SavedItem.objects.filter(
                user=user,
                item_type='assistant',
                assistant__assistant_type__in=[Assistant.TYPE_GENERAL, Assistant.TYPE_SUPPORT]
            ).select_related('assistant__company', 'assistant__listing', 'folder').order_by('assistant__name')

            context['assistant_items_in_folders'] = {}
            context['assistant_items_without_folder'] = []
            for item in regular_assistant_items_all:
                folder_id = item.folder_id if item.folder else None
                if folder_id:
                    if folder_id not in context['assistant_items_in_folders']:
                        context['assistant_items_in_folders'][folder_id] = {'folder': item.folder, 'items': []}
                    context['assistant_items_in_folders'][folder_id]['items'].append(item)
                else:
                    context['assistant_items_without_folder'].append(item)

            # Fetch community assistants for the community tab
            community_assistant_items_all = SavedItem.objects.filter(
                user=user,
                item_type='assistant',
                assistant__assistant_type=Assistant.TYPE_COMMUNITY
            ).select_related('assistant__company', 'assistant__listing', 'folder').order_by('assistant__name')

            for item in community_assistant_items_all:
                folder_id = item.folder_id if item.folder else None
                if folder_id:
                    if folder_id not in context['community_assistant_items_in_folders']:
                        context['community_assistant_items_in_folders'][folder_id] = {'folder': item.folder, 'items': []}
                    context['community_assistant_items_in_folders'][folder_id]['items'].append(item)
                else:
                    context['community_assistant_items_without_folder'].append(item)


        # Pass filter parameters and active tab back to context
        context['q_name'] = self.request.GET.get('q_name', '')
        context['q_company'] = self.request.GET.get('q_company', '')
        context['q_category'] = self.request.GET.get('q_category', '')
        context['q_tag'] = self.request.GET.get('q_tag', '')
        context['active_tab'] = self.request.GET.get('tab', 'assistants')
        context['sort_by'] = self.request.GET.get('sort_by', 'date_added') # Pass sort_by to context
        return context


# --- Rating View ---

@login_required
@require_POST
@transaction.atomic # Ensure rating save and listing update are atomic
def rate_assistant_view(request, assistant_id):
    """
    Handles POST requests to rate an assistant from the directory.
    Expects 'rating' in the POST data and assistant_id from the URL.
    """
    try:
        rating_value = int(request.POST.get('rating')) # Renamed to avoid clash
        if not (1 <= rating_value <= 5):
            raise ValueError("Rating must be between 1 and 5.")
    except (TypeError, ValueError, KeyError) as e:
        return JsonResponse({'status': 'error', 'message': f'Invalid input: {e}'}, status=400)

    user = request.user

    # Log the request for debugging
    print(f"Rating request received: assistant_id={assistant_id}, rating={rating_value}, user={user.username}")

    try:
        assistant = Assistant.objects.get(pk=assistant_id)
    except Assistant.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': f'Assistant with ID {assistant_id} not found'}, status=404)

    # Try to get the listing, create it if it doesn't exist
    try:
        listing = AssistantListing.objects.get(assistant=assistant)
    except AssistantListing.DoesNotExist:
        # Create a new listing for this assistant
        listing = AssistantListing.objects.create(
            assistant=assistant,
            is_listed=assistant.is_public,
            short_description=assistant.description or '',
            avg_rating=decimal.Decimal('0.0'),
            total_ratings=0,
            tags=[],
            categories=[]
        )
        print(f"Created new AssistantListing for assistant {assistant.id}")

    # For community assistants, we always allow rating if they exist
    if assistant.assistant_type == Assistant.TYPE_COMMUNITY:
        # Allow rating even if not public
        pass
    elif not assistant.is_public or not listing.is_listed:
        return JsonResponse({'status': 'error', 'message': 'Assistant not found or not available for rating.'}, status=404)

    try:
        # Get comment from POST data
        comment = request.POST.get('comment', '')

        # Save the individual rating with comment
        rating_obj, created = DirectoryRating.objects.update_or_create(
            user=user, assistant=assistant, defaults={'rating': rating_value, 'comment': comment}
        )

        # Recalculate stats based on ALL relevant ratings
        interaction_ratings = Interaction.objects.filter(assistant=assistant, rating__isnull=False)
        directory_ratings = DirectoryRating.objects.filter(assistant=assistant)
        total_interaction_rating = interaction_ratings.aggregate(Sum('rating'))['rating__sum'] or 0
        total_directory_rating = directory_ratings.aggregate(Sum('rating'))['rating__sum'] or 0
        count_interaction = interaction_ratings.count()
        count_directory = directory_ratings.count()
        total_ratings = count_interaction + count_directory

        if total_ratings > 0:
            total_sum = total_interaction_rating + total_directory_rating
            avg_rating = (decimal.Decimal(total_sum) / decimal.Decimal(total_ratings)).quantize(decimal.Decimal("0.01"))
        else:
            avg_rating = decimal.Decimal('0.00')

        # Update the listing directly in the database using queryset update
        rows_updated = AssistantListing.objects.filter(pk=listing.pk).update(
            avg_rating=avg_rating,
            total_ratings=total_ratings,
            updated_at=timezone.now()
        )

        # Fetch the updated listing again to ensure we have committed values
        listing.refresh_from_db()

        # Render the updated stars HTML here using the refreshed values
        stars_context = {
            'avg_rating': listing.avg_rating,
            'total_ratings': listing.total_ratings
        }
        rendered_stars_html = render_to_string('directory/partials/render_stars_partial.html', stars_context)

        return JsonResponse({
            'status': 'success',
            'message': 'Rating saved successfully.',
            'new_average': float(listing.avg_rating),
            'total_ratings': listing.total_ratings,
            'rendered_stars_html': rendered_stars_html
        })

    except IntegrityError:
        return JsonResponse({'status': 'error', 'message': 'Integrity error during rating.'}, status=500)
    except Exception as e:
        print(f"Error in rate_assistant_view: {e}")
        return JsonResponse({'status': 'error', 'message': 'An unexpected error occurred.'}, status=500)

@login_required
@require_POST
@transaction.atomic # Ensure rating save and listing update are atomic
def rate_company_view(request):
    """
    Handles POST requests to rate a company from the directory.
    Expects 'company_id' and 'rating' in the POST data.
    """
    try:
        company_id = int(request.POST.get('company_id'))
        rating_value = int(request.POST.get('rating')) # Renamed to avoid clash
        if not (1 <= rating_value <= 5):
            raise ValueError("Rating must be between 1 and 5.")
    except (TypeError, ValueError, KeyError) as e:
        return JsonResponse({'status': 'error', 'message': f'Invalid input: {e}'}, status=400)

    user = request.user

    # Log the request for debugging
    print(f"Rating request received: company_id={company_id}, rating={rating_value}, user={user.username}")

    try:
        company = Company.objects.get(pk=company_id)
    except Company.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': f'Company with ID {company_id} not found'}, status=404)

    # Try to get the listing, create it if it doesn't exist
    try:
        listing = CompanyListing.objects.get(company=company)
    except CompanyListing.DoesNotExist:
        # Create a new listing for this company
        listing = CompanyListing.objects.create(
            company=company,
            is_listed=True,  # Default to listed
            short_description=company.info.description if hasattr(company, 'info') and company.info else '',
            avg_rating=decimal.Decimal('0.0'),
            total_ratings=0,
            tags=[],
            categories=[]
        )
        print(f"Created new CompanyListing for company {company.id}")

    # Always allow rating for companies that exist
    if not listing.is_listed:
         return JsonResponse({'status': 'error', 'message': 'Company not found or not available for rating.'}, status=404)

    try:
        # Get comment from POST data
        comment = request.POST.get('comment', '')

        # Save the individual rating with comment
        rating_obj, created = DirectoryRating.objects.update_or_create(
            user=user, company=company, defaults={'rating': rating_value, 'comment': comment}
        )

        # Recalculate stats based on ALL relevant ratings
        company_ratings = DirectoryRating.objects.filter(company=company)
        total_ratings = company_ratings.count()
        if total_ratings > 0:
            total_sum = company_ratings.aggregate(Sum('rating'))['rating__sum'] or 0
            avg_rating = (decimal.Decimal(total_sum) / decimal.Decimal(total_ratings)).quantize(decimal.Decimal("0.01"))
        else:
            avg_rating = decimal.Decimal('0.00')

        # Update the listing directly in the database using queryset update
        rows_updated = CompanyListing.objects.filter(pk=listing.pk).update(
            avg_rating=avg_rating,
            total_ratings=total_ratings,
            updated_at=timezone.now()
        )

        # Fetch the updated listing again to ensure we have committed values
        listing.refresh_from_db()

        # Render the updated stars HTML here using the refreshed values
        stars_context = {
            'avg_rating': listing.avg_rating,
            'total_ratings': listing.total_ratings
        }
        rendered_stars_html = render_to_string('directory/partials/render_stars_partial.html', stars_context)

        return JsonResponse({
            'status': 'success',
            'message': 'Company rating saved successfully.',
            'new_average': float(listing.avg_rating),
            'total_ratings': listing.total_ratings,
            'rendered_stars_html': rendered_stars_html
        })
    except IntegrityError:
        return JsonResponse({'status': 'error', 'message': 'Integrity error during rating.'}, status=500)
    except Exception as e:
        print(f"Error in rate_company_view: {e}")
        return JsonResponse({'status': 'error', 'message': 'An unexpected error occurred.'}, status=500)


@require_GET
def get_rendered_stars_view(request, assistant_id):
    """
    Fetches the current rating for an assistant and returns the rendered
    HTML for the static star display using the template tag.
    """
    try:
        assistant = Assistant.objects.get(pk=assistant_id)
    except Assistant.DoesNotExist:
        # If assistant doesn't exist, return empty stars
        html = render_to_string('directory/partials/render_stars_partial.html', {'avg_rating': 0, 'total_ratings': 0})
        return HttpResponse(html)

    # Log the request for debugging
    print(f"Rendering stars for assistant ID: {assistant_id}, type: {assistant.assistant_type}")

    if not hasattr(assistant, 'listing'):
        # Create a listing if it doesn't exist
        try:
            listing = AssistantListing.objects.create(
                assistant=assistant,
                is_listed=assistant.is_public,
                short_description=assistant.description or '',
                avg_rating=decimal.Decimal('0.0'),
                total_ratings=0,
                tags=[],
                categories=[]
            )
            print(f"Created new AssistantListing for assistant {assistant.id} in get_rendered_stars_view")
            html = render_to_string('directory/partials/render_stars_partial.html', {'avg_rating': 0, 'total_ratings': 0})
            return HttpResponse(html)
        except Exception as e:
            print(f"Error creating listing in get_rendered_stars_view: {e}")
            html = render_to_string('directory/partials/render_stars_partial.html', {'avg_rating': 0, 'total_ratings': 0})
            return HttpResponse(html)

    try:
        listing = assistant.listing
        context = {
            'avg_rating': listing.avg_rating,
            'total_ratings': listing.total_ratings
        }
        html = render_to_string('directory/partials/render_stars_partial.html', context)
        return HttpResponse(html)
    except Exception as e:
        print(f"Error in get_rendered_stars_view for Assistant {assistant_id}: {e}")
        html = render_to_string('directory/partials/render_stars_partial.html', {'avg_rating': 0, 'total_ratings': 0, 'error': True})
        return HttpResponse(html, status=500) # Corrected indentation


@require_GET
def get_rendered_company_stars_view(request, company_id):
    """
    Fetches the current rating for a company and returns the rendered
    HTML for the static star display using the template tag.
    """
    try:
        company = Company.objects.get(pk=company_id)
    except Company.DoesNotExist:
        # If company doesn't exist, return empty stars
        html = render_to_string('directory/partials/render_stars_partial.html', {'avg_rating': 0, 'total_ratings': 0})
        return HttpResponse(html)

    # Log the request for debugging
    print(f"Rendering stars for company ID: {company_id}")

    try:
        listing = CompanyListing.objects.get(company_id=company_id)
    except CompanyListing.DoesNotExist:
        # Create a listing if it doesn't exist
        try:
            listing = CompanyListing.objects.create(
                company=company,
                is_listed=True,
                short_description=company.info.description if hasattr(company, 'info') and company.info else '',
                avg_rating=decimal.Decimal('0.0'),
                total_ratings=0,
                tags=[],
                categories=[]
            )
            print(f"Created new CompanyListing for company {company.id} in get_rendered_company_stars_view")
            html = render_to_string('directory/partials/render_stars_partial.html', {'avg_rating': 0, 'total_ratings': 0})
            return HttpResponse(html)
        except Exception as e:
            print(f"Error creating listing in get_rendered_company_stars_view: {e}")
            html = render_to_string('directory/partials/render_stars_partial.html', {'avg_rating': 0, 'total_ratings': 0})
            return HttpResponse(html)

    try:
        context = {
            'avg_rating': listing.avg_rating,
            'total_ratings': listing.total_ratings
        }
        html = render_to_string('directory/partials/render_stars_partial.html', context)
        return HttpResponse(html)
    except Exception as e:
        print(f"Error in get_rendered_company_stars_view for Company {company_id}: {e}")
        html = render_to_string('directory/partials/render_stars_partial.html', {'avg_rating': 0, 'total_ratings': 0, 'error': True})
        return HttpResponse(html, status=500)


# --- Directory Settings Views ---
@staff_member_required
def directory_settings_view(request):
    """View to manage directory-wide settings."""
    # Get the singleton instance
    settings_instance = DirectorySettings.load()

    if request.method == 'POST':
        form = DirectorySettingsForm(request.POST, instance=settings_instance)
        if form.is_valid():
            settings = form.save()
            import logging
            logger = logging.getLogger('directory')
            logger.info(f"Directory settings saved: hide_standard_tier_companies={settings.hide_standard_tier_companies}, hide_standard_tier_assistants={settings.hide_standard_tier_assistants}")
            messages.success(request, 'Directory settings updated successfully.')
            return redirect('directory:settings') # Redirect back to the same page
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = DirectorySettingsForm(instance=settings_instance)

    context = {
        'form': form,
        'page_title': 'Directory Settings' # Add a title for the template
    }
    return render(request, 'directory/directory_settings.html', context)

@staff_member_required
def toggle_company_filter(request):
    """Toggle the hide_standard_tier_companies setting."""
    if request.method == 'POST':
        print("\n\n[DIRECT DEBUG] toggle_company_filter view called\n\n")

        settings_instance = DirectorySettings.load()
        old_value = settings_instance.hide_standard_tier_companies
        settings_instance.hide_standard_tier_companies = not old_value

        print(f"\n\n[DIRECT DEBUG] Toggling hide_standard_tier_companies from {old_value} to {settings_instance.hide_standard_tier_companies}\n\n")

        settings_instance.save()

        print(f"\n\n[DIRECT DEBUG] After save: hide_standard_tier_companies={settings_instance.hide_standard_tier_companies}\n\n")

        # Verify the change was saved by loading a fresh instance
        fresh_settings = DirectorySettings.objects.get(pk=1)
        print(f"\n\n[DIRECT DEBUG] Fresh settings check: hide_standard_tier_companies={fresh_settings.hide_standard_tier_companies}\n\n")

        messages.success(request, f"Standard tier companies filter {'enabled' if settings_instance.hide_standard_tier_companies else 'disabled'}.")

    # Redirect back to the company list page
    return redirect('directory:company_list')

@staff_member_required
def toggle_assistant_filter(request):
    """Toggle the hide_standard_tier_assistants setting."""
    if request.method == 'POST':
        print("\n\n[DIRECT DEBUG] toggle_assistant_filter view called\n\n")

        settings_instance = DirectorySettings.load()
        old_value = settings_instance.hide_standard_tier_assistants
        settings_instance.hide_standard_tier_assistants = not old_value

        print(f"\n\n[DIRECT DEBUG] Toggling hide_standard_tier_assistants from {old_value} to {settings_instance.hide_standard_tier_assistants}\n\n")

        settings_instance.save()

        print(f"\n\n[DIRECT DEBUG] After save: hide_standard_tier_assistants={settings_instance.hide_standard_tier_assistants}\n\n")

        # Verify the change was saved by loading a fresh instance
        fresh_settings = DirectorySettings.objects.get(pk=1)
        print(f"\n\n[DIRECT DEBUG] Fresh settings check: hide_standard_tier_assistants={fresh_settings.hide_standard_tier_assistants}\n\n")

        messages.success(request, f"Standard tier assistants filter {'enabled' if settings_instance.hide_standard_tier_assistants else 'disabled'}.")

    # Redirect back to the assistant list page
    return redirect('directory:assistant_list')
