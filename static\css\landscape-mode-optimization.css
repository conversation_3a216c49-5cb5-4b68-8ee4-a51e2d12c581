/**
 * Landscape Mode Optimization CSS
 * Improves the mobile experience in landscape orientation
 */

/* Base landscape mode optimizations for mobile devices */
@media (max-width: 991.98px) and (orientation: landscape) {
  /* Adjust container padding for better space utilization */
  .container, .container-fluid {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }

  /* Improve vertical spacing in landscape mode */
  .py-5 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }

  /* Adjust hero section for landscape mode */
  .hero-section .container.py-5 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  /* Optimize vertical space usage */
  .mb-4 {
    margin-bottom: 1rem !important;
  }

  .mb-5 {
    margin-bottom: 1.5rem !important;
  }

  /* Adjust headings for landscape mode */
  h1, .h1 { font-size: 1.75rem !important; }
  h2, .h2 { font-size: 1.5rem !important; }
}

/* Directory Cards Landscape Mode Optimization */
@media (max-width: 768px) and (orientation: landscape) {
  /* Optimize directory cards layout for landscape */
  .directory-card {
    padding: 0.75rem !important;
    margin-bottom: 1rem !important;
    height: auto !important;
    min-height: 0 !important;
    max-height: none !important;
  }

  /* Adjust row layout for landscape mode - use horizontal layout */
  .directory-card .row {
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    gap: 0.5rem !important;
    padding-top: 0.5rem !important;
  }

  /* Optimize logo container size for landscape */
  .logo-container {
    height: 60px !important;
    width: 60px !important;
    min-height: 60px !important;
    min-width: 60px !important;
    max-height: 60px !important;
    max-width: 60px !important;
    margin: 0 !important;
  }

  /* Adjust logo column for landscape */
  .directory-item-link-wrapper .col-md-3 {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    width: 70px !important;
    min-width: 70px !important;
    max-width: 70px !important;
    margin-bottom: 0 !important;
    padding: 0 0 0 10px !important;
  }

  /* Optimize name and company column for landscape */
  .directory-item-link-wrapper .col-md-4 {
    width: 25% !important;
    min-width: 25% !important;
    max-width: 25% !important;
    padding: 0 0.5rem !important;
  }

  /* Optimize description column for landscape */
  .directory-item-link-wrapper .col-md-5 {
    width: 35% !important;
    min-width: 35% !important;
    max-width: 35% !important;
    padding: 0 0.5rem !important;
  }

  /* Optimize text display in cards */
  .directory-card h6 {
    font-size: 0.9rem !important;
    margin-bottom: 0.25rem !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .directory-card p {
    font-size: 0.8rem !important;
    margin-bottom: 0.25rem !important;
    line-height: 1.2 !important;
  }

  .directory-card .item-description {
    max-height: 60px !important;
    overflow: hidden !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 3 !important;
    -webkit-box-orient: vertical !important;
    font-size: 0.8rem !important;
  }

  /* Optimize badge display */
  .directory-card .badge {
    font-size: 0.7rem !important;
    padding: 0.2rem 0.4rem !important;
  }

  /* Adjust contact info column */
  .directory-card .col-md-2.d-flex.flex-column.justify-content-center {
    width: 20% !important;
    min-width: 20% !important;
    max-width: 20% !important;
    padding: 0 0.25rem !important;
  }

  /* Adjust rating column */
  .directory-card .col-md-2.d-flex.flex-column.align-items-end {
    width: 15% !important;
    min-width: 15% !important;
    max-width: 15% !important;
    padding: 0 0.5rem 0 0 !important;
  }

  /* Optimize contact information display */
  .contact-info {
    margin-bottom: 0 !important;
    font-size: 0.75rem !important;
  }

  .contact-info li {
    margin-bottom: 0.25rem !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 100% !important;
  }

  .contact-info i {
    font-size: 0.8rem !important;
    width: 12px !important;
    text-align: center !important;
    margin-right: 0.25rem !important;
  }

  .contact-text {
    display: inline !important;
    color: #cccccc !important;
  }

  /* Optimize rating display */
  .rating-display-container {
    margin-bottom: 0.5rem !important;
    transform: scale(0.85) !important;
    transform-origin: right !important;
  }

  /* Position action buttons for landscape */
  .directory-card .action-buttons-container {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-end !important;
    justify-content: center !important;
  }

  /* Optimize action buttons */
  .directory-card .btn-sm {
    padding: 0.2rem 0.4rem !important;
    font-size: 0.75rem !important;
    line-height: 1.2 !important;
  }

  /* Adjust link wrapper width */
  .directory-item-link-wrapper {
    width: 65% !important;
    min-width: 65% !important;
    max-width: 65% !important;
    margin-right: 0 !important;
  }
}

/* Company Detail Page Landscape Mode Optimization */
@media (max-width: 768px) and (orientation: landscape) {
  /* Optimize company detail layout for landscape */
  .card-header.bg-light.d-flex.align-items-center {
    flex-direction: row !important;
    justify-content: flex-start !important;
    align-items: center !important;
    padding: 0.75rem !important;
  }

  /* Adjust company logo size for landscape */
  .company-logo-container,
  .company-logo-container-sidebar {
    height: 80px !important;
    width: 80px !important;
    min-width: 80px !important;
    min-height: 80px !important;
    max-width: 80px !important;
    max-height: 80px !important;
  }

  .company-header-logo,
  .company-sidebar-logo {
    height: 80px !important;
    width: 80px !important;
    max-width: 80px !important;
    max-height: 80px !important;
  }

  /* Optimize sidebar layout for landscape */
  .col-lg-3 .card-body {
    padding: 1rem !important;
  }

  /* Adjust company info layout for landscape */
  .company-info-section {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: wrap !important;
    gap: 1rem !important;
  }

  /* Optimize contact information display */
  .contact-info-item {
    margin-bottom: 0.5rem !important;
    display: inline-block !important;
    margin-right: 1rem !important;
  }
}

/* Featured Carousel Landscape Mode Optimization */
@media (max-width: 768px) and (orientation: landscape) {
  /* Adjust featured carousel for landscape */
  .featured-carousel-container,
  .company-logo-carousel-container {
    padding: 0.5rem 0 !important;
    margin-bottom: 1rem !important;
  }

  /* Optimize carousel items for landscape */
  .featured-carousel-item,
  .company-logo-item {
    padding: 0.5rem !important;
  }

  /* Adjust logo container in carousel for landscape */
  .featured-item-wrapper .logo-container,
  .company-logo-item .logo-container {
    height: 70px !important;
    width: 70px !important;
    min-height: 70px !important;
    min-width: 70px !important;
    max-height: 70px !important;
    max-width: 70px !important;
    margin-bottom: 0.5rem !important;
  }

  /* Optimize company info in carousel for landscape */
  .company-info {
    margin-top: 0.25rem !important;
  }

  .company-info h5 {
    font-size: 0.9rem !important;
    margin-bottom: 0.25rem !important;
  }

  .company-info p {
    font-size: 0.8rem !important;
    margin-bottom: 0 !important;
  }
}

/* Chat Interface Landscape Mode Optimization */
@media (max-width: 768px) and (orientation: landscape) {
  /* Optimize chat interface for landscape */
  .message-content {
    max-width: 80% !important;
    padding: 0.6rem 0.8rem !important;
  }

  /* Adjust message spacing for landscape */
  .message {
    margin: 0.4rem 0 !important;
  }

  /* Optimize chat form for landscape */
  #chat-form {
    padding: 0.5rem !important;
  }

  /* Ensure chat container has appropriate height */
  .chat-container {
    max-height: calc(100vh - 120px) !important;
  }
}
