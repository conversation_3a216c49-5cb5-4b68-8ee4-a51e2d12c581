"""
<PERSON><PERSON><PERSON> to fix a specific migration file that's causing issues.
"""
import os
import sys
import re

def fix_migration_file(file_path):
    """Fix a migration file by modifying it directly."""
    print(f"Fixing migration file: {file_path}")
    try:
        # Read the file
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Add a do_nothing function if it doesn't exist
        if 'def do_nothing(' not in content:
            do_nothing_func = '''
def do_nothing(apps, schema_editor):
    """
    This function does nothing.
    We're using it because the model doesn't exist in the state.
    """
    pass
'''
            # Add the function after the imports
            content = re.sub(r'(from django.db import migrations.*?\n)',
                            r'\1\n' + do_nothing_func,
                            content,
                            flags=re.DOTALL)
        
        # Replace operations with RunPython
        operations_pattern = r'(operations = \[\s*)(.*?)(\s*\])'
        operations_match = re.search(operations_pattern, content, re.DOTALL)
        
        if operations_match:
            operations_content = operations_match.group(2)
            new_operations = "        migrations.RunPython(do_nothing, do_nothing),"
            
            # Replace the operations content
            content = re.sub(operations_pattern, 
                            r'\1' + new_operations + r'\3', 
                            content, 
                            flags=re.DOTALL)
        
        # Write the modified content back to the file
        with open(file_path, 'w') as f:
            f.write(content)
        
        print(f"Fixed migration file: {file_path}")
        return True
    except Exception as e:
        print(f"Error fixing migration file {file_path}: {e}")
        return False

def main():
    """Main function to fix a specific migration file."""
    print("Starting migration file fix...")
    
    # Fix the problematic migration file
    migration_file = "assistants/migrations/0030_navigationitem_gallery.py"
    if os.path.exists(migration_file):
        fix_migration_file(migration_file)
    else:
        print(f"Migration file {migration_file} not found.")
    
    print("\nNext steps:")
    print("1. Run 'python manage.py migrate --fake' to fake the migrations")
    print("2. Run 'python manage.py runserver' to start the server")

if __name__ == "__main__":
    main()
