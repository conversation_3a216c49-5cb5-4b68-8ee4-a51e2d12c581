{% load static account_tags rating_tags %}

{# Assistant Card - Enhanced design to match company card #}
<div class="list-group-item position-relative directory-card"
     data-assistant-id="{{ listing.assistant.id }}"
     data-tier="{{ listing.assistant.tier|lower }}"
     data-featured="{{ listing.assistant.is_featured|yesno:'True,False' }}"
     data-name="{{ listing.assistant.name|escapejs }}">
    {# --- Conditional Badge Logic based on display_context --- #}
    {# display_context should be 'featured' or 'tier' #}

    {# Show Featured badge ONLY if in 'featured' context AND assistant is featured #}
    {% if display_context == 'featured' and listing.assistant.is_featured %}
    <span class="badge bg-success position-absolute top-0 start-0 m-2" style="z-index: 10; font-size: 0.7em; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <i class="bi bi-star-fill me-1"></i>Featured
    </span>
    {% endif %}

    {# Show Tier badge ONLY if in 'tier' context and not Standard tier #}
    {% if display_context == 'tier' %}
        {% if listing.assistant.tier == 'Gold' %}
            <span class="badge tier-badge tier-gold"><i class="bi bi-trophy-fill me-1"></i>Gold</span>
        {% elif listing.assistant.tier == 'Silver' %}
            <span class="badge tier-badge tier-silver"><i class="bi bi-award-fill me-1"></i>Silver</span>
        {% elif listing.assistant.tier == 'Bronze' %}
            <span class="badge tier-badge tier-bronze"><i class="bi bi-award me-1"></i>Bronze</span>
        {% endif %}
        {# No badge needed for Standard tier as it's the default #}
    {% endif %}
    {# --- End Conditional Badge Logic --- #}

    {# ROW STRUCTURE: 12 = 12 (Outer), 3 + 3 + 4 + 2 = 12 (Full Row) #}
    <div class="row g-4 pt-3" style="height: 100%;">
        {# Link wrapper covers first 4 columns (col-md-10) and contains an inner row #}
        <a href="{% url 'assistants:assistant_chat' slug=listing.assistant.slug %}" class="directory-item-link-wrapper col-md-10 row g-4 me-0 text-decoration-none">
            {# Column 1: Logo with Fallback (col-md-3 within link row) #}
            <div class="col-md-3 d-flex justify-content-center align-items-center">
                <div class="logo-container">
                    {% with logo_url=listing.assistant.get_logo_url %}
                        {% if logo_url %}
                            <img src="{{ logo_url }}" alt="{{ listing.assistant.name }} logo">
                        {% else %}
                            <i class="bi bi-robot logo-placeholder"></i>
                        {% endif %}
                    {% endwith %}
                </div>
            </div>

            {# Column 2: Name, Company, Type, Tags (col-md-3 within link row) #}
            <div class="col-md-3">
                <h6 class="mb-2">
                    {{ listing.assistant.name }}
                </h6>
                <p class="mb-2 text-muted">
                    <i class="bi bi-building me-1"></i>By {{ listing.assistant.company.name }}
                </p>
                <div class="mb-2">
                     <span class="badge bg-primary bg-opacity-10 text-primary tag-badge">{{ listing.assistant.get_assistant_type_display }}</span>
                     {% for category in listing.categories %}
                        <span class="badge bg-secondary tag-badge community-badge">{{ category }}</span>
                     {% endfor %}
                     {% for tag in listing.tags %}
                        <span class="badge bg-light text-dark border tag-badge">{{ tag }}</span>
                     {% endfor %}
                </div>
            </div>

            {# Column 3: Description (col-md-4 within link row) #}
            <div class="col-md-4">
                {% with full_desc=listing.short_description|default:listing.assistant.description|default:"" %}
                <p class="mb-0 item-description">
                    {{ full_desc|default:"No description available." }}
                </p>
                {% endwith %}
            </div>
        </a> {# End of clickable area link #}

        {# Column 4: Rating & Actions (col-md-2) - Outside the link #}
        <div class="col-md-2 d-flex flex-column align-items-end justify-content-between">
            <div class="w-100">
                {# Rating Display #}
                {% with avg_rating=listing.avg_rating total_ratings=listing.total_ratings %}
                    {% if avg_rating > 0 %}
                        <div class="rating-display-container mb-3" id="rating-display-{{ listing.assistant.id }}">
                            {% render_stars avg_rating total_ratings %}
                        </div>
                    {% else %}
                        <div class="text-muted fst-italic mb-3 text-end" id="no-rating-placeholder-{{ listing.assistant.id }}">(No ratings yet)</div>
                    {% endif %}
                {% endwith %}
            </div>

            <div class="w-100">
                {# Assistant Info - Most important items only #}
                <ul class="list-unstyled small text-muted contact-info mb-3">
                    {% if listing.assistant.created_at %}
                        <li>
                            <i class="bi bi-calendar-check"></i>
                            <span>Created: {{ listing.assistant.created_at|date:"M d, Y" }}</span>
                        </li>
                    {% endif %}
                    {% if listing.assistant.updated_at %}
                        <li>
                            <i class="bi bi-arrow-clockwise"></i>
                            <span>Updated: {{ listing.assistant.updated_at|date:"M d, Y" }}</span>
                        </li>
                    {% endif %}
                </ul>
            </div>

            {# Buttons #}
            <div class="w-100 d-flex justify-content-end align-items-center">
                {% if user.is_authenticated %}
                    <button type="button"
                            class="btn btn-outline-secondary btn-sm rate-assistant-btn action-btn me-2"
                            data-bs-toggle="modal"
                            data-bs-target="#ratingModal"
                            data-assistant-id="{{ listing.assistant.id }}"
                            data-assistant-name="{{ listing.assistant.name|escapejs }}">
                        <i class="bi bi-star"></i> Rate
                    </button>

                    <button
                        class="like-button btn btn-sm p-1 {% if listing.assistant.id in saved_assistant_ids %}text-danger{% else %}text-secondary{% endif %}"
                        data-item-id="{{ listing.assistant.id }}"
                        data-item-type="assistant"
                        title="{% if listing.assistant.id in saved_assistant_ids %}Unlike{% else %}Like{% endif %}"
                        style="background: none; border: none; cursor: pointer;">
                        <i class="bi bi-heart-fill" style="font-size: 1.2rem;"></i>
                    </button>
                {% endif %}
            </div>

            <div class="w-100 text-end" style="height: 1em;">
                <span class="rating-update-message text-success small" id="rating-msg-{{ listing.assistant.id }}" style="display: none;"></span>
            </div>
        </div> {# End Actions Column #}
    </div> {# /row #}
</div> {# /list-group-item #}
