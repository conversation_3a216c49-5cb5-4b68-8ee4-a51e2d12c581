/**
 * Performance Fix
 * This file contains performance optimizations for the site
 */

console.log('Performance Fix: Loaded');

// Optimize image loading
document.addEventListener('DOMContentLoaded', function() {
    // Lazy load images
    const lazyImages = document.querySelectorAll('img[loading="lazy"]');
    if ('loading' in HTMLImageElement.prototype) {
        console.log('Performance Fix: Native lazy loading supported');
    } else {
        console.log('Performance Fix: Native lazy loading not supported, using fallback');
        // Fallback for browsers that don't support lazy loading
        const lazyImageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    const lazyImage = entry.target;
                    lazyImage.src = lazyImage.dataset.src;
                    lazyImageObserver.unobserve(lazyImage);
                }
            });
        });

        lazyImages.forEach(function(lazyImage) {
            lazyImageObserver.observe(lazyImage);
        });
    }

    // Optimize dark mode detection
    console.log('Performance Fix: Optimizing dark mode detection');

    // Limit the number of active MutationObservers
    if (window._darkModeObservers && window._darkModeObservers.length > 0) {
        console.log(`Performance Fix: Found ${window._darkModeObservers.length} dark mode observers`);

        // Disconnect any observers that are watching the entire document
        window._darkModeObservers.forEach(observer => {
            if (observer._callback && observer._callback.toString().includes('data-theme')) {
                // Keep track of disconnected observers
                if (!window._disconnectedObservers) {
                    window._disconnectedObservers = [];
                }
                window._disconnectedObservers.push(observer);

                // Disconnect the observer
                observer.disconnect();
                console.log('Performance Fix: Disconnected a dark mode observer');
            }
        });
    }

    // Apply dark mode once more to ensure everything is styled correctly
    if (typeof window.applyGlobalDarkMode === 'function') {
        window.applyGlobalDarkMode();
    }
});
