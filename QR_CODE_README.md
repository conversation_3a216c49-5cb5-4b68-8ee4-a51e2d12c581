# QR Code Generation System

This document provides information about the QR code generation system used in the 24seven application.

## Overview

The QR code generation system creates QR codes with a centered letter "A" for companies, assistants, and registration links. The letter "A" is perfectly centered vertically with equal distance from the top and bottom of the QR code.

## Key Files

- `utils/qr_generator.py` - The main QR code generation code
- `generate_sample_qr.py` - <PERSON><PERSON><PERSON> to generate a sample QR code
- `regenerate_company_qr.py` - <PERSON><PERSON><PERSON> to regenerate company QR codes
- `verify_sample_qr.py` - <PERSON>ript to verify the sample QR code
- `verify_qr_positioning.py` - Script to verify QR code positioning
- `test_qr_deployment.py` - <PERSON>ript to test QR code generation in a deployment environment

## Important Constants

The QR code generation system uses a vertical offset to ensure the letter "A" is perfectly centered:

```python
# Constants for QR code generation
# Vertical offset to ensure the letter "A" is perfectly centered
# Negative value moves the letter up, positive value moves it down
QR_LETTER_VERTICAL_OFFSET = -50
```

This offset was determined through testing and verification to ensure the letter "A" is perfectly centered with equal distance from the top and bottom of the QR code.

## Deployment Considerations

When deploying the QR code generation system, make sure:

1. The `utils/qr_generator.py` file is included in the deployment
2. The `QR_LETTER_VERTICAL_OFFSET` constant is set to -50
3. Run the `test_qr_deployment.py` script to verify that the QR code generation is working correctly

## Troubleshooting

If the letter "A" is not perfectly centered in the generated QR codes:

1. Run the `test_qr_deployment.py` script to verify the QR code generation
2. If the test fails, adjust the `QR_LETTER_VERTICAL_OFFSET` constant in `utils/qr_generator.py`
3. Run the test again to verify the adjustment

## Regenerating QR Codes

To regenerate QR codes for all companies:

```bash
python regenerate_company_qr.py
```

To regenerate QR codes for a specific company:

```bash
python regenerate_company_qr.py <company_id>
```

## Verifying QR Codes

To verify the vertical positioning of the letter "A" in all company QR codes:

```bash
python verify_qr_positioning.py
```

To verify the vertical positioning of the letter "A" in the sample QR code:

```bash
python verify_sample_qr.py
```

## Implementation Details

The QR code generation system uses the following libraries:

- `qrcode` - For generating the base QR code
- `PIL` (Pillow) - For image manipulation
- `numpy` - For image analysis in the verification scripts

The letter "A" is positioned with a vertical offset to ensure it is perfectly centered with equal distance from the top and bottom of the QR code.

## Font Handling

The system uses the Arial Black font for the letter "A". If Arial Black is not available, it will try to find alternative fonts:

1. Arial Black
2. ArialBlack
3. Arial-Black
4. arialblk
5. ariblk
6. Impact
7. Arial Bold
8. arialbd
9. arial
10. sans-serif-bold
11. sans-serif

The font loading is handled by the `load_font` function in `utils/qr_generator.py`.
