import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

print("Starting script to check accounts_membership table structure...")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

# SQL to check the table structure
check_table_sql = """
SELECT column_name, data_type, character_maximum_length
FROM information_schema.columns
WHERE table_name = 'accounts_membership'
ORDER BY ordinal_position;
"""

# Connect to the database
try:
    print("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(**db_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    
    # Check the table structure
    print("Checking accounts_membership table structure...")
    cursor.execute(check_table_sql)
    columns = cursor.fetchall()
    
    print("\nTable structure:")
    print("Column Name | Data Type | Max Length")
    print("-" * 50)
    for column in columns:
        print(f"{column[0]} | {column[1]} | {column[2]}")
    
    # Check if date_joined column exists
    date_joined_exists = any(column[0] == 'date_joined' for column in columns)
    if date_joined_exists:
        print("\nThe 'date_joined' column exists in the table!")
    else:
        print("\nWARNING: The 'date_joined' column does NOT exist in the table!")
    
    # Close the connection
    cursor.close()
    conn.close()
    print("\nDatabase connection closed.")
    
except Exception as e:
    print(f"Error: {e}")
