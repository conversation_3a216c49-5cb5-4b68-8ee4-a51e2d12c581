import os
import django
import decimal

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from assistants.models import Assistant
from directory.models import AssistantListing
from django.db import models

def sync_assistant_listings():
    print('Starting assistant listing synchronization...')
    
    # Sync Assistant Listings
    assistant_created_count = 0
    assistant_updated_count = 0
    assistants = Assistant.objects.all()
    print(f'Processing {assistants.count()} assistants...')
    
    for assistant in assistants:
        try:
            listing, created = AssistantListing.objects.update_or_create(
                assistant=assistant,
                defaults={
                    'is_listed': assistant.is_public,
                    'short_description': assistant.description,
                    'avg_rating': assistant.interactions.filter(rating__isnull=False).aggregate(models.Avg('rating'))['rating__avg'] or decimal.Decimal('0.0'),
                    'total_ratings': assistant.interactions.filter(rating__isnull=False).count(),
                    # Ensure tags/categories fields exist, default to empty list if creating
                    'tags': [], 
                    'categories': [],
                }
            )
            if created:
                assistant_created_count += 1
                print(f'Created listing for assistant: {assistant.name}')
            else:
                # If not created, explicitly update rating stats as they might have changed
                new_avg = assistant.interactions.filter(rating__isnull=False).aggregate(models.Avg('rating'))['rating__avg'] or decimal.Decimal('0.0')
                new_count = assistant.interactions.filter(rating__isnull=False).count()
                
                update_needed = False
                fields_to_update = []
                
                if listing.is_listed != assistant.is_public:
                    listing.is_listed = assistant.is_public
                    fields_to_update.append('is_listed')
                    update_needed = True
                if listing.short_description != assistant.description:
                    listing.short_description = assistant.description
                    fields_to_update.append('short_description')
                    update_needed = True
                # Use Decimal comparison for avg_rating
                # Ensure avg_rating is Decimal before quantize
                current_avg_decimal = listing.avg_rating if isinstance(listing.avg_rating, decimal.Decimal) else decimal.Decimal(str(listing.avg_rating or '0.0'))
                if current_avg_decimal.quantize(decimal.Decimal("0.01")) != new_avg.quantize(decimal.Decimal("0.01")):
                    listing.avg_rating = new_avg
                    fields_to_update.append('avg_rating')
                    update_needed = True
                if listing.total_ratings != new_count:
                    listing.total_ratings = new_count
                    fields_to_update.append('total_ratings')
                    update_needed = True

                if update_needed:
                    listing.save(update_fields=fields_to_update)
                    assistant_updated_count += 1
                    print(f'Updated listing for assistant: {assistant.name}')
        except Exception as e:
            print(f'Error processing assistant {assistant.name} (ID: {assistant.id}): {e}')

    print(f'Assistant sync complete. Created: {assistant_created_count}, Updated: {assistant_updated_count}')
    print('Listing synchronization finished.')

if __name__ == '__main__':
    sync_assistant_listings()
