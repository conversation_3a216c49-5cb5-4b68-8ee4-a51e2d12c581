/**
 * Directory Settings Debug Script
 *
 * This script helps debug issues with directory settings by:
 * 1. Detecting when the page loads with filtering settings
 * 2. Monitoring for changes in the URL parameters
 * 3. Providing visual feedback about active filters
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('[DEBUG] Directory Settings: Script loaded');

    // Check for standard tier filtering from data attributes
    const container = document.querySelector('.company-directory-container');

    // Get settings from data attributes
    let hideStandardTierCompanies = false;
    let hideStandardTierAssistants = false;

    if (container) {
        hideStandardTierCompanies = container.dataset.hideStandardTierCompanies === 'True';
        hideStandardTierAssistants = container.dataset.hideStandardTierAssistants === 'True';

        console.log('[DEBUG] Directory Settings from container:', {
            hideStandardTierCompanies,
            hideStandardTierAssistants,
            containerFound: true,
            containerDataAttributes: Object.keys(container.dataset)
        });
    } else {
        console.log('[DEBUG] Directory Settings: No container found');
    }

    // Check for debug info in the page
    const debugInfo = document.querySelector('.alert-danger');
    if (debugInfo) {
        console.log('[DEBUG] Found debug info in the page');

        // Extract settings from debug info
        const companyFilterText = debugInfo.textContent.match(/hide_standard_tier_companies: (ON|OFF)/);
        const assistantFilterText = debugInfo.textContent.match(/hide_standard_tier_assistants: (ON|OFF)/);

        if (companyFilterText) {
            hideStandardTierCompanies = companyFilterText[1] === 'ON';
        }

        if (assistantFilterText) {
            hideStandardTierAssistants = assistantFilterText[1] === 'ON';
        }

        console.log('[DEBUG] Directory Settings from debug info:', {
            hideStandardTierCompanies,
            hideStandardTierAssistants
        });
    }

    // Add visual indicator for active filtering
    const filterIndicator = document.createElement('div');
    filterIndicator.className = 'position-fixed top-0 start-50 translate-middle-x p-3 mt-5 bg-warning text-dark rounded shadow';
    filterIndicator.style.zIndex = '1050';

    // Get the current page type
    const isCompanyPage = window.location.href.includes('/directory/companies/');
    const isAssistantPage = window.location.href.includes('/directory/assistants/');
    const pageType = isCompanyPage ? 'Companies' : (isAssistantPage ? 'Assistants' : 'Unknown');

    filterIndicator.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="bi bi-funnel-fill me-2"></i>
            <strong>Directory Settings Debug:</strong>
            <span class="ms-2">
                Page Type: <strong>${pageType}</strong> |
                Companies Filter: <strong>${hideStandardTierCompanies ? 'ON' : 'OFF'}</strong> |
                Assistants Filter: <strong>${hideStandardTierAssistants ? 'ON' : 'OFF'}</strong>
            </span>
            <button type="button" class="btn-close ms-3" aria-label="Close"></button>
        </div>
    `;

    // Add close button functionality
    const closeButton = filterIndicator.querySelector('.btn-close');
    closeButton.addEventListener('click', function() {
        filterIndicator.remove();
    });

    document.body.appendChild(filterIndicator);

    // Add a debug button to check settings directly from the database
    const debugButton = document.createElement('button');
    debugButton.className = 'btn btn-sm btn-danger position-fixed bottom-0 end-0 m-3';
    debugButton.style.zIndex = '1050';
    debugButton.innerHTML = '<i class="bi bi-bug-fill me-1"></i> Check Settings';
    debugButton.addEventListener('click', function() {
        // Redirect to the admin page for DirectorySettings
        window.open('/admin/directory/directorysettings/1/change/', '_blank');
    });
    document.body.appendChild(debugButton);

    // Monitor for page navigation
    const originalPushState = history.pushState;
    history.pushState = function() {
        originalPushState.apply(this, arguments);
        console.log('[DEBUG] Directory Settings: URL changed', window.location.href);
        // Could reload settings here if needed
    };

    // Add data attributes to filter badges for easier debugging
    const filterBadges = document.querySelectorAll('.badge');
    filterBadges.forEach(badge => {
        if (badge.textContent.includes('Standard Tier Hidden')) {
            badge.dataset.filterType = 'standard-tier';
            badge.style.cursor = 'pointer';
            badge.title = 'Click to view settings in admin';
            badge.addEventListener('click', function() {
                console.log('[DEBUG] Standard tier filter badge clicked');
                window.open('/admin/directory/directorysettings/1/change/', '_blank');
            });
        }
    });
});
