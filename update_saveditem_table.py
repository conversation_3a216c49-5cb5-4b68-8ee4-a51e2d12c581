import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

print("Starting SavedItem table update script...")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

# SQL to add missing columns to the directory_saveditem table
alter_saveditem_sql = """
ALTER TABLE "directory_saveditem" 
ADD COLUMN IF NOT EXISTS "notes" text NOT NULL DEFAULT '';
"""

# Connect to the database
try:
    print("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(**db_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    
    # Alter the directory_saveditem table
    print("Altering directory_saveditem table...")
    cursor.execute(alter_saveditem_sql)
    print("SavedItem table updated successfully!")
    
    # Close the connection
    cursor.close()
    conn.close()
    print("Database connection closed.")
    
except Exception as e:
    print(f"Error: {e}")
