/**
 * Cha<PERSON> Bubbles Dark Mode CSS
 * Ensures chat bubbles have proper dark mode styling
 * With explicit light mode resets to ensure proper theme switching
 */

/* Assistant message bubbles in dark mode */
[data-theme="dark"] .assistant-message .message-content,
[data-theme="dark"] div.message.assistant-message.mb-3 span.message-content,
[data-theme="dark"] div.message.assistant-message span.message-content,
[data-theme="dark"] .message.assistant-message .message-content,
[data-theme="dark"] span.message-content.assistant-message {
    background: linear-gradient(145deg, #2a2a2a, #222222) !important;
    background-color: #2a2a2a !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    background-position: initial !important;
    background-size: initial !important;
    background-repeat: initial !important;
    background-origin: initial !important;
    background-clip: initial !important;
    background-attachment: initial !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
    border-radius: 18px 18px 18px 4px !important;
    padding: 1rem 1.25rem !important;
    font-weight: 400 !important;
    letter-spacing: 0.01em !important;
    transition: background 0.3s ease, background-color 0.3s ease, color 0.3s ease, border 0.3s ease, box-shadow 0.3s ease !important;
}

/* Explicit light mode reset for assistant messages - MATCHING DARK MODE */
[data-theme="light"] .assistant-message .message-content,
[data-theme="light"] div.message.assistant-message.mb-3 span.message-content,
[data-theme="light"] div.message.assistant-message span.message-content,
[data-theme="light"] .message.assistant-message .message-content,
[data-theme="light"] span.message-content.assistant-message {
    background: linear-gradient(145deg, #2a2a2a, #222222) !important;
    background-color: #2a2a2a !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    background-position: initial !important;
    background-size: initial !important;
    background-repeat: initial !important;
    background-origin: initial !important;
    background-clip: initial !important;
    background-attachment: initial !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
    border-radius: 18px 18px 18px 4px !important;
    padding: 1rem 1.25rem !important;
    font-weight: 400 !important;
    letter-spacing: 0.01em !important;
    transition: background 0.3s ease, background-color 0.3s ease, color 0.3s ease, border 0.3s ease, box-shadow 0.3s ease !important;
}

/* User message bubbles in dark mode */
[data-theme="dark"] .user-message .message-content,
[data-theme="dark"] div.message.user-message.mb-3 span.message-content,
[data-theme="dark"] div.message.user-message span.message-content,
[data-theme="dark"] .message.user-message .message-content,
[data-theme="dark"] span.message-content.user-message {
    background: linear-gradient(145deg, #0066ff, #0055cc) !important;
    background-color: #0066ff !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    background-position: initial !important;
    background-size: initial !important;
    background-repeat: initial !important;
    background-origin: initial !important;
    background-clip: initial !important;
    background-attachment: initial !important;
    color: #ffffff !important;
    border: none !important;
    box-shadow: 0 5px 15px rgba(0, 102, 255, 0.3) !important;
    border-radius: 18px 18px 4px 18px !important;
    padding: 1rem 1.25rem !important;
    font-weight: 500 !important;
    letter-spacing: 0.01em !important;
    transition: background 0.3s ease, background-color 0.3s ease, color 0.3s ease, border 0.3s ease, box-shadow 0.3s ease !important;
}

/* Explicit light mode reset for user messages - MATCHING DARK MODE */
[data-theme="light"] .user-message .message-content,
[data-theme="light"] div.message.user-message.mb-3 span.message-content,
[data-theme="light"] div.message.user-message span.message-content,
[data-theme="light"] .message.user-message .message-content,
[data-theme="light"] span.message-content.user-message {
    background: linear-gradient(145deg, #0066ff, #0055cc) !important;
    background-color: #0066ff !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    background-position: initial !important;
    background-size: initial !important;
    background-repeat: initial !important;
    background-origin: initial !important;
    background-clip: initial !important;
    background-attachment: initial !important;
    color: #ffffff !important;
    border: none !important;
    box-shadow: 0 5px 15px rgba(0, 102, 255, 0.3) !important;
    border-radius: 18px 18px 4px 18px !important;
    padding: 1rem 1.25rem !important;
    font-weight: 500 !important;
    letter-spacing: 0.01em !important;
    transition: background 0.3s ease, background-color 0.3s ease, color 0.3s ease, border 0.3s ease, box-shadow 0.3s ease !important;
}

/* Navigation content bubbles in dark mode */
[data-theme="dark"] .nav-content-bubble .message-content {
    background: linear-gradient(145deg, #252525, #1e1e1e) !important;
    background-color: #252525 !important;
    background-image: none !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
    border-left: 4px solid #0077ff !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
    border-radius: 8px !important;
    padding: 1.25rem !important;
    margin-bottom: 1.5rem !important;
    transition: background 0.3s ease, background-color 0.3s ease, color 0.3s ease, border 0.3s ease, box-shadow 0.3s ease !important;
}

/* Explicit light mode reset for navigation content bubbles - MATCHING DARK MODE */
[data-theme="light"] .nav-content-bubble .message-content {
    background: linear-gradient(145deg, #252525, #1e1e1e) !important;
    background-color: #252525 !important;
    background-image: none !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
    border-left: 4px solid #0077ff !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
    border-radius: 8px !important;
    padding: 1.25rem !important;
    margin-bottom: 1.5rem !important;
    transition: background 0.3s ease, background-color 0.3s ease, color 0.3s ease, border 0.3s ease, box-shadow 0.3s ease !important;
}

/* Ensure these styles are applied even when new messages are added */
[data-theme="dark"] #chat-box .message-content,
[data-theme="dark"] .chat-box .message-content {
    transition: none !important;
}

/* Override any JavaScript-applied styles */
[data-theme="dark"] .message-content[style],
[data-theme="dark"] .user-message .message-content[style],
[data-theme="dark"] .assistant-message .message-content[style] {
    background: inherit !important;
    background-color: inherit !important;
    background-image: none !important;
}

/* Fix for any hardcoded background colors in chat bubbles */
[data-theme="dark"] .message-content[style*="background-color: white"],
[data-theme="dark"] .message-content[style*="background-color: #fff"],
[data-theme="dark"] .message-content[style*="background-color: rgb(255, 255, 255)"],
[data-theme="dark"] .message-content[style*="background-color: rgba(255, 255, 255"] {
    background-color: #2a2a2a !important;
    background: linear-gradient(145deg, #2a2a2a, #222222) !important;
    color: #ffffff !important;
}

/* Fix for any hardcoded text colors in chat bubbles */
[data-theme="dark"] .message-content[style*="color: black"],
[data-theme="dark"] .message-content[style*="color: #000"],
[data-theme="dark"] .message-content[style*="color: rgb(0, 0, 0)"],
[data-theme="dark"] .message-content[style*="color: rgba(0, 0, 0"] {
    color: #ffffff !important;
}

/* Ensure the helpful/flag buttons have proper dark mode styling */
[data-theme="dark"] .message-content .btn-primary,
[data-theme="dark"] .message-content .btn-success {
    background: linear-gradient(to bottom, #0077ff, #0055cc) !important;
    border: none !important;
    color: white !important;
    box-shadow: 0 4px 10px rgba(0, 102, 255, 0.3) !important;
}

[data-theme="dark"] .message-content .btn-outline-primary,
[data-theme="dark"] .message-content .btn-outline-success {
    background-color: transparent !important;
    color: #0077ff !important;
    border: 1px solid #0077ff !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

/* Ensure the helpful/flag buttons have proper hover effects */
[data-theme="dark"] .message-content .btn-primary:hover,
[data-theme="dark"] .message-content .btn-success:hover {
    background: linear-gradient(to bottom, #0088ff, #0066dd) !important;
    box-shadow: 0 6px 15px rgba(0, 102, 255, 0.4) !important;
}

[data-theme="dark"] .message-content .btn-outline-primary:hover,
[data-theme="dark"] .message-content .btn-outline-success:hover {
    background-color: rgba(0, 102, 255, 0.1) !important;
    border-color: #0088ff !important;
    color: #0088ff !important;
}

/* Chat container with light blue glass background - LIGHT MODE */
[data-theme="light"] .chat-container,
[data-theme="light"] .general-chat-container,
[data-theme="light"] div.chat-container,
[data-theme="light"] div.general-chat-container {
    background: linear-gradient(135deg, rgba(240, 249, 255, 0.9), rgba(214, 240, 253, 0.85)) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.5) !important;
    box-shadow: 0 8px 32px rgba(0, 102, 255, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.7) !important;
    border-radius: 12px !important;
    overflow: hidden !important;
}

/* Chat container with dark glass background - DARK MODE */
[data-theme="dark"] .chat-container,
[data-theme="dark"] .general-chat-container,
[data-theme="dark"] div.chat-container,
[data-theme="dark"] div.general-chat-container {
    background: linear-gradient(135deg, #1e1e1e 0%, #252525 100%) !important;
    background-color: #1e1e1e !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
    border-radius: 12px !important;
    overflow: hidden !important;
}

/* Chat box with subtle background - LIGHT MODE */
[data-theme="light"] .chat-box,
[data-theme="light"] .general-chat-box,
[data-theme="light"] #chat-box,
[data-theme="light"] div.chat-box,
[data-theme="light"] div.general-chat-box {
    background: rgba(250, 253, 255, 0.7) !important;
    border: none !important;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.03) !important;
    padding: 1.5rem !important;
}

/* Chat box with dark background - DARK MODE */
[data-theme="dark"] .chat-box,
[data-theme="dark"] .general-chat-box,
[data-theme="dark"] #chat-box,
[data-theme="dark"] div.chat-box,
[data-theme="dark"] div.general-chat-box {
    background: #1a1a1a !important;
    background-color: #1a1a1a !important;
    border: none !important;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.15) !important;
    padding: 1.5rem !important;
}
