/**
 * Badge Visibility Enhancement
 * Improves contrast and readability of all badge categories
 */

/* Primary badges (blue) - Ensure high contrast with white text */
.badge.bg-primary,
.badge.bg-primary.tag-badge,
span.badge.bg-primary {
    background-color: #0052cc !important; /* Darker blue for better contrast */
    color: #ffffff !important; /* White text for maximum readability */
    font-weight: 600 !important; /* Slightly bolder text */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important; /* Subtle text shadow for depth */
    box-shadow: 0 2px 4px rgba(0, 82, 204, 0.3) !important; /* Enhanced shadow with matching color */
    border: none !important; /* Remove any border */
    letter-spacing: 0.02em !important; /* Slightly increased letter spacing */
}

/* Secondary badges (gray) - Ensure high contrast with white text */
.badge.bg-secondary,
.badge.bg-secondary.tag-badge,
span.badge.bg-secondary {
    background-color: #495057 !important; /* Darker gray for better contrast */
    color: #ffffff !important; /* White text for maximum readability */
    font-weight: 600 !important; /* Slightly bolder text */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important; /* Subtle text shadow for depth */
    box-shadow: 0 2px 4px rgba(73, 80, 87, 0.3) !important; /* Enhanced shadow with matching color */
    border: none !important; /* Remove any border */
    letter-spacing: 0.02em !important; /* Slightly increased letter spacing */
}

/* Success badges (green) - Ensure high contrast with white text */
.badge.bg-success,
.badge.bg-success.tag-badge,
span.badge.bg-success {
    background-color: #0a6e31 !important; /* Darker green for better contrast */
    color: #ffffff !important; /* White text for maximum readability */
    font-weight: 600 !important; /* Slightly bolder text */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important; /* Subtle text shadow for depth */
    box-shadow: 0 2px 4px rgba(10, 110, 49, 0.3) !important; /* Enhanced shadow with matching color */
    border: none !important; /* Remove any border */
    letter-spacing: 0.02em !important; /* Slightly increased letter spacing */
}

/* Warning badges (yellow) - Ensure high contrast with dark text */
.badge.bg-warning,
.badge.bg-warning.tag-badge,
span.badge.bg-warning {
    background-color: #e6a700 !important; /* Slightly darker yellow for better contrast */
    color: #212529 !important; /* Dark text for maximum readability on light background */
    font-weight: 700 !important; /* Bolder text for better visibility */
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3) !important; /* Light text shadow for depth */
    box-shadow: 0 2px 4px rgba(230, 167, 0, 0.3) !important; /* Enhanced shadow with matching color */
    border: none !important; /* Remove any border */
    letter-spacing: 0.02em !important; /* Slightly increased letter spacing */
}

/* Danger badges (red) - Ensure high contrast with white text */
.badge.bg-danger,
.badge.bg-danger.tag-badge,
span.badge.bg-danger {
    background-color: #b02a37 !important; /* Darker red for better contrast */
    color: #ffffff !important; /* White text for maximum readability */
    font-weight: 600 !important; /* Slightly bolder text */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important; /* Subtle text shadow for depth */
    box-shadow: 0 2px 4px rgba(176, 42, 55, 0.3) !important; /* Enhanced shadow with matching color */
    border: none !important; /* Remove any border */
    letter-spacing: 0.02em !important; /* Slightly increased letter spacing */
}

/* Info badges (cyan) - Ensure high contrast with white text */
.badge.bg-info,
.badge.bg-info.tag-badge,
span.badge.bg-info {
    background-color: #087990 !important; /* Darker cyan for better contrast */
    color: #ffffff !important; /* White text for maximum readability */
    font-weight: 600 !important; /* Slightly bolder text */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important; /* Subtle text shadow for depth */
    box-shadow: 0 2px 4px rgba(8, 121, 144, 0.3) !important; /* Enhanced shadow with matching color */
    border: none !important; /* Remove any border */
    letter-spacing: 0.02em !important; /* Slightly increased letter spacing */
}

/* Light badges - Ensure high contrast with dark text */
.badge.bg-light,
.badge.bg-light.tag-badge,
span.badge.bg-light,
.badge.bg-light.text-dark,
.badge.bg-light.text-dark.border,
.badge.bg-light.text-dark.border.tag-badge,
span.badge.bg-light.text-dark.border {
    background-color: #d3d4d5 !important; /* Darker light gray for better contrast */
    color: #212529 !important; /* Dark text for maximum readability */
    font-weight: 700 !important; /* Bolder text for better visibility */
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5) !important; /* Light text shadow for depth */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important; /* Enhanced shadow */
    border: 1px solid #adb5bd !important; /* Darker border for better visibility */
    letter-spacing: 0.02em !important; /* Slightly increased letter spacing */
}

/* Dark badges - Ensure high contrast with white text */
.badge.bg-dark,
.badge.bg-dark.tag-badge,
span.badge.bg-dark {
    background-color: #212529 !important; /* Dark color for better contrast */
    color: #ffffff !important; /* White text for maximum readability */
    font-weight: 600 !important; /* Slightly bolder text */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3) !important; /* Subtle text shadow for depth */
    box-shadow: 0 2px 4px rgba(33, 37, 41, 0.3) !important; /* Enhanced shadow with matching color */
    border: none !important; /* Remove any border */
    letter-spacing: 0.02em !important; /* Slightly increased letter spacing */
}

/* Community badge - improved contrast with vibrant purple theme */
.badge.bg-secondary.tag-badge.community-badge,
.badge.bg-secondary.community-badge,
.community-badge,
span.badge.bg-secondary.tag-badge,
span.badge.bg-secondary.community-badge,
span.badge.bg-secondary:contains("Community") {
    background-color: #4c2d91 !important; /* Darker, more vibrant purple for better contrast */
    color: #ffffff !important; /* White text for maximum readability */
    font-weight: 700 !important; /* Bolder text for better visibility */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important; /* Enhanced text shadow for depth */
    box-shadow: 0 3px 6px rgba(76, 45, 145, 0.4) !important; /* Enhanced shadow with matching color */
    border: none !important; /* Remove any border */
    letter-spacing: 0.03em !important; /* Increased letter spacing for better readability */
    padding: 0.4em 0.7em !important; /* More padding for better visibility */
    font-size: 0.85em !important; /* Slightly larger font size */
    line-height: 1.2 !important; /* Improved line height */
    border-radius: 0.25rem !important; /* Consistent border radius */
}

/* Assistant type badge - improved contrast */
.badge.bg-primary.bg-opacity-10.text-primary,
.badge.bg-primary.bg-opacity-10.text-primary.tag-badge,
span.badge.bg-primary.bg-opacity-10.text-primary {
    background-color: #0066ff !important; /* Vibrant blue background */
    background-image: none !important; /* Remove any gradient */
    color: #ffffff !important; /* White text for maximum readability */
    font-weight: 600 !important; /* Slightly bolder text */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important; /* Subtle text shadow for depth */
    box-shadow: 0 2px 4px rgba(0, 102, 255, 0.3) !important; /* Enhanced shadow with matching color */
    border: none !important; /* Remove any border */
    letter-spacing: 0.02em !important; /* Slightly increased letter spacing */
    opacity: 1 !important; /* Ensure full opacity */
    padding: 0.35em 0.65em !important; /* Slightly more padding */
}

/* Tier badges - improved contrast */
.tier-badge.tier-gold {
    background-color: #e6a800 !important; /* Darker gold for better contrast */
    color: #212529 !important; /* Dark text for better readability */
    font-weight: 700 !important; /* Bolder text */
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3) !important; /* Light text shadow */
    box-shadow: 0 2px 4px rgba(230, 168, 0, 0.4) !important; /* Enhanced shadow */
}

.tier-badge.tier-silver {
    background-color: #868e96 !important; /* Darker silver for better contrast */
    color: #ffffff !important; /* White text for better readability */
    font-weight: 700 !important; /* Bolder text */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important; /* Dark text shadow */
    box-shadow: 0 2px 4px rgba(134, 142, 150, 0.4) !important; /* Enhanced shadow */
}

.tier-badge.tier-bronze {
    background-color: #a86429 !important; /* Darker bronze for better contrast */
    color: #ffffff !important; /* White text for better readability */
    font-weight: 700 !important; /* Bolder text */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important; /* Dark text shadow */
    box-shadow: 0 2px 4px rgba(168, 100, 41, 0.4) !important; /* Enhanced shadow */
}

.tier-badge.tier-standard {
    background-color: #495057 !important; /* Darker gray for better contrast */
    color: #ffffff !important; /* White text for better readability */
    font-weight: 700 !important; /* Bolder text */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important; /* Dark text shadow */
    box-shadow: 0 2px 4px rgba(73, 80, 87, 0.4) !important; /* Enhanced shadow */
}

/* Featured badge - improved contrast */
.tier-badge.tier-featured,
.badge.featured-badge,
.badge.bg-success.position-absolute {
    background: linear-gradient(135deg, #0a6e31, #0a5727) !important; /* Darker green gradient */
    color: #ffffff !important; /* White text for better readability */
    font-weight: 700 !important; /* Bolder text */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3) !important; /* Dark text shadow */
    box-shadow: 0 2px 4px rgba(10, 110, 49, 0.4) !important; /* Enhanced shadow */
}
