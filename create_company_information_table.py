import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection
from django.conf import settings

# Get database settings
db_settings = settings.DATABASES['default']

# Connect directly to PostgreSQL
conn = psycopg2.connect(
    dbname=db_settings['NAME'],
    user=db_settings['USER'],
    password=db_settings['PASSWORD'],
    host=db_settings['HOST'],
    port=db_settings['PORT']
)
conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
cursor = conn.cursor()

# Create accounts_companyinformation table
print("Creating accounts_companyinformation table...")
cursor.execute("""
CREATE TABLE IF NOT EXISTS "accounts_companyinformation" (
    "id" serial NOT NULL PRIMARY KEY,
    "mission" text NOT NULL,
    "description" text NOT NULL,
    "website" varchar(200) NOT NULL,
    "contact_email" varchar(254) NOT NULL,
    "contact_phone" varchar(50) NOT NULL,
    "timezone" varchar(50) NOT NULL,
    "language" varchar(10) NOT NULL,
    "list_in_directory" boolean NOT NULL,
    "address_line1" varchar(255) NOT NULL,
    "address_line2" varchar(255) NOT NULL,
    "city" varchar(100) NOT NULL,
    "postal_code" varchar(20) NOT NULL,
    "country" varchar(100) NOT NULL,
    "logo" varchar(100) NULL,
    "industry" varchar(100) NOT NULL,
    "size" varchar(50) NOT NULL,
    "founded" integer NULL,
    "linkedin" varchar(200) NOT NULL,
    "twitter" varchar(200) NOT NULL,
    "facebook" varchar(200) NOT NULL,
    "custom_domain" varchar(255) NOT NULL,
    "company_id" integer NOT NULL REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED
);
""")
print("accounts_companyinformation table created successfully!")

# Create indexes
print("Creating indexes...")
cursor.execute("""
CREATE INDEX IF NOT EXISTS "accounts_companyinformation_company_id_idx" ON "accounts_companyinformation" ("company_id");
""")
print("Indexes created successfully!")

# Close the connection
cursor.close()
conn.close()

print("Company information table created successfully!")
