import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

# SQL to create the directory_companylisting table
sql = """
CREATE TABLE IF NOT EXISTS "directory_companylisting" (
    "id" serial NOT NULL PRIMARY KEY,
    "is_listed" boolean NOT NULL,
    "featured" boolean NOT NULL,
    "description" text NOT NULL,
    "website" varchar(200) NOT NULL,
    "social_links" jsonb NOT NULL,
    "tags" jsonb NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "updated_at" timestamp with time zone NOT NULL,
    "avg_rating" numeric(3, 2) NOT NULL,
    "total_ratings" integer NOT NULL,
    "company_id" integer NOT NULL REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED
);

CREATE INDEX IF NOT EXISTS "directory_companylisting_company_id_idx" ON "directory_companylisting" ("company_id");
"""

# Execute the SQL
with connection.cursor() as cursor:
    cursor.execute(sql)

# Create the many-to-many relationship table for categories
sql_m2m = """
CREATE TABLE IF NOT EXISTS "directory_companylisting_categories" (
    "id" serial NOT NULL PRIMARY KEY,
    "companylisting_id" integer NOT NULL,
    "companycategory_id" integer NOT NULL,
    CONSTRAINT "directory_companylisting_categories_companylisting_id_companycategory_id_key" UNIQUE ("companylisting_id", "companycategory_id"),
    CONSTRAINT "directory_companylisting_categories_companylisting_id_fkey" FOREIGN KEY ("companylisting_id") REFERENCES "directory_companylisting" ("id") DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT "directory_companylisting_categories_companycategory_id_fkey" FOREIGN KEY ("companycategory_id") REFERENCES "directory_companycategory" ("id") DEFERRABLE INITIALLY DEFERRED
);

CREATE INDEX IF NOT EXISTS "directory_companylisting_categories_companylisting_id_idx" ON "directory_companylisting_categories" ("companylisting_id");
CREATE INDEX IF NOT EXISTS "directory_companylisting_categories_companycategory_id_idx" ON "directory_companylisting_categories" ("companycategory_id");
"""

# Execute the SQL for the many-to-many table
with connection.cursor() as cursor:
    cursor.execute(sql_m2m)

print("Company listing table created successfully!")
