@echo off
echo Installing PostgreSQL packages...
cd c:\Users\<USER>\sites\24seven\virtualo
call .venv\Scripts\activate.bat
pip install psycopg2-binary
pip install psycopg
echo.
echo Packages installed. Now creating the PostgreSQL database...
echo.
echo Please enter your PostgreSQL password when prompted.
echo.
pause
echo.
echo Creating database 'virtualo'...
echo.
echo CREATE DATABASE virtualo; | psql -U postgres
echo.
echo Database created. Press any key to exit.
pause
