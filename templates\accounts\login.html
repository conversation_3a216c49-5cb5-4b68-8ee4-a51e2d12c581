{% extends 'base/layout.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Log In - 24seven{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <img src="{% static 'img/24seven-logo.svg' %}" alt="24seven" class="mb-4" height="48">
                        <h2 class="h4">Welcome Back</h2>
                        <p class="text-muted">Log in to access your company's workspace</p>
                    </div>

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                Username or Email
                            </label>
                            {% render_field form.username class="form-control" placeholder="Enter your username or email" %}
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.username.errors|join:", " }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <label for="{{ form.password.id_for_label }}" class="form-label">
                                    Password
                                </label>
                                <a href="{% url 'accounts:password_reset' %}" class="small text-muted">
                                    Forgot Password?
                                </a>
                                <a href="{% url 'accounts:password_reset_guide' %}" class="small text-muted ms-2">
                                    <i class="bi bi-question-circle"></i>
                                </a>
                            </div>
                            {% render_field form.password class="form-control" placeholder="Enter your password" %}
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.password.errors|join:", " }}
                                </div>
                            {% endif %}
                        </div>

                        {% if form.captcha %}
                            <div class="mb-3">
                                {{ form.captcha }}
                            </div>
                        {% endif %}

                        <div class="d-grid mb-4">
                            <button type="submit" class="btn btn-primary btn-lg" id="login-btn">
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                <span id="login-btn-text">Log In</span>
                            </button>
                        </div>

                        <!-- Email Verification Notice -->
                        <div id="email-verification-notice" class="alert alert-info d-none" role="alert">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-envelope-check me-2"></i>
                                <div>
                                    <strong>Email verification required</strong><br>
                                    <small>For security, we'll send a verification link to your email.</small>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="next" value="{{ next }}">
                    </form>

                    <div class="text-center">
                        <p class="text-muted">
                            Don't have an account?
                            <a href="{% url 'accounts:register' %}" class="text-decoration-none">
                                Sign up
                            </a>
                        </p>
                    </div>
                </div>
            </div>

            <!-- SSO Options -->
            {% if social_auth_providers %}
                <div class="card shadow-sm mt-4">
                    <div class="card-body p-4">
                        <p class="text-center text-muted small mb-3">Or log in with</p>
                        <div class="d-grid gap-2">
                            {% for provider in social_auth_providers %}
                                <a href="{% url 'social:begin' provider.id %}"
                                   class="btn btn-outline-secondary">
                                    <i class="bi bi-{{ provider.icon }} me-2"></i>
                                    {{ provider.name }}
                                </a>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Help Links -->
            <div class="text-center mt-4">
                <div class="btn-group">
                    <a href="{% url 'about' %}" class="btn btn-link btn-sm text-muted">About</a>
                    <a href="{% url 'privacy' %}" class="btn btn-link btn-sm text-muted">Privacy</a>
                    <a href="{% url 'terms' %}" class="btn btn-link btn-sm text-muted">Terms</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Focus username field
    document.getElementById('{{ form.username.id_for_label }}').focus();

    // Enable password toggle
    const togglePassword = document.createElement('button');
    togglePassword.type = 'button';
    togglePassword.className = 'btn btn-outline-secondary';
    togglePassword.innerHTML = '<i class="bi bi-eye"></i>';
    togglePassword.onclick = function() {
        const password = document.getElementById('{{ form.password.id_for_label }}');
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);
        this.innerHTML = type === 'password' ? '<i class="bi bi-eye"></i>' : '<i class="bi bi-eye-slash"></i>';
    };

    const passwordField = document.getElementById('{{ form.password.id_for_label }}');
    const wrapper = document.createElement('div');
    wrapper.className = 'input-group';
    passwordField.parentNode.insertBefore(wrapper, passwordField);
    wrapper.appendChild(passwordField);
    wrapper.appendChild(togglePassword);

    // Enhanced login form handling
    const loginForm = document.querySelector('form');
    const loginBtn = document.getElementById('login-btn');
    const loginBtnText = document.getElementById('login-btn-text');
    const emailNotice = document.getElementById('email-verification-notice');
    const usernameField = document.getElementById('{{ form.username.id_for_label }}');

    // Check if user might need email verification
    function checkEmailVerificationNeeded() {
        const username = usernameField.value.trim();
        if (username) {
            // Show notice for admin/staff users or if it looks like a privileged account
            if (username.includes('admin') || username.includes('superuser') || username.includes('staff')) {
                emailNotice.classList.remove('d-none');
            } else {
                emailNotice.classList.add('d-none');
            }
        } else {
            emailNotice.classList.add('d-none');
        }
    }

    // Check on username input
    usernameField.addEventListener('input', checkEmailVerificationNeeded);
    usernameField.addEventListener('blur', checkEmailVerificationNeeded);

    // Enhanced form submission
    loginForm.addEventListener('submit', function(e) {
        // Show loading state
        loginBtn.disabled = true;
        loginBtnText.innerHTML = '<i class="bi bi-arrow-clockwise spin me-2"></i>Signing In...';

        // Add loading class for visual feedback
        loginBtn.classList.add('btn-loading');

        // If email verification notice is shown, update button text
        if (!emailNotice.classList.contains('d-none')) {
            setTimeout(() => {
                if (loginBtn.disabled) {
                    loginBtnText.innerHTML = '<i class="bi bi-envelope-check me-2"></i>Sending Verification...';
                }
            }, 1000);
        }

        // Re-enable button after 10 seconds as fallback
        setTimeout(() => {
            if (loginBtn.disabled) {
                loginBtn.disabled = false;
                loginBtnText.innerHTML = 'Log In';
                loginBtn.classList.remove('btn-loading');
            }
        }, 10000);
    });

    // Add CSS for loading animation
    const style = document.createElement('style');
    style.textContent = `
        .spin {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .btn-loading {
            position: relative;
            overflow: hidden;
        }
        .btn-loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: loading-shimmer 1.5s infinite;
        }
        @keyframes loading-shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        #email-verification-notice {
            animation: slideIn 0.3s ease-out;
        }
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}
