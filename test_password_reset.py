"""
Test script for password reset functionality.
"""

import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.conf import settings
from django.contrib.auth.forms import PasswordResetForm
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes

User = get_user_model()

def test_email_config():
    """Test email configuration."""
    print(f"Email settings:")
    print(f"EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
    print(f"EMAIL_HOST: {settings.EMAIL_HOST}")
    print(f"EMAIL_PORT: {settings.EMAIL_PORT}")
    print(f"EMAIL_USE_SSL: {getattr(settings, 'EMAIL_USE_SSL', False)}")
    print(f"EMAIL_USE_TLS: {getattr(settings, 'EMAIL_USE_TLS', False)}")
    print(f"EMAIL_HOST_USER: {settings.EMAIL_HOST_USER}")
    print(f"DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}")

def test_password_reset():
    """Test password reset functionality."""
    try:
        # Get or create a test user
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',  # Replace with a test email if needed
                'is_active': True,
            }
        )

        # Make sure the user has an email
        if not user.email:
            user.email = '<EMAIL>'  # Replace with a test email if needed
            user.save()

        print(f"Testing password reset for user: {user.username} ({user.email})")

        # Create a password reset form
        form = PasswordResetForm({'email': user.email})

        # Check if the form is valid
        if form.is_valid():
            # Get the domain and use_https from settings
            domain = 'localhost:8000'  # Replace with your domain
            use_https = False  # Set to True for production

            # Save the form (this sends the email)
            form.save(
                domain_override=domain,
                use_https=use_https,
                request=None,  # We don't have a request object in this test
                email_template_name='accounts/password_reset_email.html',
                subject_template_name='accounts/password_reset_subject.txt',
                html_email_template_name='accounts/password_reset_email.html',
            )

            print(f"Password reset email sent to: {user.email}")

            # Generate the password reset URL for reference
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            token = default_token_generator.make_token(user)
            reset_url = f"{'https' if use_https else 'http'}://{domain}/accounts/reset/{uid}/{token}/"

            print(f"Password reset URL would be: {reset_url}")
        else:
            print(f"Form is not valid: {form.errors}")
    except Exception as e:
        print(f"Error testing password reset: {e}")

if __name__ == '__main__':
    print("Testing password reset functionality...")
    test_email_config()
    test_password_reset()
