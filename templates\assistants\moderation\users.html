{% extends 'base/layout.html' %}
{% load static %}

{% block title %}User Management - {{ assistant.name }}{% endblock %}

{% block extra_css %}
<link href="{% static 'css/moderation-dashboard.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">User Management</h1>
                <div>
                    <a href="{% url 'assistants:moderation_dashboard' company.id assistant.id %}" class="btn btn-outline-primary me-2">
                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>
            <p class="text-muted">Manage users, reputation, and bans for {{ assistant.name }}</p>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">Search Users</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="search" name="q" placeholder="Search by username, email, or name" value="{{ search_query|default:'' }}">
                                <button class="btn btn-primary" type="submit">
                                    <i class="bi bi-search"></i> Search
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label for="level" class="form-label">Filter by Level</label>
                            <select class="form-select" id="level" name="level" onchange="this.form.submit()">
                                <option value="">All Levels</option>
                                <option value="Restricted" {% if level_filter == 'Restricted' %}selected{% endif %}>Restricted</option>
                                <option value="New Member" {% if level_filter == 'New Member' %}selected{% endif %}>New Member</option>
                                <option value="Contributor" {% if level_filter == 'Contributor' %}selected{% endif %}>Contributor</option>
                                <option value="Trusted Member" {% if level_filter == 'Trusted Member' %}selected{% endif %}>Trusted Member</option>
                                <option value="Expert" {% if level_filter == 'Expert' %}selected{% endif %}>Expert</option>
                                <option value="Community Leader" {% if level_filter == 'Community Leader' %}selected{% endif %}>Community Leader</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label d-block">&nbsp;</label>
                            <a href="{% url 'assistants:moderation_users' company.id assistant.id %}" class="btn btn-outline-secondary w-100">
                                <i class="bi bi-x-circle"></i> Clear Filters
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Users</h5>
                </div>
                <div class="card-body">
                    {% if users %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Username</th>
                                        <th>Email</th>
                                        <th>Level</th>
                                        <th>Score</th>
                                        <th>Contributions</th>
                                        <th>Reports</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for reputation in users %}
                                        <tr>
                                            <td>{{ reputation.user.username }}</td>
                                            <td>{{ reputation.user.email }}</td>
                                            <td>
                                                <span class="badge bg-{{ reputation.level|lower|slugify }}-subtle text-{{ reputation.level|lower|slugify }}">
                                                    {{ reputation.level }}
                                                </span>
                                            </td>
                                            <td>{{ reputation.score }}</td>
                                            <td>{{ reputation.contributions_count }}</td>
                                            <td>{{ reputation.reports_against }}</td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="actionDropdown{{ reputation.id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                                        Actions
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="actionDropdown{{ reputation.id }}">
                                                        <li>
                                                            <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#adjustReputationModal" 
                                                                data-user-id="{{ reputation.user.id }}" 
                                                                data-username="{{ reputation.user.username }}">
                                                                <i class="bi bi-star me-2"></i> Adjust Reputation
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#warnUserModal" 
                                                                data-user-id="{{ reputation.user.id }}" 
                                                                data-username="{{ reputation.user.username }}">
                                                                <i class="bi bi-exclamation-triangle me-2"></i> Warn User
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#banUserModal" 
                                                                data-user-id="{{ reputation.user.id }}" 
                                                                data-username="{{ reputation.user.username }}">
                                                                <i class="bi bi-shield-fill-x me-2"></i> Ban User
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#unbanUserModal" 
                                                                data-user-id="{{ reputation.user.id }}" 
                                                                data-username="{{ reputation.user.username }}">
                                                                <i class="bi bi-shield-fill-check me-2"></i> Unban User
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center my-4">No users found matching your criteria.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Adjust Reputation Modal -->
<div class="modal fade" id="adjustReputationModal" tabindex="-1" aria-labelledby="adjustReputationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'assistants:moderation_action' company.id assistant.id %}">
                {% csrf_token %}
                <input type="hidden" name="action_type" value="adjust_reputation">
                <input type="hidden" name="target_user_id" id="adjustReputationUserId">
                <input type="hidden" name="next" value="{{ request.path }}">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="adjustReputationModalLabel">Adjust Reputation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Adjust reputation for <strong id="adjustReputationUsername"></strong></p>
                    
                    <div class="mb-3">
                        <label for="reputationAdjustment" class="form-label">Reputation Adjustment</label>
                        <input type="number" class="form-control" id="reputationAdjustment" name="reputation_adjustment" required>
                        <div class="form-text">Positive values increase reputation, negative values decrease it.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="adjustReputationNotes" class="form-label">Notes</label>
                        <textarea class="form-control" id="adjustReputationNotes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Adjust Reputation</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Warn User Modal -->
<div class="modal fade" id="warnUserModal" tabindex="-1" aria-labelledby="warnUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'assistants:moderation_action' company.id assistant.id %}">
                {% csrf_token %}
                <input type="hidden" name="action_type" value="warn">
                <input type="hidden" name="target_user_id" id="warnUserId">
                <input type="hidden" name="next" value="{{ request.path }}">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="warnUserModalLabel">Warn User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Send a warning to <strong id="warnUsername"></strong></p>
                    
                    <div class="mb-3">
                        <label for="warnNotes" class="form-label">Warning Message</label>
                        <textarea class="form-control" id="warnNotes" name="notes" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Send Warning</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Ban User Modal -->
<div class="modal fade" id="banUserModal" tabindex="-1" aria-labelledby="banUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'assistants:moderation_action' company.id assistant.id %}">
                {% csrf_token %}
                <input type="hidden" name="action_type" value="ban">
                <input type="hidden" name="target_user_id" id="banUserId">
                <input type="hidden" name="next" value="{{ request.path }}">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="banUserModalLabel">Ban User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Ban <strong id="banUsername"></strong> from using this community assistant</p>
                    
                    <div class="mb-3">
                        <label for="banDuration" class="form-label">Ban Duration (days)</label>
                        <input type="number" class="form-control" id="banDuration" name="ban_duration" value="7" min="0">
                        <div class="form-text">Enter 0 for a permanent ban.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="banNotes" class="form-label">Reason</label>
                        <textarea class="form-control" id="banNotes" name="notes" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Ban User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Unban User Modal -->
<div class="modal fade" id="unbanUserModal" tabindex="-1" aria-labelledby="unbanUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'assistants:moderation_action' company.id assistant.id %}">
                {% csrf_token %}
                <input type="hidden" name="action_type" value="unban">
                <input type="hidden" name="target_user_id" id="unbanUserId">
                <input type="hidden" name="next" value="{{ request.path }}">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="unbanUserModalLabel">Unban User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Unban <strong id="unbanUsername"></strong> from this community assistant</p>
                    
                    <div class="mb-3">
                        <label for="unbanNotes" class="form-label">Notes</label>
                        <textarea class="form-control" id="unbanNotes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Unban User</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/moderation-dashboard.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Adjust Reputation Modal
        const adjustReputationModal = document.getElementById('adjustReputationModal');
        if (adjustReputationModal) {
            adjustReputationModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const userId = button.getAttribute('data-user-id');
                const username = button.getAttribute('data-username');
                
                document.getElementById('adjustReputationUserId').value = userId;
                document.getElementById('adjustReputationUsername').textContent = username;
            });
        }
        
        // Warn User Modal
        const warnUserModal = document.getElementById('warnUserModal');
        if (warnUserModal) {
            warnUserModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const userId = button.getAttribute('data-user-id');
                const username = button.getAttribute('data-username');
                
                document.getElementById('warnUserId').value = userId;
                document.getElementById('warnUsername').textContent = username;
            });
        }
        
        // Ban User Modal
        const banUserModal = document.getElementById('banUserModal');
        if (banUserModal) {
            banUserModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const userId = button.getAttribute('data-user-id');
                const username = button.getAttribute('data-username');
                
                document.getElementById('banUserId').value = userId;
                document.getElementById('banUsername').textContent = username;
            });
        }
        
        // Unban User Modal
        const unbanUserModal = document.getElementById('unbanUserModal');
        if (unbanUserModal) {
            unbanUserModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const userId = button.getAttribute('data-user-id');
                const username = button.getAttribute('data-username');
                
                document.getElementById('unbanUserId').value = userId;
                document.getElementById('unbanUsername').textContent = username;
            });
        }
    });
</script>
{% endblock %}
