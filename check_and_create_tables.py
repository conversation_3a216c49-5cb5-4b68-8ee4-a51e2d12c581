import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

print("Starting comprehensive database check and setup script...")
print(f"Django version: {django.get_version()}")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

def get_all_tables():
    """Get all tables in the database."""
    sql = """
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public'
    ORDER BY table_name;
    """
    
    try:
        conn = psycopg2.connect(**db_params)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        cursor.execute(sql)
        tables = [row[0] for row in cursor.fetchall()]
        cursor.close()
        conn.close()
        return tables
    except Exception as e:
        print(f"Error getting tables: {e}")
        return []

def get_table_columns(table_name):
    """Get all columns for a specific table."""
    sql = """
    SELECT column_name, data_type, is_nullable, column_default
    FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name = %s
    ORDER BY ordinal_position;
    """
    
    try:
        conn = psycopg2.connect(**db_params)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        cursor.execute(sql, (table_name,))
        columns = [
            {
                'name': row[0],
                'type': row[1],
                'nullable': row[2],
                'default': row[3]
            }
            for row in cursor.fetchall()
        ]
        cursor.close()
        conn.close()
        return columns
    except Exception as e:
        print(f"Error getting columns for table {table_name}: {e}")
        return []

def check_database_structure():
    """Check the database structure and print information."""
    tables = get_all_tables()
    
    print(f"\nFound {len(tables)} tables in the database:")
    for table in tables:
        columns = get_table_columns(table)
        print(f"\n  - {table} ({len(columns)} columns)")
        for column in columns:
            nullable = "NULL" if column['nullable'] == "YES" else "NOT NULL"
            default = f"DEFAULT {column['default']}" if column['default'] else ""
            print(f"      {column['name']} {column['type']} {nullable} {default}")
    
    return tables

def run_django_check():
    """Run Django's system check to identify any issues."""
    from django.core.management import call_command
    
    print("\nRunning Django system check...")
    call_command('check')

def run_migrations():
    """Run Django migrations."""
    from django.core.management import call_command
    
    print("\nRunning Django migrations...")
    try:
        call_command('migrate', '--fake-initial')
        print("Migrations completed successfully!")
    except Exception as e:
        print(f"Error running migrations: {e}")

def main():
    """Main function to check and set up the database."""
    print("\n=== DATABASE STRUCTURE ===")
    tables = check_database_structure()
    
    print("\n=== DJANGO SYSTEM CHECK ===")
    run_django_check()
    
    # Ask if user wants to run migrations
    print("\nWould you like to run Django migrations? (y/n)")
    response = input().strip().lower()
    if response == 'y':
        run_migrations()
    
    print("\nDatabase check and setup completed!")

if __name__ == "__main__":
    main()
