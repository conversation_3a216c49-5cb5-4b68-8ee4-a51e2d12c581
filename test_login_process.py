#!/usr/bin/env python
"""
Test script for login process.
This script tests the login process to ensure it works correctly.
"""

import os
import sys
import time
import django
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('login_test.log')
    ]
)
logger = logging.getLogger(__name__)

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
# Set CPANEL_ENV to True to use file-based cache and sessions
os.environ['CPANEL_ENV'] = 'True'
django.setup()

from django.contrib.auth import get_user_model, login, authenticate
from django.utils import timezone
from django.test import RequestFactory
from django.contrib.sessions.middleware import SessionMiddleware
from django.contrib.auth.middleware import AuthenticationMiddleware
from django.contrib.messages.middleware import MessageMiddleware
from django.contrib.messages.storage.fallback import FallbackStorage
from accounts.auth_utils import store_signin_approval, verify_signin_token
from accounts.auth_views import approve_signin

User = get_user_model()

def add_middleware_to_request(request, middleware_class):
    """Add middleware to request."""
    middleware = middleware_class()
    middleware.process_request(request)
    return request

def add_session_to_request(request):
    """Add session to request."""
    middleware = SessionMiddleware(get_response=lambda r: None)
    middleware.process_request(request)
    request.session.save()
    return request

def add_messages_to_request(request):
    """Add messages to request."""
    middleware = MessageMiddleware(get_response=lambda r: None)
    middleware.process_request(request)
    request._messages = FallbackStorage(request)
    return request

def add_auth_to_request(request):
    """Add auth to request."""
    middleware = AuthenticationMiddleware(get_response=lambda r: None)
    middleware.process_request(request)
    return request

def test_login_process():
    """Test the login process."""
    logger.info("=== Testing Login Process ===")
    
    # Get or create a test user
    try:
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'is_active': True,
            }
        )
        
        if created:
            user.set_password('testpassword')
            user.save()
            logger.info(f"Created test user: {user.username} (ID: {user.id})")
        else:
            logger.info(f"Using existing test user: {user.username} (ID: {user.id})")
        
        # Test direct login
        logger.info("\nTesting direct login...")
        factory = RequestFactory()
        request = factory.get('/')
        request = add_session_to_request(request)
        request = add_auth_to_request(request)
        request = add_messages_to_request(request)
        
        # Authenticate user
        auth_user = authenticate(username='testuser', password='testpassword')
        if auth_user:
            logger.info(f"User authenticated: {auth_user.username}")
            # Login user
            login(request, auth_user)
            logger.info(f"User logged in: {request.user.is_authenticated}")
        else:
            logger.error("Authentication failed")
            return False
        
        # Test token-based login
        logger.info("\nTesting token-based login...")
        
        # Generate token
        token = store_signin_approval(user, 24)
        logger.info(f"Generated token: {token}")
        
        # Create request for token verification
        request = factory.get(f'/accounts/approve-signin/{token}/')
        request = add_session_to_request(request)
        request = add_auth_to_request(request)
        request = add_messages_to_request(request)
        
        # Call approve_signin view
        logger.info("Calling approve_signin view...")
        response = approve_signin(request, token)
        
        # Check if user is authenticated
        logger.info(f"User authenticated after approve_signin: {request.user.is_authenticated}")
        
        # Test session persistence
        logger.info("\nTesting session persistence...")
        session_key = request.session.session_key
        logger.info(f"Session key: {session_key}")
        
        # Create a new request with the same session key
        request2 = factory.get('/')
        request2.session = request.session
        request2 = add_auth_to_request(request2)
        
        # Check if user is still authenticated
        logger.info(f"User authenticated in new request: {request2.user.is_authenticated}")
        
        return request.user.is_authenticated and request2.user.is_authenticated
    
    except Exception as e:
        logger.error(f"Error testing login process: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """Run all tests."""
    logger.info("=== Login Process Tests ===")
    logger.info(f"Time: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"Django Settings Module: {os.environ.get('DJANGO_SETTINGS_MODULE')}")
    
    # Test login process
    result = test_login_process()
    
    # Print summary
    logger.info("\n=== Test Summary ===")
    logger.info(f"Login Process: {'PASSED' if result else 'FAILED'}")
    
    if result:
        logger.info("\n✅ All tests PASSED")
        logger.info("The login process should work correctly in production")
    else:
        logger.info("\n❌ Some tests FAILED")
        logger.info("Please review the test results and fix any issues")

if __name__ == "__main__":
    main()
