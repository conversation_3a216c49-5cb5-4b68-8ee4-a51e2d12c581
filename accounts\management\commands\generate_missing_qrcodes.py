from django.core.management.base import BaseCommand
from django.urls import reverse
from accounts.models import Company
from assistants.models import Assistant
from utils.qr_generator import generate_model_qr_code
import time
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Generate QR codes for all companies and assistants that do not have one'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force regeneration of QR codes even if they already exist',
        )
        parser.add_argument(
            '--companies-only',
            action='store_true',
            help='Only generate QR codes for companies',
        )
        parser.add_argument(
            '--assistants-only',
            action='store_true',
            help='Only generate QR codes for assistants',
        )
        parser.add_argument(
            '--community-only',
            action='store_true',
            help='Only generate QR codes for community assistants',
        )

    def handle(self, *args, **options):
        force = options.get('force', False)
        companies_only = options.get('companies_only', False)
        assistants_only = options.get('assistants_only', False)
        community_only = options.get('community_only', False)

        # Process companies if not assistants_only
        if not assistants_only:
            self.generate_company_qrcodes(force)

        # Process assistants if not companies_only
        if not companies_only:
            self.generate_assistant_qrcodes(force, community_only)

        self.stdout.write(self.style.SUCCESS("QR code generation completed!"))

    def generate_company_qrcodes(self, force=False):
        """Generate QR codes for companies."""
        # Build the query
        query = {}
        if not force:
            query['qr_code'] = ''  # Only get companies without QR codes

        # Get the companies
        companies = Company.objects.filter(**query)
        total = companies.count()

        self.stdout.write(f"Found {total} companies that need QR codes")

        # Process each company
        success_count = 0
        fail_count = 0

        for i, company in enumerate(companies, 1):
            self.stdout.write(f"Processing {i}/{total}: {company.name} (ID: {company.id})")

            # Skip companies without slugs
            if not company.slug:
                self.stdout.write(self.style.WARNING(
                    f"Company '{company.name}' has no slug. Generating slug..."
                ))
                from django.utils.text import slugify
                company.slug = slugify(company.name)
                company.save(update_fields=['slug'])
                self.stdout.write(self.style.SUCCESS(
                    f"Generated slug '{company.slug}' for company '{company.name}'"
                ))

            try:
                # Use the company's public detail URL for the QR code
                url_path = reverse('accounts:public_company_detail', kwargs={'slug': company.slug})
                success = generate_model_qr_code(company, url_path, field_name='qr_code')

                if success:
                    # Save the company with the new QR code
                    company.save(update_fields=['qr_code'])
                    self.stdout.write(self.style.SUCCESS(
                        f"Successfully generated QR code for {company.name}"
                    ))
                    success_count += 1
                else:
                    self.stdout.write(self.style.WARNING(
                        f"Failed to generate QR code for {company.name}"
                    ))
                    fail_count += 1
            except Exception as e:
                self.stdout.write(self.style.ERROR(
                    f"Error generating QR code for {company.name}: {str(e)}"
                ))
                fail_count += 1

        self.stdout.write(f"\nCompany QR Code Summary:")
        self.stdout.write(f"Successfully generated QR codes for {success_count} companies")
        if fail_count > 0:
            self.stdout.write(self.style.WARNING(
                f"Failed to generate QR codes for {fail_count} companies"
            ))

    def generate_assistant_qrcodes(self, force=False, community_only=False):
        """Generate QR codes for assistants."""
        # Build the query
        query = {}
        if community_only:
            query['assistant_type'] = 'community'
        if not force:
            query['qr_code'] = ''  # Only get assistants without QR codes

        # Get the assistants
        assistants = Assistant.objects.filter(**query)
        total = assistants.count()

        self.stdout.write(f"Found {total} assistants that need QR codes")

        # Process each assistant
        success_count = 0
        fail_count = 0

        for i, assistant in enumerate(assistants, 1):
            self.stdout.write(f"Processing {i}/{total}: {assistant.name} (ID: {assistant.id})")

            # Skip assistants without slugs
            if not assistant.slug:
                self.stdout.write(self.style.WARNING(
                    f"Assistant '{assistant.name}' has no slug. Generating slug..."
                ))
                from django.utils.text import slugify
                assistant.slug = slugify(f"{assistant.name}-{assistant.id}")
                assistant.save(update_fields=['slug'])
                self.stdout.write(self.style.SUCCESS(
                    f"Generated slug '{assistant.slug}' for assistant '{assistant.name}'"
                ))

            # Prioritize community assistants with more attempts
            max_attempts = 3 if assistant.assistant_type == 'community' else 1
            success = False

            for attempt in range(max_attempts):
                try:
                    # Use the assistant's public chat URL for the QR code
                    url_path = reverse('assistants:assistant_chat', kwargs={'slug': assistant.slug})
                    success = generate_model_qr_code(assistant, url_path, field_name='qr_code')

                    if success:
                        # Save the assistant with the new QR code
                        assistant.save(update_fields=['qr_code'])
                        self.stdout.write(self.style.SUCCESS(
                            f"Successfully generated QR code for {assistant.name} (attempt {attempt+1})"
                        ))
                        success_count += 1
                        break
                    else:
                        self.stdout.write(self.style.WARNING(
                            f"Failed to generate QR code for {assistant.name} (attempt {attempt+1})"
                        ))
                        # Small delay before retry
                        time.sleep(0.5)
                except Exception as e:
                    self.stdout.write(self.style.ERROR(
                        f"Error generating QR code for {assistant.name}: {str(e)}"
                    ))
                    # Continue to next attempt

            if not success:
                fail_count += 1

        self.stdout.write(f"\nAssistant QR Code Summary:")
        self.stdout.write(f"Successfully generated QR codes for {success_count} assistants")
        if fail_count > 0:
            self.stdout.write(self.style.WARNING(
                f"Failed to generate QR codes for {fail_count} assistants"
            ))
