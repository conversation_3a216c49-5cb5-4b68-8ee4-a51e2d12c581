import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

print("Starting script to verify directory_favoritefolder table structure...")

# SQL to check the table structure
check_table_sql = """
SELECT column_name, data_type, character_maximum_length
FROM information_schema.columns
WHERE table_name = 'directory_favoritefolder'
ORDER BY ordinal_position;
"""

# Connect to the database
try:
    print("Using Django's database connection...")
    cursor = connection.cursor()

    # Check the table structure
    print("Checking directory_favoritefolder table structure...")
    cursor.execute(check_table_sql)
    columns = cursor.fetchall()

    print("\nTable structure:")
    print("Column Name | Data Type | Max Length")
    print("-" * 50)
    for column in columns:
        print(f"{column[0]} | {column[1]} | {column[2]}")

    # Check if item_type column exists
    item_type_exists = any(column[0] == 'item_type' for column in columns)
    if item_type_exists:
        print("\nThe 'item_type' column exists in the table!")
    else:
        print("\nWARNING: The 'item_type' column does NOT exist in the table!")

    # Close the cursor
    cursor.close()
    print("\nDatabase connection closed.")

except Exception as e:
    print(f"Error: {e}")
