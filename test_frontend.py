"""
Frontend test script to check if the application's frontend is working correctly.
"""

import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.test import Client
from django.urls import reverse
from django.contrib.auth.models import User
from accounts.models import Company
from assistants.models import Assistant

def test_homepage():
    """Test if the homepage loads correctly."""
    print("Testing homepage...")
    client = Client()
    response = client.get('/')
    print(f"Homepage status code: {response.status_code}")
    assert response.status_code == 200, "Homepage should return 200 OK"
    print("Homepage test passed!")
    return True

def test_login_page():
    """Test if the login page loads correctly."""
    print("Testing login page...")
    client = Client()
    response = client.get('/accounts/login/')
    print(f"Login page status code: {response.status_code}")
    assert response.status_code == 200, "Login page should return 200 OK"
    print("Login page test passed!")
    return True

def test_assistant_list():
    """Test if the assistant list page loads correctly."""
    print("Testing assistant list page...")
    client = Client()
    response = client.get('/directory/assistants/')
    print(f"Assistant list page status code: {response.status_code}")
    assert response.status_code == 200, "Assistant list page should return 200 OK"
    print("Assistant list page test passed!")
    return True

def test_dark_mode_css():
    """Test if the dark mode CSS is loaded correctly."""
    print("Testing dark mode CSS...")
    client = Client()
    response = client.get('/')
    content = response.content.decode('utf-8')
    assert 'data-theme="dark"' in content, "Dark mode should be enabled by default"
    print("Dark mode CSS test passed!")
    return True

def run_all_frontend_tests():
    """Run all frontend tests."""
    print("Running all frontend tests...")

    results = []
    results.append(test_homepage())
    results.append(test_login_page())
    results.append(test_assistant_list())
    results.append(test_dark_mode_css())

    # Return True only if all tests passed
    return all(results)

if __name__ == "__main__":
    run_all_frontend_tests()
