"""
<PERSON><PERSON><PERSON> to fix migration conflicts in the assistants app using Django's management commands.
"""
import os
import sys
import django
from django.core.management import call_command

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

def fix_migrations():
    """Fix the migration conflict in the assistants app."""
    print("Starting migration fix...")
    
    # Fake the conflicting migration
    print("Faking conflicting migration...")
    try:
        call_command('migrate', 'assistants', '0009_add_featured_settings', fake=True)
        print("Migration 'assistants.0009_add_featured_settings' faked successfully.")
    except Exception as e:
        print(f"Error faking migration: {e}")
        return
    
    # Apply the merge migration
    print("Applying merge migration...")
    try:
        call_command('migrate', 'assistants', '0010_merge_20250402_0632')
        print("Migration 'assistants.0010_merge_20250402_0632' applied successfully.")
    except Exception as e:
        print(f"Error applying merge migration: {e}")
        return
    
    # Continue with the remaining migrations
    print("Applying remaining migrations...")
    try:
        call_command('migrate')
        print("All migrations applied successfully.")
    except Exception as e:
        print(f"Error applying remaining migrations: {e}")
        return
    
    print("Migration fix completed.")

if __name__ == "__main__":
    fix_migrations()
