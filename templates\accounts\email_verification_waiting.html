{% extends 'base/layout.html' %}
{% load static %}

{% block title %}Email Verification Required - 24seven{% endblock %}

{% block extra_css %}
<style>
.verification-container {
    max-width: 600px;
    margin: 0 auto;
}

.verification-icon {
    font-size: 4rem;
    color: #007bff;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.email-preview {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1.5rem 0;
}

.email-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.email-subject {
    font-weight: 600;
    color: #495057;
}

.email-body {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
}

.countdown-timer {
    font-size: 1.1rem;
    font-weight: 600;
    color: #dc3545;
}

.resend-section {
    background: rgba(0, 123, 255, 0.05);
    border: 1px solid rgba(0, 123, 255, 0.2);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1.5rem;
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.status-pending {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-sent {
    background: rgba(40, 167, 69, 0.1);
    color: #155724;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.help-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.btn-resend {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-resend:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-resend:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.progress-bar-container {
    background: #e9ecef;
    border-radius: 10px;
    height: 6px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-bar {
    background: linear-gradient(90deg, #007bff, #0056b3);
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="verification-container">
        <div class="text-center mb-4">
            <i class="bi bi-envelope-check verification-icon"></i>
            <h1 class="h3 mt-3 mb-2">Email Verification Required</h1>
            <p class="text-muted">We've sent a verification link to your email address</p>
        </div>

        <div class="card shadow-sm">
            <div class="card-body p-4">
                <!-- Status Indicator -->
                <div class="text-center mb-4">
                    <span class="status-indicator status-sent" id="status-indicator">
                        <i class="bi bi-check-circle"></i>
                        Email sent successfully
                    </span>
                </div>

                <!-- Email Preview -->
                <div class="email-preview">
                    <div class="email-header">
                        <i class="bi bi-envelope me-2 text-primary"></i>
                        <div>
                            <div class="email-subject">Sign-in Verification Required</div>
                            <small class="text-muted">To: {{ user.email }}</small>
                        </div>
                    </div>
                    <div class="email-body">
                        <p class="mb-2">
                            <strong>Hello {{ user.get_full_name|default:user.username }},</strong>
                        </p>
                        <p class="mb-2">
                            We detected a sign-in attempt to your 24seven account. For security reasons, 
                            we need to verify that it's really you.
                        </p>
                        <p class="mb-0">
                            Please click the "Approve Sign-in" button in the email to complete your login.
                        </p>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="alert alert-info">
                    <h6 class="alert-heading">
                        <i class="bi bi-info-circle me-2"></i>
                        What to do next:
                    </h6>
                    <ol class="mb-0">
                        <li>Check your email inbox for a message from 24seven</li>
                        <li>Click the "Approve Sign-in" button in the email</li>
                        <li>You'll be automatically logged in and redirected</li>
                    </ol>
                </div>

                <!-- Countdown Timer -->
                <div class="text-center mb-3">
                    <p class="mb-1">
                        <i class="bi bi-clock me-2"></i>
                        Link expires in: <span class="countdown-timer" id="countdown-timer">24:00:00</span>
                    </p>
                    <div class="progress-bar-container">
                        <div class="progress-bar" id="progress-bar" style="width: 100%"></div>
                    </div>
                </div>

                <!-- Resend Section -->
                <div class="resend-section">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">Didn't receive the email?</h6>
                            <small class="text-muted">Check your spam folder or request a new verification email</small>
                        </div>
                        <button type="button" class="btn btn-primary btn-resend" id="resend-btn" onclick="resendVerificationEmail()">
                            <i class="bi bi-arrow-clockwise me-2"></i>
                            Resend Email
                        </button>
                    </div>
                    <div id="resend-cooldown" class="mt-2" style="display: none;">
                        <small class="text-muted">
                            You can request another email in <span id="resend-timer">60</span> seconds
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Help Section -->
        <div class="help-section">
            <h6 class="mb-3">
                <i class="bi bi-question-circle me-2"></i>
                Need Help?
            </h6>
            <div class="row">
                <div class="col-md-6">
                    <h6 class="h6">Email not arriving?</h6>
                    <ul class="small text-muted">
                        <li>Check your spam/junk folder</li>
                        <li>Add <EMAIL> to your contacts</li>
                        <li>Wait a few minutes for delivery</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="h6">Still having issues?</h6>
                    <ul class="small text-muted">
                        <li><a href="{% url 'accounts:login' %}">Try logging in again</a></li>
                        <li><a href="{% url 'accounts:password_reset' %}">Reset your password</a></li>
                        <li><a href="{% url 'contact' %}">Contact support</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mt-4">
            <a href="{% url 'accounts:login' %}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-left me-2"></i>
                Back to Login
            </a>
            <a href="{% url 'home' %}" class="btn btn-outline-primary">
                <i class="bi bi-house me-2"></i>
                Go to Home
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Countdown timer functionality
    let expiryTime = new Date();
    expiryTime.setHours(expiryTime.getHours() + 24); // 24 hours from now
    
    function updateCountdown() {
        const now = new Date();
        const timeLeft = expiryTime - now;
        
        if (timeLeft <= 0) {
            document.getElementById('countdown-timer').textContent = 'Expired';
            document.getElementById('progress-bar').style.width = '0%';
            return;
        }
        
        const hours = Math.floor(timeLeft / (1000 * 60 * 60));
        const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
        
        const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        document.getElementById('countdown-timer').textContent = timeString;
        
        // Update progress bar
        const totalTime = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
        const progress = (timeLeft / totalTime) * 100;
        document.getElementById('progress-bar').style.width = progress + '%';
    }
    
    // Update countdown every second
    setInterval(updateCountdown, 1000);
    updateCountdown(); // Initial call
    
    // Auto-refresh to check for login status every 30 seconds
    setInterval(function() {
        fetch('{% url "accounts:check_login_status" %}', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.logged_in) {
                window.location.href = '{% url "home" %}';
            }
        })
        .catch(error => {
            console.log('Status check failed:', error);
        });
    }, 30000);
});

function resendVerificationEmail() {
    const resendBtn = document.getElementById('resend-btn');
    const resendCooldown = document.getElementById('resend-cooldown');
    const statusIndicator = document.getElementById('status-indicator');
    
    // Disable button and show loading
    resendBtn.disabled = true;
    resendBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin me-2"></i>Sending...';
    
    fetch('{% url "accounts:resend_verification" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': '{{ csrf_token }}',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify({
            'username': '{{ user.username }}'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            statusIndicator.className = 'status-indicator status-sent';
            statusIndicator.innerHTML = '<i class="bi bi-check-circle"></i> Email sent successfully';
            
            // Start cooldown
            startResendCooldown();
        } else {
            statusIndicator.className = 'status-indicator status-pending';
            statusIndicator.innerHTML = '<i class="bi bi-exclamation-triangle"></i> Failed to send email';
            resendBtn.disabled = false;
            resendBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Resend Email';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        statusIndicator.className = 'status-indicator status-pending';
        statusIndicator.innerHTML = '<i class="bi bi-exclamation-triangle"></i> Failed to send email';
        resendBtn.disabled = false;
        resendBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Resend Email';
    });
}

function startResendCooldown() {
    const resendBtn = document.getElementById('resend-btn');
    const resendCooldown = document.getElementById('resend-cooldown');
    const resendTimer = document.getElementById('resend-timer');
    
    let countdown = 60;
    resendCooldown.style.display = 'block';
    
    const timer = setInterval(function() {
        countdown--;
        resendTimer.textContent = countdown;
        
        if (countdown <= 0) {
            clearInterval(timer);
            resendCooldown.style.display = 'none';
            resendBtn.disabled = false;
            resendBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Resend Email';
        }
    }, 1000);
}

// Add CSS for spin animation
const style = document.createElement('style');
style.textContent = `
    .spin {
        animation: spin 1s linear infinite;
    }
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
