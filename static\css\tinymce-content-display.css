/**
 * TinyMCE Content Display CSS
 * Ensures content created in TinyMCE displays properly on the frontend
 */

/* Base content container */
.tinymce-content {
    width: 100%;
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: normal;
    line-height: 1.5;
}

/* Ensure images are responsive */
.tinymce-content img {
    max-width: 100%;
    height: auto !important;
    display: block;
    margin: 0.5em auto;
    border-radius: 4px;
}

/* Image alignment classes */
.tinymce-content img.float-start,
.tinymce-content img.float-left {
    float: left !important;
    margin-right: 1rem !important;
    margin-bottom: 0.5rem !important;
}

.tinymce-content img.float-end,
.tinymce-content img.float-right {
    float: right !important;
    margin-left: 1rem !important;
    margin-bottom: 0.5rem !important;
}

.tinymce-content img.mx-auto,
.tinymce-content img.d-block {
    margin-left: auto !important;
    margin-right: auto !important;
}

/* Table wrapper for horizontal scrolling */
.tinymce-content .table-wrapper {
    width: 100%;
    overflow-x: auto;
    margin: 0.75rem 0;
    border-radius: 0.5rem;
    position: relative;
    -webkit-overflow-scrolling: touch;
}

/* Base table styling */
.tinymce-content table {
    width: 100% !important;
    max-width: 100% !important;
    margin-bottom: 1rem;
    border-collapse: collapse !important;
    border-spacing: 0 !important;
    overflow: hidden !important;
    table-layout: auto !important;
}

/* Table cells */
.tinymce-content table td,
.tinymce-content table th {
    padding: 0.75rem !important;
    vertical-align: top !important;
    border: 1px solid #dee2e6 !important;
    word-break: normal !important;
    overflow-wrap: break-word !important;
    min-width: 50px !important;
}

/* Auto-wrap tables that don't have a wrapper */
.tinymce-content table:not(.table-wrapper table) {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Bootstrap table classes */
.tinymce-content .table {
    width: 100%;
    margin-bottom: 1rem;
    color: inherit;
}

.tinymce-content .table-bordered {
    border: 1px solid #dee2e6;
}

.tinymce-content .table-bordered td,
.tinymce-content .table-bordered th {
    border: 1px solid #dee2e6;
}

.tinymce-content .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.05);
}

.tinymce-content .table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
}

.tinymce-content .table-sm td,
.tinymce-content .table-sm th {
    padding: 0.3rem;
}

/* Responsive media queries */
@media (max-width: 992px) {
    .tinymce-content {
        font-size: 15px;
        width: 100% !important;
        max-width: 100% !important;
    }

    .tinymce-content table {
        font-size: 14px;
        width: 100% !important;
    }

    .tinymce-content table td,
    .tinymce-content table th {
        padding: 0.6rem !important;
    }
}

@media (max-width: 768px) {
    .tinymce-content {
        font-size: 16px;
        width: 100% !important;
        max-width: 100% !important;
    }

    .tinymce-content table {
        font-size: 14px;
        border: none !important;
        width: 100% !important;
    }

    .tinymce-content table td,
    .tinymce-content table th {
        padding: 0.5rem !important;
        min-width: 80px !important;
    }

    /* Ensure images don't overflow on mobile */
    .tinymce-content img.float-start,
    .tinymce-content img.float-left,
    .tinymce-content img.float-end,
    .tinymce-content img.float-right {
        float: none !important;
        display: block !important;
        margin: 0.5rem auto !important;
        max-width: 100% !important;
    }
}

@media (max-width: 576px) {
    .tinymce-content {
        width: 100% !important;
        max-width: 100% !important;
    }

    .tinymce-content table {
        font-size: 13px;
        width: 100% !important;
    }

    .tinymce-content table td,
    .tinymce-content table th {
        padding: 0.4rem !important;
        min-width: 70px !important;
    }
}

/* Dark mode adjustments */
[data-theme="dark"] .tinymce-content table {
    color: #f8f9fa;
    border-color: #495057 !important;
}

[data-theme="dark"] .tinymce-content table td,
[data-theme="dark"] .tinymce-content table th {
    border-color: #495057 !important;
}

[data-theme="dark"] .tinymce-content .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .tinymce-content .table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.075);
}
