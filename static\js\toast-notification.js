/**
 * Toast Notification System
 * A lightweight, customizable toast notification system that doesn't rely on external libraries
 */

// Create toast container if it doesn't exist
document.addEventListener('DOMContentLoaded', function() {
    if (!document.getElementById('toast-container')) {
        const toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        toastContainer.style.zIndex = '1080';
        document.body.appendChild(toastContainer);
    }
});

/**
 * Show a toast notification
 * @param {string} message - The message to display
 * @param {string} type - The type of toast (success, error, warning, info)
 * @param {number} duration - Duration in milliseconds
 */
function showToast(message, type = 'success', duration = 3000) {
    // Get or create toast container
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        toastContainer.style.zIndex = '1080';
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toastId = 'toast-' + Date.now();
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = 'toast show';
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    // Set toast background color based on type
    let bgColor, iconClass, textColor = 'text-white';

    switch (type) {
        case 'success':
            bgColor = 'bg-success';
            iconClass = 'bi-check-circle-fill';
            break;
        case 'error':
            bgColor = 'bg-danger';
            iconClass = 'bi-exclamation-circle-fill';
            break;
        case 'warning':
            bgColor = 'bg-warning';
            iconClass = 'bi-exclamation-triangle-fill';
            textColor = 'text-dark';
            break;
        case 'info':
            bgColor = 'bg-info';
            iconClass = 'bi-info-circle-fill';
            textColor = 'text-dark';
            break;
        default:
            bgColor = 'bg-dark';
            iconClass = 'bi-bell-fill';
    }

    toast.classList.add(bgColor, textColor);

    // Create toast content
    toast.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="toast-body">
                <i class="bi ${iconClass} me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    // Add toast to container
    toastContainer.appendChild(toast);

    // Auto-remove toast after duration
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            toast.remove();
        }, 300); // Wait for fade out animation
    }, duration);

    // Add click event to close button
    const closeButton = toast.querySelector('.btn-close');
    if (closeButton) {
        closeButton.addEventListener('click', () => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        });
    }

    return toastId;
}

// Add CSS for toast animations
document.addEventListener('DOMContentLoaded', function() {
    // Only add styles if they don't already exist
    if (!document.getElementById('toast-notification-styles')) {
        const style = document.createElement('style');
        style.id = 'toast-notification-styles';
        style.textContent = `
            .toast-container {
                z-index: 1080;
            }
            .toast {
                opacity: 0;
                transition: opacity 0.3s ease-in-out;
                min-width: 250px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }
            .toast.show {
                opacity: 1;
            }
            .toast .btn-close-white {
                filter: invert(1);
            }
            /* Dark mode specific styles */
            [data-theme="dark"] .toast {
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
            }
            /* Animation */
            @keyframes toast-slide-in {
                from { transform: translateY(20px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
            .toast.show {
                animation: toast-slide-in 0.3s ease forwards;
            }
        `;
        document.head.appendChild(style);
    }
});

// Make the function globally available
window.showToast = showToast;
