"""
<PERSON><PERSON><PERSON> to fix file permissions for cPanel deployment.
Run this script after uploading your code to cPanel.
"""
import os
import sys
import stat
import subprocess

def fix_permissions(path):
    """
    Fix permissions for files and directories:
    - Directories: 755 (rwxr-xr-x)
    - Files: 644 (rw-r--r--)
    - Executable files: 755 (rwxr-xr-x)
    """
    for root, dirs, files in os.walk(path):
        # Fix directory permissions
        for directory in dirs:
            dir_path = os.path.join(root, directory)
            try:
                os.chmod(dir_path, 0o755)  # rwxr-xr-x
                print(f"Set 755 permissions on directory: {dir_path}")
            except Exception as e:
                print(f"Error setting permissions on {dir_path}: {e}")
        
        # Fix file permissions
        for file in files:
            file_path = os.path.join(root, file)
            try:
                # Check if it's a Python file or shell script
                if file.endswith(('.py', '.sh')) or file == 'manage.py':
                    os.chmod(file_path, 0o755)  # rwxr-xr-x for executable files
                    print(f"Set 755 permissions on executable: {file_path}")
                else:
                    os.chmod(file_path, 0o644)  # rw-r--r-- for regular files
                    print(f"Set 644 permissions on file: {file_path}")
            except Exception as e:
                print(f"Error setting permissions on {file_path}: {e}")

if __name__ == "__main__":
    print("Fixing file permissions for cPanel deployment...")
    
    # Get the current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Fix permissions
    fix_permissions(current_dir)
    
    # Special directories that need write permissions
    special_dirs = [
        os.path.join(current_dir, 'media'),
        os.path.join(current_dir, 'media', 'tinymce_uploads'),
        os.path.join(current_dir, 'logs'),
        os.path.join(current_dir, 'staticfiles')
    ]
    
    # Ensure these directories exist and have proper permissions
    for directory in special_dirs:
        os.makedirs(directory, exist_ok=True)
        os.chmod(directory, 0o755)
        print(f"Ensured directory exists with 755 permissions: {directory}")
    
    print("\nPermissions fixed successfully!")
    print("\nNext steps:")
    print("1. Run collect_static.py to collect static files")
    print("2. Restart your application in cPanel")
