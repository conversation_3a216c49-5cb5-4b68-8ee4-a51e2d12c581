/**
 * Folder Functionality Tests
 * Tests for the folder-related JavaScript functionality
 */

// Import test framework
const { describe, test, beforeEach, afterEach, expect } = require('./test-runner');

// Mock DOM elements and functions
const setupMockDOM = () => {
    // Create mock DOM elements
    document.body.innerHTML = `
        <div id="folderOptionsModal" class="modal">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="folderModalTitle">Add to Favorites</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Add <strong id="modalItemName">Test Item</strong> to:</p>
                        <div class="d-grid gap-2 mb-3">
                            <button type="button" id="saveWithoutFolderBtn" class="btn btn-primary save-without-folder-btn">
                                <i class="bi bi-heart-fill"></i> Save to Favorites
                            </button>
                        </div>
                        <div class="mb-3">
                            <label for="selectFolder" class="form-label">Select a folder:</label>
                            <select id="selectFolder" class="form-select">
                                <option value="">-- Select Folder --</option>
                            </select>
                            <button type="button" id="addToFolderBtn" class="btn btn-outline-primary mt-2" disabled>
                                Add to Selected Folder
                            </button>
                        </div>
                        <div class="mb-3">
                            <button type="button" id="createFolderToggleBtn" class="btn btn-outline-secondary">
                                <i class="bi bi-plus-circle"></i> Create New Folder
                            </button>
                            <div id="createFolderForm" style="display: none;" class="mt-2">
                                <div class="input-group">
                                    <input type="text" id="newFolderName" class="form-control" placeholder="Folder name">
                                    <button type="button" id="createAndSaveBtn" class="btn btn-success">
                                        Create & Save
                                    </button>
                                </div>
                                <div id="folderModalErrorMsg" class="alert alert-danger mt-2" style="display: none;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <button class="like-button" data-item-id="123" data-item-type="assistant">
            <i class="bi bi-heart"></i>
        </button>
    `;

    // Mock Bootstrap modal
    window.bootstrap = {
        Modal: class {
            constructor(element) {
                this.element = element;
            }
            show() {
                this.element.classList.add('show');
            }
            hide() {
                this.element.classList.remove('show');
            }
        }
    };

    // Mock fetch
    global.fetch = jest.fn().mockImplementation((url) => {
        if (url.includes('toggle_saved_item')) {
            return Promise.resolve({
                ok: true,
                json: () => Promise.resolve({
                    status: 'options',
                    folders: [
                        { id: 1, name: 'Test Folder 1' },
                        { id: 2, name: 'Test Folder 2' }
                    ],
                    item_name: 'Test Item'
                })
            });
        } else if (url.includes('save_item_no_folder')) {
            return Promise.resolve({
                ok: true,
                json: () => Promise.resolve({
                    status: 'success',
                    saved: true,
                    action: 'saved_without_folder'
                })
            });
        } else if (url.includes('create_folder_and_save')) {
            return Promise.resolve({
                ok: true,
                json: () => Promise.resolve({
                    status: 'success',
                    saved: true,
                    action: 'saved_in_new_folder',
                    folder_id: 3,
                    folder_name: 'New Test Folder'
                })
            });
        } else if (url.includes('add_item_to_folder')) {
            return Promise.resolve({
                ok: true,
                json: () => Promise.resolve({
                    status: 'success',
                    saved: true,
                    action: 'saved_in_folder',
                    folder_id: 1,
                    folder_name: 'Test Folder 1'
                })
            });
        }
        return Promise.reject(new Error('Unhandled fetch URL'));
    });

    // Mock CSRF token
    window.getCsrfToken = jest.fn().mockReturnValue('mock-csrf-token');

    // Set global variables used by the folder functionality
    window.currentModalItemId = null;
    window.currentModalItemType = null;
};

// Clean up after tests
const cleanupMockDOM = () => {
    document.body.innerHTML = '';
    delete window.bootstrap;
    global.fetch.mockClear();
    delete window.getCsrfToken;
    delete window.currentModalItemId;
    delete window.currentModalItemType;
};

describe('Folder Functionality', () => {
    beforeEach(() => {
        setupMockDOM();
    });

    afterEach(() => {
        cleanupMockDOM();
    });

    test('initFolderModal initializes the folder modal correctly', () => {
        // Import the mock function
        const { initFolderModal } = require('./mocks/favorites-functionality');

        // Call the function
        initFolderModal();

        // Check function was called
        expect(initFolderModal).toHaveBeenCalled();
    });

    test('populateAndShowFolderModal populates the modal with folder data', () => {
        // Import the mock function
        const { populateAndShowFolderModal } = require('./mocks/favorites-functionality');

        // Mock data
        const data = {
            folders: [
                { id: 1, name: 'Test Folder 1' },
                { id: 2, name: 'Test Folder 2' }
            ],
            item_name: 'Test Item'
        };

        // Call the function
        populateAndShowFolderModal(data);

        // Check function was called with correct data
        expect(populateAndShowFolderModal).toHaveBeenCalledWith(data);
    });

    test('saveToFavorites saves an item without a folder', async () => {
        // Import the mock function
        const { saveToFavorites } = require('./mocks/favorites-functionality');

        // Set up global variables
        window.currentModalItemId = '123';
        window.currentModalItemType = 'assistant';

        // Call the function
        await saveToFavorites();

        // Check function was called
        expect(saveToFavorites).toHaveBeenCalled();
    });

    test('saveToFavorites creates a new folder and saves an item to it', async () => {
        // Import the mock function
        const { saveToFavorites } = require('./mocks/favorites-functionality');

        // Set up global variables
        window.currentModalItemId = '123';
        window.currentModalItemType = 'assistant';

        // Call the function with a folder name
        await saveToFavorites('New Test Folder');

        // Check function was called with correct parameters
        expect(saveToFavorites).toHaveBeenCalledWith('New Test Folder');
    });

    test('saveToFavorites adds an item to an existing folder', async () => {
        // Import the mock function
        const { saveToFavorites } = require('./mocks/favorites-functionality');

        // Set up global variables
        window.currentModalItemId = '123';
        window.currentModalItemType = 'assistant';

        // Call the function with a folder ID
        await saveToFavorites(null, 1);

        // Check function was called with correct parameters
        expect(saveToFavorites).toHaveBeenCalledWith(null, 1);
    });

    test('handleFavoriteClick handles clicking on a favorite button', async () => {
        // Import the mock function
        const { handleFavoriteClick } = require('./mocks/favorites-functionality');

        // Get the like button
        const likeButton = document.querySelector('.like-button');

        // Call the function
        await handleFavoriteClick(likeButton, '123', 'assistant');

        // Check function was called with correct parameters
        expect(handleFavoriteClick).toHaveBeenCalledWith(likeButton, '123', 'assistant');
    });

    test('updateHeartIcon updates the heart icon appearance', () => {
        // Import the mock function
        const { updateHeartIcon } = require('./mocks/favorites-functionality');

        // Get the like button
        const likeButton = document.querySelector('.like-button');

        // Call the function
        updateHeartIcon(likeButton, true);

        // Check function was called with correct parameters
        expect(updateHeartIcon).toHaveBeenCalledWith(likeButton, true);

        // Call the function again with false
        updateHeartIcon(likeButton, false);

        // Check function was called with correct parameters
        expect(updateHeartIcon).toHaveBeenCalledWith(likeButton, false);
    });
});
