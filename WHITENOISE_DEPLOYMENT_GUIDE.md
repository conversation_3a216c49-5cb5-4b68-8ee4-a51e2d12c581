# Whitenoise Deployment Guide for cPanel

This guide explains how to use Whitenoise to serve static files in your Django application on cPanel. Whitenoise allows your Django application to serve its own static files, eliminating the need for complex web server configurations.

## Benefits of Using Whitenoise

- **Simplified Deployment**: No need to configure a separate web server for static files
- **Improved Performance**: Automatic compression and caching of static files
- **Compatibility**: Works well with cPanel and other hosting environments
- **Security**: <PERSON>per<PERSON> handles file permissions and security headers

## Implementation Steps

### 1. Install Whitenoise

Whitenoise is already included in your `requirements.txt` file. Make sure it's installed in your cPanel environment:

```bash
pip install whitenoise
```

### 2. Configure Django Settings

We've created a `production_settings.py` file with Whitenoise configuration:

```python
# Static files settings with Whitenoise
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [BASE_DIR / 'static']
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Whitenoise settings
WHITENOISE_MAX_AGE = 31536000  # 1 year in seconds
WHITENOISE_KEEP_ONLY_HASHED_FILES = True  # Only keep files with hashed names in production

# Add Whitenoise to middleware
# Insert WhiteNoiseMiddleware right after SecurityMiddleware
MIDDLEWARE.insert(1, 'whitenoise.middleware.WhiteNoiseMiddleware')
```

### 3. Update WSGI Configuration

The `passenger_wsgi.py` file has been updated to use production settings:

```python
import os
import sys

# Add the project directory to the Python path
sys.path.insert(0, os.path.dirname(__file__))

# Set the Django settings module to use production settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.production_settings')

# Import the WSGI application
from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()
```

### 4. Collect Static Files

Run the Django collectstatic command to collect all static files:

```bash
python manage.py collectstatic --noinput
```

This will collect all static files into the `staticfiles` directory.

### 5. Deploy to cPanel

1. Upload your code to cPanel, including:
   - `company_assistant/production_settings.py`
   - `passenger_wsgi.py`
   - All your application code

2. Set up a Python application in cPanel:
   - Go to "Setup Python App"
   - Select the Python version (3.8 or higher)
   - Set the application path to your project directory
   - Set the application entry point to `passenger_wsgi.py`
   - Save and deploy

3. Run collectstatic on the server:
   ```bash
   cd ~/your_project_directory
   python manage.py collectstatic --noinput
   ```

4. Restart your application:
   - Go to "Setup Python App" in cPanel
   - Click "Restart App" for your application

### 6. Verify Static Files

1. Clear your browser cache
2. Visit your website
3. Check if CSS and other static files are loading properly
4. Use browser developer tools (F12) to check for any 404 errors

## Troubleshooting

### If Static Files Still Don't Load

1. **Check Whitenoise Installation**:
   ```bash
   pip show whitenoise
   ```

2. **Verify Static Files Collection**:
   ```bash
   ls -la staticfiles/
   ```

3. **Check Application Logs**:
   ```bash
   tail -f logs/django.log
   ```

4. **Try a Different Storage Backend**:
   If you're having issues with `CompressedManifestStaticFilesStorage`, try the simpler storage:
   ```python
   STATICFILES_STORAGE = 'whitenoise.storage.CompressedStaticFilesStorage'
   ```
   Or even:
   ```python
   STATICFILES_STORAGE = 'whitenoise.storage.StaticFilesStorage'
   ```

5. **Check File Permissions**:
   ```bash
   find staticfiles/ -type d -exec chmod 755 {} \;
   find staticfiles/ -type f -exec chmod 644 {} \;
   ```

### Common Issues and Solutions

#### Issue: 404 Errors for Static Files

**Solution**: Make sure you've run `collectstatic` and that the files exist in the `staticfiles` directory.

#### Issue: CSS Files Load But Styles Are Not Applied

**Solution**: Check for JavaScript errors in the console that might be preventing CSS from being applied.

#### Issue: "ImportError: No module named whitenoise"

**Solution**: Make sure Whitenoise is installed in your cPanel Python environment.

## Advanced Configuration

### Serving Media Files

Whitenoise doesn't serve media files by default. For media files, you have a few options:

1. **Use .htaccess Rules**:
   ```apache
   # Serve media files directly
   RewriteCond %{REQUEST_URI} ^/media/
   RewriteCond %{REQUEST_FILENAME} -f
   RewriteRule ^(.*)$ $1 [L]
   ```

2. **Use Django-Storages with S3**:
   For production, consider using Django-Storages with Amazon S3 or similar for media files.

### Performance Optimization

For even better performance:

1. **Enable Brotli Compression**:
   ```bash
   pip install whitenoise[brotli]
   ```

2. **Configure Browser Caching**:
   ```python
   WHITENOISE_MAX_AGE = 31536000  # 1 year in seconds
   ```

## Conclusion

Using Whitenoise simplifies static file serving in Django applications, especially on cPanel. By following this guide, you should have a robust setup for serving static files without relying on complex web server configurations.
