from django.urls import path
from . import views
from . import community_views
from . import moderation_views
from . import test_views
from .community_views import get_community_context_content

app_name = 'assistants'

urlpatterns = [
    # Community Assistant URLs
    path('company/<int:company_id>/assistants/<int:assistant_id>/contexts/',
         community_views.list_contexts,
         name='contexts'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/contexts/add/',
         community_views.add_context,
         name='add_context'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/contexts/<int:context_id>/edit/',
         community_views.edit_context,
         name='edit_context'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/flag/',
         community_views.flag_question,
         name='flag_question'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/flagged/',
         community_views.list_flagged_questions,
         name='flagged_questions'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/flagged/<int:flagged_id>/',
         community_views.flagged_question_detail,
         name='flagged_question_detail'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/flagged/<int:flagged_id>/resolve/',
         community_views.resolve_flagged_question,
         name='resolve_flagged_question'),

    # AJAX endpoint for fetching context content
    path('context/<int:context_id>/', get_community_context_content, name='get_community_context_content'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/flag-from-chat/',
         community_views.flag_from_chat,
         name='flag_from_chat'),
    # LLM Suggested Questions API
    path('api/llm-suggested-questions/', views.api_llm_suggested_questions, name='api_llm_suggested_questions'),
    # List and Detail Views
    path('company/<int:company_id>/assistants/',
         views.assistant_list,
         name='list'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/',
          views.assistant_detail,
          name='detail'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/history/',
         views.assistant_history,
         name='history'), # Added history URL

    # Public Chat Interface (Slug-based, for QR codes)
    path('assistant/assistant/<slug:slug>/chat/',
         views.assistant_chat_view,
          name='assistant_chat'),

    # Direct access to assistant list (for redirects)
    path('assistant/',
         views.community_assistants_list,
          name='assistant_list_redirect'),

    # Legacy URL format (for backward compatibility)
    path('assistant/<slug:slug>/chat/',
         views.assistant_chat_view,
          name='assistant_chat_legacy'),

    # Public Interact Endpoint (Slug-based, for public chat)
    path('assistant/interact/<slug:slug>/',
         views.public_assistant_interact,
         name='public_interact'),

    # ID-based Chat Interface (for backward compatibility)
    path('company/<int:company_id>/assistants/<int:assistant_id>/chat/',
         views.assistant_chat_view_by_id,
         name='assistant_chat_by_id'),

    # CRUD Operations (Create uses Wizard now)
    path('company/<int:company_id>/assistants/create/',
         views.AssistantCreateWizard.as_view(views.FORMS), # Use the wizard view
         name='create'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/update/',
         views.assistant_update,
         name='update'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/delete/',
         views.assistant_delete,
         name='delete'),

    # Interaction Views
    path('company/<int:company_id>/assistants/<int:assistant_id>/interact/',
         views.assistant_interact,
         name='interact'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/rate/',
         views.rate_interaction,
         name='rate_interaction'),

    # Training Views
    path('company/<int:company_id>/assistants/<int:assistant_id>/train/',
         views.assistant_train,
         name='train'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/training-status/',
         views.training_status,
         name='training_status'),

    # API Endpoints
    path('api/company/<int:company_id>/assistants/<int:assistant_id>/chat/',
         views.api_chat,
         name='api_chat'),
    path('api/company/<int:company_id>/assistants/<int:assistant_id>/stream/',
         views.api_stream,
         name='api_stream'),

    # Analytics Views
    path('company/<int:company_id>/assistants/<int:assistant_id>/analytics/',
         views.assistant_analytics,
         name='analytics'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/usage/',
         views.assistant_usage,
         name='usage'),

    # Viewer Management
    path('company/<int:company_id>/assistants/<int:assistant_id>/add-viewer/',
         views.add_viewer,
         name='add_viewer'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/remove-viewer/',
         views.remove_viewer,
         name='remove_viewer'),

    # Folder Management URLs
    path('company/<int:company_id>/folders/create/', views.assistant_folder_create, name='folder_create'),
    path('folder/<int:folder_id>/edit/', views.assistant_folder_edit, name='folder_edit'),
    path('folder/<int:folder_id>/delete/', views.assistant_folder_delete, name='folder_delete'),
    path('assistant/<int:assistant_id>/assign_folder/', views.assign_assistant_folder, name='assign_folder'), # Added assign folder URL

    # Utility Views
    path('company/<int:company_id>/assistants/suggest-prompt/',
         views.suggest_prompt,
         name='suggest_prompt'),
    path('company/<int:company_id>/assistants/estimate-cost/',
     views.estimate_cost,
     name='estimate_cost'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/generate-suggestions/',
         views.generate_suggested_questions_view,
         name='generate_suggestions'), # Added URL for suggestions
    path('assistant/<int:assistant_id>/generate-qr-code/',
         views.generate_assistant_qr_code,
         name='generate_qr_code'),

    # HTMX Partial Views
    path('company/<int:company_id>/assistants/partial/',
         views.assistant_list_partial,
         name='list_partial'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/responses/',
         views.chat_responses_partial,
         name='responses_partial'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/status/',
         views.assistant_status_partial,
         name='status_partial'),
    path('tinymce/upload/', views.tinymce_image_upload, name='tinymce-upload'),
    # Alias with underscore for backward compatibility
    path('tinymce/upload/', views.tinymce_image_upload, name='tinymce_upload'),

    # Test URL for logo upload
    path('test-logo-upload/<int:assistant_id>/', views.test_logo_upload, name='test_logo_upload'),

    # Media directory check and fix
    path('check-media-dirs/', test_views.check_media_dirs, name='check_media_dirs'),

    # Community Assistant Views
    path('company/<int:company_id>/assistants/<int:assistant_id>/community/',
         views.community_dashboard,
         name='community_dashboard'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/community-chat/',
         views.community_chat,
         name='community_chat'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/add-context/',
         community_views.add_context,
         name='add_context'),
    # Alias for backward compatibility
    path('company/<int:company_id>/assistants/<int:assistant_id>/create-context/',
         community_views.add_context,
         name='create_context'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/edit-context/<int:context_id>/',
         community_views.edit_context,
         name='edit_context'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/contexts/',
         community_views.list_contexts,
         name='contexts'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/flag-question/',
         community_views.flag_question,
         name='flag_question'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/flagged-questions/',
         community_views.list_flagged_questions,
         name='flagged_questions'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/flagged-question/<int:flagged_id>/',
         community_views.flagged_question_detail,
         name='flagged_question_detail'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/flagged-question/<int:flagged_id>/add-context/',
         community_views.add_context_to_flagged,
         name='add_context_to_flagged'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/resolve-flagged/<int:flagged_id>/',
         community_views.resolve_flagged_question,
         name='resolve_flagged'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/flag-from-chat/',
         community_views.flag_from_chat,
         name='flag_from_chat'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/contexts/recent/',
         community_views.recent_contexts,
         name='recent_contexts'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/flagged/recent/',
         community_views.recent_flagged_questions,
         name='recent_flagged_questions'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/contexts/<int:context_id>/delete/',
         community_views.delete_context,
         name='delete_context'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/context/<int:context_id>/',
         views.get_context_content,
         name='get_context_content'),
    # URL for showing content by section ID
    path('company/<int:company_id>/assistants/<int:assistant_id>/section/<str:section_id>/',
         views.get_content_by_section_id,
         name='show_content_by_id'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/contexts/<int:context_id>/upvote/',
         community_views.upvote_context,
         name='upvote_context'),
    path('interaction/<int:interaction_id>/upvote/',
         views.upvote_answer,
         name='upvote_answer'),

    # Community Assistants List
    path('community/',
         views.community_assistants_list,
         name='community_assistants_list'),

    # Moderation Dashboard URLs
    path('company/<int:company_id>/assistants/<int:assistant_id>/moderation/',
         moderation_views.moderation_dashboard,
         name='moderation_dashboard'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/moderation/users/',
         moderation_views.user_management,
         name='moderation_users'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/moderation/content/',
         moderation_views.content_moderation,
         name='moderation_content'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/moderation/statistics/',
         moderation_views.statistics,
         name='moderation_statistics'),
    path('company/<int:company_id>/assistants/<int:assistant_id>/moderation/action/',
         moderation_views.take_action,
         name='moderation_action'),

    # Report content URL
    path('company/<int:company_id>/assistants/<int:assistant_id>/report-content/',
         community_views.report_content,
         name='report_content'),

    # Add comment URL
    path('company/<int:company_id>/assistants/<int:assistant_id>/add-comment/',
         community_views.add_comment,
         name='add_comment'),
]
