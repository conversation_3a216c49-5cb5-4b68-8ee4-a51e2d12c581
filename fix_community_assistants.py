"""
<PERSON><PERSON><PERSON> to fix the issue with companies being filtered out due to community assistants.
This script will:
1. Check which companies have community assistants
2. Fix the issue by updating the assistant_type field
"""
import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from accounts.models import Company
from assistants.models import Assistant
from directory.models import CompanyListing
from django.db.models import Q

def check_community_assistants():
    """Check which companies have community assistants."""
    print("\n=== Checking Community Assistants ===\n")
    
    # Get all companies
    companies = Company.objects.all()
    print(f"Total companies: {companies.count()}")
    
    # Get active companies
    active_companies = Company.objects.filter(is_active=True)
    print(f"Active companies: {active_companies.count()}")
    
    # Get companies with community assistants
    companies_with_community_assistants = Company.objects.filter(
        assistants__assistant_type=Assistant.TYPE_COMMUNITY
    ).distinct()
    print(f"Companies with community assistants: {companies_with_community_assistants.count()}")
    
    # Print details of companies with community assistants
    for company in companies_with_community_assistants:
        community_assistants = company.assistants.filter(assistant_type=Assistant.TYPE_COMMUNITY)
        print(f"\nCompany: {company.name} (ID: {company.id})")
        print(f"  Active: {company.is_active}")
        print(f"  Entity Type: {company.entity_type}")
        print(f"  Community Assistants: {community_assistants.count()}")
        for assistant in community_assistants:
            print(f"    - {assistant.name} (ID: {assistant.id}, Type: {assistant.assistant_type})")
    
    # Check companies that should be visible in the directory
    visible_companies = CompanyListing.objects.filter(
        company__is_active=True,
        company__info__list_in_directory=True,
        is_listed=True
    ).exclude(
        company__assistants__assistant_type=Assistant.TYPE_COMMUNITY
    )
    print(f"\nCompanies visible in directory (after excluding community assistants): {visible_companies.count()}")
    for listing in visible_companies:
        print(f"  - {listing.company.name} (ID: {listing.company.id})")
    
    # Check companies that should be visible but aren't due to community assistants
    filtered_out_companies = CompanyListing.objects.filter(
        company__is_active=True,
        company__info__list_in_directory=True,
        is_listed=True,
        company__assistants__assistant_type=Assistant.TYPE_COMMUNITY
    ).distinct()
    print(f"\nCompanies filtered out due to community assistants: {filtered_out_companies.count()}")
    for listing in filtered_out_companies:
        print(f"  - {listing.company.name} (ID: {listing.company.id})")

def fix_community_assistants():
    """Fix the issue with companies being filtered out due to community assistants."""
    print("\n=== Fixing Community Assistants ===\n")
    
    # Get companies with community assistants that should be visible
    companies_to_fix = Company.objects.filter(
        is_active=True,
        info__list_in_directory=True,
        assistants__assistant_type=Assistant.TYPE_COMMUNITY
    ).exclude(
        entity_type='community'  # Only fix regular companies, not actual communities
    ).distinct()
    
    print(f"Companies to fix: {companies_to_fix.count()}")
    
    # Fix each company
    for company in companies_to_fix:
        print(f"\nFixing company: {company.name} (ID: {company.id})")
        
        # Get community assistants for this company
        community_assistants = company.assistants.filter(assistant_type=Assistant.TYPE_COMMUNITY)
        print(f"  Community assistants to fix: {community_assistants.count()}")
        
        # Update assistant_type to regular for each assistant
        for assistant in community_assistants:
            old_type = assistant.assistant_type
            assistant.assistant_type = Assistant.TYPE_REGULAR
            assistant.save()
            print(f"    - Updated {assistant.name} (ID: {assistant.id}) from {old_type} to {assistant.assistant_type}")
    
    # Check if the fix worked
    companies_still_with_community_assistants = Company.objects.filter(
        is_active=True,
        info__list_in_directory=True,
        assistants__assistant_type=Assistant.TYPE_COMMUNITY
    ).exclude(
        entity_type='community'
    ).distinct()
    
    print(f"\nCompanies still with community assistants: {companies_still_with_community_assistants.count()}")
    
    # Check companies that should now be visible in the directory
    visible_companies = CompanyListing.objects.filter(
        company__is_active=True,
        company__info__list_in_directory=True,
        is_listed=True
    ).exclude(
        company__assistants__assistant_type=Assistant.TYPE_COMMUNITY
    )
    print(f"\nCompanies now visible in directory: {visible_companies.count()}")

if __name__ == "__main__":
    # First check the current state
    check_community_assistants()
    
    # Ask for confirmation before fixing
    response = input("\nDo you want to fix the community assistants issue? (y/n): ")
    if response.lower() == 'y':
        fix_community_assistants()
    else:
        print("Fix cancelled.")
