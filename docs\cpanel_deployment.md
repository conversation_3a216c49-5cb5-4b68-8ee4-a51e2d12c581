# cPanel Deployment Guide

This guide explains how to deploy the Django application to a cPanel hosting environment.

## Prerequisites

1. A cPanel hosting account with Python support
2. SSH access to your cPanel account (recommended)
3. MySQL database access

## Step 1: Create a Python Application in cPanel

1. Log in to your cPanel account
2. Navigate to the "Setup Python App" section
3. Click "Create Application"
4. Fill in the following details:
   - Python version: 3.9+ (or the highest available)
   - Application root: Your domain or subdomain (e.g., `yourdomain.com` or `app.yourdomain.com`)
   - Application URL: Leave as default or set to your preference
   - Application startup file: `passenger_wsgi.py`
   - Application Entry point: `application`
5. Click "Create"

## Step 2: Set Up the Database

1. In cPanel, go to "MySQL Databases"
2. Create a new database
3. Create a new database user
4. Add the user to the database with all privileges
5. Note down the database name, username, and password

## Step 3: Upload the Application

### Option 1: Using File Manager

1. In cPanel, go to "File Manager"
2. Navigate to the Python application directory created in Step 1
3. Upload all project files to this directory

### Option 2: Using Git (Recommended)

1. In cPanel, go to "Git Version Control"
2. Create a new repository
3. Clone your project repository
4. Set up deployment hooks if needed

## Step 4: Configure the Application

1. Edit `company_assistant/production_settings.py` with your actual settings:
   - Update `ALLOWED_HOSTS` with your domain
   - Update database settings with your MySQL credentials
   - Update email settings with your mail server details

2. Create a virtual environment and install dependencies:
   ```bash
   cd ~/your_app_directory
   python -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

3. Collect static files:
   ```bash
   python manage.py collectstatic --settings=company_assistant.production_settings
   ```

4. Run migrations:
   ```bash
   python manage.py migrate --settings=company_assistant.production_settings
   ```

5. Create a superuser:
   ```bash
   python manage.py createsuperuser --settings=company_assistant.production_settings
   ```

## Step 5: Configure Passenger

1. Make sure the `passenger_wsgi.py` file is in the root directory
2. Ensure the `.htaccess` file is properly configured
3. Restart the Python application from cPanel

## Step 6: Test the Application

1. Visit your domain in a web browser
2. Check for any errors in the logs:
   - Error logs: `~/logs/error_log`
   - Application logs: `~/your_app_directory/django_error.log`

## Troubleshooting

### Application Shows 500 Error

1. Check the error logs
2. Verify database connection settings
3. Ensure all required packages are installed
4. Check file permissions (directories should be 755, files 644)

### Static Files Not Loading

1. Verify the `STATIC_ROOT` and `STATIC_URL` settings
2. Make sure you ran `collectstatic`
3. Check the `.htaccess` file for proper static file routing

### Database Connection Issues

1. Verify database credentials in `production_settings.py`
2. Check if the database user has proper permissions
3. Ensure the MySQL server is running

## Maintenance

### Updating the Application

1. Pull the latest changes from your repository
2. Activate the virtual environment:
   ```bash
   source ~/your_app_directory/venv/bin/activate
   ```
3. Install any new dependencies:
   ```bash
   pip install -r requirements.txt
   ```
4. Run migrations if needed:
   ```bash
   python manage.py migrate --settings=company_assistant.production_settings
   ```
5. Collect static files if needed:
   ```bash
   python manage.py collectstatic --settings=company_assistant.production_settings
   ```
6. Restart the Python application from cPanel

### Backup

Regularly backup your:
1. Database (using cPanel's backup tools)
2. Media files
3. Custom settings files
