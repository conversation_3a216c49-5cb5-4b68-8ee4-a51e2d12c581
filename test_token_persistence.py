#!/usr/bin/env python
"""
Test script for token persistence.
This script tests the token generation and verification to ensure tokens persist between server restarts.
"""

import os
import sys
import time
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
# Set CPANEL_ENV to True to use file-based cache
os.environ['CPANEL_ENV'] = 'True'
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from accounts.auth_utils import store_signin_approval, verify_signin_token
from accounts.models import Company

User = get_user_model()

def test_token_persistence():
    """Test token persistence between server restarts."""
    print("\n=== Testing Token Persistence ===")

    # Get or create a test user
    try:
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'is_active': True,
            }
        )

        if created:
            print(f"Created test user: {user.username} (ID: {user.id})")
        else:
            print(f"Using existing test user: {user.username} (ID: {user.id})")

        # Store sign-in approval
        token = store_signin_approval(user, 24)
        print(f"Sign-in approval token generated: {token}")

        # Verify token immediately
        print("\nVerifying token immediately...")
        verified_user = verify_signin_token(token)
        if verified_user:
            print(f"Token verified successfully for user: {verified_user.username}")
        else:
            print("Token verification failed")
            return False

        # Generate a new token for persistence testing
        token = store_signin_approval(user, 24)
        print(f"\nGenerated new token for persistence testing: {token}")

        # Simulate server restart by clearing Django's cache
        from django.core.cache import cache
        cache.clear()
        print("\nSimulated server restart by clearing Django's cache")

        # Verify token after "server restart"
        print("\nVerifying token after simulated server restart...")
        verified_user = verify_signin_token(token)
        if verified_user:
            print(f"Token verified successfully for user: {verified_user.username}")
            print("✅ Token persistence test PASSED")
            return True
        else:
            print("❌ Token verification failed after simulated server restart")
            print("This indicates that tokens would not persist between actual server restarts")
            return False

    except Exception as e:
        print(f"Error testing token persistence: {e}")
        return False

def test_token_expiration():
    """Test token expiration."""
    print("\n=== Testing Token Expiration ===")

    # Get or create a test user
    try:
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'is_active': True,
            }
        )

        # Store sign-in approval with short expiry
        token = store_signin_approval(user, expiry_hours=1/60)  # 1 minute
        print(f"Sign-in approval token generated with 1 minute expiry: {token}")

        # Verify token immediately
        print("\nVerifying token immediately...")
        verified_user = verify_signin_token(token)
        if verified_user:
            print(f"Token verified successfully for user: {verified_user.username}")
        else:
            print("Token verification failed")
            return False

        # Generate a new token for expiration testing
        token = store_signin_approval(user, expiry_hours=1/60)  # 1 minute
        print(f"\nGenerated new token for expiration testing: {token}")

        # Wait for token to expire
        print("\nWaiting for token to expire (70 seconds)...")
        time.sleep(70)  # Wait 70 seconds (longer than the 1 minute expiry)

        # Verify token after expiration
        print("\nVerifying token after expiration...")
        verified_user = verify_signin_token(token)
        if verified_user:
            print("❌ Token verification succeeded when it should have failed")
            print("This indicates that token expiration is not working correctly")
            return False
        else:
            print("✅ Token verification failed as expected (token expired)")
            print("Token expiration test PASSED")
            return True

    except Exception as e:
        print(f"Error testing token expiration: {e}")
        return False

def main():
    """Run all tests."""
    print("=== Token Persistence and Expiration Tests ===")
    print(f"Time: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Django Settings Module: {os.environ.get('DJANGO_SETTINGS_MODULE')}")

    # Test token persistence
    persistence_result = test_token_persistence()

    # Test token expiration
    expiration_result = test_token_expiration()

    # Print summary
    print("\n=== Test Summary ===")
    print(f"Token Persistence: {'PASSED' if persistence_result else 'FAILED'}")
    print(f"Token Expiration: {'PASSED' if expiration_result else 'FAILED'}")

    if persistence_result and expiration_result:
        print("\n✅ All tests PASSED")
        print("The token system should work correctly in production")
    else:
        print("\n❌ Some tests FAILED")
        print("Please review the test results and fix any issues")

if __name__ == "__main__":
    main()
