from django.core.management.base import BaseCommand
from accounts.models import Company
from directory.models import CompanyListing

class Command(BaseCommand):
    help = 'Sets some companies as featured for testing'

    def handle(self, *args, **options):
        # Get all companies
        companies = Company.objects.all()[:5]  # Get first 5 companies
        
        if not companies:
            self.stdout.write(self.style.WARNING('No companies found'))
            return
            
        # Set companies as featured
        for company in companies:
            company.is_featured = True
            company.save()
            
            # Also set the company listing as featured
            try:
                listing = CompanyListing.objects.get(company=company)
                listing.featured = True
                listing.save()
                self.stdout.write(self.style.SUCCESS(f'Set company {company.name} (ID: {company.id}) as featured'))
            except CompanyListing.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'No listing found for company {company.name} (ID: {company.id})'))
                
        self.stdout.write(self.style.SUCCESS(f'Set {len(companies)} companies as featured'))
