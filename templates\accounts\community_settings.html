{% extends 'base/layout.html' %}
{% load static %}
{% load widget_tweaks %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/community-settings-enhanced.css' %}">
<link rel="stylesheet" href="{% static 'css/category-dropdowns.css' %}">
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/qr-code-regeneration.js' %}"></script>
<script src="{% static 'js/category-dropdowns.js' %}"></script>
{% endblock %}

{% block title %}{{ company.name }} - Community Settings{% endblock %}

{% block content %}
<div class="container py-4">
    {% if not company.is_active %}
        {% include 'accounts/tags/simple_pending_approval.html' with entity_type='community' entity_name=company.name %}
    {% endif %}
    <!-- Header -->
    <div class="row align-items-center mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Community Settings</h1>
            <p class="text-muted mb-0">
                Manage your community settings
            </p>
        </div>
        <div class="col-auto">
            <a href="{% url 'accounts:dashboard' %}" class="btn btn-light">
                <i class="bi bi-arrow-left me-2"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Settings Form -->
    <div class="row g-4">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        <!-- Basic Information Card -->
                        <div class="card mb-4 border-0 shadow-sm rounded-3">
                            <div class="card-header py-3">
                                <h5 class="card-title mb-0"><i class="bi bi-info-circle me-2"></i>Basic Information</h5>
                            </div>
                            <div class="card-body p-4">
                                <div class="row g-4">

                                    <!-- Community Name -->
                                    <div class="col-12">
                                        <label for="{{ form.name.id_for_label }}" class="form-label fw-medium">
                                            <i class="bi bi-people-fill me-1 text-primary"></i> Community Name
                                        </label>
                                        {% render_field form.name class="form-control" %}
                                        {% if form.name.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.name.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <!-- Industry -->
                                    <div class="col-md-6">
                                        <label for="industry_input" class="form-label fw-medium">
                                            <i class="bi bi-building me-1 text-primary"></i> Industry
                                        </label>
                                        <div class="dropdown-container industry-dropdown-container">
                                            <input type="text" id="industry_input" name="industry" class="form-control"
                                                   placeholder="Select or type industry" value="{{ form.industry.value|default:'' }}">
                                            <input type="hidden" name="industry_value" id="industry_value" value="{{ form.industry.value|default:'' }}">
                                            <div class="dropdown-list"></div>
                                        </div>
                                        {% if form.industry.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.industry.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">
                                            Select your community's primary industry
                                        </div>
                                    </div>

                                    <!-- Categories -->
                                    <div class="col-md-6">
                                        <label for="categories_input" class="form-label fw-medium">
                                            <i class="bi bi-tags me-1 text-primary"></i> Categories
                                        </label>
                                        <div class="dropdown-container category-dropdown-container">
                                            <input type="text" id="categories_input" name="categories" class="form-control"
                                                   placeholder="Select or type categories" value="{{ form.categories.value|default:'' }}">
                                            <input type="hidden" name="categories_value" id="categories_value" value="{{ form.categories.value|default:'' }}">
                                            <div class="dropdown-list"></div>
                                        </div>
                                        {% if form.categories.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.categories.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">Select categories related to your community's industry</div>
                                    </div>

                                    <!-- Community Purpose -->
                                    <div class="col-12">
                                        <label for="{{ form.mission.id_for_label }}" class="form-label fw-medium">
                                            <i class="bi bi-bullseye me-1 text-primary"></i> Community Purpose
                                        </label>
                                        {% render_field form.mission class="form-control" rows="3" %}
                                        {% if form.mission.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.mission.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">
                                            Describe what your community is about and what kind of knowledge you're collecting.
                                        </div>
                                    </div>

                                    <!-- Website -->
                                    <div class="col-12">
                                        <label for="{{ form.website.id_for_label }}" class="form-label fw-medium">
                                            <i class="bi bi-globe me-1 text-primary"></i> Website
                                        </label>
                                        {% render_field form.website class="form-control" %}
                                        {% if form.website.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.website.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Community Branding Card -->
                        <div class="card mb-4 border-0 shadow-sm rounded-3">
                            <div class="card-header py-3">
                                <h5 class="card-title mb-0"><i class="bi bi-image me-2"></i>Community Branding</h5>
                            </div>
                            <div class="card-body p-4">
                                <div class="mb-3">
                                    <label for="{{ form.logo.id_for_label }}" class="form-label fw-medium">
                                        <i class="bi bi-image me-1 text-primary"></i> Community Logo
                                    </label>
                                    {% if company.info.logo %}
                                        <div class="mb-2">
                                            <img src="{{ company.info.logo.url }}" alt="Current Logo" class="img-thumbnail" style="max-width: 100px;">
                                        </div>
                                    {% endif %}
                                    {% render_field form.logo class="form-control" %}
                                    {% if form.logo.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.logo.errors|join:", " }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information Card -->
                        <div class="card mb-4 border-0 shadow-sm rounded-3">
                            <div class="card-header py-3">
                                <h5 class="card-title mb-0"><i class="bi bi-envelope me-2"></i>Contact Information</h5>
                            </div>
                            <div class="card-body p-4">
                                <div class="row g-3">
                                    <!-- Contact Email -->
                                    <div class="col-md-6">
                                        <label for="{{ form.contact_email.id_for_label }}" class="form-label fw-medium">
                                            <i class="bi bi-envelope-at me-1 text-primary"></i> Contact Email
                                        </label>
                                        {% render_field form.contact_email class="form-control" %}
                                        {% if form.contact_email.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.contact_email.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <!-- Contact Phone -->
                                    <div class="col-md-6">
                                        <label for="{{ form.contact_phone.id_for_label }}" class="form-label fw-medium">
                                            <i class="bi bi-telephone me-1 text-primary"></i> Contact Phone <span class="text-danger">*</span>
                                        </label>
                                        {% render_field form.contact_phone class="form-control" required="required" %}
                                        {% if form.contact_phone.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.contact_phone.errors|join:", " }}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">
                                            {{ form.contact_phone.help_text }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Directory Settings Card -->
                        <div class="card mb-4 border-0 shadow-sm rounded-3">
                            <div class="card-header py-3">
                                <h5 class="card-title mb-0"><i class="bi bi-globe me-2"></i>Directory Settings</h5>
                            </div>
                            <div class="card-body p-4">
                                <div class="form-check form-switch mb-3">
                                    {% render_field form.list_in_directory class="form-check-input" %}
                                    <label class="form-check-label fw-medium" for="{{ form.list_in_directory.id_for_label }}">
                                        List in Public Directory
                                    </label>
                                    <div class="form-text">
                                        Make your community visible in our public directory.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <button type="reset" class="btn btn-light btn-lg">
                                <i class="bi bi-arrow-counterclockwise me-2"></i> Reset
                            </button>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-save me-2"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Community QR Code -->
            <div class="card border-0 shadow-sm rounded-3 mb-4">
                <div class="card-header py-3">
                    <h5 class="card-title mb-0"><i class="bi bi-qr-code me-2"></i>Community QR Code</h5>
                </div>
                <div class="card-body p-4 text-center">
                    <div id="qr-code-container">
                        {% if company_qr_code_url %}
                            <img src="{{ company_qr_code_url }}" alt="Community QR Code" class="img-fluid mb-3" style="max-width: 200px;" id="community-qr-code-img">
                            <p class="text-muted small mb-3">
                                Share this QR code to let others join your community.
                            </p>
                            <div class="d-flex justify-content-center gap-2 mb-2">
                                <a href="{{ company_qr_code_url }}" download="community-qr-code.png" class="btn btn-outline-primary" id="qr-code-download-btn">
                                    <i class="bi bi-download me-2"></i> Download QR Code
                                </a>
                            </div>
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="regenerate-qr-code-btn" data-company-id="{{ company.id }}">
                                <i class="bi bi-arrow-clockwise me-1"></i> Regenerate QR Code
                            </button>
                        {% else %}
                            <div class="alert alert-info mb-3">
                                <p class="mb-0">QR code not available.</p>
                            </div>
                            <button type="button" class="btn btn-primary" id="regenerate-qr-code-btn" data-company-id="{{ company.id }}">
                                <i class="bi bi-qr-code me-1"></i> Generate QR Code
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Assistant Settings -->
            <div class="card border-0 shadow-sm rounded-3 mb-4">
                <div class="card-header py-3">
                    <h5 class="card-title mb-0"><i class="bi bi-robot me-2"></i>Community Assistant Settings</h5>
                </div>
                <div class="card-body p-4">
                    {% for assistant in community_assistants %}
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <div class="bg-success bg-opacity-10 p-3 rounded">
                                    <i class="bi bi-robot text-success"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-1">{{ assistant.name }}</h5>
                                <p class="text-muted mb-0">{{ assistant.description|truncatechars:100 }}</p>
                            </div>
                            <div class="ms-auto">
                                <a href="{% url 'assistants:update' company.id assistant.id %}" class="btn btn-outline-primary">
                                    <i class="bi bi-pencil me-2"></i> Edit
                                </a>
                            </div>
                        </div>
                    {% empty %}
                        <div class="alert alert-warning">
                            <p class="mb-0">No community assistant available. Please <a href="{{ site_config.contact_url|default:'/contact/' }}">contact support</a>.</p>
                        </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Danger Zone -->
            <div class="card border-0 shadow-sm rounded-3 mb-4">
                <div class="card-header text-danger py-3">
                    <h5 class="card-title mb-0"><i class="bi bi-exclamation-triangle-fill me-2"></i>Danger Zone</h5>
                </div>
                <div class="card-body p-4">
                    <div class="d-grid">
                        <button type="button"
                                class="btn btn-outline-danger"
                                data-bs-toggle="modal"
                                data-bs-target="#transferModal">
                            <i class="bi bi-arrow-left-right me-2"></i>
                            Transfer Ownership
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Ownership Modal -->
<div class="modal fade" id="transferModal" tabindex="-1" aria-labelledby="transferModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="transferModalLabel">Transfer Community Ownership</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="post" action="{% url 'accounts:transfer_ownership' company.id %}">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="new_owner" class="form-label fw-medium">
                            <i class="bi bi-person-check me-1 text-primary"></i> New Owner
                        </label>
                        <select name="new_owner" id="new_owner" class="form-select" required>
                            <option value="">Select a member</option>
                            {% for membership in company.memberships.all %}
                                {% if membership.user != request.user %}
                                    <option value="{{ membership.user.id }}">{{ membership.user.get_full_name|default:membership.user.username }}</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    <div class="alert alert-warning">
                        <p class="mb-0">
                            <strong>Warning:</strong> This action cannot be undone. You will lose ownership privileges.
                        </p>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i> Transfer Ownership
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
