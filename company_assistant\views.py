from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.db.models import Count, Q
from django.urls import reverse
# Import models
from accounts.models import Company, Membership
from assistants.models import Assistant

def home(request):
    """Homepage view"""
    # Show landing page for anonymous users (and logged-in users)
    # Count distinct users who are members of any company using the Membership model

    users_count = Membership.objects.aggregate(distinct_users=Count('user', distinct=True))['distinct_users']

    # Get featured companies with public assistants count
    featured_companies = Company.objects.filter(
        info__list_in_directory=True,
        is_featured=True,
        is_active=True
    ).annotate(
        public_assistants_count=Count(
            'assistants',
            filter=Q(assistants__is_public=True, assistants__is_active=True)
        )
    ).order_by('-created_at')[:6]

    context = {
        'companies_count': Company.objects.count(),
        'users_count': users_count, # Use the count from Membership
        'featured_companies': featured_companies
    }
    return render(request, 'home.html', context)

def handler404(request, exception):
    """Custom 404 error handler"""
    context = {
        'title': 'Page Not Found',
        'error_code': '404',
        'error_message': 'The page you requested could not be found.',
        'show_search': True  # Enable search functionality on 404 pages
    }
    response = render(request, 'errors/404.html', context)
    response.status_code = 404
    return response

def handler500(request):
    """Custom 500 error handler"""
    context = {
        'title': 'Server Error',
        'error_code': '500',
        'error_message': 'Something went wrong on our end.',
        'show_support': True  # Show support contact info
    }
    response = render(request, 'errors/500.html', context)
    response.status_code = 500
    return response

def handler403(request, exception):
    """Custom 403 error handler"""
    context = {
        'title': 'Access Denied',
        'error_code': '403',
        'error_message': str(exception) if str(exception) else 'You do not have permission to access this page.',
        'show_login': not request.user.is_authenticated  # Show login option for anonymous users
    }
    response = render(request, 'errors/403.html', context)
    response.status_code = 403
    return response

# Removed @login_required to allow public search
def search(request):
    """
    Global search functionality for companies and assistants.
    """
    from django.db.models import Q
    from django.core.paginator import Paginator
    from accounts.models import Company
    from assistants.models import Assistant # Import models here
    from directory.models import DirectorySettings # Import DirectorySettings

    query = request.GET.get('q', '').strip()
    search_type = request.GET.get('type', 'assistant') # Default to searching assistants
    page_number = request.GET.get('page', 1)

    results = []
    page_obj = None
    is_company_search = search_type == 'company'
    is_community_search = search_type == 'community'

    # Load directory settings to check if standard tier should be hidden
    settings_obj = DirectorySettings.load()

    if query:
        if is_company_search:
            # Search Companies: only those listed in directory
            company_qs = Company.objects.filter(
                Q(name__icontains=query) | Q(info__description__icontains=query),
                info__list_in_directory=True,
                is_active=True  # Ensure company is active
            ).select_related('info').distinct()

            # Apply standard tier filtering if enabled
            if settings_obj.hide_standard_tier_companies:
                # Get featured companies (these should always be shown)
                featured_companies = company_qs.filter(is_featured=True)

                # Get non-standard tier companies
                non_standard_companies = company_qs.exclude(tier=Company.TIER_STANDARD)

                # Combine featured and non-standard (avoiding duplicates)
                company_qs = (featured_companies | non_standard_companies).distinct()

            paginator = Paginator(company_qs, 10) # Show 10 companies per page
            page_obj = paginator.get_page(page_number)
            results = page_obj.object_list
        elif is_community_search:
            # Search Community Assistants: only public and active ones with community type
            assistant_qs = Assistant.objects.filter(
                Q(name__icontains=query) | Q(description__icontains=query),
                is_public=True,
                is_active=True,
                assistant_type=Assistant.TYPE_COMMUNITY
            ).select_related('company').distinct()

            # Apply standard tier filtering if enabled
            if settings_obj.hide_standard_tier_assistants:
                # Get featured assistants (these should always be shown)
                featured_assistants = assistant_qs.filter(is_featured=True)

                # Get non-standard tier assistants
                non_standard_assistants = assistant_qs.exclude(tier=Assistant.TIER_STANDARD)

                # Combine featured and non-standard (avoiding duplicates)
                assistant_qs = (featured_assistants | non_standard_assistants).distinct()

            paginator = Paginator(assistant_qs, 10) # Show 10 community assistants per page
            page_obj = paginator.get_page(page_number)
            results = page_obj.object_list
        else:
            # Search Regular Assistants: only public and active ones (excluding community type)
            assistant_qs = Assistant.objects.filter(
                Q(name__icontains=query) | Q(description__icontains=query),
                is_public=True,
                is_active=True,
                assistant_type=Assistant.TYPE_GENERAL
            ).select_related('company').distinct()

            # Apply standard tier filtering if enabled
            if settings_obj.hide_standard_tier_assistants:
                # Get featured assistants (these should always be shown)
                featured_assistants = assistant_qs.filter(is_featured=True)

                # Get non-standard tier assistants
                non_standard_assistants = assistant_qs.exclude(tier=Assistant.TIER_STANDARD)

                # Combine featured and non-standard (avoiding duplicates)
                assistant_qs = (featured_assistants | non_standard_assistants).distinct()

            paginator = Paginator(assistant_qs, 10) # Show 10 assistants per page
            page_obj = paginator.get_page(page_number)
            results = page_obj.object_list
    else:
        # Handle empty query - maybe show featured or recent items?
        # For now, just show an empty page object
        paginator = Paginator([], 10)
        page_obj = paginator.get_page(page_number)


    context = {
        'query': query,
        'search_type': search_type,
        'is_company_search': is_company_search,
        'is_community_search': is_community_search,
        'results': results,
        'page_obj': page_obj, # Pass the page object for pagination controls
    }

    # Check if it's an HTMX request for partial updates (optional)
    # if request.headers.get('HX-Request'):
    #     template_name = 'directory/partials/search_results.html' # Need to create this partial
    # else:
    template_name = 'search.html' # The main search template

    return render(request, template_name, context)

@login_required
def switch_theme(request):
    """Theme switcher - now always sets dark theme"""
    # Always set theme to dark
    request.session['theme'] = 'dark'

    # Redirect back to previous page
    return redirect(request.META.get('HTTP_REFERER', reverse('home')))

from django.views.decorators.csrf import csrf_exempt
from assistants.views import tinymce_image_upload

@csrf_exempt
def tinymce_image_upload_proxy(request):
    """
    Proxy view for TinyMCE image uploads.
    This ensures the upload endpoint is accessible at /assistants/tinymce/upload/
    without being redirected to /assistant/tinymce/upload/
    """
    return tinymce_image_upload(request)

def assistant_redirect(request):
    """
    Redirect view for assistant URLs.
    This ensures that URLs with multiple 'assistants' segments are properly redirected.
    """
    from django.shortcuts import redirect
    return redirect('assistants:community_assistants_list')


