# TinyMCE Integration Guide

This document explains how to use TinyMCE in your Django project. We've set up a global TinyMCE integration that makes it easy to add rich text editing to any textarea in your application.

## Quick Start

To add TinyMCE to any textarea, simply add the `tinymce-editor` class:

```html
<textarea class="form-control tinymce-editor" name="content" id="id_content"></textarea>
```

That's it! The textarea will automatically be converted to a TinyMCE editor.

## Usage Methods

There are several ways to use TinyMCE in your project:

### 1. Using the `tinymce-editor` Class

The simplest way is to add the `tinymce-editor` class to any textarea:

```html
<textarea class="form-control tinymce-editor" name="content" id="id_content"></textarea>
```

### 2. Using the Template Tag

We've created a template tag to make it even easier:

```html
{% load tinymce_tags %}
{% tinymce_textarea name="content" id="id_content" placeholder="Enter content..." %}
```

### 3. Using the Filter with Django Forms

For Django forms, you can use the `add_tinymce_class` filter:

```html
{% load tinymce_tags %}
{{ form.content|add_tinymce_class }}
```

### 4. Using HTMLField in Models

For model fields, you can use the `HTMLField` from `tinymce.models`:

```python
from tinymce.models import HTMLField

class MyModel(models.Model):
    content = HTMLField()
```

### 5. Dynamically Adding TinyMCE

You can also dynamically add TinyMCE to textareas using JavaScript:

```javascript
// Add the tinymce-editor class to the textarea
document.getElementById('my-textarea').classList.add('tinymce-editor');

// Then initialize TinyMCE on this element
if (typeof window.initializeDynamicTinyMCE === 'function') {
    window.initializeDynamicTinyMCE(document.getElementById('my-textarea'));
}
```

## Configuration

The global TinyMCE configuration is defined in `settings.py` as `TINYMCE_DEFAULT_CONFIG`. If you need to customize the configuration for a specific textarea, you can use the TinyMCE API directly:

```javascript
tinymce.init({
    selector: '#my-custom-editor',
    // Custom configuration here
    height: 500,
    plugins: ['code', 'link', 'image'],
    toolbar: 'undo redo | bold italic | link image',
});
```

## Fallback for When TinyMCE Fails to Load

We've included a fallback mechanism that will show a regular textarea if TinyMCE fails to load. This is handled automatically when using the template tag, but if you're using the class method, you might want to add a fallback:

```html
<textarea class="form-control tinymce-editor" name="content" id="id_content"></textarea>
<div id="fallback-content" style="display: none;">
    <div class="alert alert-warning">
        <strong>Note:</strong> The rich text editor failed to load. You can still enter content below:
    </div>
    <textarea name="content_fallback" class="form-control"></textarea>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if TinyMCE is loaded after a delay
        setTimeout(function() {
            if (typeof tinymce === 'undefined' || !tinymce.get('id_content')) {
                // Show fallback textarea
                document.getElementById('fallback-content').style.display = 'block';
            }
        }, 2000);
    });
</script>
```

## Example

Visit `/tinymce-example/` to see a working example of all these methods.

## Troubleshooting

If TinyMCE is not appearing:

1. Check the browser console for errors
2. Make sure the `tinymce-editor` class is added to your textarea
3. Verify that the TinyMCE scripts are being loaded (check the Network tab in your browser's developer tools)
4. Try using the template tag instead of the class method
5. Make sure your form is not being dynamically added to the page after TinyMCE initialization

If you're still having issues, try manually initializing TinyMCE:

```javascript
if (typeof tinymce !== 'undefined') {
    tinymce.init({
        selector: '#your-textarea-id',
        // Other options...
    });
}
```
