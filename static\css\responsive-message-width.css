/**
 * Responsive Message Width CSS
 * Makes user message bubbles adjust width based on content length
 */

/* Base message styling */
.message {
  display: flex;
  width: 100%;
  margin: 0.75rem 0;
  padding: 0;
  transition: all 0.2s ease;
}

/* User message alignment */
.user-message {
  justify-content: flex-end !important;
}

/* Assistant message alignment */
.assistant-message {
  justify-content: flex-start !important;
}

/* Base message content styling */
.message-content {
  border-radius: 1.25rem;
  padding: 0.8rem 1.2rem !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  hyphens: auto !important;
  line-height: 1.5 !important;
  font-size: 1rem !important;
  display: inline-block !important; /* Changed to inline-block for width adjustment */
}

/* User message content - responsive width */
.user-message .message-content {
  background: #3b7dd8 !important; /* Solid blue background */
  color: #ffffff !important; /* White text color */
  text-align: left !important;
  border-radius: 1.25rem !important;
  border-bottom-right-radius: 0.3rem !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
  font-weight: 450 !important;
  border: none !important;
  transform: translateZ(0) !important;
  transition: all 0.2s ease !important;
  padding: 1rem 1.25rem !important;
  line-height: 1.6 !important;
  font-size: 0.95rem !important;

  /* Responsive width settings */
  width: auto !important; /* Allow width to be determined by content */
  min-width: auto !important; /* Remove minimum width to allow very short messages */
  max-width: 75% !important; /* Maximum width on desktop */
  box-sizing: border-box !important;
  margin: 0 !important;

  /* Ensure text doesn't wrap for very short messages */
  white-space: nowrap !important;
}

/* For messages with more content, allow wrapping */
.user-message .message-content.multi-word {
  white-space: normal !important;
}

/* Assistant message content - fixed width */
.assistant-message .message-content {
  background: #ffffff !important; /* Solid white background */
  color: #333333 !important; /* Dark text color */
  text-align: left !important;
  border-radius: 1.25rem !important;
  border-bottom-left-radius: 0.3rem !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
  font-weight: 450 !important;
  border: none !important;
  transform: translateZ(0) !important;
  transition: all 0.2s ease !important;
  padding: 1rem 1.25rem !important;
  line-height: 1.6 !important;
  font-size: 0.95rem !important;

  /* Fixed width settings */
  width: auto !important;
  min-width: 60px !important;
  max-width: 85% !important; /* Slightly wider than user messages */
  box-sizing: border-box !important;
  margin: 0 !important;
}

/* Ensure content inside bubbles is properly contained */
.message-content * {
  max-width: 100% !important;
  overflow-wrap: break-word !important;
  word-wrap: break-word !important;
  box-sizing: border-box !important;
}

/* Hover effects */
.user-message:hover .message-content {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  transform: translateY(-2px) !important;
  background: #3069b9 !important; /* Slightly darker blue on hover */
}

.assistant-message:hover .message-content {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
  transform: translateY(-2px) !important;
  background: #ffffff !important; /* Keep solid white on hover */
}

/* Responsive adjustments for tablet devices */
@media (max-width: 992px) {
  .user-message .message-content {
    max-width: 80% !important; /* Slightly wider on tablets */
  }

  .assistant-message .message-content {
    max-width: 90% !important;
  }
}

/* Responsive adjustments for mobile devices */
@media (max-width: 768px) {
  /* User messages should be responsive with max width on mobile */
  .user-message .message-content {
    width: auto !important;
    min-width: auto !important; /* Allow very short messages */
    max-width: 80% !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.95rem !important;
  }

  /* Very short messages (1-3 characters) */
  .user-message .message-content.very-short {
    max-width: fit-content !important;
    width: fit-content !important;
    padding: 0.4rem 0.7rem !important;
    display: inline-block !important;
    white-space: nowrap !important;
    box-sizing: content-box !important;
    min-width: auto !important;
  }

  /* Short messages (4-10 characters) */
  .user-message .message-content.short {
    max-width: 40% !important;
  }

  /* Assistant messages should be 80% width on mobile */
  .assistant-message .message-content {
    width: auto !important;
    min-width: auto !important;
    max-width: 80% !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.95rem !important;
  }

  /* Adjust spacing for mobile */
  .message {
    margin: 0.5rem 0 !important;
  }
}

/* Small mobile devices */
@media (max-width: 576px) {
  .user-message .message-content {
    width: auto !important;
    min-width: auto !important; /* Allow very short messages */
    max-width: 80% !important;
    padding: 0.7rem 0.9rem !important;
    font-size: 0.9rem !important;
  }

  /* Very short messages (1-3 characters) */
  .user-message .message-content.very-short {
    max-width: fit-content !important;
    width: fit-content !important;
    padding: 0.4rem 0.7rem !important;
    display: inline-block !important;
    white-space: nowrap !important;
    box-sizing: content-box !important;
    min-width: auto !important;
  }

  /* Short messages (4-10 characters) */
  .user-message .message-content.short {
    max-width: 35% !important;
  }

  .assistant-message .message-content {
    width: auto !important;
    min-width: auto !important;
    max-width: 80% !important;
    padding: 0.7rem 0.9rem !important;
    font-size: 0.9rem !important;
  }
}

/* Special styling for very short messages */
.user-message .message-content.very-short {
  padding: 0.4rem 0.7rem !important;
  border-radius: 1rem !important;
  text-align: center !important;
  max-width: fit-content !important; /* Use fit-content to match text width */
  min-width: auto !important;
  width: fit-content !important; /* Use fit-content instead of auto */
  display: inline-block !important;
  white-space: nowrap !important;
  box-sizing: content-box !important; /* Use content-box to ensure padding doesn't affect width */
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Special styling for single character messages */
.user-message .message-content.single-char {
  padding: 0.3rem 0.6rem !important;
  border-radius: 1rem !important;
  text-align: center !important;
  max-width: fit-content !important;
  min-width: auto !important;
  width: fit-content !important;
  display: inline-block !important;
  white-space: nowrap !important;
  box-sizing: content-box !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Special styling for short messages */
.user-message .message-content.short {
  padding: 0.7rem 1rem !important;
}

/* Fix for empty messages to ensure minimum bubble size */
.message-content:empty::before {
  content: "\00a0"; /* Add non-breaking space to ensure minimum height */
}

/* Ensure short messages have proper height */
.message-content:only-child {
  min-height: 20px !important;
}
