# Generated by Django 5.2.1 on 2025-05-20 18:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("directory", "0002_directorysettings_hide_community_assistants"),
    ]

    operations = [
        migrations.AddField(
            model_name="directorysettings",
            name="hide_standard_tier_community_assistants",
            field=models.BooleanField(
                default=False,
                help_text="If checked, community assistants with the 'Standard' tier will not be shown in the public directory.",
                verbose_name="Hide Standard Tier Community Assistants",
            ),
        ),
    ]
