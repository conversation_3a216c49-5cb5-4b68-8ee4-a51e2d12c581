{% if page_obj.paginator.count > 0 %}
<div class="d-flex flex-column align-items-center">
  <!-- Page X of Y Information -->
  <div class="d-flex justify-content-between align-items-center w-100 mb-2">
    <div>
      <span class="text-muted">Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }} ({{ page_obj.paginator.count }} total items)</span>
    </div>

    <!-- Items Per Page Selector -->
    <div class="d-flex align-items-center">
      <label for="items-per-page" class="me-2">Items per page:</label>
      <select id="items-per-page" class="form-select form-select-sm" style="width: auto;">
        <option value="10" {% if page_obj.paginator.per_page == 10 %}selected{% endif %}>10</option>
        <option value="25" {% if page_obj.paginator.per_page == 25 %}selected{% endif %}>25</option>
        <option value="50" {% if page_obj.paginator.per_page == 50 %}selected{% endif %}>50</option>
        <option value="100" {% if page_obj.paginator.per_page == 100 %}selected{% endif %}>100</option>
      </select>
    </div>
  </div>

  <!-- Pagination Controls -->
  <nav aria-label="Page navigation">
    <ul class="pagination justify-content-center mt-2">
      {% if page_obj.has_previous %}
        <li class="page-item">
          <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
            <span aria-hidden="true">&laquo;</span>
          </a>
        </li>
      {% else %}
        <li class="page-item disabled">
          <span class="page-link" aria-hidden="true">&laquo;</span>
        </li>
      {% endif %}

      {% for i in page_obj.paginator.page_range %}
        {% if page_obj.number == i %}
          <li class="page-item active" aria-current="page"><span class="page-link">{{ i }}</span></li>
        {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
          {# Show nearby page numbers #}
          <li class="page-item"><a class="page-link" href="?page={{ i }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a></li>
        {% elif i == page_obj.number|add:'-3' or i == page_obj.number|add:'3' %}
           {# Add ellipsis for gaps #}
           <li class="page-item disabled"><span class="page-link">...</span></li>
        {% endif %}
      {% endfor %}

      {% if page_obj.has_next %}
        <li class="page-item">
          <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
            <span aria-hidden="true">&raquo;</span>
          </a>
        </li>
      {% else %}
        <li class="page-item disabled">
          <span class="page-link" aria-hidden="true">&raquo;</span>
        </li>
      {% endif %}
    </ul>
  </nav>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const itemsPerPageSelect = document.getElementById('items-per-page');
    if (itemsPerPageSelect) {
      itemsPerPageSelect.addEventListener('change', function() {
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.set('items_per_page', this.value);
        urlParams.delete('page'); // Reset to first page when changing items per page
        window.location.search = urlParams.toString();
      });
    }
  });
</script>
{% endif %}
