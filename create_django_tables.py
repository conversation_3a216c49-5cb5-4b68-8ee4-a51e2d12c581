import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

# SQL to create the django_content_type table
content_type_sql = """
CREATE TABLE IF NOT EXISTS "django_content_type" (
    "id" serial NOT NULL PRIMARY KEY,
    "app_label" varchar(100) NOT NULL,
    "model" varchar(100) NOT NULL,
    CONSTRAINT "django_content_type_app_label_model_76bd3d3b_uniq" UNIQUE ("app_label", "model")
);
"""

# SQL to create the auth_permission table
auth_permission_sql = """
CREATE TABLE IF NOT EXISTS "auth_permission" (
    "id" serial NOT NULL PRIMARY KEY,
    "name" varchar(255) NOT NULL,
    "content_type_id" integer NOT NULL REFERENCES "django_content_type" ("id") DEFERRABLE INITIALLY DEFERRED,
    "codename" varchar(100) NOT NULL,
    CONSTRAINT "auth_permission_content_type_id_codename_01ab375a_uniq" UNIQUE ("content_type_id", "codename")
);
"""

# SQL to create the auth_group table
auth_group_sql = """
CREATE TABLE IF NOT EXISTS "auth_group" (
    "id" serial NOT NULL PRIMARY KEY,
    "name" varchar(150) NOT NULL UNIQUE
);
"""

# SQL to create the auth_group_permissions table
auth_group_permissions_sql = """
CREATE TABLE IF NOT EXISTS "auth_group_permissions" (
    "id" serial NOT NULL PRIMARY KEY,
    "group_id" integer NOT NULL REFERENCES "auth_group" ("id") DEFERRABLE INITIALLY DEFERRED,
    "permission_id" integer NOT NULL REFERENCES "auth_permission" ("id") DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT "auth_group_permissions_group_id_permission_id_0cd325b0_uniq" UNIQUE ("group_id", "permission_id")
);
"""

# SQL to create the django_migrations table
django_migrations_sql = """
CREATE TABLE IF NOT EXISTS "django_migrations" (
    "id" serial NOT NULL PRIMARY KEY,
    "app" varchar(255) NOT NULL,
    "name" varchar(255) NOT NULL,
    "applied" timestamp with time zone NOT NULL
);
"""

# SQL to create the django_session table
django_session_sql = """
CREATE TABLE IF NOT EXISTS "django_session" (
    "session_key" varchar(40) NOT NULL PRIMARY KEY,
    "session_data" text NOT NULL,
    "expire_date" timestamp with time zone NOT NULL
);
CREATE INDEX IF NOT EXISTS "django_session_expire_date_a5c62663" ON "django_session" ("expire_date");
"""

# SQL to create the django_site table
django_site_sql = """
CREATE TABLE IF NOT EXISTS "django_site" (
    "id" serial NOT NULL PRIMARY KEY,
    "domain" varchar(100) NOT NULL,
    "name" varchar(50) NOT NULL
);
"""

# Execute the SQL statements
with connection.cursor() as cursor:
    print("Creating django_content_type table...")
    cursor.execute(content_type_sql)
    
    print("Creating auth_permission table...")
    cursor.execute(auth_permission_sql)
    
    print("Creating auth_group table...")
    cursor.execute(auth_group_sql)
    
    print("Creating auth_group_permissions table...")
    cursor.execute(auth_group_permissions_sql)
    
    print("Creating django_migrations table...")
    cursor.execute(django_migrations_sql)
    
    print("Creating django_session table...")
    cursor.execute(django_session_sql)
    
    print("Creating django_site table...")
    cursor.execute(django_site_sql)
    
    # Insert the default site
    print("Inserting default site...")
    cursor.execute("""
    INSERT INTO django_site (id, domain, name)
    VALUES (1, 'example.com', 'example.com')
    ON CONFLICT (id) DO NOTHING;
    """)

print("All Django tables created successfully!")
