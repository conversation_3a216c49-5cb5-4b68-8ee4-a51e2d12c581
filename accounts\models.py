import uuid
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group # Import Group
from django.db import models
from django.db.models import Avg, Q
from django.conf import settings
from django.utils import timezone
from django.utils.text import slugify
from django.urls import reverse

# Forward declaration for type hinting if needed later
# class AssistantFolder: pass
# Use string reference 'assistants.AssistantFolder' instead for simplicity

# Removed top-level User = get_user_model()
# Use settings.AUTH_USER_MODEL directly in ForeignKeys

from datetime import timedelta # Ensure timedelta is imported

class EntityType(models.TextChoices):
    COMPANY = 'company', 'Company'
    COMMUNITY = 'community', 'Community'


class Company(models.Model):
    name = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True)
    entity_type = models.CharField(
        max_length=20,
        choices=EntityType.choices,
        default=EntityType.COMPANY,
        help_text="Whether this is a company or a community"
    )
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='owned_companies'
    )
    # The 'members' field was removed here and replaced by the Membership model below.
    qr_code = models.ImageField(upload_to='company_qrcodes/', blank=True, null=True) # Will likely store QR for a RegistrationLink

    # Tier Configuration (mirrors Assistant model)
    TIER_GOLD = 'Gold'
    TIER_SILVER = 'Silver'
    TIER_BRONZE = 'Bronze'
    TIER_STANDARD = 'Standard'

    TIER_CHOICES = [
        (TIER_GOLD, 'Gold'),
        (TIER_SILVER, 'Silver'),
        (TIER_BRONZE, 'Bronze'),
        (TIER_STANDARD, 'Standard'),
    ]
    # Choices specifically for the request dropdown (excluding Standard)
    TIER_REQUEST_CHOICES = [
        (TIER_GOLD, 'Gold'),
        (TIER_SILVER, 'Silver'),
        (TIER_BRONZE, 'Bronze'),
    ]
    # Duration Choices (can reuse from Assistant or define here if needed)
    DURATION_MONTHLY = 'monthly'
    DURATION_QUARTERLY = 'quarterly'
    DURATION_ANNUALLY = 'annually'
    DURATION_CHOICES = [
        (DURATION_MONTHLY, 'Monthly'),
        (DURATION_QUARTERLY, 'Quarterly'),
        (DURATION_ANNUALLY, 'Annually'),
    ]

    tier = models.CharField(
        max_length=10,
        choices=TIER_CHOICES,
        default=TIER_STANDARD,
        db_index=True,
        help_text="Directory display tier for this company."
    )
    is_featured = models.BooleanField(
        default=False,
         db_index=True,
         help_text="Mark this company to appear in the featured section on the directory."
     )
    is_active = models.BooleanField(
        default=False,
        db_index=True,
        help_text="Whether the company or community is approved and active on the platform."
    )
    requested_tier = models.CharField(
        max_length=10,
        choices=TIER_CHOICES,
        blank=True,
        null=True,
        help_text="Tier requested by the company owner, pending superadmin approval."
    )
    tier_change_pending = models.BooleanField(
        default=False,
        db_index=True,
         help_text="Indicates if a tier change request is pending approval."
    )
    tier_expiry_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Date and time when the current tier expires (if applicable)."
    )
    requested_tier_duration = models.CharField(
        max_length=10,
        choices=DURATION_CHOICES,
        blank=True,
        null=True,
        help_text="Duration requested for the tier upgrade."
    )
    featured_request_pending = models.BooleanField(
        default=False,
        db_index=True,
        help_text="Indicates if a request to feature this company is pending approval."
    )
    featured_expiry_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Date and time when the featured status expires (if applicable)."
    )
    requested_featured_duration = models.CharField(
        max_length=10,
        choices=DURATION_CHOICES,
        blank=True,
        null=True,
        help_text="Duration requested for the featured status."
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    @property
    def average_assistant_rating(self):
        """Calculates the average rating of this company's public assistants."""
        # Import Assistant model locally to avoid circular dependency issues
        from assistants.models import Assistant

        # Filter for public assistants with a rating
        rated_assistants = self.assistants.filter(
            is_public=True,
            average_rating__isnull=False
        )

        # Calculate the average
        result = rated_assistants.aggregate(Avg('average_rating'))

        # Return the average or None if no rated assistants
        return result.get('average_rating__avg')

    class Meta:
        verbose_name_plural = "Companies"
        permissions = [
            ("change_company_settings", "Can change company settings"),
            ("manage_billing", "Can manage billing"),
            ("delete_company_object", "Can delete company"), # Renamed to avoid clash with default delete perm
            ("manage_directory_listing", "Can manage directory listing"),
            ("manage_members", "Can add/remove/change roles of members"),
            ("manage_invites_links", "Can manage invitations and registration links"),
            ("view_company_activity", "Can view company activity log"), # Added for activity log
            # Add Assistant Folder permissions related to the Company context
            ("add_assistantfolder", "Can add assistant folders to the company"),
            ("change_assistantfolder", "Can change assistant folders within the company"),
            ("delete_assistantfolder", "Can delete assistant folders within the company"),
            ("view_assistantfolder", "Can view assistant folders within the company"),
            ("manage_folder_access", "Can manage user access to specific folders"), # Added for clarity
            ("change_membership_role", "Can change member roles"), # Added for clarity
            ("manage_company_assistants", "Can manage assistants within the company"), # New permission
        ]

# Removed the old Role model definition

class Membership(models.Model):
    """Links a User to a Company. Permissions are handled by django-guardian."""
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='memberships') # Use settings.AUTH_USER_MODEL
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='memberships')
    # Removed role ForeignKey
    # Removed accessible_folders ManyToManyField
    date_joined = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'company') # User can only have one role per company
        ordering = ['company__name', 'user__username']
        # Removed permissions = [...] as they are less relevant now

    def __str__(self):
        # Updated __str__ to remove role reference
        return f"{self.user.username} in {self.company.name}"


class CompanyInformation(models.Model):
    company = models.OneToOneField(
        Company,
        on_delete=models.CASCADE,
        related_name='info'
    )
    mission = models.TextField(blank=True)
    description = models.TextField(blank=True)
    website = models.URLField(blank=True)
    contact_email = models.EmailField(blank=True)
    contact_phone = models.CharField(max_length=50, blank=False)  # Phone number is now required
    timezone = models.CharField(max_length=50, default='UTC')
    language = models.CharField(max_length=10, default='en')

    # Address Fields
    address_line1 = models.CharField(max_length=255, blank=True)
    address_line2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    country = models.CharField(max_length=100, blank=True) # Consider using django-countries later

    # Company Logo
    logo = models.ImageField(upload_to='company_logos/', blank=True, null=True)

    # Fields from the template that were missing in the model
    industry = models.CharField(max_length=100, blank=True, help_text="Industry from category.json")
    # categories field is stored in CompanyListing instead
    size = models.CharField(max_length=50, blank=True) # Consider choices later if needed
    founded = models.PositiveIntegerField(null=True, blank=True)
    linkedin = models.URLField(blank=True)
    twitter = models.URLField(blank=True)
    facebook = models.URLField(blank=True)
    custom_domain = models.CharField(max_length=255, blank=True) # Consider validation

    list_in_directory = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.company.name} Information"

class CompanyInvitation(models.Model):
    STATUS_PENDING = 'pending'
    STATUS_ACCEPTED = 'accepted'
    STATUS_DECLINED = 'declined'
    STATUS_EXPIRED = 'expired'

    STATUS_CHOICES = [
        (STATUS_PENDING, 'Pending'),
        (STATUS_ACCEPTED, 'Accepted'),
        (STATUS_DECLINED, 'Declined'),
        (STATUS_EXPIRED, 'Expired'),
    ]

    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name='invitations'
    )
    email = models.EmailField()
    token = models.CharField(max_length=100, unique=True)
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=STATUS_PENDING
    )
    # Removed role ForeignKey - Role assignment is now handled by permission assignment in views
    invited_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, # Use settings.AUTH_USER_MODEL
        on_delete=models.SET_NULL,
        null=True,
        related_name='sent_invitations'
    )
    invited_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    accepted_at = models.DateTimeField(null=True, blank=True)
    metadata = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"Invitation for {self.email} to join {self.company.name}"

    def save(self, *args, **kwargs):
        if not self.pk and not self.expires_at:
            # Set default expiration to 7 days from now
            self.expires_at = timezone.now() + timezone.timedelta(days=7)
        super().save(*args, **kwargs)

    @property
    def is_expired(self):
        return timezone.now() > self.expires_at

    def accept(self):
        self.status = self.STATUS_ACCEPTED
        self.accepted_at = timezone.now()
        self.save()

    def decline(self):
        self.status = self.STATUS_DECLINED
        self.save()

    class Meta:
        ordering = ['-invited_at']

class ActivityLog(models.Model):
    """Model for tracking company-related activities."""

    TYPE_USER_JOINED = 'user_joined'
    TYPE_INVITATION_SENT = 'invitation_sent'
    TYPE_INVITATION_ACCEPTED = 'invitation_accepted'
    TYPE_INVITATION_CANCELLED = 'invitation_cancelled' # New
    TYPE_INVITATION_RESENT = 'invitation_resent' # New
    TYPE_MEMBER_REMOVED = 'member_removed' # New
    TYPE_ROLE_CHANGED = 'role_changed' # Future use
    TYPE_FOLDER_ACCESS_CHANGED = 'folder_access_changed' # Future use
    TYPE_SETTINGS_CHANGED = 'settings_changed'
    TYPE_ASSISTANT_CREATED = 'assistant_created'
    TYPE_ASSISTANT_UPDATED = 'assistant_updated'
    TYPE_ASSISTANT_DELETED = 'assistant_deleted' # Future use
    TYPE_FOLDER_CREATED = 'folder_created' # Future use
    TYPE_FOLDER_UPDATED = 'folder_updated' # Future use
    TYPE_FOLDER_DELETED = 'folder_deleted' # Future use

    TYPE_CHOICES = [
        (TYPE_USER_JOINED, 'User Joined'),
        (TYPE_INVITATION_SENT, 'Invitation Sent'),
        (TYPE_INVITATION_ACCEPTED, 'Invitation Accepted'),
        (TYPE_INVITATION_CANCELLED, 'Invitation Cancelled'),
        (TYPE_INVITATION_RESENT, 'Invitation Resent'),
        (TYPE_MEMBER_REMOVED, 'Member Removed'),
        (TYPE_ROLE_CHANGED, 'Member Role Changed'),
        (TYPE_FOLDER_ACCESS_CHANGED, 'Folder Access Changed'),
        (TYPE_SETTINGS_CHANGED, 'Settings Changed'),
        (TYPE_ASSISTANT_CREATED, 'Assistant Created'),
        (TYPE_ASSISTANT_UPDATED, 'Assistant Updated'),
        (TYPE_ASSISTANT_DELETED, 'Assistant Deleted'),
        (TYPE_FOLDER_CREATED, 'Folder Created'),
        (TYPE_FOLDER_UPDATED, 'Folder Updated'),
        (TYPE_FOLDER_DELETED, 'Folder Deleted'),
    ]

    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name='activity_logs'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='activities'
    )
    activity_type = models.CharField(
        max_length=50,
        choices=TYPE_CHOICES
    )
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Activity Log'
        verbose_name_plural = 'Activity Logs'

    def __str__(self):
        return f"{self.get_activity_type_display()} - {self.company.name}"


class RegistrationLink(models.Model):
    """A shareable link for users to join a company."""
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name='registration_links'
    )
    # Removed role ForeignKey - Role assignment is now handled by permission assignment in views
    # We might add a field like 'intended_role_name' (CharField) if we need to store
    # the intended role for display or logic before permission assignment.
    # For now, assume the view logic determines permissions based on context.
    token = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, # Use settings.AUTH_USER_MODEL
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_registration_links'
    )
    # Add a field to store the intended group (role) for the link
    intended_group = models.ForeignKey(
        Group,
        on_delete=models.SET_NULL, # Keep link if group is deleted, but maybe warn admin
        null=True, # Allow links without a specific role initially? Or require it? Let's allow null for now.
        blank=True, # Make optional in forms for now, can be adjusted
        related_name='registration_links',
        help_text="The role (group) the user will be assigned upon joining via this link."
    )
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Optional date/time when this link expires."
    )
    max_uses = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Optional limit on the number of times this link can be used."
    )
    uses_count = models.PositiveIntegerField(default=0, editable=False)
    is_active = models.BooleanField(default=True)
    notes = models.CharField(
        max_length=255,
        blank=True,
        help_text="Optional notes for the creator about this link's purpose."
    )
    accessible_folders = models.ManyToManyField(
        'assistants.AssistantFolder',
        blank=True, # Allow links that grant access to no specific folders initially
        related_name='registration_links',
        help_text="Select folders the user joining via this link should have access to."
    )
    qr_code = models.ImageField(upload_to='registration_link_qrcodes/', blank=True, null=True, help_text="QR code for this registration link.") # Added QR code field
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Registration Link"
        verbose_name_plural = "Registration Links"

    def __str__(self):
        role_name = f" ({self.intended_group.name})" if self.intended_group else ""
        return f"Link for {self.company.name}{role_name} - {self.token}"

    @property
    def is_valid(self):
        """Checks if the link is currently valid."""
        if not self.is_active:
            return False
        if self.expires_at and timezone.now() > self.expires_at:
            return False
        if self.max_uses is not None and self.uses_count >= self.max_uses:
            return False
        return True

    def increment_use(self):
        """Increments the use count."""
        if self.max_uses is not None: # Only increment if there's a limit
            self.uses_count = models.F('uses_count') + 1
            self.save(update_fields=['uses_count'])

    def get_absolute_url(self):
        """Returns the full URL for joining via this link."""
        # Assuming you have a URL pattern named 'accounts:join_via_link'
        return reverse('accounts:join_via_link', kwargs={'token': self.token})


# --- User Profile Model ---

class UserProfile(models.Model):
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='profile'
    )
    avatar = models.ImageField(
        upload_to='user_avatars/',
        null=True,
        blank=True,
        help_text="User profile picture."
    )
    bio = models.TextField(
        blank=True,
        help_text="A short description about the user."
    )
    # Add other profile fields here later if needed (e.g., timezone, language, theme)

    def __str__(self):
        return f"{self.user.username}'s Profile"

# Optional: Add a signal to automatically create/update UserProfile when User is created/saved
# (This would go in accounts/signals.py or models.py depending on preference)
# from django.db.models.signals import post_save
# from django.dispatch import receiver
#
# @receiver(post_save, sender=settings.AUTH_USER_MODEL)
# def create_or_update_user_profile(sender, instance, created, **kwargs):
#     if created:
#         UserProfile.objects.create(user=instance)
#     # Ensure profile exists before trying to save it, especially if signal runs before profile creation somehow
#     try:
#         instance.profile.save() # Use related_name 'profile'
#     except UserProfile.DoesNotExist:
#         UserProfile.objects.create(user=instance) # Create if it missed creation somehow
