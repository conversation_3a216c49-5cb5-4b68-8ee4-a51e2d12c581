/**
 * Enhanced Company Settings CSS
 * Improved styling for company settings page with dark mode support
 */

/* Card styling */
.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Card headers with gradient */
.bg-gradient-primary-subtle {
  background: linear-gradient(135deg, var(--bs-primary-rgb, 13, 110, 253) 0%, rgba(var(--bs-primary-rgb, 13, 110, 253), 0.7) 100%);
  color: white;
}

[data-theme="dark"] .bg-gradient-primary-subtle {
  background: linear-gradient(135deg, #1a3a6c 0%, #2c5eaa 100%);
  color: white;
}

/* Form controls */
.form-control, .form-select {
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  height: auto;
  font-size: 1rem;
  line-height: 1.5;
  width: 100%;
}

.form-control:focus, .form-select:focus {
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb, 13, 110, 253), 0.25);
  border-color: #0d6efd;
}

/* Fix for squished inputs */
.input-group {
  flex-wrap: nowrap;
}

.input-group .form-control {
  flex: 1 1 auto;
  width: 1%;
}

/* Spacing between form elements */
.form-label {
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-text {
  margin-top: 0.25rem;
  font-size: 0.875rem;
}

/* Row spacing */
.row.g-4 {
  --bs-gutter-y: 1.5rem;
}

/* Dark mode specific styling */
[data-theme="dark"] .card {
  background-color: #1e1e1e;
  border-color: #333;
}

[data-theme="dark"] .card-header {
  background-color: #252525;
  border-bottom-color: #333;
}

[data-theme="dark"] .card-body {
  background-color: #1e1e1e;
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
  background-color: #252525;
  border-color: #444;
  color: #fff;
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
  background-color: #252525;
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

[data-theme="dark"] .form-check-input {
  background-color: #252525;
  border-color: #444;
}

[data-theme="dark"] .form-text {
  color: #aaa;
}

/* Input groups in dark mode */
[data-theme="dark"] .input-group-text {
  background-color: #252525;
  border-color: #444;
  color: #fff;
}

/* QR code container in dark mode */
[data-theme="dark"] .bg-light {
  background-color: #252525 !important;
}

/* Tier & Featured Status cards in dark mode */
[data-theme="dark"] .bg-light-subtle {
  background-color: #252525 !important;
}

[data-theme="dark"] .bg-opacity-50.bg-white {
  background-color: #2a2a2a !important;
}

/* Modal styling for dark mode */
[data-theme="dark"] .modal-content {
  background-color: #1e1e1e;
  border-color: #333;
}

[data-theme="dark"] .modal-header {
  border-bottom-color: #333;
}

[data-theme="dark"] .modal-footer {
  border-top-color: #333;
}

/* Alert styling for dark mode */
[data-theme="dark"] .alert-info {
  background-color: #0d3a58;
  border-color: #0c5460;
  color: #d1ecf1;
}

[data-theme="dark"] .alert-warning {
  background-color: #3a2e0a;
  border-color: #856404;
  color: #fff3cd;
}

[data-theme="dark"] .alert-danger {
  background-color: #3a0a0a;
  border-color: #721c24;
  color: #f8d7da;
}

/* Badge styling for dark mode */
[data-theme="dark"] .badge.bg-primary {
  background-color: #0d6efd !important;
}

[data-theme="dark"] .badge.bg-success {
  background-color: #198754 !important;
}

[data-theme="dark"] .badge.bg-secondary {
  background-color: #6c757d !important;
}

/* Button styling */
.btn {
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background: linear-gradient(135deg, var(--bs-primary-rgb, 13, 110, 253) 0%, rgba(var(--bs-primary-rgb, 13, 110, 253), 0.8) 100%);
  border: none;
}

[data-theme="dark"] .btn-primary {
  background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
}

.btn-outline-primary:hover {
  background: linear-gradient(135deg, var(--bs-primary-rgb, 13, 110, 253) 0%, rgba(var(--bs-primary-rgb, 13, 110, 253), 0.8) 100%);
}

[data-theme="dark"] .btn-outline-primary {
  color: #0d6efd;
  border-color: #0d6efd;
}

[data-theme="dark"] .btn-outline-primary:hover {
  background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
  color: white;
}

/* Rounded pill buttons */
.btn.rounded-pill {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

/* Form labels */
.form-label.fw-medium {
  font-weight: 500;
}

/* Icons in form labels */
.form-label i {
  opacity: 0.8;
}

/* Dropdown styling */
.dropdown-container {
  position: relative;
  width: 100%;
  margin-bottom: 0;
}

.dropdown-container input {
  width: 100%;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #ced4da;
  background-color: #fff;
  transition: all 0.3s ease;
}

.dropdown-container input:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  outline: none;
}

.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1050;
  display: none;
  max-height: 200px;
  overflow-y: auto;
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  margin-top: 0.25rem;
  padding: 0.5rem 0;
}

.dropdown-list.show {
  display: block;
}

.dropdown-item {
  padding: 0.5rem 1rem;
  cursor: pointer;
  color: #212529;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

/* Dark mode dropdown styling */
[data-theme="dark"] .dropdown-container input {
  background-color: #252525;
  border-color: #444;
  color: #fff;
}

[data-theme="dark"] .dropdown-container input:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

[data-theme="dark"] .dropdown-list {
  background-color: #252525;
  border-color: #444;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .dropdown-item {
  color: #fff;
}

[data-theme="dark"] .dropdown-item:hover,
[data-theme="dark"] .dropdown-item:focus {
  background-color: #333;
  color: #fff;
}

/* Arrow indicator for dropdowns */
.dropdown-container::after {
  content: '';
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #6c757d;
  pointer-events: none;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .card {
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 768px) {
  .btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1rem;
  }
}
