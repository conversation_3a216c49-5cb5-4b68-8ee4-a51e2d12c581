/**
 * Container 3D Glow Effect CSS
 * Adds a 3D edge glow effect to containers with py-5 class while preserving the original background
 */

/* Base container 3D glow styling for light mode - EDGE GLOW ONLY */
.container.py-5 {
    /* No background changes */
    border: 1px solid rgba(0, 102, 255, 0.3) !important;
    box-shadow: 0 0 15px rgba(0, 102, 255, 0.2) !important;
    border-radius: 1rem !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
}

/* Add subtle edge glow */
.container.py-5::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 1rem;
    box-shadow: inset 0 0 8px rgba(0, 102, 255, 0.2);
    pointer-events: none;
    z-index: 1;
}

/* Hero section container specific styling */
.hero-section .container.py-5,
.hero-container-glow {
    /* No background changes */
    border: 1px solid rgba(0, 102, 255, 0.4) !important;
    box-shadow: 0 0 20px rgba(0, 102, 255, 0.25) !important;
}

/* Features section container specific styling */
.features-section .container.py-5,
.feature-container-glow {
    /* No background changes */
    border: 1px solid rgba(0, 102, 255, 0.3) !important;
    box-shadow: 0 0 15px rgba(0, 102, 255, 0.2) !important;
}

/* CTA section container specific styling */
.cta-section .container.py-5,
.cta-container-glow {
    /* No background changes */
    border: 1px solid rgba(0, 102, 255, 0.35) !important;
    box-shadow: 0 0 18px rgba(0, 102, 255, 0.22) !important;
}

/* Standard container glow */
.standard-container-glow {
    /* No background changes */
    border: 1px solid rgba(0, 102, 255, 0.25) !important;
    box-shadow: 0 0 12px rgba(0, 102, 255, 0.18) !important;
}

/* Dark mode container styling */
[data-theme="dark"] .container.py-5 {
    /* No background changes */
    border: 1px solid rgba(74, 125, 255, 0.3) !important;
    box-shadow: 0 0 15px rgba(74, 125, 255, 0.2) !important;
}

/* Add subtle edge glow for dark mode */
[data-theme="dark"] .container.py-5::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 1rem;
    box-shadow: inset 0 0 8px rgba(74, 125, 255, 0.2);
    pointer-events: none;
    z-index: 1;
}

/* Dark mode hero section container specific styling */
[data-theme="dark"] .hero-section .container.py-5,
[data-theme="dark"] .hero-container-glow {
    /* No background changes */
    border: 1px solid rgba(74, 125, 255, 0.4) !important;
    box-shadow: 0 0 20px rgba(74, 125, 255, 0.25) !important;
}

/* Dark mode features section container specific styling */
[data-theme="dark"] .features-section .container.py-5,
[data-theme="dark"] .feature-container-glow {
    /* No background changes */
    border: 1px solid rgba(74, 125, 255, 0.3) !important;
    box-shadow: 0 0 15px rgba(74, 125, 255, 0.2) !important;
}

/* Dark mode CTA section container specific styling */
[data-theme="dark"] .cta-section .container.py-5,
[data-theme="dark"] .cta-container-glow {
    /* No background changes */
    border: 1px solid rgba(74, 125, 255, 0.35) !important;
    box-shadow: 0 0 18px rgba(74, 125, 255, 0.22) !important;
}

/* Dark mode standard container glow */
[data-theme="dark"] .standard-container-glow {
    /* No background changes */
    border: 1px solid rgba(74, 125, 255, 0.25) !important;
    box-shadow: 0 0 12px rgba(74, 125, 255, 0.18) !important;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .container.py-5 {
        border-radius: 0.75rem !important;
        box-shadow: 0 0 12px rgba(0, 102, 255, 0.15) !important;
    }

    [data-theme="dark"] .container.py-5 {
        box-shadow: 0 0 12px rgba(74, 125, 255, 0.15) !important;
    }
}
