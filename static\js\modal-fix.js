/**
 * Modal Fix
 * Ensures proper handling of modal backdrops and prevents overlay issues
 */

document.addEventListener('DOMContentLoaded', function() {
    // Fix for modal backdrop issues
    fixModalBackdrops();
    
    // Add global event listeners for modals
    addModalEventListeners();
});

/**
 * Fix modal backdrops by removing any lingering ones
 */
function fixModalBackdrops() {
    // Remove any lingering backdrops on page load
    const backdrops = document.querySelectorAll('.modal-backdrop');
    if (backdrops.length > 0) {
        backdrops.forEach(backdrop => {
            backdrop.remove();
        });
        
        // Also clean up body classes
        document.body.classList.remove('modal-open');
        document.body.style.removeProperty('overflow');
        document.body.style.removeProperty('padding-right');
    }
}

/**
 * Add global event listeners for all modals
 */
function addModalEventListeners() {
    // Get all modals on the page
    const modals = document.querySelectorAll('.modal');
    
    modals.forEach(modal => {
        // Add hidden.bs.modal event listener to each modal
        modal.addEventListener('hidden.bs.modal', function() {
            // Remove any lingering backdrop
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => {
                backdrop.remove();
            });
            
            // Ensure body classes are cleaned up
            document.body.classList.remove('modal-open');
            document.body.style.removeProperty('overflow');
            document.body.style.removeProperty('padding-right');
        });
        
        // Add shown.bs.modal event listener to ensure only one backdrop exists
        modal.addEventListener('shown.bs.modal', function() {
            // Ensure only one backdrop exists
            const backdrops = document.querySelectorAll('.modal-backdrop');
            if (backdrops.length > 1) {
                // Keep only the last backdrop
                for (let i = 0; i < backdrops.length - 1; i++) {
                    backdrops[i].remove();
                }
            }
        });
    });
    
    // Add click event listener to document to handle clicks outside modals
    document.addEventListener('click', function(event) {
        // Check if click was on a modal backdrop
        if (event.target.classList.contains('modal-backdrop')) {
            // Remove all backdrops
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => {
                backdrop.remove();
            });
            
            // Clean up body classes
            document.body.classList.remove('modal-open');
            document.body.style.removeProperty('overflow');
            document.body.style.removeProperty('padding-right');
        }
    });
}

// Add a MutationObserver to handle dynamically added modals
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            // Check if any of the added nodes are modals or contain modals
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === 1) { // Element node
                    // Check if the node is a modal
                    if (node.classList && node.classList.contains('modal')) {
                        addModalEventListeners();
                    }
                    
                    // Check if the node contains modals
                    const modals = node.querySelectorAll('.modal');
                    if (modals.length > 0) {
                        addModalEventListeners();
                    }
                }
            });
        }
    });
});

// Start observing the document body for added modals
observer.observe(document.body, { childList: true, subtree: true });

// Expose functions to window for external use
window.fixModalBackdrops = fixModalBackdrops;
