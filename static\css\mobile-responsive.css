/**
 * Mobile Responsive CSS
 * Comprehensive mobile responsiveness improvements for the entire site
 */

/* Base Mobile Adjustments */
@media (max-width: 991.98px) {
  /* Improve text readability on small screens */
  body {
    font-size: 16px !important;
    line-height: 1.5 !important;
  }

  /* Ensure all containers have proper padding */
  .container, .container-fluid {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }

  /* Improve button usability on mobile */
  .btn {
    padding: 0.5rem 1rem !important;
    font-size: 1rem !important;
    min-height: 44px !important; /* Minimum touch target size */
  }

  /* Ensure form controls are easily tappable */
  .form-control, .form-select {
    min-height: 44px !important;
    font-size: 16px !important; /* Prevents iOS zoom on focus */
    padding: 0.5rem 0.75rem !important;
  }

  /* Improve modal usability on mobile */
  .modal-dialog {
    margin: 0.5rem !important;
    max-width: calc(100% - 1rem) !important;
  }

  .modal-body {
    padding: 1rem !important;
  }

  /* Ensure tables are responsive */
  .table-responsive {
    display: block !important;
    width: 100% !important;
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }
}

/* Small Mobile Devices (up to 576px) */
@media (max-width: 576px) {
  /* Adjust headings for better readability */
  h1, .h1 { font-size: 1.75rem !important; }
  h2, .h2 { font-size: 1.5rem !important; }
  h3, .h3 { font-size: 1.25rem !important; }
  h4, .h4 { font-size: 1.1rem !important; }
  h5, .h5 { font-size: 1rem !important; }

  /* Ensure proper spacing */
  .container, .container-fluid {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }

  /* Adjust container padding on mobile */
  .py-5 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }

  /* Adjust hero section container padding */
  .hero-section .container.py-5 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }

  /* Adjust card padding */
  .card-body {
    padding: 1rem !important;
  }

  /* Ensure buttons stack properly on very small screens */
  .btn-group {
    display: flex !important;
    flex-direction: column !important;
  }

  .btn-group > .btn {
    margin-bottom: 0.5rem !important;
    border-radius: 0.25rem !important;
  }

  /* Improve form layout */
  .form-group {
    margin-bottom: 1rem !important;
  }

  /* Ensure proper spacing between stacked elements */
  .mb-3 {
    margin-bottom: 0.75rem !important;
  }

  /* Adjust padding for list items */
  .list-group-item {
    padding: 0.75rem 1rem !important;
  }
}

/* Container Padding Mobile Optimization */
@media (max-width: 768px) {
  /* Adjust container padding on mobile */
  .py-5 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }

  /* Adjust hero section container padding */
  .hero-section .container.py-5 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }
}

/* Directory Cards Mobile Optimization */
@media (max-width: 768px) {
  /* Make cards full width on mobile */
  .directory-card, .list-group-item {
    width: 100% !important;
  }

  /* Adjust logo container size for mobile */
  .logo-container {
    height: 100px !important;
    width: 100px !important;
    min-height: 100px !important;
    min-width: 100px !important;
    margin: 0 auto 1rem auto !important;
  }

  /* Center logo on mobile */
  .directory-item-link-wrapper .col-md-2 {
    display: flex !important;
    justify-content: center !important;
    width: 100% !important;
    margin-bottom: 1rem !important;
  }

  /* Make description column full width */
  .directory-item-link-wrapper .col-md-7,
  .directory-item-link-wrapper .col-md-3 {
    width: 100% !important;
    text-align: center !important;
    padding: 0 1rem !important;
  }

  /* Adjust action buttons */
  .directory-card .col-md-2.text-end {
    width: 100% !important;
    text-align: center !important;
    margin-top: 1rem !important;
  }

  /* Ensure proper row layout */
  .directory-card .row {
    flex-direction: column !important;
  }

  /* Adjust badge positioning */
  .tier-badge, .featured-badge {
    position: absolute !important;
    top: 0.5rem !important;
    font-size: 0.7rem !important;
    z-index: 10 !important;
  }

  .tier-badge {
    left: 0.5rem !important;
  }

  .featured-badge {
    right: 0.5rem !important;
  }
}

/* Chat Interface Mobile Optimization */
@media (max-width: 768px) {
  /* Optimize chat container */
  .chat-container {
    margin: 0 !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    border-left: none !important;
    border-right: none !important;
    max-width: 100% !important;
    width: 100% !important;
  }

  /* Adjust chat messages */
  .message-content {
    max-width: 100% !important;
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.95rem !important;
  }

  /* Optimize chat input area */
  #chat-form {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    padding: 0.5rem !important;
    background-color: #fff !important;
    border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
    z-index: 1000 !important;
    margin: 0 !important;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
  }

  /* Ensure chat box has enough bottom padding for fixed input */
  .chat-box {
    padding-bottom: 70px !important;
  }

  /* Optimize sidebar for mobile */
  #chat-sidebar {
    width: 85% !important;
    max-width: 300px !important;
  }
}

/* Directory Cards Tablet Optimization */
@media (min-width: 769px) and (max-width: 991.98px) {
  /* Make cards properly sized for tablets */
  .directory-card, .list-group-item {
    width: 100% !important;
  }

  /* Adjust logo container size for tablets */
  .logo-container {
    height: 90px !important;
    width: 90px !important;
    min-height: 90px !important;
    min-width: 90px !important;
  }

  /* Optimize row layout for tablets */
  .directory-card .row {
    flex-wrap: wrap !important;
  }

  /* Optimize logo column for tablets */
  .directory-item-link-wrapper .col-md-2 {
    width: 25% !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }

  /* Optimize name and company column for tablets */
  .directory-item-link-wrapper .col-md-3 {
    width: 75% !important;
  }

  /* Optimize description column for tablets */
  .directory-item-link-wrapper .col-md-7 {
    width: 100% !important;
    clear: both !important;
  }

  /* Optimize action buttons for tablets */
  .directory-card .col-md-2.text-end {
    width: 100% !important;
    text-align: right !important;
    margin-top: 0.75rem !important;
  }
}

/* Fix for dark mode on mobile */
@media (max-width: 768px) {
  [data-theme="dark"] #chat-form {
    background-color: #1a1a1a !important;
    border-top: 1px solid #333333 !important;
  }

  [data-theme="dark"] .filter-form,
  [data-theme="dark"] form.filter-form {
    background-color: #1a1a1a !important;
    border: 1px solid #333333 !important;
  }
}

/* Fix for dark mode on tablets */
@media (min-width: 769px) and (max-width: 991.98px) {
  [data-theme="dark"] .filter-form,
  [data-theme="dark"] form.filter-form {
    background-color: #1a1a1a !important;
    border: 1px solid #333333 !important;
  }
}
