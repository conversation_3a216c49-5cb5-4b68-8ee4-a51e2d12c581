import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

# SQL to create the django_site table
sql = """
CREATE TABLE IF NOT EXISTS "django_site" (
    "id" serial NOT NULL PRIMARY KEY,
    "domain" varchar(100) NOT NULL,
    "name" varchar(50) NOT NULL
);
"""

# Execute the SQL
with connection.cursor() as cursor:
    cursor.execute(sql)

# Insert the default site
sql_insert = """
INSERT INTO django_site (id, domain, name)
VALUES (1, 'example.com', 'example.com')
ON CONFLICT (id) DO NOTHING;
"""

with connection.cursor() as cursor:
    cursor.execute(sql_insert)

print("Site table created successfully!")
