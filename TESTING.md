# Testing Guide for Company Assistant

This document provides instructions for running tests on the Company Assistant application.

## Test Scripts

The following test scripts are available:

### Basic Tests
1. **Basic Tests** (`test_basic.py`): Tests basic model functionality.
2. **Frontend Tests** (`test_frontend.py`): Tests frontend pages and functionality.
3. **Backend Tests** (`test_backend.py`): Tests backend functionality like user creation, company creation, etc.

### User and Company Management Tests
4. **User Management Tests** (`test_user_management.py`): Tests user registration, login/logout, password changes, and profile updates.
5. **Company Management Tests** (`test_company_management.py`): Tests company creation, settings, team management, and company switching.

### Assistant Tests
6. **Assistant Tests** (`test_assistant.py`): Tests assistant creation, settings, navigation items, and chat functionality.

### Directory Tests
7. **Directory Tests** (`test_directory.py`): Tests directory listings, filtering, rating system, favorites, and pagination.

### Rich Text Editor Tests
8. **Rich Text Editor Tests** (`test_rich_text_editor.py`): Tests TinyMCE initialization, content creation, image uploads, and rich text display.

### Dark Mode Tests
9. **Dark Mode Tests** (`test_dark_mode.py`): Tests dark mode default settings, CSS classes, consistency across pages, and styling of UI elements.

### Comprehensive Tests
10. **Comprehensive Tests** (`run_all_tests.py`): Runs all the above tests in sequence.

## Running Tests

### Running All Tests

To run all tests at once, use the comprehensive test script:

```bash
python run_all_tests.py
```

This will run all test modules and provide a summary of the results.

### Running Individual Test Modules

You can also run individual test modules:

```bash
python test_basic.py
python test_frontend.py
python test_backend.py
```

### Running Django Tests

To run the Django test suite:

```bash
python manage.py test
```

Note: There might be issues with the Django test suite due to migration conflicts. If you encounter errors, try running individual test modules instead.

## Test Coverage

To run tests with coverage:

```bash
coverage run manage.py test
coverage report
```

## Troubleshooting

### Migration Issues

If you encounter migration issues when running Django tests, you might need to fix the migration files. Common issues include:

1. Duplicate column names in migrations
2. Conflicting migrations

### Database Issues

If tests fail due to database issues, you can try:

1. Deleting the test database
2. Running migrations with the `--fake` flag
3. Using a fresh database

## Adding New Tests

When adding new tests:

1. Follow the existing test structure
2. Make sure test functions return `True` on success
3. Add the new test module to `run_all_tests.py` if needed

## Test Environment

Tests are designed to run in a development environment with:

- SQLite database
- Debug mode enabled
- No SSL/HTTPS requirements

For testing in a production-like environment, use:

```bash
python manage.py test --settings=company_assistant.local_production_settings
```

## Frontend Testing

Frontend tests use Django's test client to check:

1. Page loading
2. CSS and styling
3. Basic functionality

For more comprehensive frontend testing, consider adding JavaScript tests using a framework like Jest.
