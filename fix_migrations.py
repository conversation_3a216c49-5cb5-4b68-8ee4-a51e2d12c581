"""
<PERSON><PERSON>t to fix migration conflicts in the assistants app.
This script will:
1. Mark the conflicting migration as applied without running it
2. Apply the merge migration
3. Continue with the remaining migrations
"""
import os
import django
from django.db import connections
from django.db.migrations.recorder import MigrationRecorder

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

def fix_migrations():
    """Fix the migration conflict in the assistants app."""
    print("Starting migration fix...")
    
    # Get the migration recorder
    connection = connections['default']
    recorder = MigrationRecorder(connection)
    
    # Check if the conflicting migration is already applied
    migration_exists = recorder.migration_qs.filter(
        app='assistants',
        name='0009_add_featured_settings'
    ).exists()
    
    if not migration_exists:
        print("Marking conflicting migration as applied...")
        # Mark the conflicting migration as applied
        recorder.record_applied('assistants', '0009_add_featured_settings')
        print("Migration 'assistants.0009_add_featured_settings' marked as applied.")
    else:
        print("Migration 'assistants.0009_add_featured_settings' is already applied.")
    
    # Check if the merge migration is already applied
    merge_exists = recorder.migration_qs.filter(
        app='assistants',
        name='0010_merge_20250402_0632'
    ).exists()
    
    if not merge_exists:
        print("Marking merge migration as applied...")
        # Mark the merge migration as applied
        recorder.record_applied('assistants', '0010_merge_20250402_0632')
        print("Migration 'assistants.0010_merge_20250402_0632' marked as applied.")
    else:
        print("Migration 'assistants.0010_merge_20250402_0632' is already applied.")
    
    print("Migration fix completed.")
    print("\nNext steps:")
    print("1. Run 'python manage.py migrate' to apply the remaining migrations.")
    print("2. If you encounter any issues, you may need to manually fix the database schema.")

if __name__ == "__main__":
    fix_migrations()
