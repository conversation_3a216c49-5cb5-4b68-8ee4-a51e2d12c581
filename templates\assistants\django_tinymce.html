{% load static %}
<!-- Django TinyMCE implementation -->
<script src="{% static 'tinymce/tinymce.min.js' %}"></script>
<script>
// Define a reusable function to initialize TinyMCE
function initDjangoTinyMCE() {
    console.log("Initializing Django TinyMCE");

    // Remove any existing instances
    if (typeof tinymce !== 'undefined' && tinymce.get('id_text_content')) {
        tinymce.get('id_text_content').remove();
    }

    // Initialize TinyMCE with the settings from TINYMCE_DEFAULT_CONFIG
    if (typeof tinymce !== 'undefined') {
        tinymce.init({
            selector: '#id_text_content',
            height: 360,
            width: 'auto',
            menubar: true,
            plugins: 'advlist autolink lists link image charmap print preview anchor ' +
                    'searchreplace visualblocks code fullscreen ' +
                    'insertdatetime media table paste code help wordcount imagetools',
            toolbar: 'undo redo | formatselect | ' +
                    'bold italic backcolor | alignleft aligncenter ' +
                    'alignright alignjustify | bullist numlist outdent indent | ' +
                    'removeformat | table | image | help',
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
            image_advtab: true,
            image_title: true,
            automatic_uploads: true,
            file_picker_types: 'image',
            images_upload_url: '/assistants/tinymce/upload/',
            file_picker_callback: function (callback, value, meta) {
                if (meta.filetype == 'image') {
                    var input = document.createElement('input');
                    input.setAttribute('type', 'file');
                    input.setAttribute('accept', 'image/*');
                    input.onchange = function () {
                        var file = this.files[0];
                        var reader = new FileReader();
                        reader.onload = function () {
                            callback(reader.result, {
                                alt: file.name
                            });
                        };
                        reader.readAsDataURL(file);
                    };
                    input.click();
                }
            },
            setup: function(editor) {
                editor.on('change', function() {
                    editor.save();
                });

                editor.on('init', function() {
                    console.log("TinyMCE initialized with Django settings");

                    // Force visibility of the editor container
                    var editorContainer = editor.getContainer();
                    if (editorContainer) {
                        editorContainer.style.visibility = 'visible';
                        editorContainer.style.display = 'block';
                    }
                });
            }
        });
    } else {
        console.error("TinyMCE not loaded");
        document.getElementById('tinymce-fallback').style.display = 'block';
    }
}

// Initialize TinyMCE when the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM loaded, initializing Django TinyMCE");
    setTimeout(initDjangoTinyMCE, 500);
});

// Also initialize when the window is fully loaded
window.addEventListener('load', function() {
    console.log("Window loaded, initializing Django TinyMCE");
    setTimeout(initDjangoTinyMCE, 500);
});
</script>
