import os
import django
import psycopg2

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.db import connection

def test_db_connection():
    """Test if the database connection is working."""
    print("Testing database connection...")
    
    try:
        # Try to execute a simple query
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            print(f"Database query result: {result}")
            
        print("Database connection test passed!")
        return True
    except Exception as e:
        print(f"Error connecting to database: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_postgres_connection():
    """Test direct PostgreSQL connection."""
    print("Testing direct PostgreSQL connection...")
    
    try:
        # Database connection parameters
        db_params = {
            'dbname': 'virtualo',
            'user': 'postgres',
            'password': 'M@kerere1',
            'host': 'localhost',
            'port': '5432'
        }
        
        # Connect to the database
        conn = psycopg2.connect(**db_params)
        cursor = conn.cursor()
        
        # Execute a simple query
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"PostgreSQL version: {version[0]}")
        
        # Close the connection
        cursor.close()
        conn.close()
        
        print("Direct PostgreSQL connection test passed!")
        return True
    except Exception as e:
        print(f"Error connecting directly to PostgreSQL: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("Running database connection tests...")
    
    results = []
    results.append(test_db_connection())
    results.append(test_postgres_connection())
    
    # Print summary
    print("\nTEST SUMMARY")
    print("=" * 80)
    
    all_passed = all(results)
    print(f"Overall result: {'PASSED' if all_passed else 'FAILED'}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    main()
