# Django Settings
DEBUG=True
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Settings (Optional - SQLite is default)
# DATABASE_URL=postgres://user:password@localhost:5432/dbname

# OpenAI Settings
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1  # Default OpenAI API URL
OPENAI_MODEL=gpt-4  # or gpt-3.5-turbo

# Groq Settings (for Llama models)
GROQ_API_KEY=your-groq-api-key
GROQ_BASE_URL=https://api.groq.com/openai/v1

# Anthropic Settings
ANTHROPIC_API_KEY=your-anthropic-api-key

# Gemini Settings
GEMINI_API_KEY=your-gemini-api-key

# Email Settings
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-specific-password
DEFAULT_FROM_EMAIL=<EMAIL>

# AWS Settings (Optional - for production file storage)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_STORAGE_BUCKET_NAME=your-bucket-name
# AWS_S3_REGION_NAME=your-region

# Redis Settings (Optional - for caching and Celery)
# REDIS_URL=redis://localhost:6379/0
# CELERY_BROKER_URL=redis://localhost:6379/1

# Security Settings
CSRF_COOKIE_SECURE=False  # Set to True in production
SESSION_COOKIE_SECURE=False  # Set to True in production
SECURE_SSL_REDIRECT=False  # Set to True in production
SECURE_HSTS_SECONDS=0  # Set to 31536000 in production
SECURE_HSTS_INCLUDE_SUBDOMAINS=False  # Set to True in production
SECURE_HSTS_PRELOAD=False  # Set to True in production

# Application Settings
MAX_UPLOAD_SIZE=5242880  # 5MB in bytes
ALLOWED_CONTENT_TYPES=application/pdf,text/plain,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document

# Development Tools
DJANGO_DEBUG_TOOLBAR=True

# Google Analytics (Optional)
# GA_TRACKING_ID=your-ga-tracking-id

# Sentry Error Tracking (Optional)
# SENTRY_DSN=your-sentry-dsn

# Payment Processing (Optional)
# STRIPE_PUBLIC_KEY=your-stripe-public-key
# STRIPE_SECRET_KEY=your-stripe-secret-key
# STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
