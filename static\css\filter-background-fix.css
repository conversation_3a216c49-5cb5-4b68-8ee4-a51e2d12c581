/**
 * Dark Background Fix
 * Ensures backgrounds are always dark to prevent white flash on page reload
 * - Applies to filter forms
 * - Applies to community assistant social dashboard
 * - Applies to Facebook-style community dashboard
 */

/* Global filter form styling - highest specificity to override everything */
html body .filter-form,
html body form.filter-form,
html body #form-filter-form,
html body .form-filter-form,
html body div.filter-form,
html body div > form.filter-form,
html body div > #form-filter-form,
html body div > .form-filter-form,
html body .container .filter-form,
html body .container form.filter-form,
html body .container #form-filter-form,
html body .container .form-filter-form,
html body [class*="filter-form"],
html body [id*="filter-form"],
html body form[class*="filter"],
html body div[class*="filter"],
html body *[class*="filter-form"],
html body *[id*="filter-form"] {
    background-color: #121212 !important;
    background: #121212 !important;
    border: 1px solid #333333 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
    color: #ffffff !important;
    transition: none !important; /* Disable transitions to prevent flashing */
}

/* Ensure all elements inside filter forms have appropriate styling */
html body .filter-form *,
html body form.filter-form *,
html body #form-filter-form *,
html body .form-filter-form *,
html body [class*="filter-form"] *,
html body [id*="filter-form"] * {
    color: #ffffff !important;
    background-color: transparent !important;
}

/* Style form controls inside filter forms */
html body .filter-form .form-control,
html body form.filter-form .form-control,
html body #form-filter-form .form-control,
html body .form-filter-form .form-control,
html body [class*="filter-form"] .form-control,
html body [id*="filter-form"] .form-control,
html body .filter-form input[type="text"],
html body .filter-form input[type="search"],
html body .filter-form select,
html body .filter-form textarea {
    background-color: #252525 !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

/* Style input group text inside filter forms */
html body .filter-form .input-group-text,
html body form.filter-form .input-group-text,
html body #form-filter-form .input-group-text,
html body .form-filter-form .input-group-text,
html body [class*="filter-form"] .input-group-text,
html body [id*="filter-form"] .input-group-text {
    background-color: #252525 !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

/* Style buttons inside filter forms */
html body .filter-form .btn-primary,
html body form.filter-form .btn-primary,
html body #form-filter-form .btn-primary,
html body .form-filter-form .btn-primary,
html body [class*="filter-form"] .btn-primary,
html body [id*="filter-form"] .btn-primary {
    background-color: #0077ff !important;
    border-color: #0066dd !important;
    color: #ffffff !important;
}

/* Style secondary buttons inside filter forms */
html body .filter-form .btn-outline-secondary,
html body form.filter-form .btn-outline-secondary,
html body #form-filter-form .btn-outline-secondary,
html body .form-filter-form .btn-outline-secondary,
html body [class*="filter-form"] .btn-outline-secondary,
html body [id*="filter-form"] .btn-outline-secondary {
    border-color: #444444 !important;
    color: #ffffff !important;
}

/* Style hover state for secondary buttons */
html body .filter-form .btn-outline-secondary:hover,
html body form.filter-form .btn-outline-secondary:hover,
html body #form-filter-form .btn-outline-secondary:hover,
html body .form-filter-form .btn-outline-secondary:hover,
html body [class*="filter-form"] .btn-outline-secondary:hover,
html body [id*="filter-form"] .btn-outline-secondary:hover {
    background-color: #333333 !important;
}

/* Style form labels inside filter forms */
html body .filter-form label,
html body form.filter-form label,
html body #form-filter-form label,
html body .form-filter-form label,
html body [class*="filter-form"] label,
html body [id*="filter-form"] label {
    color: #ffffff !important;
}

/* Style headings inside filter forms */
html body .filter-form h1,
html body .filter-form h2,
html body .filter-form h3,
html body .filter-form h4,
html body .filter-form h5,
html body .filter-form h6,
html body form.filter-form h1,
html body form.filter-form h2,
html body form.filter-form h3,
html body form.filter-form h4,
html body form.filter-form h5,
html body form.filter-form h6 {
    color: #ffffff !important;
}

/* Style icons inside filter forms */
html body .filter-form i,
html body form.filter-form i,
html body #form-filter-form i,
html body .form-filter-form i,
html body [class*="filter-form"] i,
html body [id*="filter-form"] i {
    color: inherit !important;
}

/* Ensure filter form is visible during page load */
html body .filter-form,
html body form.filter-form,
html body #form-filter-form,
html body .form-filter-form {
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
}

/* Community Assistant Social Dashboard Styles */

/* Main dashboard container */
html body .community-dashboard,
html body .community-container,
html body [class*="community-dashboard"],
html body [class*="community-container"],
html body [id*="community-dashboard"],
html body [id*="community-container"],
html body div[class*="community"],
html body section[class*="community"],
html body .card,
html body .dashboard-card,
html body .community-card,
html body .feed-card,
html body .stats-card,
html body [class*="dashboard-card"],
html body [class*="community-card"],
html body [class*="feed-card"],
html body [class*="stats-card"] {
    background-color: #121212 !important;
    background: #121212 !important;
    border-color: #333333 !important;
    color: #ffffff !important;
    transition: none !important;
}

/* Feed items and posts */
html body .feed-item,
html body .post,
html body .comment,
html body .question,
html body .answer,
html body [class*="feed-item"],
html body [class*="post"],
html body [class*="comment"],
html body [class*="question"],
html body [class*="answer"] {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    border-color: #333333 !important;
    color: #ffffff !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

/* Tabs and navigation */
html body .nav-tabs,
html body .nav-pills,
html body .tab-content,
html body .tab-pane,
html body [class*="nav-tabs"],
html body [class*="nav-pills"],
html body [class*="tab-content"],
html body [class*="tab-pane"] {
    background-color: transparent !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

/* Stats and metrics */
html body .stats,
html body .metrics,
html body .counter,
html body [class*="stats"],
html body [class*="metrics"],
html body [class*="counter"] {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

/* Buttons and interactive elements */
html body .community-dashboard .btn,
html body .community-container .btn,
html body [class*="community"] .btn,
html body .feed-item .btn,
html body .post .btn,
html body .comment-btn,
html body .upvote-btn,
html body .share-btn,
html body [class*="comment-btn"],
html body [class*="upvote-btn"],
html body [class*="share-btn"] {
    background-color: #252525 !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

/* Primary buttons */
html body .community-dashboard .btn-primary,
html body .community-container .btn-primary,
html body [class*="community"] .btn-primary {
    background-color: #0077ff !important;
    border-color: #0066dd !important;
    color: #ffffff !important;
}

/* Input fields */
html body .community-dashboard input,
html body .community-dashboard textarea,
html body .community-dashboard select,
html body .community-container input,
html body .community-container textarea,
html body .community-container select,
html body [class*="community"] input,
html body [class*="community"] textarea,
html body [class*="community"] select {
    background-color: #252525 !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

/* Tags and badges */
html body .tag,
html body .badge,
html body [class*="tag"],
html body [class*="badge"] {
    background-color: #333333 !important;
    color: #ffffff !important;
}

/* Specific elements from the screenshot */
html body .feed,
html body #feed,
html body .community-stats,
html body .top-contributors,
html body .popular-topics,
html body [id="feed"],
html body [class*="feed"],
html body [class*="community-stats"],
html body [class*="top-contributors"],
html body [class*="popular-topics"] {
    background-color: #121212 !important;
    background: #121212 !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

/* Facebook-style specific rules */
html body.facebook-style,
html body.facebook-style .container,
html body.facebook-style .row,
html body.facebook-style .col,
html body.facebook-style [class*="col-"] {
    background-color: #121212 !important;
    background: #121212 !important;
    color: #ffffff !important;
    transition: none !important;
}

html body.facebook-style .navbar {
    background-color: #121212 !important;
    background: #121212 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    border-bottom: 1px solid #333333 !important;
}

html body.facebook-style .card {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    color: #ffffff !important;
}

html body.facebook-style .card-header {
    background-color: #252525 !important;
    background: #252525 !important;
    border-bottom: 1px solid #333333 !important;
}

html body.facebook-style .card-body {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
}

html body.facebook-style .list-group-item {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

html body.facebook-style .alert-info {
    background-color: #1e3a5f !important;
    background: #1e3a5f !important;
    border-color: #264b7a !important;
    color: #ffffff !important;
}

html body.facebook-style .alert-warning {
    background-color: #3a3000 !important;
    background: #3a3000 !important;
    border-color: #4d4000 !important;
    color: #ffffff !important;
}

html body.facebook-style .text-muted {
    color: #aaaaaa !important;
}

html body.facebook-style .btn-light {
    background-color: #252525 !important;
    background: #252525 !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

html body.facebook-style input,
html body.facebook-style textarea,
html body.facebook-style select {
    background-color: #252525 !important;
    background: #252525 !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

html body.facebook-style .bg-light {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
}
