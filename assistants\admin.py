from django.contrib import admin
from django.utils.html import format_html, mark_safe # Import mark_safe
from django.urls import reverse
from .models import Assistant, Inter<PERSON>, AssistantFolder, AssistantAccessToken # Import other models

@admin.register(Assistant)
class AssistantAdmin(admin.ModelAdmin):
    list_display = [
        'name',
        'display_logo',     # Added logo display
        'display_avatar',   # Added avatar display
        'company_link',
        'assistant_type',
        'tier',             # Added tier
        'is_featured',      # Added is_featured
        'model',
        'created_by_link',
        'is_active',
        'is_public',        # Added is_public for consistency
        # 'hide_if_standard', # Removed field
        'total_interactions',
        'average_rating',
        'created_at'
    ]
    list_filter = [
        'assistant_type',
        'folder',           # Added folder filter
        'tier',             # Added tier
        'is_featured',      # Added is_featured
        'model',
        'is_active',
        'is_public',        # Added is_public for consistency
        # 'hide_if_standard', # Removed field
        'created_at',
        'company'
    ]
    search_fields = [
        'name',
        'description',
        'company__name',
        'created_by__email'
    ]
    readonly_fields = [
        'slug',
        'total_interactions',
        'average_rating',
        'last_trained',
        'created_at',
        'updated_at'
    ]
    fieldsets = [
        ('Basic Information', {
            'fields': [
                'name',
                'persona_name',     # Added persona_name
                'slug',
                'logo',             # Added logo field
                'avatar',           # Added avatar field
                'description',
                'assistant_type',
                'folder',           # Added folder field
                'company',
                'created_by'
            ]
        }),
        ('Configuration', {
            'fields': [
                'model',
                'greeting_message', # Added greeting_message
                'temperature',
                'max_tokens',
                'system_prompt',
                'knowledge_base',
                'custom_css',       # Added custom_css
            ]
        }),
        ('Status & Visibility', { # Added new section
            'fields': [
                'tier',
                'is_featured',
                'is_active',
                'is_public',
                # 'hide_if_standard', # Removed field
                 'qr_code'
             ]
         }),
         # Removed 'Featured Carousel Settings' fieldset
         ('Statistics', {
             'classes': ['collapse'],
            'fields': [
                'total_interactions',
                'average_rating',
                'last_trained',
                'created_at',
                'updated_at'
            ]
        })
    ]
    
    def company_link(self, obj):
        """Display clickable company name."""
        if obj.company:
            url = reverse('admin:accounts_company_change', args=[obj.company.id])
            return format_html('<a href="{}">{}</a>', url, obj.company.name)
        return "-"
    company_link.short_description = 'Company'
    company_link.admin_order_field = 'company__name'

    def display_logo(self, obj):
        """Display assistant logo as a small image in list view."""
        if obj.logo:
            return format_html('<img src="{}" width="40" height="40" style="object-fit: contain; border-radius: 4px;" />', obj.logo.url)
        return "No Logo"
    display_logo.short_description = 'Logo'

    def display_avatar(self, obj):
        """Display assistant avatar as a small image in list view."""
        if obj.avatar:
            return format_html('<img src="{}" width="40" height="40" style="object-fit: contain; border-radius: 4px;" />', obj.avatar.url)
        return "No Avatar"
    display_avatar.short_description = 'Avatar'

    def created_by_link(self, obj):
        """Display clickable creator name."""
        if obj.created_by:
            url = reverse('admin:auth_user_change', args=[obj.created_by.id])
            return format_html('<a href="{}">{}</a>', url, obj.created_by.get_full_name() or obj.created_by.email)
        return "-"
    created_by_link.short_description = 'Created By'
    created_by_link.admin_order_field = 'created_by__email'

@admin.register(Interaction)
class InteractionAdmin(admin.ModelAdmin):
    list_display = [
        'assistant_link',
        'user_link',
        'truncated_prompt',
        'rating',
        'duration',
        'token_count',
        'created_at'
    ]
    list_filter = [
        'assistant__assistant_type',
        'assistant__model',
        'rating',
        'created_at'
    ]
    search_fields = [
        'assistant__name',
        'user__email',
        'prompt',
        'response'
    ]
    readonly_fields = [
        'assistant',
        'user',
        'prompt',
        'response',
        'context',
        'duration',
        'token_count',
        'created_at'
    ]
    fieldsets = [
        ('Interaction Details', {
            'fields': [
                'assistant',
                'user',
                'prompt',
                'response',
                'context'
            ]
        }),
        ('Metrics', {
            'fields': [
                'rating',
                'duration',
                'token_count',
                'created_at'
            ]
        })
    ]
    
    def assistant_link(self, obj):
        """Display clickable assistant name."""
        if obj.assistant:
            url = reverse('admin:assistants_assistant_change', args=[obj.assistant.id])
            return format_html('<a href="{}">{}</a>', url, obj.assistant.name)
        return "-"
    assistant_link.short_description = 'Assistant'
    assistant_link.admin_order_field = 'assistant__name'
    
    def user_link(self, obj):
        """Display clickable user name."""
        if obj.user:
            url = reverse('admin:auth_user_change', args=[obj.user.id])
            return format_html('<a href="{}">{}</a>', url, obj.user.get_full_name() or obj.user.email)
        return "-"
    user_link.short_description = 'User'
    user_link.admin_order_field = 'user__email'
    
    def truncated_prompt(self, obj):
        """Display truncated prompt text."""
        max_length = 50
        if len(obj.prompt) > max_length:
            return obj.prompt[:max_length] + "..."
        return obj.prompt
    truncated_prompt.short_description = 'Prompt'
