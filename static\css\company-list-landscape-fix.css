/**
 * Company List Landscape Mode Fixes
 * Specific fixes for company list cards in landscape mode on mobile devices
 */

/* Landscape optimizations for mobile devices */
@media (max-width: 768px) and (orientation: landscape) {
  /* Fix for company list cards in landscape mode */
  .company-directory-page .directory-card {
    display: flex !important;
    flex-direction: column !important;
    min-height: 0 !important;
    height: auto !important;
    padding: 0.75rem !important;
    margin-bottom: 1rem !important;
  }

  /* Fix for list-group-item height */
  .company-directory-page .list-group-item {
    height: auto !important;
    min-height: 0 !important;
  }

  /* Fix for company list card row layout */
  .company-directory-page .directory-card .row {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    width: 100% !important;
    height: auto !important;
    gap: 0.5rem !important;
    padding-top: 0.5rem !important;
  }

  /* Fix for company list card link wrapper */
  .company-directory-page .directory-item-link-wrapper {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    width: 65% !important;
    min-width: 65% !important;
    max-width: 65% !important;
    margin-right: 0 !important;
  }

  /* Fix for company list card logo column */
  .company-directory-page .directory-item-link-wrapper .col-md-3 {
    width: 70px !important;
    min-width: 70px !important;
    max-width: 70px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 0 0 0 10px !important;
    margin-bottom: 0 !important;
  }

  /* Fix for company list card logo container */
  .company-directory-page .logo-container {
    height: 60px !important;
    width: 60px !important;
    min-height: 60px !important;
    min-width: 60px !important;
    max-height: 60px !important;
    max-width: 60px !important;
    margin: 0 !important;
  }

  /* Fix for company list card name column */
  .company-directory-page .directory-item-link-wrapper .col-md-4 {
    width: 25% !important;
    min-width: 25% !important;
    max-width: 25% !important;
    padding: 0 0.5rem !important;
  }

  /* Fix for company list card description column */
  .company-directory-page .directory-item-link-wrapper .col-md-5 {
    width: 35% !important;
    min-width: 35% !important;
    max-width: 35% !important;
    padding: 0 0.5rem !important;
  }

  /* Fix for company list card contact info column */
  .company-directory-page .directory-card .col-md-2.d-flex.flex-column.justify-content-center {
    width: 20% !important;
    min-width: 20% !important;
    max-width: 20% !important;
    padding: 0 0.25rem !important;
  }

  /* Fix for company list card rating column */
  .company-directory-page .directory-card .col-md-2.d-flex.flex-column.align-items-end {
    width: 15% !important;
    min-width: 15% !important;
    max-width: 15% !important;
    padding: 0 0.5rem 0 0 !important;
  }

  /* Fix for company list card text */
  .company-directory-page .directory-card h6 {
    font-size: 0.9rem !important;
    margin-bottom: 0.25rem !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .company-directory-page .directory-card p {
    font-size: 0.8rem !important;
    margin-bottom: 0.25rem !important;
    line-height: 1.2 !important;
  }

  .company-directory-page .directory-card .item-description {
    max-height: 60px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 3 !important;
    -webkit-box-orient: vertical !important;
    font-size: 0.8rem !important;
  }

  /* Fix for company list card badges */
  .company-directory-page .directory-card .badge {
    font-size: 0.7rem !important;
    padding: 0.2rem 0.4rem !important;
  }

  /* Fix for company list card contact info */
  .company-directory-page .contact-info {
    margin-bottom: 0 !important;
    font-size: 0.75rem !important;
  }

  .company-directory-page .contact-info li {
    margin-bottom: 0.25rem !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 100% !important;
  }

  .company-directory-page .contact-info i {
    font-size: 0.8rem !important;
    width: 12px !important;
    text-align: center !important;
    margin-right: 0.25rem !important;
  }

  .company-directory-page .contact-text {
    display: inline !important;
    color: #cccccc !important;
  }

  /* Fix for company list card rating display */
  .company-directory-page .rating-display-container {
    margin-bottom: 0.5rem !important;
    transform: scale(0.85) !important;
    transform-origin: right !important;
  }

  /* Fix for company list card action buttons */
  .company-directory-page .directory-card .btn-sm {
    padding: 0.2rem 0.4rem !important;
    font-size: 0.75rem !important;
    line-height: 1.2 !important;
  }

  /* Dark mode fixes for company list cards in landscape mode */
  [data-theme="dark"] .company-directory-page .directory-card {
    background: linear-gradient(145deg, #1e1e1e, #252525) !important;
    border: 1px solid #333333 !important;
  }

  [data-theme="dark"] .company-directory-page .directory-card h6 {
    color: #ffffff !important;
  }

  [data-theme="dark"] .company-directory-page .directory-card p {
    color: #cccccc !important;
  }

  [data-theme="dark"] .company-directory-page .contact-text {
    color: #aaaaaa !important;
  }
}
