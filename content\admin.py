from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Content, ContentImage

class ContentImageInline(admin.TabularInline):
    model = ContentImage
    extra = 1

@admin.register(Content)
class ContentAdmin(admin.ModelAdmin):
    list_display = ('title', 'company', 'assistant', 'content_type', 'created_at') # Corrected: navigation was already removed here, error was elsewhere or state mismatch
    list_filter = ('content_type', 'created_at', 'company')
    search_fields = ('title', 'company__name', 'assistant__name') # Corrected: navigation was already removed here
    readonly_fields = ('created_at', 'updated_at')
    inlines = [ContentImageInline]
    date_hierarchy = 'created_at'
    
    # Removed get_display_name as title is sufficient
    
    fieldsets = (
        (None, {
            'fields': ('title', 'company', 'assistant', 'content_type') # Use title, company, assistant, removed navigation
        }),
        (_('Content Data'), {
            'fields': ('data',),
            'description': _('JSON data structure varies based on content type')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(ContentImage)
class ContentImageAdmin(admin.ModelAdmin):
    list_display = ('content', 'alt_text', 'created_at')
    search_fields = ('content__title', 'alt_text') # Search by content title instead of assistant name
    readonly_fields = ('created_at',)
    list_filter = ('created_at',)
    date_hierarchy = 'created_at'
