/**
 * Home Page Carousel Mobile Fixes
 * Improves the display of company logos in the home page carousel on mobile devices
 */

/* Base improvements for all screen sizes */
.company-logo-carousel-container {
  width: 100%;
  overflow: hidden;
  position: relative;
  padding: 20px 0 50px 0;
  margin-bottom: 20px;
}

.company-logo-carousel {
  display: flex;
  animation: scroll 60s linear infinite;
  width: max-content;
}

.company-logo-item {
  flex: 0 0 auto;
  margin: 0 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.company-logo-item a {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-bottom: 10px;
}

.company-logo {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* Mobile optimizations (up to 768px) */
@media (max-width: 768px) {
  /* Optimize carousel container */
  .company-logo-carousel-container {
    padding: 15px 0 40px 0;
    margin-bottom: 15px;
  }

  /* Optimize carousel items */
  .company-logo-item {
    margin: 0 10px;
    width: 80vw;
    max-width: 300px;
  }

  /* Optimize logo links */
  .company-logo-item a {
    width: 100%;
    max-width: 280px;
  }

  /* Optimize logo container */
  .home-carousel-logo {
    height: 120px !important;
    width: 120px !important;
    min-height: 120px !important;
    min-width: 120px !important;
    max-height: 120px !important;
    max-width: 120px !important;
    margin: 0 auto 10px auto !important;
    border-radius: 8px !important;
    background-color: #ffffff !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
    padding: 10px !important;
  }

  /* Optimize logo image */
  .home-carousel-logo .company-logo {
    max-width: 100% !important;
    max-height: 100% !important;
    object-fit: contain !important;
    width: auto !important;
    height: auto !important;
  }

  /* Optimize company logo placeholder */
  .home-carousel-placeholder {
    height: 120px !important;
    width: 120px !important;
    min-height: 120px !important;
    min-width: 120px !important;
    max-height: 120px !important;
    max-width: 120px !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: #f8f9fa !important;
    border-radius: 8px !important;
    margin: 0 auto 10px auto !important;
  }

  /* Optimize placeholder icon */
  .home-carousel-placeholder i {
    font-size: 50px !important;
    color: #6c757d !important;
    margin-bottom: 5px !important;
  }

  /* Optimize placeholder text */
  .home-carousel-placeholder span {
    font-size: 14px !important;
    color: #6c757d !important;
    text-align: center !important;
    max-width: 100px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }

  /* Optimize company info */
  .company-info {
    text-align: center !important;
    width: 100% !important;
  }

  /* Optimize company name */
  .company-name {
    font-size: 16px !important;
    margin-bottom: 5px !important;
    font-weight: 600 !important;
  }

  /* Optimize assistant count */
  .assistant-count {
    font-size: 14px !important;
    color: #6c757d !important;
    margin-bottom: 0 !important;
  }
}

/* Very small screens (up to 576px) */
@media (max-width: 576px) {
  /* Further optimize carousel items */
  .company-logo-item {
    width: 90vw;
    max-width: 250px;
  }

  /* Further optimize logo container */
  .home-carousel-logo {
    height: 100px !important;
    width: 100px !important;
    min-height: 100px !important;
    min-width: 100px !important;
    max-height: 100px !important;
    max-width: 100px !important;
  }

  /* Further optimize company logo placeholder */
  .home-carousel-placeholder {
    height: 100px !important;
    width: 100px !important;
    min-height: 100px !important;
    min-width: 100px !important;
    max-height: 100px !important;
    max-width: 100px !important;
  }

  /* Further optimize placeholder icon */
  .home-carousel-placeholder i {
    font-size: 40px !important;
  }
}

/* Dark mode optimizations */
@media (max-width: 768px) {
  [data-theme="dark"] .home-carousel-logo {
    background-color: #1a1a1a !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
  }

  [data-theme="dark"] .home-carousel-placeholder {
    background-color: #1a1a1a !important;
  }

  [data-theme="dark"] .home-carousel-placeholder i,
  [data-theme="dark"] .home-carousel-placeholder span {
    color: #aaaaaa !important;
  }

  [data-theme="dark"] .company-name {
    color: #ffffff !important;
  }

  [data-theme="dark"] .assistant-count {
    color: #aaaaaa !important;
  }
}
