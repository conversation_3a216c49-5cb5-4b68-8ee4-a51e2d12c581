import os
import django
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import the models
try:
    from accounts.models import RegistrationLink
    from django.contrib.auth import get_user_model
    from accounts.models import Company
    
    print("Successfully imported models")
    User = get_user_model()
except Exception as e:
    print(f"Error importing models: {e}")
    sys.exit(1)

def test_query():
    """Test querying the RegistrationLink model."""
    print("Testing RegistrationLink query...")
    
    # Get all registration links
    try:
        links = RegistrationLink.objects.all()
        print(f"Found {links.count()} registration links")
    except Exception as e:
        print(f"Error querying registration links: {e}")
        return
    
    if links.exists():
        link = links.first()
        print(f"Testing with link: {link.token}")
        print(f"Company: {link.company.name}")
        print(f"Created at: {link.created_at}")
    else:
        print("No registration links found in the database")
        
        # Try to create a test link if there are companies
        companies = Company.objects.all()
        
        if companies.exists():
            company = companies.first()
            
            try:
                link = RegistrationLink.objects.create(
                    company=company,
                    notes="Test link",
                    uses_count=0,
                    is_active=True
                )
                print(f"Created test link: {link.token}")
                print(f"Company: {link.company.name}")
                print(f"Created at: {link.created_at}")
                
                # Clean up
                link.delete()
                print("Deleted test link")
            except Exception as e:
                print(f"Error creating registration link: {e}")
        else:
            print("No companies found to create a test link")

if __name__ == "__main__":
    test_query()
