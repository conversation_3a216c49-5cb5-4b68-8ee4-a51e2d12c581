"""
<PERSON><PERSON><PERSON> to create a PostgreSQL database for the Django project.
"""
import os
import sys
import subprocess
import psycopg2
from psycopg2 import sql
from getpass import getpass

def create_database(db_name, username, password=None):
    """Create a PostgreSQL database."""
    conn_params = {
        'user': username,
        'host': 'localhost',
        'port': '5432',
    }
    
    if password:
        conn_params['password'] = password
    
    try:
        # Connect to PostgreSQL (to postgres database by default)
        conn_params['dbname'] = 'postgres'
        conn = psycopg2.connect(**conn_params)
        conn.autocommit = True
        cursor = conn.cursor()
        
        # Check if database already exists
        cursor.execute(
            sql.SQL("SELECT 1 FROM pg_database WHERE datname = %s"),
            [db_name]
        )
        exists = cursor.fetchone()
        
        if exists:
            print(f"Database '{db_name}' already exists.")
        else:
            # Create the database
            cursor.execute(sql.SQL("CREATE DATABASE {}").format(sql.Identifier(db_name)))
            print(f"Database '{db_name}' created successfully.")
        
        cursor.close()
        conn.close()
        return True
    except Exception as e:
        print(f"Error creating database: {e}")
        return False

def main():
    """Main function to create the database."""
    print("PostgreSQL Database Creation Script")
    print("==================================")
    
    # Get database name
    db_name = input("Enter database name [virtualo]: ").strip() or "virtualo"
    
    # Get username
    username = input("Enter PostgreSQL username [postgres]: ").strip() or "postgres"
    
    # Get password
    use_password = input("Does your PostgreSQL user require a password? (y/n) [n]: ").strip().lower() or "n"
    password = None
    if use_password == "y":
        password = getpass("Enter PostgreSQL password: ")
    
    # Create the database
    success = create_database(db_name, username, password)
    
    if success:
        # Update local_settings.py
        update_settings = input("Update local_settings.py with these credentials? (y/n) [y]: ").strip().lower() or "y"
        if update_settings == "y":
            try:
                settings_path = os.path.join("company_assistant", "local_settings.py")
                
                # Read the current settings file
                with open(settings_path, 'r') as f:
                    content = f.read()
                
                # Update the database settings
                import re
                content = re.sub(
                    r"'NAME': '.*?'", 
                    f"'NAME': '{db_name}'", 
                    content
                )
                content = re.sub(
                    r"'USER': '.*?'", 
                    f"'USER': '{username}'", 
                    content
                )
                
                if password:
                    content = re.sub(
                        r"'PASSWORD': '.*?'", 
                        f"'PASSWORD': '{password}'", 
                        content
                    )
                
                # Write the updated settings file
                with open(settings_path, 'w') as f:
                    f.write(content)
                
                print(f"Updated {settings_path} with new database settings.")
            except Exception as e:
                print(f"Error updating settings file: {e}")
        
        print("\nNext steps:")
        print("1. Run migrations: python manage.py migrate")
        print("2. Create a superuser: python manage.py createsuperuser")
        print("3. Run the development server: python manage.py runserver")
    else:
        print("\nFailed to create the database. Please check your PostgreSQL installation and credentials.")

if __name__ == "__main__":
    main()
