/**
 * Enhanced Thinking Indicator CSS
 * Provides a modern, animated thinking indicator for the chat interface
 */

/* Base thinking message container */
.message.assistant-message.thinking,
.message.assistant-message.loading {
  background: transparent !important;
  box-shadow: none !important;
  margin: 0.5rem 0 !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
}

/* Modern thinking indicator container */
.thinking-indicator {
  display: inline-flex !important;
  align-items: center !important;
  background-color: rgba(255, 255, 255, 0.9) !important;
  border-radius: 18px !important;
  padding: 0.6rem 1.2rem !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
  margin: 0.5rem 0 !important;
  position: relative !important;
  overflow: hidden !important;
  backdrop-filter: blur(5px) !important;
  -webkit-backdrop-filter: blur(5px) !important;
  transition: all 0.3s ease !important;
  max-width: 180px !important;
}

/* Thinking dots animation */
.thinking-dots {
  display: inline-flex !important;
  align-items: center !important;
  margin-left: 0.5rem !important;
}

.thinking-dots span {
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
  background-color: #3b7dd8 !important;
  margin: 0 2px !important;
  display: inline-block !important;
  animation: thinking-bounce 1.4s infinite ease-in-out both !important;
}

.thinking-dots span:nth-child(1) {
  animation-delay: -0.32s !important;
}

.thinking-dots span:nth-child(2) {
  animation-delay: -0.16s !important;
}

@keyframes thinking-bounce {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.6;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Subtle pulse animation for the container */
.thinking-indicator {
  animation: thinking-pulse 2s infinite ease-in-out !important;
}

@keyframes thinking-pulse {
  0% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  }
  50% {
    box-shadow: 0 4px 20px rgba(59, 125, 216, 0.15);
  }
  100% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  }
}

/* Dark mode styles */
[data-theme="dark"] .thinking-indicator {
  background-color: rgba(30, 30, 30, 0.8) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] .thinking-dots span {
  background-color: #4a90e2 !important;
}

[data-theme="dark"] .thinking-text {
  color: #e0e0e0 !important;
}

@keyframes thinking-pulse-dark {
  0% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
  50% {
    box-shadow: 0 4px 20px rgba(74, 144, 226, 0.25);
  }
  100% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
}

[data-theme="dark"] .thinking-indicator {
  animation: thinking-pulse-dark 2s infinite ease-in-out !important;
}

/* Shimmer effect for the thinking indicator */
.thinking-indicator::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  transform: rotate(45deg);
  animation: thinking-shimmer 3s infinite linear;
  pointer-events: none;
}

@keyframes thinking-shimmer {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  20%, 100% {
    transform: translateX(100%) rotate(45deg);
  }
}

/* Dark mode shimmer */
[data-theme="dark"] .thinking-indicator::after {
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(255, 255, 255, 0.05) 50%,
    transparent 100%
  );
}

/* Responsive styles for mobile */
@media (max-width: 768px) {
  .thinking-indicator {
    padding: 0.5rem 1rem !important;
    max-width: 150px !important;
  }
  
  .thinking-dots span {
    width: 6px !important;
    height: 6px !important;
  }
  
  .thinking-text {
    font-size: 0.9rem !important;
  }
}
