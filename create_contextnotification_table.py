import os
import django
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

print("Starting script to create assistants_contextnotification table...")

# SQL to create the assistants_contextnotification table
contextnotification_sql = """
CREATE TABLE IF NOT EXISTS "assistants_contextnotification" (
    "id" serial NOT NULL PRIMARY KEY,
    "is_read" boolean NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "context_id" integer NOT NULL REFERENCES "assistants_communitycontext" ("id") DEFERRABLE INITIALLY DEFERRED,
    "flagged_question_id" integer NULL REFERENCES "assistants_flaggedquestion" ("id") DEFERRABLE INITIALLY DEFERRED,
    "user_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# SQL to create indexes
indexes_sql = """
CREATE INDEX IF NOT EXISTS "assistants_contextnotification_context_id_idx" ON "assistants_contextnotification" ("context_id");
CREATE INDEX IF NOT EXISTS "assistants_contextnotification_flagged_question_id_idx" ON "assistants_contextnotification" ("flagged_question_id");
CREATE INDEX IF NOT EXISTS "assistants_contextnotification_user_id_idx" ON "assistants_contextnotification" ("user_id");
CREATE INDEX IF NOT EXISTS "assistants_contextnotification_user_id_is_read_idx" ON "assistants_contextnotification" ("user_id", "is_read");
"""

# Execute the SQL
try:
    with connection.cursor() as cursor:
        print("Creating assistants_contextnotification table...")
        cursor.execute(contextnotification_sql)
        print("assistants_contextnotification table created successfully!")
        
        print("Creating indexes...")
        cursor.execute(indexes_sql)
        print("Indexes created successfully!")
    
    print("All tables created successfully!")
except Exception as e:
    print(f"Error creating tables: {e}")
    sys.exit(1)
