#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
# Load .env file as early as possible
from dotenv import load_dotenv
load_dotenv()

import os
import sys


def main():
    """Run administrative tasks."""
    # load_dotenv() # Moved to top
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    main()
