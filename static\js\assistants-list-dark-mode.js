/**
 * Assistants List Dark Mode Handler
 * Ensures dark mode is properly applied to assistants list pages
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on an assistants list page
    const isAssistantsListPage = document.querySelector('.tier-section') ||
                               document.title.includes('Manage Assistants') ||
                               window.location.href.includes('/assistants/');

    if (!isAssistantsListPage) {
        return; // Exit if not on an assistants list page
    }

    // Load the assistants list dark mode CSS
    loadAssistantsListCss();

    // Check if dark mode is active
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';

    // Apply dark mode styles to assistants list elements
    applyDarkModeToAssistantsList(isDarkMode);

    // Listen for theme changes
    document.addEventListener('themeChanged', function(e) {
        const isDarkMode = e.detail.theme === 'dark';
        applyDarkModeToAssistantsList(isDarkMode);
    });

    // Function to load the assistants list dark mode CSS
    function loadAssistantsListCss() {
        if (!document.getElementById('assistants-list-dark-mode-css')) {
            const link = document.createElement('link');
            link.id = 'assistants-list-dark-mode-css';
            link.rel = 'stylesheet';
            link.href = '/static/css/assistants-list-dark-mode.css';
            document.head.appendChild(link);
        }
    }

    // Function to apply dark mode to assistants list elements
    function applyDarkModeToAssistantsList(isDarkMode) {
        if (isDarkMode) {
            // Apply dark mode to body if it has bg-light class
            const body = document.body;
            if (body.classList.contains('bg-light')) {
                body.style.backgroundColor = '#121212';
                body.style.color = '#ffffff';
            }

            // Apply dark mode to all elements with bg-light class
            document.querySelectorAll('.bg-light').forEach(el => {
                el.style.backgroundColor = '#121212';
                el.style.color = '#ffffff';
            });

            // Apply dark mode to filter form
            document.querySelectorAll('.filter-form').forEach(form => {
                form.style.backgroundColor = '#1a1a1a';
                form.style.border = '1px solid #333333';
                form.style.borderRadius = '8px';
                form.style.padding = '20px';
                form.style.marginBottom = '30px';
                form.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
                form.style.color = '#ffffff';

                // Style the heading
                const heading = form.querySelector('h5');
                if (heading) {
                    heading.style.color = '#ffffff';

                    // Style the icon in the heading
                    const icon = heading.querySelector('i');
                    if (icon) {
                        icon.style.color = '#0077ff';
                    }
                }

                // Style the input group
                const inputGroup = form.querySelector('.input-group');
                if (inputGroup) {
                    inputGroup.style.backgroundColor = 'transparent';
                    inputGroup.style.border = 'none';

                    // Style the input group text
                    const inputGroupText = inputGroup.querySelector('.input-group-text');
                    if (inputGroupText) {
                        inputGroupText.style.backgroundColor = '#252525';
                        inputGroupText.style.borderColor = '#444444';
                        inputGroupText.style.color = '#ffffff';
                    }

                    // Style the input
                    const input = inputGroup.querySelector('.form-control');
                    if (input) {
                        input.style.backgroundColor = '#252525';
                        input.style.borderColor = '#444444';
                        input.style.color = '#ffffff';
                    }

                    // Style the search button
                    const searchButton = inputGroup.querySelector('.btn-primary');
                    if (searchButton) {
                        searchButton.style.backgroundColor = '#0077ff';
                        searchButton.style.borderColor = '#0066dd';
                        searchButton.style.color = '#ffffff';
                    }
                }

                // Style the clear search button
                const clearButton = form.querySelector('.btn-outline-secondary');
                if (clearButton) {
                    clearButton.style.borderColor = '#444444';
                    clearButton.style.color = '#ffffff';

                    // Add hover effect
                    clearButton.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = '#333333';
                    });

                    // Add leave effect
                    clearButton.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = 'transparent';
                    });
                }

                // Style any text in the filter form
                const textMuted = form.querySelector('.text-muted');
                if (textMuted) {
                    textMuted.style.color = '#aaaaaa !important';
                }
            });

            // Apply dark mode to tier sections
            document.querySelectorAll('.tier-section').forEach(section => {
                section.style.backgroundColor = '#1a1a1a';
                section.style.border = '1px solid #333333';
                section.style.borderRadius = '8px';
                section.style.padding = '20px';
                section.style.marginBottom = '30px';
                section.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';

                // Style the heading
                const heading = section.querySelector('h3');
                if (heading) {
                    heading.style.color = '#ffffff';
                    heading.style.borderBottom = '1px solid #333333';
                    heading.style.paddingBottom = '10px';
                    heading.style.marginBottom = '20px';
                }
            });

            // Apply dark mode to list group items (assistant cards)
            document.querySelectorAll('.list-group-item, .directory-card').forEach(item => {
                item.style.backgroundColor = '#252525';
                item.style.border = '1px solid #333333';
                item.style.borderRadius = '8px';
                item.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
                item.style.color = '#ffffff';
                item.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';
                item.style.marginBottom = '15px';
                item.style.overflow = 'hidden';

                // Add hover effect
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.3)';
                });

                // Add leave effect
                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
                });

                // Style logo container
                const logoContainer = item.querySelector('.logo-container');
                if (logoContainer) {
                    logoContainer.style.backgroundColor = '#1a1a1a';
                    logoContainer.style.border = '1px solid #333333';
                    logoContainer.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
                }

                // Style logo placeholder
                const logoPlaceholder = item.querySelector('.logo-placeholder');
                if (logoPlaceholder) {
                    logoPlaceholder.style.backgroundColor = 'rgba(26, 26, 26, 0.5)';
                    logoPlaceholder.style.color = '#0077ff';
                }

                // Style assistant name
                const assistantName = item.querySelector('.assistant-name a');
                if (assistantName) {
                    assistantName.style.color = '#ffffff';
                    assistantName.style.textDecoration = 'none';

                    // Add hover effect
                    assistantName.addEventListener('mouseenter', function() {
                        this.style.color = '#0088ff';
                    });

                    // Add leave effect
                    assistantName.addEventListener('mouseleave', function() {
                        this.style.color = '#ffffff';
                    });
                }

                // Style assistant meta information
                const assistantMeta = item.querySelector('.assistant-meta');
                if (assistantMeta) {
                    assistantMeta.style.color = '#cccccc';
                }

                // Style badges
                item.querySelectorAll('.badge').forEach(badge => {
                    if (badge.classList.contains('bg-secondary')) {
                        badge.style.backgroundColor = '#444444';
                    } else if (badge.classList.contains('bg-success')) {
                        badge.style.backgroundColor = '#198754';
                    } else if (badge.classList.contains('community-badge')) {
                        badge.style.backgroundColor = '#6c757d';
                    }
                });

                // Style action buttons
                item.querySelectorAll('.btn-outline-primary').forEach(button => {
                    button.style.color = '#0088ff';
                    button.style.borderColor = '#0088ff';
                    button.style.backgroundColor = 'transparent';

                    // Add hover effect
                    button.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = 'rgba(0, 136, 255, 0.2)';
                        this.style.color = '#ffffff';
                    });

                    // Add leave effect
                    button.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = 'transparent';
                        this.style.color = '#0088ff';
                    });
                });

                item.querySelectorAll('.btn-outline-secondary').forEach(button => {
                    button.style.color = '#cccccc';
                    button.style.borderColor = '#555555';
                    button.style.backgroundColor = 'transparent';

                    // Add hover effect
                    button.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = '#333333';
                        this.style.color = '#ffffff';
                    });

                    // Add leave effect
                    button.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = 'transparent';
                        this.style.color = '#cccccc';
                    });
                });

                item.querySelectorAll('.btn-outline-info').forEach(button => {
                    button.style.color = '#0dcaf0';
                    button.style.borderColor = '#0dcaf0';
                    button.style.backgroundColor = 'transparent';

                    // Add hover effect
                    button.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = 'rgba(13, 202, 240, 0.2)';
                        this.style.color = '#ffffff';
                    });

                    // Add leave effect
                    button.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = 'transparent';
                        this.style.color = '#0dcaf0';
                    });
                });

                item.querySelectorAll('.btn-outline-danger').forEach(button => {
                    button.style.color = '#dc3545';
                    button.style.borderColor = '#dc3545';
                    button.style.backgroundColor = 'transparent';

                    // Add hover effect
                    button.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = 'rgba(220, 53, 69, 0.2)';
                        this.style.color = '#ffffff';
                    });

                    // Add leave effect
                    button.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = 'transparent';
                        this.style.color = '#dc3545';
                    });
                });
            });

            // Apply dark mode to tier badges
            document.querySelectorAll('.tier-badge').forEach(badge => {
                badge.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.3)';
            });
        }
    }

    // Use a more efficient approach for observing DOM changes
    // Only create observer if window.optimizedDarkMode is not true
    if (!window._isDarkModeActive) {
        // Set up a mutation observer with debouncing to prevent excessive calls
        let debounceTimer;
        const observer = new MutationObserver(function() {
            // Clear any existing timeout
            if (debounceTimer) {
                clearTimeout(debounceTimer);
            }

            // Set a new timeout to run the function after a delay
            debounceTimer = setTimeout(function() {
                const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
                if (isDarkMode) {
                    applyDarkModeToAssistantsList(true);
                }
            }, 1000); // 1 second debounce
        });

        // Observe only significant changes
        observer.observe(document.body, {
            childList: true,
            subtree: false,
            attributeFilter: ['class', 'style']
        });
    }
});
