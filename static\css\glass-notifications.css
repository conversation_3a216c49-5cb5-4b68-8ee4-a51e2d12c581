/**
 * Glass Notifications CSS
 * Adds a modern glass-like appearance to notification alerts
 */

/* Base glass notification style */
.glass-notification {
    background: rgba(255, 255, 255, 0.15) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border-radius: 12px !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    color: #ffffff !important;
    padding: 1rem 1.25rem !important;
    margin-bottom: 1.5rem !important;
    position: relative !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

/* Glass notification warning style (yellow/gold) */
.glass-notification.notification-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.25), rgba(255, 152, 0, 0.2)) !important;
    border: 1px solid rgba(255, 193, 7, 0.3) !important;
    box-shadow: 0 8px 32px rgba(255, 193, 7, 0.15), 0 0 0 1px rgba(255, 193, 7, 0.05) !important;
}

/* Glass notification warning style for dark mode */
[data-theme="dark"] .glass-notification.notification-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 152, 0, 0.15)) !important;
    border: 1px solid rgba(255, 193, 7, 0.25) !important;
    box-shadow: 0 8px 32px rgba(255, 193, 7, 0.1), 0 0 0 1px rgba(255, 193, 7, 0.1) !important;
    color: rgba(255, 255, 255, 0.95) !important;
}

/* Icon styling */
.glass-notification .notification-icon {
    font-size: 1.5rem !important;
    margin-right: 1rem !important;
    color: rgba(255, 193, 7, 0.9) !important;
    text-shadow: 0 0 10px rgba(255, 193, 7, 0.5) !important;
}

/* Text styling */
.glass-notification .notification-text {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 400 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* Strong text styling */
.glass-notification .notification-text strong {
    font-weight: 600 !important;
    color: #ffffff !important;
}

/* Hover effect */
.glass-notification:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 10px 40px rgba(255, 193, 7, 0.2), 0 0 0 1px rgba(255, 193, 7, 0.1) !important;
}

/* Glow effect */
.glass-notification::before {
    content: "" !important;
    position: absolute !important;
    top: -10px !important;
    left: -10px !important;
    right: -10px !important;
    bottom: -10px !important;
    z-index: -1 !important;
    background: radial-gradient(circle at center, rgba(255, 193, 7, 0.2) 0%, rgba(255, 193, 7, 0) 70%) !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
}

.glass-notification:hover::before {
    opacity: 1 !important;
}

/* Pending items list styling */
.glass-notification .pending-items-list {
    display: inline-block;
    margin-top: 0.25rem;
}

.glass-notification .pending-item {
    display: inline-block;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.95) !important;
}

/* Animation for new notifications */
@keyframes notificationPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}

.glass-notification.new-notification {
    animation: notificationPulse 2s infinite;
}
