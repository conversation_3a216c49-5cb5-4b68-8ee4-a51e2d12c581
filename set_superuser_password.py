import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from django.contrib.auth.hashers import make_password

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

print("Starting superuser password update script...")
print(f"Django version: {django.get_version()}")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

# Superuser details
username = 'admin'
password = 'admin123'  # You should change this to a secure password
hashed_password = make_password(password)

# SQL to update the superuser password
update_password_sql = """
UPDATE auth_user
SET password = %s
WHERE username = %s
RETURNING id;
"""

# Connect to the database
try:
    print("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(**db_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    
    # Update the superuser password
    print(f"Updating password for user '{username}'...")
    cursor.execute(update_password_sql, (hashed_password, username))
    
    result = cursor.fetchone()
    if result:
        print(f"Password updated for user with ID: {result[0]}")
    else:
        print(f"User '{username}' not found.")
    
    # Close the connection
    cursor.close()
    conn.close()
    print("Database connection closed.")
    
    print("\nPassword updated successfully!")
    print(f"Username: {username}")
    print(f"Password: {password}")
    print("\nYou can now log in to the admin interface at http://127.0.0.1:8000/admin/")
    
except Exception as e:
    print(f"Error: {e}")
