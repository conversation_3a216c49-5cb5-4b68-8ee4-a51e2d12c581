from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    help = 'Checks and fixes the assistants_interaction table structure'

    def handle(self, *args, **options):
        self.stdout.write('Checking assistants_interaction table structure...')
        
        # Check if the table exists
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'assistants_interaction'
                );
            """)
            table_exists = cursor.fetchone()[0]
            
            if table_exists:
                self.stdout.write(self.style.SUCCESS('The assistants_interaction table exists!'))
            else:
                self.stdout.write(self.style.WARNING('The assistants_interaction table does NOT exist!'))
                
                # Create the table
                self.stdout.write('Creating assistants_interaction table...')
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS "assistants_interaction" (
                        "id" serial NOT NULL PRIMARY KEY,
                        "prompt" text NOT NULL,
                        "response" text NOT NULL,
                        "context" text NOT NULL,
                        "rating" integer NULL,
                        "duration" double precision NOT NULL,
                        "token_count" integer NOT NULL,
                        "use_community_context" boolean NOT NULL DEFAULT false,
                        "created_at" timestamp with time zone NOT NULL,
                        "assistant_id" integer NOT NULL REFERENCES "assistants_assistant" ("id") DEFERRABLE INITIALLY DEFERRED,
                        "user_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
                    );
                """)
                self.stdout.write(self.style.SUCCESS('Table created successfully!'))
                
                # Create indexes
                self.stdout.write('Creating indexes...')
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS "assistants_interaction_assistant_id_created_at_idx" 
                    ON "assistants_interaction" ("assistant_id", "created_at");
                    
                    CREATE INDEX IF NOT EXISTS "assistants_interaction_user_id_created_at_idx" 
                    ON "assistants_interaction" ("user_id", "created_at");
                """)
                self.stdout.write(self.style.SUCCESS('Indexes created successfully!'))
            
            # Check if the many-to-many table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'assistants_interaction_used_contexts'
                );
            """)
            m2m_table_exists = cursor.fetchone()[0]
            
            if m2m_table_exists:
                self.stdout.write(self.style.SUCCESS('The assistants_interaction_used_contexts table exists!'))
            else:
                self.stdout.write(self.style.WARNING('The assistants_interaction_used_contexts table does NOT exist!'))
                
                # Create the many-to-many table
                self.stdout.write('Creating assistants_interaction_used_contexts table...')
                try:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS "assistants_interaction_used_contexts" (
                            "id" serial NOT NULL PRIMARY KEY,
                            "interaction_id" integer NOT NULL,
                            "communitycontext_id" integer NOT NULL,
                            CONSTRAINT "assistants_interaction_used_contexts_interaction_id_communitycontext_id_key" UNIQUE ("interaction_id", "communitycontext_id"),
                            CONSTRAINT "assistants_interaction_used_contexts_interaction_id_fkey" FOREIGN KEY ("interaction_id") REFERENCES "assistants_interaction" ("id") DEFERRABLE INITIALLY DEFERRED
                        );
                    """)
                    self.stdout.write(self.style.SUCCESS('Table created successfully!'))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error creating table: {e}'))
                    self.stdout.write('Trying to continue...')
                
        self.stdout.write(self.style.SUCCESS('Table check and fix completed successfully!'))
