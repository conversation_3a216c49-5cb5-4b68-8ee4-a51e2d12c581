/**
 * Responsive Chat Tables CSS
 * Improves table rendering in chat bubbles
 */

/* Table wrapper to enable horizontal scrolling */
.table-wrapper {
  width: 100%;
  overflow-x: auto;
  margin: 0.75rem 0;
  border-radius: 0.5rem;
  position: relative;
  -webkit-overflow-scrolling: touch;
}

/* Base table styling */
.message-content table {
  width: 100% !important;
  margin: 0 !important;
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  font-size: 0.9rem !important;
  overflow: hidden !important;
  table-layout: auto !important; /* Changed from fixed to auto for better content fitting */
}

/* Table header styling */
.message-content table th {
  background-color: rgba(0, 0, 0, 0.05) !important;
  font-weight: 600 !important;
  text-align: left !important;
  padding: 0.75rem !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  white-space: nowrap !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 1 !important;
}

/* Table cell styling */
.message-content table td {
  padding: 0.75rem !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  vertical-align: top !important;
  word-break: normal !important; /* Changed from break-word to normal for better readability */
  min-width: 100px !important; /* Minimum width for cells */
}

/* Zebra striping for better readability */
.message-content table tr:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.02) !important;
}

/* Hover effect for rows */
.message-content table tr:hover {
  background-color: rgba(0, 0, 0, 0.05) !important;
}

/* Ensure tables don't break the chat bubble layout */
.message-content table {
  max-width: 100% !important;
  box-sizing: border-box !important;
  table-layout: auto !important;
}

/* Handle tables without explicit structure */
.message-content table:not([border]) {
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

/* Handle tables without headers */
.message-content table:not(:has(th)) tr:first-child td {
  background-color: rgba(0, 0, 0, 0.05) !important;
  font-weight: 500 !important;
}

/* Ensure content in cells doesn't overflow */
.message-content table td > *,
.message-content table th > * {
  max-width: 100% !important;
  overflow-wrap: break-word !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
}

/* Handle cells with long content */
.message-content table td,
.message-content table th {
  max-width: 300px !important;
  overflow-wrap: break-word !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
}

/* Fix for tables with layout issues */
.message-content > table,
.message-content > p > table {
  display: table !important;
  width: 100% !important;
}

/* Responsive adjustments for different screen sizes */
@media (max-width: 992px) {
  .message-content table {
    font-size: 0.85rem !important;
  }

  .message-content table th,
  .message-content table td {
    padding: 0.6rem !important;
  }
}

@media (max-width: 768px) {
  .table-wrapper {
    margin: 0.5rem 0;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.4rem;
  }

  .message-content table {
    font-size: 0.8rem !important;
    border: none !important;
  }

  .message-content table th,
  .message-content table td {
    padding: 0.5rem !important;
    min-width: 80px !important;
  }
}

@media (max-width: 576px) {
  .message-content table {
    font-size: 0.75rem !important;
  }

  .message-content table th,
  .message-content table td {
    padding: 0.4rem !important;
    min-width: 70px !important;
  }
}

/* Dark mode support */
[data-theme="dark"] .message-content table {
  border-color: rgba(255, 255, 255, 0.1) !important;
}

[data-theme="dark"] .message-content table th {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

[data-theme="dark"] .message-content table td {
  border-color: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

[data-theme="dark"] .message-content table tr:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.03) !important;
}

[data-theme="dark"] .message-content table tr:hover {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

/* Scrollbar styling for table wrapper */
.table-wrapper::-webkit-scrollbar {
  height: 6px;
  background-color: rgba(0, 0, 0, 0.05);
}

.table-wrapper::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

[data-theme="dark"] .table-wrapper::-webkit-scrollbar {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .table-wrapper::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Indicator for scrollable tables */
.table-wrapper::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 20px;
  background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.05));
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.table-wrapper.scrollable::after {
  opacity: 1;
}

[data-theme="dark"] .table-wrapper::after {
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.05));
}

/* Handle contact information tables specifically */
.message-content table.contact-info,
.message-content table:has(td:contains("CHAT")),
.message-content table:has(td:contains("CORPORATE OFFICE")),
.message-content table:has(td:contains("MEDIA & PRESS")) {
  border: none !important;
  background: transparent !important;
}

.message-content table.contact-info td,
.message-content table:has(td:contains("CHAT")) td,
.message-content table:has(td:contains("CORPORATE OFFICE")) td,
.message-content table:has(td:contains("MEDIA & PRESS")) td {
  border: none !important;
  padding: 0.5rem 1rem !important;
  vertical-align: top !important;
}

/* Fix for tables without proper structure */
.message-content table:not(:has(tbody)) {
  display: block !important;
  width: 100% !important;
}

.message-content table:not(:has(tbody)) tr {
  display: flex !important;
  flex-wrap: wrap !important;
  margin-bottom: 0.5rem !important;
}

.message-content table:not(:has(tbody)) td {
  flex: 1 1 200px !important;
  min-width: 0 !important;
}

/* Fix for nested tables */
.message-content table table {
  width: 100% !important;
  margin: 0 !important;
  border: none !important;
}
