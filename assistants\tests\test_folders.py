import uuid
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model

from accounts.models import Company
from assistants.models import Assistant, AssistantFolder

User = get_user_model()

class AssistantFolderModelTests(TestCase):
    """Tests for the AssistantFolder model."""

    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='foldertest',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create test company
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.user
        )

    def test_assistant_folder_creation(self):
        """Test creating an assistant folder."""
        folder = AssistantFolder.objects.create(
            name='Test Folder',
            company=self.company
        )

        self.assertEqual(folder.name, 'Test Folder')
        self.assertEqual(folder.company, self.company)
        self.assertEqual(folder.order, 0)  # Default order

    def test_assistant_folder_unique_constraint(self):
        """Test that a company cannot have two folders with the same name."""
        # Create first folder
        AssistantFolder.objects.create(
            name='Test Folder',
            company=self.company
        )

        # Try to create another folder with the same name
        with self.assertRaises(Exception):
            AssistantFolder.objects.create(
                name='Test Folder',
                company=self.company
            )

    def test_assistant_folder_ordering(self):
        """Test that folders are ordered by order field, then name."""
        # Create folders with different orders
        folder1 = AssistantFolder.objects.create(
            name='B Folder',  # Name starts with B
            company=self.company,
            order=2
        )
        folder2 = AssistantFolder.objects.create(
            name='A Folder',  # Name starts with A
            company=self.company,
            order=1
        )
        folder3 = AssistantFolder.objects.create(
            name='C Folder',  # Name starts with C
            company=self.company,
            order=1  # Same order as folder2
        )

        # Get ordered folders
        folders = AssistantFolder.objects.filter(company=self.company).order_by('order', 'name')

        # Check order
        self.assertEqual(folders[0], folder2)  # Order 1, name A
        self.assertEqual(folders[1], folder3)  # Order 1, name C
        self.assertEqual(folders[2], folder1)  # Order 2, name B

    def test_assistant_in_folder(self):
        """Test adding an assistant to a folder."""
        # Create folder
        folder = AssistantFolder.objects.create(
            name='Test Folder',
            company=self.company
        )

        # Create assistant in folder
        assistant = Assistant.objects.create(
            name='Test Assistant',
            company=self.company,
            assistant_type='general',
            folder=folder
        )

        # Check assistant is in folder
        self.assertEqual(assistant.folder, folder)

        # Check folder has assistant
        self.assertEqual(folder.assistants.count(), 1)
        self.assertEqual(folder.assistants.first(), assistant)


class AssistantFolderViewTests(TestCase):
    """Tests for the assistant folder views."""

    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='folderviewtest',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create test company
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.user
        )

        # Create test assistant
        self.assistant = Assistant.objects.create(
            name='Test Assistant',
            company=self.company,
            assistant_type='general'
        )

        # Create test client and login
        self.client = Client()
        self.client.login(username='folderviewtest', password='testpass123')

    def test_folder_create(self):
        """Test creating a folder."""
        response = self.client.post(
            reverse('assistants:folder_create', args=[self.company.id]),
            {'name': 'New Test Folder'}
        )

        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], 'success')

        # Check folder was created
        folder = AssistantFolder.objects.get(name='New Test Folder', company=self.company)
        self.assertEqual(folder.company, self.company)

    def test_folder_edit(self):
        """Test editing a folder."""
        # Create folder
        folder = AssistantFolder.objects.create(
            name='Folder to Edit',
            company=self.company
        )

        response = self.client.post(
            reverse('assistants:folder_edit', args=[folder.id]),
            {'name': 'Edited Folder Name'}
        )

        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], 'success')

        # Check folder was edited
        folder.refresh_from_db()
        self.assertEqual(folder.name, 'Edited Folder Name')

    def test_folder_delete(self):
        """Test deleting a folder."""
        # Create folder
        folder = AssistantFolder.objects.create(
            name='Folder to Delete',
            company=self.company
        )

        # Add assistant to folder
        self.assistant.folder = folder
        self.assistant.save()

        response = self.client.post(
            reverse('assistants:folder_delete', args=[folder.id])
        )

        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], 'success')

        # Check folder was deleted
        self.assertFalse(AssistantFolder.objects.filter(id=folder.id).exists())

        # Check assistant folder is now null
        self.assistant.refresh_from_db()
        self.assertIsNone(self.assistant.folder)

    def test_assign_assistant_folder(self):
        """Test assigning an assistant to a folder."""
        # Create folder
        folder = AssistantFolder.objects.create(
            name='Assignment Folder',
            company=self.company
        )

        response = self.client.post(
            reverse('assistants:assign_folder', args=[self.assistant.id]),
            {'folder_id': folder.id}
        )

        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], 'success')

        # Check assistant was assigned to folder
        self.assistant.refresh_from_db()
        self.assertEqual(self.assistant.folder, folder)

    def test_remove_assistant_from_folder(self):
        """Test removing an assistant from a folder."""
        # Create folder and assign assistant
        folder = AssistantFolder.objects.create(
            name='Removal Folder',
            company=self.company
        )
        self.assistant.folder = folder
        self.assistant.save()

        response = self.client.post(
            reverse('assistants:assign_folder', args=[self.assistant.id]),
            {'folder_id': ''}  # Empty folder_id means remove from folder
        )

        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], 'success')

        # Check assistant was removed from folder
        self.assistant.refresh_from_db()
        self.assertIsNone(self.assistant.folder)

    def test_permission_checks(self):
        """Test that only authorized users can manage folders."""
        # Create another user
        other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create another company
        other_company = Company.objects.create(
            name='Other Company',
            owner=other_user
        )

        # Login as other user
        self.client.logout()
        self.client.login(username='otheruser', password='testpass123')

        # Try to create folder in first company
        response = self.client.post(
            reverse('assistants:folder_create', args=[self.company.id]),
            {'name': 'Unauthorized Folder'}
        )

        # Check response (should be forbidden)
        self.assertEqual(response.status_code, 403)

        # Try to edit folder in first company
        folder = AssistantFolder.objects.create(
            name='Protected Folder',
            company=self.company
        )

        response = self.client.post(
            reverse('assistants:folder_edit', args=[folder.id]),
            {'name': 'Hacked Folder'}
        )

        # Check response (should be forbidden)
        self.assertEqual(response.status_code, 403)

        # Try to delete folder in first company
        response = self.client.post(
            reverse('assistants:folder_delete', args=[folder.id])
        )

        # Check response (should be forbidden)
        self.assertEqual(response.status_code, 403)
