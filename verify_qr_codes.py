#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to verify that QR codes are being generated correctly.
This script will check the QR codes for all companies and compare them to the standard QR code.
"""

import os
import sys
import django
from PIL import Image
import numpy as np

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from accounts.models import Company

def compare_images(img1_path, img2_path):
    """
    Compare two images and return the similarity score.

    Args:
        img1_path (str): Path to the first image
        img2_path (str): Path to the second image

    Returns:
        float: Similarity score between 0 and 1
    """
    try:
        # Open images
        img1 = Image.open(img1_path).convert('RGB')
        img2 = Image.open(img2_path).convert('RGB')

        # Resize to the same size for comparison
        size = (min(img1.width, img2.width), min(img1.height, img2.height))
        img1 = img1.resize(size)
        img2 = img2.resize(size)

        # Convert to numpy arrays
        arr1 = np.array(img1)
        arr2 = np.array(img2)

        # Calculate similarity (normalized by the maximum possible difference)
        max_diff = 255.0 * 3.0 * arr1.shape[0] * arr1.shape[1]  # 3 channels, 255 max diff per channel
        diff = np.sum(np.abs(arr1.astype(float) - arr2.astype(float)))
        similarity = 1.0 - (diff / max_diff)

        return similarity
    except Exception as e:
        print(f"Error comparing images: {e}")
        return 0.0

def verify_company_qr_codes():
    """
    Verify that QR codes for all companies are being generated correctly.

    Returns:
        bool: True if all QR codes are valid, False otherwise
    """
    try:
        # Get all companies
        companies = Company.objects.all()
        print(f"Verifying QR codes for {companies.count()} companies")

        # Check if standard QR code exists
        standard_qr_path = "standard_qr_code.png"
        if not os.path.exists(standard_qr_path):
            print(f"Standard QR code not found: {standard_qr_path}")
            print("Generating standard QR code...")

            # Import and run the standard QR code generation script
            from test_standard_qr import test_standard_qr_code
            test_standard_qr_code()

            if not os.path.exists(standard_qr_path):
                print(f"Failed to generate standard QR code: {standard_qr_path}")
                return False

        valid_count = 0
        invalid_count = 0

        for company in companies:
            print(f"\nCompany: {company.name} (ID: {company.id})")

            if not company.qr_code:
                print(f"❌ Company has no QR code")
                invalid_count += 1
                continue

            qr_path = company.qr_code.path
            if not os.path.exists(qr_path):
                print(f"❌ QR code file not found: {qr_path}")
                invalid_count += 1
                continue

            print(f"QR code: {qr_path}")

            # Compare with standard QR code
            similarity = compare_images(standard_qr_path, qr_path)
            print(f"Similarity to standard QR code: {similarity:.2%}")

            # QR codes will not be identical due to different encoded data,
            # but they should have a reasonable similarity in structure
            if similarity > 0.6:  # 60% similarity threshold - lowered because different URLs create different patterns
                print(f"✅ QR code is valid (similar to standard)")
                valid_count += 1
            else:
                print(f"❌ QR code differs significantly from standard")
                invalid_count += 1

        print(f"\nSummary: {valid_count} valid QR codes, {invalid_count} invalid QR codes")
        return invalid_count == 0

    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    success = verify_company_qr_codes()
    sys.exit(0 if success else 1)
