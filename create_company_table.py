import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

# SQL to create the accounts_company table
sql = """
CREATE TABLE IF NOT EXISTS "accounts_company" (
    "id" serial NOT NULL PRIMARY KEY,
    "name" varchar(200) NOT NULL,
    "slug" varchar(200) NOT NULL UNIQUE,
    "entity_type" varchar(20) NOT NULL,
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
    "qr_code" varchar(100) NULL,
    "tier" varchar(10) NOT NULL,
    "is_featured" boolean NOT NULL,
    "is_active" boolean NOT NULL,
    "tier_expiry_date" timestamp with time zone NULL,
    "featured_expiry_date" timestamp with time zone NULL,
    "tier_change_pending" boolean NOT NULL DEFAULT false,
    "featured_request_pending" boolean NOT NULL DEFAULT false,
    "requested_tier" varchar(10) NULL,
    "requested_tier_duration" integer NULL,
    "requested_featured_duration" integer NULL,
    "owner_id" integer NOT NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# Execute the SQL
try:
    with connection.cursor() as cursor:
        print("Creating accounts_company table...")
        cursor.execute(sql)
    
    print("Company table created successfully!")
except Exception as e:
    print(f"Error creating table: {e}")
