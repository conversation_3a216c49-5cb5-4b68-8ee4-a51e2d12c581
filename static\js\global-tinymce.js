/**
 * Global TinyMCE Initialization
 * This script automatically initializes TinyMCE on all textareas with the class 'tinymce-editor'
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeGlobalTinyMCE();
});

// Also initialize when the window is fully loaded (for dynamic content)
window.addEventListener('load', function() {
    initializeGlobalTinyMCE();
});

// Function to initialize TinyMCE on all textareas with class 'tinymce-editor'
function initializeGlobalTinyMCE() {
    console.log("Initializing Global TinyMCE");

    // Check if TinyMCE is loaded
    if (typeof tinymce === 'undefined') {
        console.error("TinyMCE not loaded, attempting to load it");
        loadTinyMCEScript();
        return;
    }

    // Get all textareas with the class 'tinymce-editor'
    const textareas = document.querySelectorAll('textarea.tinymce-editor');
    console.log(`Found ${textareas.length} textareas to initialize with TinyMCE`);

    if (textareas.length === 0) {
        return; // No textareas to initialize
    }

    // Create a selector string for all textareas
    const selectorString = 'textarea.tinymce-editor';

    // Initialize TinyMCE with the settings from TINYMCE_DEFAULT_CONFIG
    tinymce.init({
        selector: selectorString,
        height: 360,
        width: 'auto',
        menubar: true,
        skin: 'oxide-dark', // Use dark skin
        content_css: [
            '/static/css/tinymce-dark-theme.css', // Our custom CSS for the content
            'dark' // Use TinyMCE's built-in dark content CSS
        ],
        plugins: 'advlist autolink lists link image charmap print preview anchor ' +
                'searchreplace visualblocks code fullscreen ' +
                'insertdatetime media table paste code help wordcount imagetools ' +
                'emoticons hr pagebreak nonbreaking toc textpattern codesample ' +
                'quickbars directionality visualchars template save importcss',
        toolbar1: 'formatselect fontselect fontsizeselect styleselect | ' +
                 'bold italic underline strikethrough subscript superscript | forecolor backcolor | ' +
                 'alignleft aligncenter alignright alignjustify | ' +
                 'bullist numlist outdent indent | ltr rtl',
        toolbar2: 'undo redo | cut copy paste pastetext | searchreplace | ' +
                 'link unlink image media table emoticons charmap | ' +
                 'hr pagebreak nonbreaking template | removeformat code | fullscreen preview | help',
        toolbar3: 'insertdatetime | visualchars visualblocks | codesample blockquote cite | save print',
        // Hide HTML elements in the edit interface
        hidden_input: false,
        element_format: 'html',
        entity_encoding: 'raw',
        convert_fonts_to_spans: false,
        verify_html: false,
        visual: false,
        content_css: false,
        content_style: `
            body { font-family:Helvetica,Arial,sans-serif; font-size:14px; }
            img { max-width: 100%; height: auto; }
            img.img-fluid { max-width: 100%; height: auto; }
            img.float-left { float: left; margin-right: 1rem; margin-bottom: 0.5rem; }
            img.float-right { float: right; margin-left: 1rem; margin-bottom: 0.5rem; }
            img.mx-auto.d-block { display: block; margin-left: auto; margin-right: auto; }
            figure.image { display: inline-block; margin: 0; }
            figure.image figcaption { font-size: 0.8em; color: #555; text-align: center; }
            table { border-collapse: collapse; width: 100%; }
            table td, table th { border: 1px solid #ddd; padding: 8px; }
            blockquote { border-left: 3px solid #ccc; margin-left: 1.5em; padding-left: 1em; }
            pre { background-color: #f5f5f5; padding: 1em; border-radius: 3px; }
            .mce-content-body [data-mce-selected="inline-boundary"] { background-color: #b4d7ff; }
        `,
        // Add 500-word limit with warning
        max_chars: 2500, // Approximately 500 words (average 5 chars per word)
        setup: function(editor) {
            editor.on('init', function() {
                var maxChars = editor.settings.max_chars;
                if (maxChars) {
                    var content = editor.getContent({format: 'text'});
                    var remainingChars = maxChars - content.length;

                    // Create character count display
                    var countDisplay = document.createElement('div');
                    countDisplay.className = 'mce-char-count';
                    countDisplay.style.position = 'absolute';
                    countDisplay.style.bottom = '0';
                    countDisplay.style.right = '3px';
                    countDisplay.style.padding = '3px 8px';
                    countDisplay.style.color = remainingChars < 0 ? 'red' : '#333';
                    countDisplay.style.fontSize = '12px';
                    countDisplay.style.background = '#f8f8f8';
                    countDisplay.style.borderTop = '1px solid #ddd';
                    countDisplay.style.borderLeft = '1px solid #ddd';
                    countDisplay.style.borderTopLeftRadius = '3px';
                    countDisplay.style.zIndex = '100';
                    countDisplay.innerHTML = 'Words: ' + Math.round(content.length / 5) + ' / 500';

                    // Add to editor
                    var editorContainer = editor.getContainer();
                    editorContainer.style.position = 'relative';
                    editorContainer.appendChild(countDisplay);

                    // Update on content change
                    editor.on('keyup change', function() {
                        var content = editor.getContent({format: 'text'});
                        var words = Math.round(content.length / 5);
                        var remainingWords = 500 - words;

                        countDisplay.innerHTML = 'Words: ' + words + ' / 500';
                        countDisplay.style.color = remainingWords < 0 ? 'red' : '#333';

                        // Show warning if over limit
                        if (remainingWords < 0 && !editor.wordLimitWarningShown) {
                            editor.wordLimitWarningShown = true;
                            editor.notificationManager.open({
                                text: 'You have exceeded the 500-word limit. Your content may be truncated when saved.',
                                type: 'warning',
                                timeout: 5000
                            });
                        } else if (remainingWords >= 0) {
                            editor.wordLimitWarningShown = false;
                        }
                    });
                }

                console.log(`TinyMCE initialized for ${editor.id}`);

                // Force visibility of the editor container
                var editorContainer = editor.getContainer();
                if (editorContainer) {
                    editorContainer.style.visibility = 'visible';
                    editorContainer.style.display = 'block';
                }
            });

            editor.on('change', function() {
                editor.save();
            });
        },
        // Enhanced image upload handling
        image_advtab: true,
        image_title: true,
        automatic_uploads: true,
        images_reuse_filename: true,
        paste_data_images: true,
        file_picker_types: 'image',
        // Enable image resizing
        image_dimensions: true,
        image_caption: true,
        image_class_list: [
            {title: 'None', value: ''},
            {title: 'Responsive', value: 'img-fluid'},
            {title: 'Left Aligned', value: 'float-left mr-3'},
            {title: 'Right Aligned', value: 'float-right ml-3'},
            {title: 'Centered', value: 'mx-auto d-block'}
        ],
        // Enable drag and resize of images
        resize_img_proportional: true,
        object_resizing: 'img,table',
        resize: true,
        // Add debug information to image upload
        images_upload_handler: function (blobInfo, success, failure, progress) {
            console.log("TinyMCE image upload handler called");

            var xhr, formData;
            xhr = new XMLHttpRequest();
            xhr.withCredentials = false;

            // Set up progress handler
            xhr.upload.onprogress = function (e) {
                progress(e.loaded / e.total * 100);
            };

            // Set up completion handler
            xhr.onload = function() {
                var json;

                if (xhr.status === 403) {
                    failure('HTTP Error: ' + xhr.status, { remove: true });
                    return;
                }

                if (xhr.status < 200 || xhr.status >= 300) {
                    console.error("TinyMCE upload error: HTTP Error", xhr.status, xhr.responseText);
                    failure('HTTP Error: ' + xhr.status);
                    return;
                }

                try {
                    json = JSON.parse(xhr.responseText);
                } catch (e) {
                    console.error("TinyMCE upload error: Invalid JSON", xhr.responseText);
                    failure('Invalid JSON: ' + xhr.responseText);
                    return;
                }

                if (!json || typeof json.location != 'string') {
                    console.error("TinyMCE upload error: Invalid response", json);
                    failure('Invalid response: ' + xhr.responseText);
                    return;
                }

                console.log("TinyMCE upload success: " + json.location);
                success(json.location);
            };

            // Set up error handler
            xhr.onerror = function () {
                console.error("TinyMCE upload error: Network error");
                failure('Image upload failed due to a network error');
            };

            // Prepare form data
            formData = new FormData();
            formData.append('file', blobInfo.blob(), blobInfo.filename());

            // Get CSRF token if available
            var csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                           document.querySelector('input[name="csrfmiddlewaretoken"]')?.value;
            console.log("CSRF Token found:", csrfToken);

            // Send request - must open connection before setting headers
            xhr.open('POST', '/assistants/tinymce/upload/');

            // Always set the CSRF token header if found
            if (csrfToken) {
                xhr.setRequestHeader('X-CSRFToken', csrfToken);
                console.log("Set X-CSRFToken header:", csrfToken);
            } else {
                console.warn("No CSRF token found in the page");
            }

            xhr.send(formData);

            console.log("TinyMCE upload request sent");
        },
        // Legacy file picker for browsers that don't support direct uploads
        file_picker_callback: function (callback, value, meta) {
            if (meta.filetype == 'image') {
                console.log("TinyMCE file picker callback called");
                var input = document.createElement('input');
                input.setAttribute('type', 'file');
                input.setAttribute('accept', 'image/*');
                input.onchange = function () {
                    var file = this.files[0];
                    console.log("File selected:", file.name, file.size, file.type);

                    // For modern browsers, use the images_upload_handler
                    if (window.FormData && window.FileReader) {
                        var blobInfo = {
                            blob: function() { return file; },
                            filename: function() { return file.name; }
                        };

                        tinymce.activeEditor.uploadImages(function(success) {
                            console.log("Upload result:", success);
                        });

                        // Still provide base64 fallback for immediate display
                        var reader = new FileReader();
                        reader.onload = function () {
                            callback(reader.result, {
                                alt: file.name
                            });
                        };
                        reader.readAsDataURL(file);
                    } else {
                        // Fallback for older browsers
                        var reader = new FileReader();
                        reader.onload = function () {
                            callback(reader.result, {
                                alt: file.name
                            });
                        };
                        reader.readAsDataURL(file);
                    }
                };
                input.click();
            }
        }
    });
}

// Initialize TinyMCE when document is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeGlobalTinyMCE();
});

// Function to load TinyMCE script if not already loaded
function loadTinyMCEScript() {
    // Check if script is already being loaded
    if (document.querySelector('script[src*="tinymce.min.js"]')) {
        console.log("TinyMCE script is already being loaded");
        // Try again after a delay
        setTimeout(initializeGlobalTinyMCE, 1000);
        return;
    }

    // Create script element
    const script = document.createElement('script');
    script.src = '/static/tinymce/tinymce.min.js'; // Adjust path if needed
    script.onload = function() {
        console.log("TinyMCE script loaded successfully");
        initializeGlobalTinyMCE();
    };
    script.onerror = function() {
        console.error("Failed to load TinyMCE script");
        // Try CDN as fallback
        const cdnScript = document.createElement('script');
        cdnScript.src = 'https://cdn.tiny.cloud/1/no-api-key/tinymce/5/tinymce.min.js';
        cdnScript.onload = function() {
            console.log("TinyMCE loaded from CDN");
            initializeGlobalTinyMCE();
        };
        cdnScript.onerror = function() {
            console.error("Failed to load TinyMCE from CDN");
        };
        document.head.appendChild(cdnScript);
    };
    document.head.appendChild(script);
}

// Function to initialize TinyMCE on dynamically added textareas
function initializeDynamicTinyMCE(element) {
    if (!element) return;

    // If element is already a TinyMCE editor, do nothing
    if (element.id && tinymce.get(element.id)) {
        return;
    }

    // Add the tinymce-editor class if not already present
    if (!element.classList.contains('tinymce-editor')) {
        element.classList.add('tinymce-editor');
    }

    // Initialize TinyMCE on this element
    tinymce.init({
        target: element,
        height: 360,
        width: 'auto',
        menubar: true,
        plugins: 'advlist autolink lists link image charmap print preview anchor ' +
                'searchreplace visualblocks code fullscreen ' +
                'insertdatetime media table paste code help wordcount imagetools ' +
                'emoticons hr pagebreak nonbreaking toc textpattern codesample ' +
                'quickbars directionality visualchars template save importcss',
        toolbar1: 'formatselect fontselect fontsizeselect styleselect | ' +
                 'bold italic underline strikethrough subscript superscript | forecolor backcolor | ' +
                 'alignleft aligncenter alignright alignjustify | ' +
                 'bullist numlist outdent indent | ltr rtl',
        toolbar2: 'undo redo | cut copy paste pastetext | searchreplace | ' +
                 'link unlink image media table emoticons charmap | ' +
                 'hr pagebreak nonbreaking template | removeformat code | fullscreen preview | help',
        toolbar3: 'insertdatetime | visualchars visualblocks | codesample blockquote cite | save print',
        // Hide HTML elements in the edit interface
        hidden_input: false,
        element_format: 'html',
        entity_encoding: 'raw',
        convert_fonts_to_spans: false,
        verify_html: false,
        visual: false,
        content_css: false,
        content_style: `
            body { font-family:Helvetica,Arial,sans-serif; font-size:14px; }
            img { max-width: 100%; height: auto; }
            img.img-fluid { max-width: 100%; height: auto; }
            img.float-left { float: left; margin-right: 1rem; margin-bottom: 0.5rem; }
            img.float-right { float: right; margin-left: 1rem; margin-bottom: 0.5rem; }
            img.mx-auto.d-block { display: block; margin-left: auto; margin-right: auto; }
            figure.image { display: inline-block; margin: 0; }
            figure.image figcaption { font-size: 0.8em; color: #555; text-align: center; }
            table { border-collapse: collapse; width: 100%; }
            table td, table th { border: 1px solid #ddd; padding: 8px; }
            blockquote { border-left: 3px solid #ccc; margin-left: 1.5em; padding-left: 1em; }
            pre { background-color: #f5f5f5; padding: 1em; border-radius: 3px; }
            .mce-content-body [data-mce-selected="inline-boundary"] { background-color: #b4d7ff; }
        `,
        // Add 500-word limit with warning
        max_chars: 2500, // Approximately 500 words (average 5 chars per word)
        // Enhanced image upload handling
        image_advtab: true,
        image_title: true,
        automatic_uploads: true,
        images_reuse_filename: true,
        paste_data_images: true,
        file_picker_types: 'image',
        // Enable image resizing
        image_dimensions: true,
        image_caption: true,
        image_class_list: [
            {title: 'None', value: ''},
            {title: 'Responsive', value: 'img-fluid'},
            {title: 'Left Aligned', value: 'float-left mr-3'},
            {title: 'Right Aligned', value: 'float-right ml-3'},
            {title: 'Centered', value: 'mx-auto d-block'}
        ],
        // Enable drag and resize of images
        resize_img_proportional: true,
        object_resizing: 'img,table',
        resize: true,
        images_upload_url: '/assistants/tinymce/upload/',
        setup: function(editor) {
            editor.on('init', function() {
                var maxChars = editor.settings.max_chars;
                if (maxChars) {
                    var content = editor.getContent({format: 'text'});
                    var remainingChars = maxChars - content.length;

                    // Create character count display
                    var countDisplay = document.createElement('div');
                    countDisplay.className = 'mce-char-count';
                    countDisplay.style.position = 'absolute';
                    countDisplay.style.bottom = '0';
                    countDisplay.style.right = '3px';
                    countDisplay.style.padding = '3px 8px';
                    countDisplay.style.color = remainingChars < 0 ? 'red' : '#333';
                    countDisplay.style.fontSize = '12px';
                    countDisplay.style.background = '#f8f8f8';
                    countDisplay.style.borderTop = '1px solid #ddd';
                    countDisplay.style.borderLeft = '1px solid #ddd';
                    countDisplay.style.borderTopLeftRadius = '3px';
                    countDisplay.style.zIndex = '100';
                    countDisplay.innerHTML = 'Words: ' + Math.round(content.length / 5) + ' / 500';

                    // Add to editor
                    var editorContainer = editor.getContainer();
                    editorContainer.style.position = 'relative';
                    editorContainer.appendChild(countDisplay);

                    // Update on content change
                    editor.on('keyup change', function() {
                        var content = editor.getContent({format: 'text'});
                        var words = Math.round(content.length / 5);
                        var remainingWords = 500 - words;

                        countDisplay.innerHTML = 'Words: ' + words + ' / 500';
                        countDisplay.style.color = remainingWords < 0 ? 'red' : '#333';

                        // Show warning if over limit
                        if (remainingWords < 0 && !editor.wordLimitWarningShown) {
                            editor.wordLimitWarningShown = true;
                            editor.notificationManager.open({
                                text: 'You have exceeded the 500-word limit. Your content may be truncated when saved.',
                                type: 'warning',
                                timeout: 5000
                            });
                        } else if (remainingWords >= 0) {
                            editor.wordLimitWarningShown = false;
                        }
                    });
                }

                console.log(`TinyMCE initialized for dynamic element`);

                // Force visibility of the editor container
                var editorContainer = editor.getContainer();
                if (editorContainer) {
                    editorContainer.style.visibility = 'visible';
                    editorContainer.style.display = 'block';
                }
            });

            editor.on('change', function() {
                editor.save();
            });
        }
    });
}

// Make the function available globally
window.initializeDynamicTinyMCE = initializeDynamicTinyMCE;
