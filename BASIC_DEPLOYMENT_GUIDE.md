# Basic Django Deployment Guide for cPanel

This guide provides the simplest approach to deploy your Django project on cPanel and fix CSS loading issues.

## What We've Done

1. **Added Whitenoise to settings.py**:
   - Added Whitenoise middleware
   - Used the simplest Whitenoise storage configuration
   - Set DEBUG to False for production

2. **Kept passenger_wsgi.py simple**:
   - Uses the standard settings.py file
   - No extra environment variables or complexity

## Deployment Steps

### 1. Upload Your Project to cPanel

Upload your entire project to your cPanel account.

### 2. Install Required Packages

In your cPanel Python application, install the required packages:

```bash
pip install -r requirements.txt
```

### 3. Collect Static Files

Run the Django collectstatic command:

```bash
python manage.py collectstatic --noinput
```

### 4. Restart Your Application

In cPanel, go to "Setup Python App" and click "Restart App".

### 5. Test Your Site

Visit your website and check if CSS is loading properly.

## Troubleshooting

### If CSS Still Doesn't Load

1. **Check if Whitenoise is installed**:
   ```bash
   pip show whitenoise
   ```

2. **Verify static files were collected**:
   ```bash
   ls -la staticfiles/css/
   ```

3. **Try running collectstatic again**:
   ```bash
   python manage.py collectstatic --noinput --clear
   ```

4. **Restart your application** after making any changes.

### Fixing Migration Errors

If you encounter migration errors:

1. Run the script to mark problematic migrations as applied:
   ```bash
   python mark_migration_applied.py
   ```

2. Continue with migrations:
   ```bash
   python manage.py migrate
   ```
