"""
Simple script to validate JavaScript files.
"""

import os
import re
import sys

def validate_js_file(file_path):
    """Validate a JavaScript file for basic syntax errors."""
    print(f"Validating {file_path}...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for unbalanced braces
    open_braces = content.count('{')
    close_braces = content.count('}')
    if open_braces != close_braces:
        print(f"  ❌ Unbalanced braces: {open_braces} opening vs {close_braces} closing")
        return False
    
    # Check for unbalanced parentheses
    open_parens = content.count('(')
    close_parens = content.count(')')
    if open_parens != close_parens:
        print(f"  ❌ Unbalanced parentheses: {open_parens} opening vs {close_parens} closing")
        return False
    
    # Check for unbalanced brackets
    open_brackets = content.count('[')
    close_brackets = content.count(']')
    if open_brackets != close_brackets:
        print(f"  ❌ Unbalanced brackets: {open_brackets} opening vs {close_brackets} closing")
        return False
    
    # Check for missing semicolons (simple check)
    lines = content.split('\n')
    for i, line in enumerate(lines):
        line = line.strip()
        if line and not line.endswith('{') and not line.endswith('}') and \
           not line.endswith(';') and not line.endswith(',') and \
           not line.endswith('(') and not line.endswith('[') and \
           not line.endswith(':') and not line.endswith('?') and \
           not line.endswith('*/') and not line.startswith('//') and \
           not line.startswith('/*') and not line.startswith('*') and \
           not line.startswith('import') and not line.startswith('export') and \
           not line.startswith('function') and not line.startswith('class') and \
           not line.startswith('if') and not line.startswith('else') and \
           not line.startswith('for') and not line.startswith('while') and \
           not line.startswith('switch') and not line.startswith('case') and \
           not line.startswith('default') and not line.startswith('return') and \
           not line.startswith('const') and not line.startswith('let') and \
           not line.startswith('var') and not line.startswith('//') and \
           not line.startswith('/*') and not line.startswith('*/') and \
           not line.startswith('*') and not line.startswith('#') and \
           not line.startswith('describe') and not line.startswith('test') and \
           not line.startswith('it') and not line.startswith('beforeEach') and \
           not line.startswith('afterEach') and not line.startswith('expect') and \
           not line.startswith('jest') and not line.startswith('module.exports') and \
           not line.startswith('require') and not line.startswith('try') and \
           not line.startswith('catch') and not line.startswith('finally') and \
           not line.startswith('async') and not line.startswith('await') and \
           not line.startswith('throw') and not line.startswith('break') and \
           not line.startswith('continue') and not line.startswith('do') and \
           not line.startswith('with') and not line.startswith('yield') and \
           not line.startswith('delete') and not line.startswith('typeof') and \
           not line.startswith('instanceof') and not line.startswith('in') and \
           not line.startswith('new') and not line.startswith('this') and \
           not line.startswith('super') and not line.startswith('debugger') and \
           not line.startswith('void') and not line.startswith('null') and \
           not line.startswith('undefined') and not line.startswith('true') and \
           not line.startswith('false') and not line.startswith('NaN') and \
           not line.startswith('Infinity') and not line.startswith('console') and \
           not line.startswith('window') and not line.startswith('document') and \
           not line.startswith('global') and not line.startswith('process') and \
           not line.startswith('__dirname') and not line.startswith('__filename') and \
           not line.startswith('module') and not line.startswith('exports') and \
           not line.startswith('require') and not line.startswith('import') and \
           not line.startswith('export') and not line.startswith('default') and \
           not line.startswith('from') and not line.startswith('as') and \
           not line.startswith('of') and not line.startswith('get') and \
           not line.startswith('set') and not line.startswith('static') and \
           not line.startswith('public') and not line.startswith('private') and \
           not line.startswith('protected') and not line.startswith('abstract') and \
           not line.startswith('readonly') and not line.startswith('interface') and \
           not line.startswith('type') and not line.startswith('namespace') and \
           not line.startswith('declare') and not line.startswith('enum') and \
           not line.startswith('implements') and not line.startswith('extends') and \
           not line.startswith('constructor') and not line.startswith('get') and \
           not line.startswith('set') and not line.startswith('static') and \
           not line.startswith('async') and not line.startswith('await') and \
           not line.startswith('yield') and not line.startswith('*') and \
           not line.startswith('...') and not line.startswith('=>') and \
           not line.startswith('(') and not line.startswith('[') and \
           not line.startswith('{') and not line.startswith('}') and \
           not line.startswith(')') and not line.startswith(']') and \
           not line.startswith('</') and not line.startswith('/>') and \
           not line.startswith('<') and not line.startswith('>') and \
           not line.startswith('`') and not line.startswith('\'') and \
           not line.startswith('"') and not line.startswith('`') and \
           not line.startswith('//') and not line.startswith('/*') and \
           not line.startswith('*/') and not line.startswith('*') and \
           not line.startswith('#') and not line.startswith('') and \
           not line.endswith('\\'):
            print(f"  ⚠️ Line {i+1} might be missing a semicolon: {line}")
    
    print(f"  ✅ {file_path} looks valid")
    return True

def main():
    """Main function."""
    js_files = [
        'static/js/tests/folder-functionality.test.js',
        'static/js/tests/assistant-folder-functionality.test.js',
        'static/js/tests/mocks/favorites-functionality.js',
        'static/js/tests/test-runner.js'
    ]
    
    all_valid = True
    for js_file in js_files:
        if not validate_js_file(js_file):
            all_valid = False
    
    if all_valid:
        print("\n✅ All JavaScript files look valid!")
        return 0
    else:
        print("\n❌ Some JavaScript files have issues!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
