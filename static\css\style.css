/* Custom Variables */
:root {
    --primary-light: #e6f0ff;
    --primary-dark: #0056b3;
    --surface-hover: #f8f9fa;
    --surface-active: #e9ecef;
}

/* General Styles */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

/* Navigation */
.navbar {
    box-shadow: 0 2px 8px rgba(0,0,0,.08);
    padding: 0.75rem 1rem;
    background-color: #ffffff;
    border-bottom: 1px solid rgba(0,0,0,.05);
    transition: all 0.3s ease;
}

/* Nav icon container - matches assistant logo styling */
.nav-icon-container {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    border-radius: 0.25rem;
    width: 24px;
    height: 24px;
    margin-right: 6px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.nav-icon-container i {
    font-size: 16px;
    color: #0d6efd;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
    color: #333;
    padding: 0.5rem 0;
}

.navbar-brand img {
    transition: transform 0.2s;
    height: 32px;
    width: auto;
}

.navbar-brand:hover img {
    transform: scale(1.05);
}

.navbar-toggler {
    border: none;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.navbar-toggler:focus {
    box-shadow: none;
    outline: none;
}

.navbar-toggler-icon {
    width: 1.25em;
    height: 1.25em;
}

.navbar .nav-link {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    color: #555;
    transition: all 0.2s ease;
}

.navbar .nav-link:hover {
    color: #0d6efd;
}

/* Improve navbar on mobile */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background-color: #ffffff;
        border-radius: 0.5rem;
        box-shadow: 0 4px 12px rgba(0,0,0,.08);
        padding: 1rem;
        margin-top: 0.5rem;
        max-height: 80vh;
        overflow-y: auto;
    }

    .navbar .nav-link {
        padding: 0.75rem 1rem;
        border-radius: 0.375rem;
    }

    .navbar .nav-link:hover {
        background-color: rgba(13, 110, 253, 0.05);
    }

    /* Improve dropdown menus on mobile */
    .navbar .dropdown-menu {
        border: none;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        padding: 0.5rem;
        margin-top: 0.25rem;
        max-height: 50vh;
        overflow-y: auto;
    }

    .navbar .dropdown-item {
        padding: 0.75rem 1rem;
        border-radius: 0.375rem;
    }

    .navbar .dropdown-item:hover {
        background-color: rgba(13, 110, 253, 0.05);
    }
}

/* Cards and Shadows */
.card {
    transition: transform 0.2s, box-shadow 0.2s;
}

/*
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.1)!important;
}
*/

/* Buttons */
.btn {
    transition: all 0.2s;
}

.btn:active {
    transform: scale(0.98);
}

.btn-icon {
    width: 38px;
    height: 38px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

/* Forms */
.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.15);
}

.form-floating label {
    color: #6c757d;
}

/* Messages */
.message-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1050;
    max-width: 350px;
}

.alert {
    margin-bottom: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,.075);
}

/* Impersonation Alert */
#impersonation-alert {
    position: relative;
    z-index: 1040;
    border-left: 4px solid #fd7e14;
    box-shadow: 0 4px 6px rgba(0,0,0,.1);
    font-weight: 500;
}

/* Feature Icons */
.feature-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.feature-icon i {
    font-size: 2rem;
}

/* Avatar Groups */
.avatar-group {
    display: flex;
    margin-left: -0.5rem;
}

.avatar-group .avatar {
    margin-left: -0.5rem;
    border: 2px solid #fff;
    transition: transform 0.2s;
}

.avatar-group .avatar:hover {
    transform: translateY(-3px);
    z-index: 1;
}

/* Team Member Cards */
.member-card {
    position: relative;
    overflow: hidden;
}

.member-card .member-actions {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    opacity: 0;
    transition: opacity 0.2s;
}

.member-card:hover .member-actions {
    opacity: 1;
}

/* Activity Timeline */
.activity-timeline {
    position: relative;
    padding-left: 2rem;
}

.activity-timeline::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--surface-active);
}

.activity-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.activity-item::before {
    content: '';
    position: absolute;
    left: -1.5rem;
    top: 0.25rem;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    background: var(--primary);
    border: 2px solid #fff;
}

/* Company Switcher */
.company-switcher {
    max-height: 400px;
    overflow-y: auto;
}

.company-switcher .dropdown-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
}

.company-switcher .company-logo {
    width: 32px;
    height: 32px;
    margin-right: 0.75rem;
}

/* Dashboard Metrics */
.metric-card {
    border-left: 4px solid transparent;
}

.metric-card.metric-primary { border-left-color: var(--primary); }
.metric-card.metric-success { border-left-color: var(--success); }
.metric-card.metric-info { border-left-color: var(--info); }
.metric-card.metric-warning { border-left-color: var(--warning); }

/* Error Pages */
.error-page {
    background: linear-gradient(135deg, var(--primary-light) 0%, #fff 100%);
}

/* Footer Styles */
footer.footer {
    background-color: #f8f9fa;
    border-top: 1px solid rgba(0,0,0,.05);
    padding: 2.5rem 0 1.5rem;
    margin-top: 3rem;
    color: #6c757d;
    font-size: 0.95rem;
}

@media (max-width: 768px) {
    footer.footer {
        padding: 1.25rem 0 0.75rem;
        margin-top: 2rem;
    }
}

footer.footer h5 {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 1.25rem;
    color: #333;
}

footer.footer h6 {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 1rem;
    color: #495057;
}

footer.footer .list-unstyled li {
    margin-bottom: 0.5rem;
}

footer.footer .list-unstyled a {
    color: #6c757d;
    text-decoration: none;
    transition: color 0.2s ease;
}

footer.footer .list-unstyled a:hover {
    color: #0d6efd;
    text-decoration: underline;
}

footer.footer .text-muted {
    color: #adb5bd !important;
}

footer.footer .social-icons {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

footer.footer .social-icons a {
    color: #6c757d;
    font-size: 1.25rem;
    transition: all 0.2s ease;
}

footer.footer .social-icons a:hover {
    color: #0d6efd;
    transform: translateY(-2px);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .message-container {
        left: 1rem;
        right: 1rem;
    }

    .navbar-brand img {
        height: 28px;
    }

    /* Home page hero section adjustments */
    .hero-section {
        padding-top: 2rem !important;
        padding-bottom: 2rem !important;
    }

    .hero-section .container {
        padding-top: 1.5rem !important;
        padding-bottom: 1.5rem !important;
    }

    .hero-section .display-4 {
        font-size: 2rem !important;
        line-height: 1.2 !important;
    }

    .hero-section .lead {
        font-size: 1.1rem !important;
        margin-bottom: 1.5rem !important;
    }

    .feature-icon {
        width: 3rem;
        height: 3rem;
    }

    .feature-icon i {
        font-size: 1.5rem;
    }

    /* Footer responsive improvements */
    footer.footer {
        padding: 1.5rem 0 1rem;
        text-align: center;
    }

    /* Mobile footer accordion styles */
    footer.footer .footer-accordion {
        margin-bottom: 1rem;
    }

    footer.footer .social-icons-mobile {
        display: flex;
        justify-content: center;
        gap: 1.5rem;
        margin-top: 0.5rem;
    }

    footer.footer .social-icons-mobile a {
        color: #6c757d;
        font-size: 1.25rem;
        transition: all 0.2s ease;
    }

    footer.footer .social-icons-mobile a:hover {
        color: #0d6efd;
        transform: translateY(-2px);
    }

    /* Accordion styling */
    footer.footer .accordion {
        --bs-accordion-border-width: 0;
        --bs-accordion-inner-border-radius: 0;
        --bs-accordion-btn-padding-y: 0.75rem;
        --bs-accordion-btn-padding-x: 1rem;
        --bs-accordion-active-bg: transparent;
        --bs-accordion-active-color: #0d6efd;
    }

    footer.footer .accordion-item {
        border-bottom: 1px solid rgba(0,0,0,.05);
        background-color: transparent;
    }

    footer.footer .accordion-button {
        font-weight: 600;
        font-size: 0.95rem;
        color: #495057;
        box-shadow: none;
        padding: 0.5rem 0;
        background-color: transparent;
    }

    footer.footer .accordion-button:not(.collapsed) {
        color: #0d6efd;
    }

    footer.footer .accordion-button::after {
        width: 1rem;
        height: 1rem;
        background-size: 1rem;
    }

    footer.footer .accordion-button:focus {
        box-shadow: none;
        border-color: transparent;
    }

    footer.footer .accordion-body {
        padding: 0.25rem 0 0.5rem 1rem;
    }

    footer.footer .accordion-body ul {
        margin-bottom: 0;
    }

    footer.footer .accordion-body li {
        margin-bottom: 0.35rem;
    }

    footer.footer .accordion-body a {
        font-size: 0.9rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Removed duplicate .fade-in { line */
.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Star Rating Styles - Removed */


/* Print Styles */
@media print {
    .navbar, .footer, .btn-group {
        display: none !important;
    }

    .card {
        break-inside: avoid;
        border: none;
        box-shadow: none !important;
    }
}

/* === Assistant Directory Styles === */
.list-group-item {
    transition: box-shadow 0.2s ease-in-out, background-color 0.2s ease-in-out; /* Added background-color transition */
    padding: 1rem;
    position: relative; /* Needed for absolute positioning of child badge */
    /* overflow: hidden; */ /* Temporarily remove to see if badge appears */
    border: 1px solid rgba(0, 0, 0, 0.08); /* Lighter, slightly transparent border */
    /* Apply a clearer, more layered shadow */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.06);
    margin-bottom: 1rem; /* Add some space between items */
    background-color: rgba(255, 255, 255, 0.7); /* Semi-transparent white background */
    backdrop-filter: blur(5px); /* Add blur for frosted glass effect */
    border-radius: 0.375rem; /* Add slight rounding */
}
.list-group-item:hover {
    /* Apply an even stronger shadow on hover for a "lifted" effect */
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.07) !important;
    background-color: rgba(255, 255, 255, 0.85); /* Slightly less transparent on hover */
}
.tag-badge {
    font-size: 0.75em;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
    padding: 0.2em 0.5em;
}
.list-group-item .like-button {
    z-index: 10;
    cursor: pointer;
}
.item-description {
    font-size: 0.9em;
    line-height: 1.4;
    text-align: left;
}
.logo-placeholder {
    width: 180px; /* Large square size */
    height: 180px; /* Equal to width for perfect square */
    min-width: 180px; /* Ensure minimum width */
    min-height: 180px; /* Ensure minimum height */
    max-width: 180px; /* Ensure maximum width */
    max-height: 180px; /* Ensure maximum height */
    flex-shrink: 0;
    border-radius: 0.25rem; /* Very slight rounding for square appearance */
    aspect-ratio: 1/1; /* Enforce 1:1 aspect ratio */
}

/* Hover effect for clickable directory item area */
a.directory-item-link-wrapper,
a.directory-item-link-wrapper:hover { /* Use generic class, ensure no underline */
    text-decoration: none !important;
    color: inherit;
}
.directory-item-link-wrapper > .row:hover { /* Apply background hover to the row inside the link */
    background-color: rgba(0, 0, 0, 0.03);
}
/* Style the main item name (h5.h6) within the link */
.directory-item-link-wrapper h5.h6 {
    font-weight: 600; /* Make it bolder */
    font-size: 1.05em; /* Make it slightly larger */
    margin-bottom: 0.2rem !important;
    color: #0d6efd; /* Use a distinct color (Bootstrap primary blue) */
    transition: color 0.15s ease-in-out; /* Smooth color transition */
    text-decoration: none !important; /* Ensure no underline */
}
/* Style the secondary text (e.g., company name under assistant, or similar) */
.directory-item-link-wrapper p.small { /* Target p.small within the link */
    color: #6c757d; /* Default muted color */
    transition: color 0.15s ease-in-out; /* Smooth color transition */
    text-decoration: none !important; /* Ensure no underline */
}
/* Change main name color on hover */
.directory-item-link-wrapper:hover h5.h6 {
    color: #000000; /* Change to black for high contrast hover */
    text-decoration: none !important; /* FORCE no underline on hover */
}
/* Change secondary text color on hover */
.directory-item-link-wrapper:hover p.small {
    color: #343a40; /* Use a standard dark grey for hover */
    text-decoration: none !important; /* Ensure no underline */
}
/* Star Rating Specifics (already present but grouped here) */
.star-rating { display: inline-flex; align-items: center; font-size: 0.9em; }
.star-rating .bi-star-fill { color: #f5c518; } /* Gold for filled */
.star-rating .bi-star { color: #ddd; } /* Light grey for empty */
.star-rating .rating-count { margin-left: 0.4em; font-size: 0.9em; color: #6c757d; }
/* Interactive Stars */
.interactive-star-rating { cursor: default; } /* Container isn't clickable */
.interactive-star-rating .star-btn {
    background: none;
    border: none;
    padding: 0 0.1em; /* Small spacing */
    margin: 0;
    cursor: pointer;
    color: #ddd; /* Default empty */
    transition: color 0.2s ease-in-out, transform 0.1s ease;
    font-size: 1.1em; /* Slightly larger stars */
}
.interactive-star-rating .star-btn:hover {
    color: #f8d96c; /* Lighter gold on hover */
    transform: scale(1.1);
}
.interactive-star-rating .star-btn.filled {
    color: #f5c518; /* Gold for filled */
}
.interactive-star-rating .star-btn.user-rated {
    color: #f5c518; /* Keep gold if user rated */
}
/* Style for when rating is submitted */
.interactive-star-rating.rating-submitted .star-btn {
    cursor: not-allowed; /* Indicate no further action */
    opacity: 0.7;
}
/* === End Assistant Directory Styles === */


/* === Chat Interface Styles === */

.chat-container {
    max-width: 1200px !important; /* Increased from 800px to 1200px */
    margin: 2rem auto !important;
    display: flex !important;
    flex-direction: column !important;
    padding-bottom: 1.25rem !important;
    min-height: 300px !important;
    background: #ffffff !important; /* Solid white background */
    border-radius: 1.25rem !important;
    border: 1px solid #e0e0e0 !important; /* Solid light gray border */
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important; /* Soft shadow */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.chat-box {
    border: none !important;
    padding: 1.75rem !important;
    margin-bottom: 1rem !important;
    background: #f9f9f9 !important; /* Solid very light gray background */
    border-radius: 1.25rem !important;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.05) !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 1.5rem !important;
    position: relative !important;
    min-height: 300px !important; /* Minimum height to ensure it takes up space */
    transition: all 0.3s ease !important;
    border: 1px solid #f0f0f0 !important; /* Solid very light gray border */
}

/* Custom Scrollbar for Chat Box (WebKit browsers) - Keep for potential future use, but not active now */
.chat-box::-webkit-scrollbar {
    width: 8px; /* Width of the scrollbar */
}

.chat-box::-webkit-scrollbar-track {
    background: #f1f1f1; /* Track color */
    border-radius: 10px;
}

.chat-box::-webkit-scrollbar-thumb {
    background: #c1c1c1; /* Handle color */
    border-radius: 10px;
}

.chat-box::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8; /* Handle color on hover */
}

.message {
    padding: 1.35rem 1.85rem;
    border-radius: 1.35rem;
    line-height: 1.8;
    box-shadow: 0 6px 16px rgba(0,0,0,0.06);
    word-wrap: break-word;
    overflow-wrap: break-word;
    display: inline-block;
    max-width: 85%;
    margin: 0.85rem 0;
    font-size: 1.05rem;
    letter-spacing: 0.3px;
    font-weight: 400;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    border: 1px solid rgba(0,0,0,0.04);
}

.user-message {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    color: #2c3e50;
    text-align: left;
    margin-left: auto;
    border-bottom-right-radius: 0.35rem;
    border: none !important;
    text-shadow: none !important;
}

.assistant-message {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    color: #1a365d;
    text-align: left;
    margin-right: auto;
    border-bottom-left-radius: 0.35rem;
    border: none !important;
    text-shadow: none !important;
}

/* Input Area */
#chat-form.input-group {
    border-radius: 1.5rem;
    padding: 0.5rem;
    background: rgba(255,255,255,0.9);
    box-shadow: 0 8px 32px rgba(31,38,135,0.1);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(255,255,255,0.18);
    align-items: center;
    margin: 1rem;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

#message-input.form-control {
    border: 1px solid rgba(0,0,0,0.08);
    background-color: rgba(255,255,255,0.9);
    border-radius: 1.25rem;
    padding: 1rem 1.5rem;
    resize: none;
    height: auto;
    min-height: 50px;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0,0,0,0.03);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}
#message-input.form-control:focus {
    box-shadow: 0 8px 24px rgba(0,0,0,0.05);
    border-color: rgba(0,123,255,0.2);
    background-color: #ffffff;
    transform: translateY(-2px);
    outline: none;
}

#send-button.btn-primary {
    border-radius: 50%;
    width: 45px;
    height: 45px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    background: linear-gradient(145deg, #333333, #000000);
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}
#send-button.btn-primary i { /* Example if using icon tag */
    font-size: 1.2rem;
}


/* Suggestions */
.suggestions {
    padding: 0.5rem 0;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.suggestions button {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 1rem; /* Pill shape */
    padding: 0.3rem 0.8rem;
    font-size: 0.85rem;
    color: #000000;
    cursor: pointer;
    transition: all 0.3s ease;
}

.suggestions button:hover {
    background-color: #000000;
    color: #ffffff;
    border-color: #000000;
}

/* Interaction Rating Stars (inside assistant message) - Reverted */
.interaction-rating {
    border-top: 1px solid rgba(0, 0, 0, 0.08); /* Restore border */
    margin-top: 0.75rem; /* Restore margin */
    padding-top: 0.5rem; /* Restore padding */
    opacity: 0.6; /* Slightly faded */
    transition: opacity 0.2s;
    font-size: 0.9em; /* Make rating section slightly smaller */
}
.message.assistant-message:hover .interaction-rating {
    opacity: 1; /* Show clearly on hover */
}

.interaction-rating .rating-stars .star {
    cursor: pointer;
    color: #b0b3b8; /* Default grey */
    font-size: 1.3em; /* Make stars slightly larger */
    transition: color 0.2s, transform 0.1s;
    padding: 0 1px;
}
.interaction-rating .rating-stars .star:hover {
    transform: scale(1.2);
    color: #f5c518; /* Gold on hover */
}
/* Style for stars when rated */
.interaction-rating .rating-stars.rated .star {
    cursor: default; /* Not clickable after rating */
}
.interaction-rating .rating-stars.rated .star.selected {
    color: #f5c518; /* Gold for selected */
}
.interaction-rating .rating-stars.rated .star:not(.selected) {
    color: #e0e0e0; /* Lighter grey for non-selected after rating */
}
.interaction-rating .rating-feedback {
    font-size: 0.9em; /* Match parent */
    color: var(--success);
}

/* Thinking Indicator */
.message.thinking {
    color: #6c757d;
    font-style: italic;
    background-color: #f8f9fa; /* Use a neutral background */
    box-shadow: none; /* Remove shadow while thinking */
}
.message.thinking .spinner-border {
    width: 0.8rem;
    height: 0.8rem;
    margin-right: 0.5rem;
    vertical-align: text-bottom;
}

/* Sidebar and Layout Adjustments */
.sidebar {
    border-right: 1px solid rgba(224, 224, 224, 0.5);
    padding: 1.5rem;
    height: 100vh;
    position: sticky;
    top: 0;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1001;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* Main content wrapper */
.main-content {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    transition: margin-left 0.3s ease-in-out;
}

/* Header alignment with sidebar */
.customer-support-header {
    position: sticky;
    top: 0;
    z-index: 100;
    background: rgba(255, 255, 255, 0.98);
    padding: 1.25rem;
    border-bottom: 1px solid rgba(224, 224, 224, 0.5);
    margin-bottom: 1.25rem;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Sidebar toggle button */
#sidebar-toggle-btn {
    position: fixed;
    right: 1.25rem;
    bottom: 1.25rem;
    z-index: 1002;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(224, 224, 224, 0.6);
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 6px 16px rgba(0,0,0,0.12);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    color: #333;
    margin-bottom: 70px;
}

#sidebar-toggle-btn:hover {
    background: #f8f9fa;
    transform: scale(1.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: 0;
        top: 0;
        width: 280px;
        transform: translateX(-100%);
        z-index: 1001;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        box-shadow: 2px 0 20px rgba(0,0,0,0.1);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0 !important;
    }

    .customer-support-header {
        padding-left: 4rem;
    }

    /* Overlay when sidebar is open */
    .sidebar-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: transparent;
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
        z-index: 1000;
        transition: opacity 0.3s ease;
    }

    .sidebar-overlay.show {
        display: block;
    }
}

/* --- End Chat Interface Styles --- */


/* === General Purpose Assistant Minimalist Styles === */

.general-assistant-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.general-assistant-header {
    max-width: 1200px; /* Increased from 768px to 1200px */
    margin: 1.5rem auto;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, #ffffff 0%, var(--primary-light) 100%);
    border-radius: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.08);
}

.general-assistant-header h1 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--primary-dark);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.general-assistant-header p {
    font-size: 1.1rem;
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 0;
}

.general-chat-container {
    max-width: 1200px !important; /* Increased from 768px to 1200px */
    margin-left: auto !important;
    margin-right: auto !important;
    display: flex !important;
    flex-direction: column !important;
    min-height: 300px !important; /* Minimum height to ensure it takes up space */
    padding-bottom: 6rem !important; /* Increased padding to prevent overlap with sticky input */
    background: #ffffff !important; /* Solid white background */
    border-radius: 1.25rem !important;
    border: 1px solid #e0e0e0 !important; /* Solid light gray border */
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important; /* Soft shadow */
}

.general-chat-box {
    border: none !important; /* Remove border */
    padding: 1.75rem !important; /* Adjust padding */
    flex-grow: 1 !important; /* Allow chat box to expand */
    display: flex !important;
    flex-direction: column !important;
    min-height: 300px !important; /* Minimum height to ensure it takes up space */
    background: #f9f9f9 !important; /* Solid very light gray background */
    border-radius: 1.25rem !important;
    transition: all 0.3s ease !important;
    border: 1px solid #f0f0f0 !important; /* Solid very light gray border */
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.05) !important;
}

/* Center initial greeting when chat box is otherwise empty */
.general-chat-box .initial-greeting:only-child {
    margin-top: auto; /* Push to center vertically */
    margin-bottom: auto;
    text-align: center;
    font-size: 1.5rem; /* Larger text */
    color: #6c757d; /* Muted color */
    background-color: transparent; /* No background */
    box-shadow: none; /* No shadow */
    max-width: 100%; /* Allow full width */
    align-self: center; /* Center horizontally */
}

/* Style subsequent messages normally */
.general-chat-box .message:not(.initial-greeting) {
    margin: 0.5rem 0;
}

.general-input-area {
    max-width: 1150px; /* Increased from 720px to 1150px (slightly less than container to maintain margins) */
    margin: 0 auto 0 auto;
    border: none;
    border-radius: 25px;
    background-color: #ffffff;
    padding: 0.75rem;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    position: sticky;
    bottom: 1rem;
    z-index: 10;
    backdrop-filter: blur(8px);
    flex-shrink: 0;
    transition: all 0.2s ease;
}

.general-input-area #message-input {
    background-color: #f8f9fa;
    border: 1px solid #c0c0c0;
    border-radius: 20px;
    padding: 0.75rem 1.25rem;
    min-height: 45px;
    font-size: 0.95rem;
    transition: all 0.2s ease;
}
.general-input-area #message-input:focus {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    border-color: #a0a0a0;
    background-color: #ffffff;
}

.general-input-area .general-send-btn {
    background-color: #000000;
    color: #ffffff;
    border: none;
    border-radius: 20px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-left: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.general-input-area .general-send-btn:hover {
    background-color: #333333;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.general-input-area .general-send-btn:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
/* Style for Reset button in general layout */
.general-reset-area {
    max-width: 720px;
    margin: 1rem auto;
    text-align: center;
}

.general-reset-area button {
    background-color: #f8f9fa;
    color: #000000;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    padding: 0.5rem 1.25rem;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.general-reset-area button:hover {
    background-color: #000000;
    color: #ffffff;
    border-color: #000000;
    transform: translateY(-1px);
}

.general-reset-area button:active {
    transform: translateY(1px);
    background-color: #dee2e6;
}

/* --- End General Purpose Assistant Styles --- */

/* === Tier Badge Styles (Moved from template) === */
/* .list-group-item rule with position:relative is already defined above */

.tier-badge {
    position: absolute;
    top: 0.75rem; /* Adjust distance from top */
    left: 0.75rem; /* Position to the left */
    /* transform: rotate(45deg); */ /* Rotation removed */
    padding: 0.25em 0.6em; /* Adjust padding */
    font-size: 0.75em;
    font-weight: bold;
    text-align: center;
    /* box-shadow: 0 2px 3px rgba(0,0,0,0.2); */ /* Optional shadow */
    z-index: 5; /* Ensure it's above other card content */
    border-radius: 0.25rem; /* Add border radius */
    border: none; /* Remove border or adjust as needed */
    color: #fff; /* Default text color to white for better contrast on dark badges */
}

.tier-featured { background-color: #198754; color: #fff; } /* Green for Featured */
.tier-gold { background-color: #ffc107; color: #333; } /* Keep Gold yellow */
.tier-silver { background-color: #adb5bd; color: #fff; } /* Use Bootstrap's secondary/gray color */
.tier-bronze { background-color: #cd7f32; color: #fff; }
/* === End Tier Badge Styles === */


/* === Folder Filter Buttons (from my_favorites.html) === */
.folder-filter-buttons .btn {
    margin-right: 0.25rem; /* Keep spacing for btn-link */
    margin-bottom: 0.25rem;
    font-weight: normal; /* Default state */
    color: var(--bs-link-color); /* Default link color */
    text-decoration: none; /* Ensure no underline by default */
    border: none; /* Remove default button border */
    background: none; /* Remove default button background */
    padding: 0.25rem 0.5rem; /* Adjust padding as needed */
}
.folder-filter-buttons .btn:hover {
    text-decoration: underline; /* Underline on hover */
    background-color: transparent; /* Ensure no background on hover */
    color: var(--bs-link-hover-color); /* Use standard link hover color */
}
.folder-filter-buttons .btn.active {
    font-weight: bold; /* Make active link bold */
    color: var(--bs-primary); /* Use primary color for active link */
    text-decoration: underline; /* Underline active link */
    background-color: transparent; /* Ensure no background when active */
}
/* === End Folder Filter Buttons === */

/* Assistant Chat Initial Display */
#initial-display-area .assistant-profile-pic {
  display: block; /* Make it a block element for centering */
  margin-left: auto;
  margin-right: auto;
  width: 190px; /* Doubled size */
  height: 190px; /* Doubled size */
  border-radius: 50%; /* Make it circular */
  object-fit: cover; /* Ensure image covers the area nicely */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); /* Add a nice shadow */
  border: 4px solid rgba(255, 255, 255, 0.2); /* Subtle border */
  margin-bottom: 1.5rem; /* Add more space below the avatar */
}

#initial-display-area .assistant-profile-pic-placeholder {
  display: block; /* Make it a block element for centering */
  margin-left: auto;
  margin-right: auto;
  font-size: 190px; /* Doubled size */
  width: 190px; /* Doubled size */
  height: 190px; /* Doubled size */
  line-height: 190px; /* Center icon vertically */
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); /* Add a nice shadow */
  margin-bottom: 1.5rem; /* Add more space below the avatar */
}

#initial-display-area .welcome-text-small {
  font-size: 2.5rem; /* Make significantly larger, similar to reference */
  font-weight: bold; /* Keep bold */
  color: inherit; /* Use default text color (not muted) */
  margin-top: 0.5rem; /* Add some space below the image/icon */
}

/* Mobile styles for the avatar and welcome text */
@media (max-width: 768px) {
  #initial-display-area .assistant-profile-pic {
    width: 120px !important; /* Smaller on mobile but still prominent */
    height: 120px !important;
    margin-bottom: 1rem !important;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15) !important;
    border: 3px solid rgba(255, 255, 255, 0.2) !important;
  }

  #initial-display-area .assistant-profile-pic-placeholder {
    font-size: 120px !important;
    width: 120px !important;
    height: 120px !important;
    line-height: 120px !important;
    margin-bottom: 1rem !important;
  }

  #initial-display-area .welcome-text-small {
    font-size: 1.8rem !important; /* Smaller on mobile */
    margin-top: 0.25rem !important;
  }
}

/* Ensure the initial greeting div itself doesn't have conflicting styles */
#initial-display-area .message.initial-greeting {
    background-color: transparent; /* Remove default message background */
    border: none; /* Remove default message border */
    padding: 0; /* Remove default message padding */
    text-align: center; /* Center the text */
}

/* Registration Link Highlighting based on Role */
.reg-link-item {
    padding-top: 0.75rem; /* Add some padding */
    padding-bottom: 0.75rem;
    border-left: 4px solid transparent; /* Default border */
    transition: background-color 0.2s ease-in-out; /* Smooth transition */
}

/* Example Role Styles - Adjust names and colors as needed */
.reg-link-item.role-admin {
    background-color: #fff8e1; /* Light yellow background for Admin */
    border-left-color: #ffc107; /* Yellow border */
}

.reg-link-item.role-member {
    background-color: #e3f2fd; /* Light blue background for Member */
    border-left-color: #2196f3; /* Blue border */
}

.reg-link-item.role-viewer { /* Example for another role */
    background-color: #e8f5e9; /* Light green background for Viewer */
    border-left-color: #4caf50; /* Green border */
}

.reg-link-item.role-none {
    background-color: #fce4ec; /* Light pink/red background if no role assigned */
    border-left-color: #f44336; /* Red border */
}

/* Optional: Hover effect */
.reg-link-item:hover {
    background-color: #f5f5f5; /* Slightly darker background on hover */
}

/* Chat Avatars */
.chat-avatar {
    width: 36px; /* Slightly larger size */
    height: 36px;
    border-radius: 50%; /* Make it circular */
    object-fit: cover; /* Ensure image covers the area well */
    flex-shrink: 0; /* Prevent avatar from shrinking */
    border: 1px solid rgba(0, 0, 0, 0.1); /* Add a subtle border */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* Add a subtle shadow */
}

.chat-avatar-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #666;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Message container styling */
.message {
    display: flex;
    width: 100%;
    margin-bottom: 1rem;
}

/* User message alignment */
.user-message {
    justify-content: flex-end;
}

/* Assistant message alignment */
.assistant-message {
    justify-content: flex-start;
}

/* Message content styling */
.message-content {
    max-width: 75%;
    border-radius: 1rem;
    padding: 1rem 1.25rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* User message content styling */
.user-message .message-content,
div.message.user-message.mb-3 span.message-content,
div.message.user-message span.message-content,
.message.user-message .message-content,
.user-message .message-content,
span.message-content.user-message {
    /* background: linear-gradient(135deg, #f8f9fa 0%, #e6e6e6 100%); */ /* Old background */
    /* background: #6c757d; */ /* Medium Grey background */
    background: #3b7dd8 !important; /* Solid blue background */
    background-color: #3b7dd8 !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    background-position: initial !important;
    background-size: initial !important;
    background-repeat: initial !important;
    background-origin: initial !important;
    background-clip: initial !important;
    background-attachment: initial !important;
    /* color: #333333; */ /* Old text color */
    color: #ffffff !important; /* White text color */
    border-bottom-right-radius: 0.4rem;
    border: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    font-weight: 500; /* Slightly bolder for better readability */
    font-size: 1.05rem; /* Increased font size */
}

/* Assistant message content styling */
.assistant-message .message-content,
div.message.assistant-message.mb-3 span.message-content,
div.message.assistant-message span.message-content,
.message.assistant-message .message-content,
.assistant-message .message-content,
span.message-content.assistant-message {
    /* background: linear-gradient(135deg, #ffffff 0%, var(--primary-light) 100%); */ /* Original background */
    /* background: linear-gradient(135deg, var(--primary-light) 0%, #F3F8FF 100%); */ /* Previous Gradient */
    /* background: linear-gradient(135deg, var(--primary-light) 0%, rgba(230, 240, 255, 0.5) 100%); */ /* New Gradient: Light Blue to Half Light Blue */
    background: #ffffff !important; /* Solid white background */
    background-color: #ffffff !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    background-position: initial !important;
    background-size: initial !important;
    background-repeat: initial !important;
    background-origin: initial !important;
    background-clip: initial !important;
    background-attachment: initial !important;
    /* color: #333333; */ /* Original text color */
    color: #333333 !important; /* Dark gray text for better readability */
    border-bottom-left-radius: 0.4rem;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
    font-weight: 500; /* Slightly bolder for better readability */
    font-size: 1.05rem; /* Increased font size */
    line-height: 1.6;
    letter-spacing: 0.01em;
}

/* --- Sidebar Toggle Styles --- */
#chat-sidebar {
    transition: width 0.3s ease, opacity 0.3s ease, padding 0.3s ease, margin 0.3s ease; /* Smooth transition */
    overflow-x: hidden; /* Hide horizontal overflow during transition */
    flex-shrink: 0; /* Prevent sidebar from shrinking unexpectedly */
}

body.sidebar-collapsed #chat-sidebar {
    width: 0 !important; /* Collapse width */
    padding-left: 0 !important; /* Remove padding */
    padding-right: 0 !important;
    margin-left: 0 !important; /* Remove margin */
    margin-right: 0 !important;
    opacity: 0; /* Fade out */
    border-right: none !important; /* Remove border */
    /* display: none; /* Alternative, but less smooth transition */
}

/* Adjust main content column when sidebar is collapsed */
/* Bootstrap's flexbox grid should handle this, but explicitly setting width ensures it */
body.sidebar-collapsed .row > .col-md-9 {
    width: 100%;
    max-width: 100%;
    flex: 0 0 100%;
    transition: width 0.3s ease, max-width 0.3s ease, flex 0.3s ease; /* Add transition */
}

/* Ensure the main content column transitions back smoothly */
.row > .col-md-9 {
     transition: width 0.3s ease, max-width 0.3s ease, flex 0.3s ease;
}


/* Adjust toggle button position slightly when collapsed if needed */
/* body.sidebar-collapsed #sidebar-toggle-btn { */
    /* left: 5px; */ /* Example adjustment */
/* } */

/* --- Mobile Sidebar Styles (Below 768px) --- */
@media (max-width: 767.98px) {
    /* Ensure mobile header stays on top */
    .d-md-none.sticky-top {
        z-index: 1051; /* Higher than sidebar and overlay */
    }

    /* Add top padding to main content container to avoid overlap with sticky mobile header */
    .container-fluid.mt-md-4 { /* Target the container that has margin only on md+ */
        padding-top: 70px; /* Increased padding to clear sticky header */
        margin-top: 0 !important; /* Remove the default top margin on mobile */
    }

    /* Hide sidebar by default on mobile */
    #chat-sidebar {
        position: fixed; /* Position fixed to overlay */
        top: 0; /* Start from the very top */
        left: 0;
        height: 100vh; /* Full height */
        width: 280px; /* Fixed width when shown */
        max-width: 80%; /* Max width */
        background-color: #fff; /* Ensure background */
        z-index: 1045; /* Below modals but above content */
        box-shadow: 0 0 15px rgba(0,0,0,0.2);
        transform: translateX(-100%); /* Start off-screen */
        transition: transform 0.3s ease-in-out; /* Smooth slide */
        padding: 1rem; /* Original padding */
        border-right: 1px solid #e0e0e0; /* Add border back */
        opacity: 1; /* Ensure visible when shown */
        margin: 0 !important; /* Reset margins */
        overflow-y: auto; /* Allow scrolling within sidebar */
        padding-top: 60px; /* Add padding to push content below sticky header (adjust as needed) */
    }

    /* When sidebar is SHOWN on mobile (body does NOT have .sidebar-collapsed) */
    body:not(.sidebar-collapsed) #chat-sidebar {
        transform: translateX(0); /* Slide in */
    }

    /* Main content takes full width by default on mobile */
    .row > .col-md-9 {
        width: 100% !important;
        max-width: 100% !important;
        flex: 0 0 100% !important;
        transition: none; /* No transition needed for main content on mobile */
    }

    /* Toggle button position might need adjustment */
    #sidebar-toggle-btn {
        /* Keep position absolute, top/start/m-2 should be okay */
        z-index: 1050; /* Ensure button is above sidebar */
    }

    /* Optional: Add an overlay */
    #sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: transparent;
        z-index: 1040; /* Below sidebar, above content */
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
    }
    body:not(.sidebar-collapsed) #sidebar-overlay {
        opacity: 1;
        visibility: visible;
    }

}

/* --- Desktop Sidebar Styles (768px and above) --- */
@media (min-width: 768px) {
    /* Default desktop state (sidebar visible) */
    #chat-sidebar {
        transition: width 0.3s ease, opacity 0.3s ease, padding 0.3s ease, margin 0.3s ease;
        overflow-x: hidden;
        flex-shrink: 0;
        /* Reset mobile fixed positioning */
        position: static;
        height: auto;
        transform: none;
        box-shadow: none;
        width: 25%; /* Corresponds to col-md-3 */
        max-width: none;
        opacity: 1;
        visibility: visible;
        padding: 0 1.5rem 0 0; /* Original padding approx */
        border-right: 1px solid #e0e0e0;
    }

    /* When sidebar is HIDDEN on desktop */
    body.sidebar-collapsed #chat-sidebar {
        width: 0 !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        opacity: 0;
        border-right: none !important;
        overflow: hidden; /* Hide content cleanly */
    }

    /* Adjust main content column when sidebar is collapsed on desktop */
    body.sidebar-collapsed .row > .col-md-9 {
        width: 100%;
        max-width: 100%;
        flex: 0 0 100%;
        transition: width 0.3s ease, max-width 0.3s ease, flex 0.3s ease;
    }

    /* Ensure the main content column transitions back smoothly on desktop */
    .row > .col-md-9 {
         transition: width 0.3s ease, max-width 0.3s ease, flex 0.3s ease;
         width: 75%; /* Default desktop width */
         max-width: 75%;
         flex: 0 0 75%;
    }

    /* Hide overlay on desktop */
    #sidebar-overlay {
        display: none;
    }
}
/* --- End Sidebar Toggle Styles --- */

/* Remove any pseudo-elements that might add gradients */
.message-content::before,
.message-content::after,
span.message-content::before,
span.message-content::after,
div.message.assistant-message.mb-3 span.message-content::before,
div.message.assistant-message.mb-3 span.message-content::after,
div.message.user-message.mb-3 span.message-content::before,
div.message.user-message.mb-3 span.message-content::after {
    display: none !important;
    content: none !important;
    background: none !important;
    background-image: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}
