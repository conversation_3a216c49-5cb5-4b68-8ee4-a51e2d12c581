"""
Script to debug and fix company directory issues.
This script will:
1. Print detailed information about the directory view filtering
2. Check for any issues with the database tables
3. Fix any issues found
"""
import os
import sys
import django
import logging

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from accounts.models import Company, CompanyInformation
from directory.models import CompanyListing, DirectorySettings
from django.db import connection
from django.db.models import Q

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def check_database_tables():
    """Check if all required database tables exist."""
    logger.info("Checking database tables...")
    
    tables_to_check = [
        'accounts_company',
        'accounts_companyinformation',
        'directory_companylisting',
        'directory_directorysettings'
    ]
    
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
        """)
        existing_tables = [row[0] for row in cursor.fetchall()]
    
    for table in tables_to_check:
        if table in existing_tables:
            logger.info(f"  ✓ Table {table} exists")
        else:
            logger.error(f"  ✗ Table {table} does not exist")
    
    return all(table in existing_tables for table in tables_to_check)

def check_directory_settings():
    """Check directory settings."""
    logger.info("Checking directory settings...")
    
    try:
        settings = DirectorySettings.load()
        logger.info(f"  ✓ Directory settings loaded: {settings}")
        return settings
    except Exception as e:
        logger.error(f"  ✗ Error loading directory settings: {e}")
        return None

def check_company_visibility():
    """Check company visibility in the directory."""
    logger.info("Checking company visibility...")
    
    # Get all companies
    companies = Company.objects.all()
    logger.info(f"  Total companies: {companies.count()}")
    
    # Active companies
    active_companies = companies.filter(is_active=True)
    logger.info(f"  Active companies: {active_companies.count()}")
    
    # Companies with list_in_directory=True
    public_companies = active_companies.filter(info__list_in_directory=True)
    logger.info(f"  Public companies (list_in_directory=True): {public_companies.count()}")
    
    # Companies with CompanyListing
    companies_with_listing = public_companies.filter(listing__isnull=False)
    logger.info(f"  Public companies with listing: {companies_with_listing.count()}")
    
    # Companies with is_listed=True
    visible_companies = companies_with_listing.filter(listing__is_listed=True)
    logger.info(f"  Companies with is_listed=True: {visible_companies.count()}")
    
    # List all companies with their visibility status
    logger.info("\nDetailed company visibility status:")
    for company in companies:
        is_active = company.is_active
        has_info = hasattr(company, 'info') and company.info is not None
        list_in_directory = has_info and company.info.list_in_directory
        has_listing = hasattr(company, 'listing') and company.listing is not None
        is_listed = has_listing and company.listing.is_listed
        
        status = "VISIBLE" if (is_active and list_in_directory and has_listing and is_listed) else "NOT_VISIBLE"
        reasons = []
        
        if not is_active:
            reasons.append("not active")
        if not has_info:
            reasons.append("no CompanyInformation")
        elif not list_in_directory:
            reasons.append("list_in_directory=False")
        if not has_listing:
            reasons.append("no CompanyListing")
        elif not is_listed:
            reasons.append("is_listed=False")
        
        reason_str = f" ({', '.join(reasons)})" if reasons else ""
        logger.info(f"  {company.name}: {status}{reason_str}")

def fix_company_listings():
    """Fix company listings."""
    logger.info("Fixing company listings...")
    
    # Get all companies
    companies = Company.objects.all()
    
    # Track counts
    info_created = 0
    listing_created = 0
    listing_updated = 0
    
    # Process each company
    for company in companies:
        # Ensure CompanyInformation exists
        info, info_created_now = CompanyInformation.objects.get_or_create(
            company=company,
            defaults={
                'mission': '',
                'description': '',
                'website': '',
                'contact_email': '',
                'contact_phone': '',
                'timezone': 'UTC',
                'language': 'en',
                'list_in_directory': True,  # Default to visible
                'address_line1': '',
                'address_line2': '',
                'city': '',
                'postal_code': '',
                'country': '',
                'industry': '',
                'size': ''
            }
        )
        
        if info_created_now:
            info_created += 1
        
        # Ensure CompanyListing exists and is synced with CompanyInformation
        listing, listing_created_now = CompanyListing.objects.get_or_create(
            company=company,
            defaults={
                'is_listed': info.list_in_directory,
                'featured': company.is_featured,
                'description': info.description,
                'website': info.website
            }
        )
        
        if listing_created_now:
            listing_created += 1
        
        # Ensure listing.is_listed matches info.list_in_directory
        if listing.is_listed != info.list_in_directory:
            listing.is_listed = info.list_in_directory
            listing.save()
            listing_updated += 1
    
    logger.info(f"  CompanyInformation records created: {info_created}")
    logger.info(f"  CompanyListing records created: {listing_created}")
    logger.info(f"  CompanyListing records updated: {listing_updated}")

def main():
    """Main function."""
    logger.info("=== Company Directory Debug ===")
    
    # Check database tables
    if not check_database_tables():
        logger.error("Database tables check failed. Exiting.")
        return
    
    # Check directory settings
    settings = check_directory_settings()
    if not settings:
        logger.error("Directory settings check failed. Exiting.")
        return
    
    # Check company visibility
    check_company_visibility()
    
    # Fix company listings
    fix_company_listings()
    
    # Check company visibility again
    logger.info("\nAfter fixing:")
    check_company_visibility()
    
    logger.info("=== Debug Complete ===")

if __name__ == "__main__":
    main()
