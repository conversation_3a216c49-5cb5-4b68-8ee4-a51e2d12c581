{% extends "base/layout.html" %}
{% load static account_tags rating_tags %} {# Load rating_tags #}

{% block title %}My Favorites{% endblock %}

{% block body_class %}bg-light company-directory-page{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="{% static 'css/directory.css' %}">
<link rel="stylesheet" href="{% static 'css/favorites-animations.css' %}">
<link rel="stylesheet" href="{% static 'css/unified-cards-responsive.css' %}">
<link rel="stylesheet" href="{% static 'css/unified-cards-dark-mode.css' %}">
<style>
    /* Override specific styles for favorites page to match directory.css */

    /* Smooth transition for favorite items when removing */
    .favorite-item {
        transition: opacity 0.3s ease, transform 0.3s ease;
    }

    /* Empty state messages */
    .no-favorites-message, .empty-folder-message {
        opacity: 0;
        animation: fadeIn 0.5s ease forwards;
        animation-delay: 0.3s;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* Card styling to match directory.css */
    .list-group-item {
        transition: all 0.3s ease;
        position: relative;
        overflow: visible;
        border-radius: 1rem;
        border: 1px solid rgba(0, 0, 0, 0.08);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15), 0 10px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
        padding: 2rem;
        background-color: rgba(240, 248, 255, 0.9); /* Light blue background */
        min-height: 220px; /* Increased minimum height for all cards */
        height: 100%; /* Make all cards the same height */
        box-sizing: border-box; /* Include padding in the height calculation */
        transform: perspective(1000px) rotateX(2deg); /* Subtle 3D effect */
        backdrop-filter: blur(5px); /* Glass effect */
    }

    .list-group-item:hover {
        transform: perspective(1000px) rotateX(0deg) translateY(-10px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2), 0 15px 15px rgba(0, 0, 0, 0.15);
        border-color: rgba(13, 110, 253, 0.4);
        background-color: rgba(240, 248, 255, 0.95); /* Slightly more opaque on hover */
    }

    /* Logo styling to match directory.css */
    .logo-container {
        height: 120px; /* Slightly smaller than directory.css but larger than original */
        width: 120px;
        min-height: 120px;
        min-width: 120px;
        max-height: 120px;
        max-width: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: white;
        border-radius: 0.25rem; /* Square appearance like directory.css */
        overflow: hidden;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 8px 15px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 0, 0, 0.08);
        position: relative;
        aspect-ratio: 1/1;
        margin: 0 auto;
        z-index: 2;
    }

    .logo-container:hover {
        transform: scale(1.05);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25), 0 10px 20px rgba(0, 0, 0, 0.15);
    }

    .logo-container img {
        max-height: 100%;
        max-width: 100%;
        height: 100%;
        width: 100%;
        object-fit: contain;
        display: block;
        padding: 10px;
        transition: all 0.3s ease;
    }

    .logo-placeholder {
        color: #0d6efd;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        overflow: visible;
        padding: 0;
        background-color: rgba(240, 248, 255, 0.5);
    }

    .logo-placeholder i {
        font-size: 80px;
        line-height: 1;
        display: block;
        transform: scale(1);
        width: 100%;
        height: 100%;
        text-align: center;
        margin: 0;
        padding: 0;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    /* Filter form styling to match directory.css */
    .filter-form {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 1rem;
        border: 1px solid rgba(0, 0, 0, 0.08);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.05);
        margin-bottom: 2.5rem;
        padding: 2rem;
    }

    .filter-form .form-control,
    .filter-form .form-select {
        border-radius: 0.375rem;
        border: 1px solid rgba(0, 0, 0, 0.15);
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
    }

    .filter-form .form-control:focus,
    .filter-form .form-select:focus {
        border-color: rgba(13, 110, 253, 0.4);
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
    }

    .filter-form .btn {
        padding: 0.5rem 1rem;
        font-weight: 500;
        border-radius: 0.375rem;
        transition: all 0.2s ease;
    }

    .filter-form .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Folder filter buttons styling to match directory.css */
    .folder-filter-buttons .btn {
        margin-right: 0.25rem;
        margin-bottom: 0.25rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .folder-filter-buttons .btn.active {
        font-weight: 700;
        color: #0d6efd;
        text-decoration: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .folder-filter-buttons .btn:hover {
        transform: translateY(-2px);
    }

    /* Badge styling to match directory.css */
    .tier-badge {
        position: absolute;
        top: 0;
        left: 0;
        margin: 0.5rem;
        z-index: 10;
        font-size: 0.7em;
        padding: 0.25em 0.5em;
        border-radius: 0.25rem;
        font-weight: normal;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        pointer-events: none;
        max-height: 24px;
    }

    .tier-gold {
        background-color: #ffc107;
        color: #212529;
    }

    .tier-silver {
        background-color: #adb5bd;
        color: #212529;
    }

    .tier-bronze {
        background-color: #cd7f32;
        color: #fff;
    }

    .tag-badge {
        margin-right: 0.25rem;
        margin-bottom: 0.25rem;
        font-weight: 500;
        font-size: 0.8em;
        padding: 0.4em 0.7em;
        border-radius: 0.25rem;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        letter-spacing: 0.02em;
    }

    .tag-badge:hover {
        transform: scale(1.05);
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
    }

    /* Item description styling to match directory.css */
    .item-description {
        line-height: 1.7;
        color: #343a40;
        font-size: 1.05rem;
        font-weight: 400;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        letter-spacing: 0.01em;
        text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
    }

    /* Fix for inline styles with line-clamp */
    [style*="-webkit-line-clamp"] {
        line-clamp: attr(-webkit-line-clamp number);
    }

    /* Rating stars styling to match directory.css */
    .star-rating {
        display: inline-flex;
        align-items: center;
        font-size: 1.2em;
    }

    .star-rating .bi-star-fill {
        color: #ffc107;
        filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
        margin: 0 1px;
    }

    .star-rating .bi-star {
        color: #dee2e6;
        filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
        margin: 0 1px;
    }

    .star-rating .rating-count {
        font-size: 0.9em;
        margin-left: 0.5em;
        color: #495057;
        font-weight: 500;
        text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
    }

    /* Like button styling to match directory.css */
    .like-button {
        transition: all 0.2s ease;
        z-index: 10;
        cursor: pointer;
    }

    .like-button:hover {
        transform: scale(1.1);
    }

    /* Tab styling to match directory.css */
    .nav-tabs .nav-link.active {
        color: #0d6efd;
        background-color: #fff;
        border-color: #dee2e6 #dee2e6 #fff;
        font-weight: 600;
        box-shadow: 0 -4px 8px rgba(0, 0, 0, 0.05);
    }

    .nav-tabs .nav-link {
        margin-bottom: -1px;
        background: 0 0;
        border: 1px solid transparent;
        border-top-left-radius: .5rem;
        border-top-right-radius: .5rem;
        padding: 1rem 1.5rem;
        transition: all 0.2s ease;
    }

    .nav-tabs .nav-link:hover:not(.active) {
        background-color: rgba(13, 110, 253, 0.05);
        border-color: transparent;
    }

    /* Card container styling */
    .card {
        border-radius: 1rem;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        background-color: rgba(240, 248, 255, 0.9);
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        padding: 1rem 1.5rem;
    }

    /* Interactive Stars */
    .interactive-star-rating {
        cursor: default;
    }

    .interactive-star-rating .star-btn {
        background: none;
        border: none;
        padding: 0 0.1em;
        margin: 0;
        cursor: pointer;
        color: #ddd;
        transition: color 0.2s ease-in-out, transform 0.1s ease;
        font-size: 1.1em;
    }

    .interactive-star-rating .star-btn:hover {
        color: #f8d96c;
        transform: scale(1.1);
    }

    .interactive-star-rating .star-btn.filled {
        color: #ffc107;
    }

    .interactive-star-rating .star-btn.user-rated {
        color: #ffc107;
    }

    .interactive-star-rating.rating-submitted .star-btn {
        cursor: not-allowed;
        opacity: 0.7;
    }

    /* Modal styling */
    .modal-content {
        border-radius: 1rem;
        border: none;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        overflow: hidden;
    }

    .modal-header {
        background-color: rgba(240, 248, 255, 0.9);
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    }

    .modal-stars .modal-star-btn {
        font-size: 2rem;
        transition: all 0.2s ease;
    }

    .modal-stars .modal-star-btn:hover {
        transform: scale(1.2);
        color: #ffc107;
    }
/* Folder item layout */
    .folder-item-container {
        display: flex;
        align-items: center;
        width: 100%; /* Ensure it takes full width if needed within its parent */
    }

    .folder-filter-link {
        flex-grow: 1; /* Allows the link to take up available space */
        text-align: left; /* Ensure text aligns to the left */
    }

    .folder-actions-dropdown {
        position: relative; /* For dropdown positioning */
        margin-left: 0.5rem; /* Add some space between name and dots */
    }
</style>
{% endblock %}

{% block content %}
<!-- Rating Modal -->
<div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="ratingModalLabel">Rate Assistant</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Select your rating for <strong id="modalItemName">this assistant</strong>:</p>
        <div class="modal-stars text-center mb-3">
            {% for i in "12345"|make_list %}
                <button type="button" class="modal-star-btn btn btn-link p-0 mx-1 text-secondary" data-rating-value="{{ i }}" style="font-size: 1.75rem; line-height: 1;">
                    <i class="bi bi-star"></i>
                </button>
            {% endfor %}
        </div>
        <div class="alert alert-danger" id="modalErrorMsg" style="display: none;"></div>
        <div class="d-grid">
            <button type="button" id="submitRatingBtn" class="btn btn-primary" disabled>Submit Rating</button>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="container mt-5 mb-5 company-directory-container">
    <h1 class="display-5 mb-4 fw-bold">My Favorites</h1>

    {# Tabs Navigation - Set active class based on context #}
    <ul class="nav nav-tabs mb-4" id="favoritesTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link {% if active_tab == 'assistants' %}active{% endif %} px-4 py-3" id="assistants-tab" data-bs-toggle="tab" data-bs-target="#assistants-panel" type="button" role="tab" aria-controls="assistants-panel" aria-selected="{% if active_tab == 'assistants' %}true{% else %}false{% endif %}">
                <i class="bi bi-robot me-2"></i>Assistants
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link {% if active_tab == 'community_assistants' %}active{% endif %} px-4 py-3" id="community-assistants-tab" data-bs-toggle="tab" data-bs-target="#community-assistants-panel" type="button" role="tab" aria-controls="community-assistants-panel" aria-selected="{% if active_tab == 'community_assistants' %}true{% else %}false{% endif %}">
                <i class="bi bi-people me-2"></i>Community Assistants
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link {% if active_tab == 'companies' %}active{% endif %} px-4 py-3" id="companies-tab" data-bs-toggle="tab" data-bs-target="#companies-panel" type="button" role="tab" aria-controls="companies-panel" aria-selected="{% if active_tab == 'companies' %}true{% else %}false{% endif %}">
                <i class="bi bi-building me-2"></i>Companies
            </button>
        </li>
    </ul>

    {# Tab Content - Set active class based on context #}
    <div class="tab-content" id="favoritesTabContent">
    {# Assistants Panel #}
    <div class="tab-pane fade {% if active_tab == 'assistants' %}show active{% endif %}" id="assistants-panel" role="tabpanel" aria-labelledby="assistants-tab">
        {# Filter Form (Assistant) - Enhanced Style #}
        <form method="get" action="{% url 'directory:my_favorites' %}" class="filter-form">
            <input type="hidden" name="tab" value="assistants">
            <h5 class="mb-3 fw-bold"><i class="bi bi-funnel-fill me-2 text-primary"></i>Filter Assistants</h5>
            <div class="row g-3 mb-3">
                <div class="col-md-6"> {# Name/Description Filter #}
                    <label for="q_name_asst" class="form-label">Search</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" name="q_name" id="q_name_asst" class="form-control" placeholder="Search name or description..." value="{{ q_name|default:'' }}">
                    </div>
                </div>
                <div class="col-md-3"> {# Company Filter #}
                    <label for="q_company_asst" class="form-label">Company</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-building"></i></span>
                        <input type="text" name="q_company" id="q_company_asst" class="form-control" placeholder="Company..." value="{{ q_company|default:'' }}">
                    </div>
                </div>
                <div class="col-md-3"> {# Sort Dropdown #}
                    <label for="sort_by_asst" class="form-label">Sort By</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-sort-down"></i></span>
                        <select name="sort_by" id="sort_by_asst" class="form-select">
                            <option value="date_added" {% if sort_by == 'date_added' or not sort_by %}selected{% endif %}>Date Added</option>
                            <option value="tier" {% if sort_by == 'tier' %}selected{% endif %}>Tier</option>
                            <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Name</option>
                            <option value="rating" {% if sort_by == 'rating' %}selected{% endif %}>Rating</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row g-3">
                <div class="col-md-4"> {# Category Filter #}
                    <label for="q_category_asst" class="form-label">Category</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-tag"></i></span>
                        <input type="text" name="q_category" id="q_category_asst" class="form-control" placeholder="Category..." value="{{ q_category|default:'' }}">
                    </div>
                </div>
                <div class="col-md-4"> {# Tag Filter #}
                    <label for="q_tag_asst" class="form-label">Tag</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-tags"></i></span>
                        <input type="text" name="q_tag" id="q_tag_asst" class="form-control" placeholder="Tag..." value="{{ q_tag|default:'' }}">
                    </div>
                </div>
                <div class="col-md-4"> {# Buttons #}
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex">
                        <button type="submit" class="btn btn-primary me-2 flex-grow-1">
                            <i class="bi bi-filter me-1"></i> Apply Filters
                        </button>
                        <a href="{% url 'directory:my_favorites' %}?tab=assistants" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i>
                        </a>
                    </div>
                </div>
            </div>
        </form>
        {# End Filter Form #}

        {# Folder Filter Buttons (Assistant) #}
        {% with folders=assistant_folders %}
        {% if folders %}
        <div class="folder-filter-buttons mb-4 d-flex flex-wrap align-items-center border-bottom pb-3" role="group" aria-label="Filter by folder">
            <h5 class="me-3 mb-0"><i class="bi bi-folder2-open me-2 text-primary"></i>My Folders:</h5>
            <button class="btn btn-link text-decoration-none p-1 me-3 active" data-folder-id="all">
                <i class="bi bi-grid-fill me-1"></i>Show All
            </button>
            <button class="btn btn-link text-decoration-none p-1 me-3" data-folder-id="uncategorized">
                <i class="bi bi-box me-1"></i>Uncategorized
            </button>
            {% for folder in folders %}
                <div class="d-flex align-items-center me-3 folder-item-container">
                    <button class="btn btn-link text-decoration-none p-1 folder-filter-link me-auto" data-folder-id="{{ folder.id }}">
                        <i class="bi bi-folder me-1"></i>{{ folder.name }}
                    </button>
                    <div class="folder-actions-dropdown position-relative">
                        <button class="btn btn-sm btn-link text-secondary p-0" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="line-height: 1;">
                            <i class="bi bi-three-dots-vertical"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item edit-folder-btn" href="#" data-folder-id="{{ folder.id }}" data-folder-name="{{ folder.name|escapejs }}"><i class="bi bi-pencil-square me-2"></i>Edit</a></li>
                            <li><a class="dropdown-item delete-folder-btn" href="#" data-folder-id="{{ folder.id }}" data-folder-name="{{ folder.name|escapejs }}"><i class="bi bi-trash me-2"></i>Delete</a></li>
                        </ul>
                    </div>
                </div>
            {% endfor %}
            <button type="button" class="btn btn-sm btn-outline-primary ms-auto">
                <i class="bi bi-plus-circle me-1"></i> Add Folder
            </button>
        </div>
        {% endif %}
        {% endwith %}
        {# End Folder Filter Buttons #}

        {# Display Active Filters (Assistant) #}
        {% if active_tab == 'assistants' and q_name or q_company or q_category or q_tag %}
        <div class="alert alert-light border mb-4 mt-2">
            <div class="d-flex align-items-center">
                <i class="bi bi-funnel me-2 text-primary"></i>
                <span class="fw-medium">Filtering by:</span>
                <div class="ms-2">
                    {% if q_name %}<span class="badge bg-primary me-1">Name/Desc: {{ q_name }}</span>{% endif %}
                    {% if q_company %}<span class="badge bg-info text-dark me-1">Company: {{ q_company }}</span>{% endif %}
                    {% if q_category %}<span class="badge bg-secondary me-1">Category: {{ q_category }}</span>{% endif %}
                    {% if q_tag %}<span class="badge bg-light text-dark border me-1">Tag: {{ q_tag }}</span>{% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        {# End Display Active Filters #}

        {# Display Assistants - Uncategorized First #}
        <div id="favorite-assistants-items-container">
        {% with items_in_folders=assistant_items_in_folders items_without_folder=assistant_items_without_folder folders=assistant_folders %}
            {% if folders or items_without_folder %}

                {# Display items without a folder FIRST #}
                <div data-item-group="true" data-folder-id="uncategorized"> {# Wrapper for JS filtering #}
                    {% if items_without_folder %}
                        <h4 class="h5 mt-4 mb-3 border-bottom pb-2"><i class="bi bi-inbox me-2"></i>Uncategorized Assistants</h4>
                        <div class="list-group list-group-uncategorized"> {# Container for uncategorized items #}
                            {% for item in items_without_folder %}
                                 {% if item.assistant %}
                                    {% include "directory/partials/favorite_assistant_item.html" with item=item %}
                                 {% endif %}
                            {% endfor %}
                        </div>
                    {% elif not folders %} {# Only show if no folders exist either and no uncategorized items #}
                         <p class="text-muted no-favorites-message">You haven't saved any favorite assistants yet{% if active_tab == 'assistants' and q_name or q_company or q_category or q_tag %} matching your filters{% endif %}.</p>
                    {% endif %}
                </div>

                {# Loop through folders SECOND #}
                {% for folder_id, folder_data in items_in_folders.items %}
                    {% with folder=folder_data.folder items=folder_data.items %}
                        <div data-item-group="true" data-folder-id="{{ folder.id }}"> {# Wrapper for JS filtering #}
                            <h4 class="h5 mt-4 mb-3 border-bottom pb-2"><i class="bi bi-folder me-2"></i>{{ folder.name }}</h4>
                            <div class="list-group list-group-folder mb-4"> {# Container for folder items #}
                                {% for item in items %}
                                    {% if item.assistant %}
                                        {% include "directory/partials/favorite_assistant_item.html" with item=item %}
                                    {% endif %}
                                {% empty %}
                                    <p class="text-muted small ms-3"><em>No assistants in this folder{% if active_tab == 'assistants' and q_name or q_company or q_category or q_tag %} matching filters{% endif %}.</em></p>
                                {% endfor %}
                            </div>
                        </div>
                    {% endwith %}
                {% endfor %}

            {% else %}
                 {# Message if no folders AND no uncategorized items exist AT ALL for this user #}
                 <p class="text-muted no-favorites-message">You haven't saved any favorite assistants yet{% if active_tab == 'assistants' and q_name or q_company or q_category or q_tag %} matching your filters{% endif %}.</p>
            {% endif %}
        {% endwith %}
        </div>
        {# End Display Assistants #}

        {# --- Start of Assistant Item Partial (for include) --- #}
        {% comment %} The following block is the content for "directory/partials/favorite_assistant_item.html" {% endcomment %}
        {% comment %}
        <!-- templates/directory/partials/favorite_assistant_item.html -->
        {% load rating_tags %}
        <div class="list-group-item mb-3 border rounded shadow-sm favorite-item" data-assistant-id="{{ item.assistant.id }}"> {# Add data-id #}
            <div class="row g-3">
                {# Link wrapper is col-md-10 and contains an inner row #}
                <a href="{% url 'assistants:assistant_chat' slug=item.assistant.slug %}" class="directory-item-link-wrapper col-md-10 row g-3 me-0 text-decoration-none">
                    {# Column 1: Logo Placeholder (col-md-2 within link row) #}
                    <div class="col-md-2 d-flex justify-content-center align-items-center">
                        <div class="logo-placeholder bg-light rounded d-flex align-items-center justify-content-center h-100">
                            <i class="bi bi-robot text-muted fs-2"></i>
                        </div>
                    </div>
                    {# Column 2: Name, Company, Type, Tags (col-md-3 within link row) #}
                    <div class="col-md-3">
                        <h5 class="mb-1 h6">
                            {{ item.assistant.name }}
                        </h5>
                        <p class="mb-1 text-muted small">
                            By {{ item.assistant.company.name }}
                        </p>
                        <span class="badge bg-primary bg-opacity-10 text-primary mb-2 tag-badge">{{ item.assistant.get_assistant_type_display }}</span>
                        {# Display tags/categories if available on listing #}
                        {% if item.assistant.listing %}
                        <div class="mt-1 mb-2">
                            {% for category in item.assistant.listing.categories %}
                                <span class="badge bg-secondary tag-badge">{{ category }}</span>
                            {% endfor %}
                            {% for tag in item.assistant.listing.tags %}
                                <span class="badge bg-light text-dark border tag-badge">{{ tag }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    {# Column 3: Description (col-md-7 within link row) #}
                    <div class="col-md-7">
                        {% with full_desc=item.assistant.listing.short_description|default:item.assistant.description|default:"" %}
                        <p class="text-muted mb-0 item-description">
                            {{ full_desc|truncatewords:50 }}
                            {% if full_desc|wordcount > 50 %}
                                <span class="text-decoration-none small">... Read More</span>
                            {% elif not full_desc %}
                                No description available.
                            {% endif %}
                        </p>
                        {% endwith %}
                    </div>
                </a> {# End of clickable area link #}

                {# Column 4: Rating, Favorite Button (col-md-2) - Outside the link #}
                <div class="col-md-2 d-flex flex-column align-items-end justify-content-start">
                    {# Unlike Button #}
                    <button
                        class="like-button btn btn-sm p-1 text-danger" {# Always red on this page #}
                        data-item-id="{{ item.assistant.id }}"
                        data-item-type="assistant"
                        title="Unlike"
                        style="background: none; border: none; cursor: pointer; margin-bottom: 0.5rem; z-index: 5;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-heart-fill" viewBox="0 0 16 16" style="pointer-events: none;">
                            <path fill-rule="evenodd" d="M8 1.314C12.438-3.248 23.534 4.735 8 15-7.534 4.736 3.562-3.248 8 1.314"/>
                        </svg>
                    </button>
                    {# Static Star Rating Display #}
                    <div class="rating-display-container mb-1 w-100 d-flex justify-content-end" id="rating-display-{{ item.assistant.id }}">
                        {% if item.assistant.listing %}
                            {% render_stars item.assistant.listing.avg_rating item.assistant.listing.total_ratings %}
                        {% else %}
                            <span class="small text-muted fst-italic">(No rating info)</span>
                        {% endif %}
                    </div>
                    {# Rate Button #}
                    {% if user.is_authenticated and item.assistant.listing %} {# Only show if listing exists #}
                    <div class="w-100 d-flex justify-content-end">
                        <button type="button"
                                class="btn btn-outline-secondary btn-sm rate-assistant-btn"
                                data-bs-toggle="modal"
                                data-bs-target="#ratingModal"
                                data-assistant-id="{{ item.assistant.id }}"
                                data-assistant-name="{{ item.assistant.name|escapejs }}"
                                style="z-index: 5;">
                            <i class="bi bi-star me-1"></i> Rate
                        </button>
                    </div>
                    <div class="w-100 d-flex justify-content-end">
                        <span class="rating-update-message small text-success mt-1" id="rating-msg-{{ item.assistant.id }}" style="display: none;"></span>
                    </div>
                    {% endif %}
                </div>
            </div> {# /row #}
        </div> {# /list-group-item #}
        {% endcomment %}
        {# --- End of Assistant Item Partial --- #}
    </div>

    {# Community Assistants Panel #}
    <div class="tab-pane fade {% if active_tab == 'community_assistants' %}show active{% endif %}" id="community-assistants-panel" role="tabpanel" aria-labelledby="community-assistants-tab">
        {# Filter Form (Community Assistant) - Enhanced Style #}
        <form method="get" action="{% url 'directory:my_favorites' %}" class="filter-form">
            <input type="hidden" name="tab" value="community_assistants">
            <h5 class="mb-3 fw-bold"><i class="bi bi-funnel-fill me-2 text-primary"></i>Filter Community Assistants</h5>
            <div class="row g-3 mb-3">
                <div class="col-md-6"> {# Name/Description Filter #}
                    <label for="q_name_comm" class="form-label">Search</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" name="q_name" id="q_name_comm" class="form-control" placeholder="Search name or description..." value="{{ q_name|default:'' }}">
                    </div>
                </div>
                <div class="col-md-3"> {# Company Filter #}
                    <label for="q_company_comm" class="form-label">Company</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-building"></i></span>
                        <input type="text" name="q_company" id="q_company_comm" class="form-control" placeholder="Company..." value="{{ q_company|default:'' }}">
                    </div>
                </div>
                <div class="col-md-3"> {# Sort Dropdown #}
                    <label for="sort_by_comm" class="form-label">Sort By</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-sort-down"></i></span>
                        <select name="sort_by" id="sort_by_comm" class="form-select">
                            <option value="date_added" {% if sort_by == 'date_added' or not sort_by %}selected{% endif %}>Date Added</option>
                            <option value="tier" {% if sort_by == 'tier' %}selected{% endif %}>Tier</option>
                            <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Name</option>
                            <option value="rating" {% if sort_by == 'rating' %}selected{% endif %}>Rating</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row g-3">
                <div class="col-md-4"> {# Category Filter #}
                    <label for="q_category_comm" class="form-label">Category</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-tag"></i></span>
                        <input type="text" name="q_category" id="q_category_comm" class="form-control" placeholder="Category..." value="{{ q_category|default:'' }}">
                    </div>
                </div>
                <div class="col-md-4"> {# Tag Filter #}
                    <label for="q_tag_comm" class="form-label">Tag</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-tags"></i></span>
                        <input type="text" name="q_tag" id="q_tag_comm" class="form-control" placeholder="Tag..." value="{{ q_tag|default:'' }}">
                    </div>
                </div>
                <div class="col-md-4"> {# Buttons #}
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex">
                        <button type="submit" class="btn btn-primary me-2 flex-grow-1">
                            <i class="bi bi-filter me-1"></i> Apply Filters
                        </button>
                        <a href="{% url 'directory:my_favorites' %}?tab=community_assistants" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i>
                        </a>
                    </div>
                </div>
            </div>
        </form>
        {# End Filter Form #}

        {# Folder Filter Buttons (Community Assistant) #}
        {% with folders=community_assistant_folders %}
        {% if folders %}
        <div class="folder-filter-buttons mb-4 d-flex flex-wrap align-items-center border-bottom pb-3" role="group" aria-label="Filter by folder">
            <h5 class="me-3 mb-0"><i class="bi bi-folder2-open me-2 text-primary"></i>My Folders:</h5>
            <button class="btn btn-link text-decoration-none p-1 me-3 active" data-folder-id="all">
                <i class="bi bi-grid-fill me-1"></i>Show All
            </button>
            <button class="btn btn-link text-decoration-none p-1 me-3" data-folder-id="uncategorized">
                <i class="bi bi-box me-1"></i>Uncategorized
            </button>
            {% for folder in folders %}
                <div class="d-flex align-items-center me-3 folder-item-container">
                    <button class="btn btn-link text-decoration-none p-1 folder-filter-link me-auto" data-folder-id="{{ folder.id }}">
                        <i class="bi bi-folder me-1"></i>{{ folder.name }}
                    </button>
                    <div class="folder-actions-dropdown position-relative">
                        <button class="btn btn-sm btn-link text-secondary p-0" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="line-height: 1;">
                            <i class="bi bi-three-dots-vertical"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item edit-folder-btn" href="#" data-folder-id="{{ folder.id }}" data-folder-name="{{ folder.name|escapejs }}"><i class="bi bi-pencil-square me-2"></i>Edit</a></li>
                            <li><a class="dropdown-item delete-folder-btn" href="#" data-folder-id="{{ folder.id }}" data-folder-name="{{ folder.name|escapejs }}"><i class="bi bi-trash me-2"></i>Delete</a></li>
                        </ul>
                    </div>
                </div>
            {% endfor %}
            <button type="button" class="btn btn-sm btn-outline-primary ms-auto">
                <i class="bi bi-plus-circle me-1"></i> Add Folder
            </button>
        </div>
        {% endif %}
        {% endwith %}
        {# End Folder Filter Buttons #}

        {# Display Active Filters (Community Assistant) #}
        {% if active_tab == 'community_assistants' and q_name or q_company or q_category or q_tag %}
        <div class="alert alert-light border mb-4 mt-2">
            <div class="d-flex align-items-center">
                <i class="bi bi-funnel me-2 text-primary"></i>
                <span class="fw-medium">Filtering by:</span>
                <div class="ms-2">
                    {% if q_name %}<span class="badge bg-primary me-1">Name/Desc: {{ q_name }}</span>{% endif %}
                    {% if q_company %}<span class="badge bg-info text-dark me-1">Company: {{ q_company }}</span>{% endif %}
                    {% if q_category %}<span class="badge bg-secondary me-1">Category: {{ q_category }}</span>{% endif %}
                    {% if q_tag %}<span class="badge bg-light text-dark border me-1">Tag: {{ q_tag }}</span>{% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        {# End Display Active Filters #}

        {# Display Community Assistants - Uncategorized First #}
        <div id="favorite-community-assistants-items-container">
        {% with items_in_folders=community_assistant_items_in_folders items_without_folder=community_assistant_items_without_folder folders=community_assistant_folders %}
            {% if folders or items_without_folder %}

                {# Display items without a folder FIRST #}
                <div data-item-group="true" data-folder-id="uncategorized"> {# Wrapper for JS filtering #}
                    {% if items_without_folder %}
                        <h4 class="h5 mt-4 mb-3 border-bottom pb-2"><i class="bi bi-inbox me-2"></i>Uncategorized Community Assistants</h4>
                        <div class="list-group list-group-uncategorized"> {# Container for uncategorized items #}
                            {% for item in items_without_folder %}
                                 {% if item.assistant %}
                                    {% include "directory/partials/favorite_community_assistant_item.html" with item=item %}
                                 {% endif %}
                            {% endfor %}
                        </div>
                    {% elif not folders %} {# Only show if no folders exist either and no uncategorized items #}
                         <p class="text-muted no-favorites-message">You haven't saved any favorite community assistants yet{% if active_tab == 'community_assistants' and q_name or q_company or q_category or q_tag %} matching your filters{% endif %}.</p>
                    {% endif %}
                </div>

                {# Loop through folders SECOND #}
                {% for folder_id, folder_data in items_in_folders.items %}
                    {% with folder=folder_data.folder items=folder_data.items %}
                        <div data-item-group="true" data-folder-id="{{ folder.id }}"> {# Wrapper for JS filtering #}
                            <h4 class="h5 mt-4 mb-3 border-bottom pb-2"><i class="bi bi-folder me-2"></i>{{ folder.name }}</h4>
                            <div class="list-group list-group-folder mb-4"> {# Container for folder items #}
                                {% for item in items %}
                                    {% if item.assistant %}
                                        {% include "directory/partials/favorite_community_assistant_item.html" with item=item %}
                                    {% endif %}
                                {% empty %}
                                    <p class="text-muted small ms-3"><em>No community assistants in this folder{% if active_tab == 'community_assistants' and q_name or q_company or q_category or q_tag %} matching filters{% endif %}.</em></p>
                                {% endfor %}
                            </div>
                        </div>
                    {% endwith %}
                {% endfor %}

            {% else %}
                 {# Message if no folders AND no uncategorized items exist AT ALL for this user #}
                 <p class="text-muted no-favorites-message">You haven't saved any favorite community assistants yet{% if active_tab == 'community_assistants' and q_name or q_company or q_category or q_tag %} matching your filters{% endif %}.</p>
            {% endif %}
        {% endwith %}
        </div>
        {# End Display Community Assistants #}
    </div>

    {# Companies Panel #}
    <div class="tab-pane fade {% if active_tab == 'companies' %}show active{% endif %}" id="companies-panel" role="tabpanel" aria-labelledby="companies-tab">
        {# Filter Form (Company) - Single Row Style #}
        <form method="get" action="{% url 'directory:my_favorites' %}" class="filter-form">
            <input type="hidden" name="tab" value="companies">
            <h5 class="mb-3 fw-bold"><i class="bi bi-funnel-fill me-2 text-primary"></i>Filter Companies</h5>
            <div class="row g-3 mb-3">
                <div class="col-md-6"> {# Name/Description Filter #}
                    <label for="q_name_comp" class="form-label">Search</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" name="q_name" id="q_name_comp" class="form-control" placeholder="Search name or description..." value="{{ q_name|default:'' }}">
                    </div>
                </div>
                <div class="col-md-3"> {# Industry Filter #}
                    <label for="q_industry_comp" class="form-label">Industry</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-briefcase"></i></span>
                        <input type="text" name="q_industry" id="q_industry_comp" class="form-control" placeholder="Industry..." value="{{ q_industry|default:'' }}">
                    </div>
                </div>
                <div class="col-md-3"> {# Sort Dropdown #}
                    <label for="sort_by_comp" class="form-label">Sort By</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-sort-down"></i></span>
                        <select name="sort_by" id="sort_by_comp" class="form-select">
                            <option value="date_added" {% if sort_by == 'date_added' or not sort_by %}selected{% endif %}>Date Added</option>
                            <option value="tier" {% if sort_by == 'tier' %}selected{% endif %}>Tier</option>
                            <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Name</option>
                            <option value="rating" {% if sort_by == 'rating' %}selected{% endif %}>Rating</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row g-3">
                <div class="col-md-4"> {# Category Filter #}
                    <label for="q_category_comp" class="form-label">Category</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-tag"></i></span>
                        <input type="text" name="q_category" id="q_category_comp" class="form-control" placeholder="Category..." value="{{ q_category|default:'' }}">
                    </div>
                </div>
                <div class="col-md-4"> {# Tag Filter #}
                    <label for="q_tag_comp" class="form-label">Tag</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-tags"></i></span>
                        <input type="text" name="q_tag" id="q_tag_comp" class="form-control" placeholder="Tag..." value="{{ q_tag|default:'' }}">
                    </div>
                </div>
                <div class="col-md-4"> {# Buttons #}
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex">
                        <button type="submit" class="btn btn-primary me-2 flex-grow-1">
                            <i class="bi bi-filter me-1"></i> Apply Filters
                        </button>
                        <a href="{% url 'directory:my_favorites' %}?tab=companies" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i>
                        </a>
                    </div>
                </div>
            </div>
        </form>
        {# End Filter Form #}

        {# Folder Filter Buttons (Company) #}
        {% with folders=company_folders %}
        {% if folders %}
        <div class="folder-filter-buttons mb-4 d-flex flex-wrap align-items-center border-bottom pb-3" role="group" aria-label="Filter by folder">
            <h5 class="me-3 mb-0"><i class="bi bi-folder2-open me-2 text-primary"></i>My Folders:</h5>
            <button class="btn btn-link text-decoration-none p-1 me-3 active" data-folder-id="all">
                <i class="bi bi-grid-fill me-1"></i>Show All
            </button>
            <button class="btn btn-link text-decoration-none p-1 me-3" data-folder-id="uncategorized">
                <i class="bi bi-box me-1"></i>Uncategorized
            </button>
            {% for folder in folders %}
                <div class="d-flex align-items-center me-3 folder-item-container">
                    <button class="btn btn-link text-decoration-none p-1 folder-filter-link me-auto" data-folder-id="{{ folder.id }}">
                        <i class="bi bi-folder me-1"></i>{{ folder.name }}
                    </button>
                    <div class="folder-actions-dropdown position-relative">
                        <button class="btn btn-sm btn-link text-secondary p-0" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="line-height: 1;">
                            <i class="bi bi-three-dots-vertical"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item edit-folder-btn" href="#" data-folder-id="{{ folder.id }}" data-folder-name="{{ folder.name|escapejs }}"><i class="bi bi-pencil-square me-2"></i>Edit</a></li>
                            <li><a class="dropdown-item delete-folder-btn" href="#" data-folder-id="{{ folder.id }}" data-folder-name="{{ folder.name|escapejs }}"><i class="bi bi-trash me-2"></i>Delete</a></li>
                        </ul>
                    </div>
                </div>
            {% endfor %}
            <button type="button" class="btn btn-sm btn-outline-primary ms-auto">
                <i class="bi bi-plus-circle me-1"></i> Add Folder
            </button>
        </div>
        {% endif %}
        {% endwith %}
        {# End Folder Filter Buttons #}

        {# Display Active Filters (Company) #}
        {% if active_tab == 'companies' and q_name or q_industry or q_category or q_tag %}
        <div class="alert alert-light border mb-4 mt-2">
            <div class="d-flex align-items-center">
                <i class="bi bi-funnel me-2 text-primary"></i>
                <span class="fw-medium">Filtering by:</span>
                <div class="ms-2">
                    {% if q_name %}<span class="badge bg-primary me-1">Name/Desc: {{ q_name }}</span>{% endif %}
                    {% if q_industry %}<span class="badge bg-info text-dark me-1">Industry: {{ q_industry }}</span>{% endif %}
                    {% if q_category %}<span class="badge bg-secondary me-1">Category: {{ q_category }}</span>{% endif %}
                    {% if q_tag %}<span class="badge bg-light text-dark border me-1">Tag: {{ q_tag }}</span>{% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        {# End Display Active Filters #}

        {# Display Companies - Uncategorized First #}
        <div id="favorite-companies-items-container">
        {% with items_in_folders=company_items_in_folders items_without_folder=company_items_without_folder folders=company_folders %}
             {% if folders or items_without_folder %}

                {# Display items without a folder FIRST #}
                <div data-item-group="true" data-folder-id="uncategorized"> {# Wrapper for JS filtering #}
                    {% if items_without_folder %}
                        <h4 class="h5 mt-4 mb-3 border-bottom pb-2"><i class="bi bi-inbox me-2"></i>Uncategorized Companies</h4>
                        <div class="list-group list-group-uncategorized"> {# Container for uncategorized items #}
                            {% for item in items_without_folder %}
                                 {% if item.company %}
                                    {% include "directory/partials/favorite_company_item.html" with item=item %}
                                 {% endif %}
                            {% endfor %}
                        </div>
                    {% elif not folders %} {# Only show if no folders exist either and no uncategorized items #}
                         <p class="text-muted no-favorites-message">You haven't saved any favorite companies yet{% if active_tab == 'companies' and q_name or q_category or q_tag %} matching your filters{% endif %}.</p>
                    {% endif %}
                </div>

                {# Loop through folders SECOND #}
                {% for folder_id, folder_data in items_in_folders.items %}
                    {% with folder=folder_data.folder items=folder_data.items %}
                         <div data-item-group="true" data-folder-id="{{ folder.id }}"> {# Wrapper for JS filtering #}
                            <h4 class="h5 mt-4 mb-3 border-bottom pb-2"><i class="bi bi-folder me-2"></i>{{ folder.name }}</h4>
                            <div class="list-group list-group-folder mb-4"> {# Container for folder items #}
                                {% for item in items %}
                                    {% if item.company %}
                                        {% include "directory/partials/favorite_company_item.html" with item=item %}
                                    {% endif %}
                                {% empty %}
                                    <p class="text-muted small ms-3"><em>No companies in this folder{% if active_tab == 'companies' and q_name or q_category or q_tag %} matching filters{% endif %}.</em></p>
                                {% endfor %}
                            </div>
                        </div>
                    {% endwith %}
                {% endfor %}

            {% else %}
                 {# Message if no folders AND no uncategorized items exist AT ALL for this user #}
                 <p class="text-muted no-favorites-message">You haven't saved any favorite companies yet{% if active_tab == 'companies' and q_name or q_category or q_tag %} matching your filters{% endif %}.</p>
            {% endif %}
        {% endwith %}
        </div>
        {# End Display Companies #}

        {# --- Start of Company Item Partial (for include) --- #}
        {% comment %} The following block is the content for "directory/partials/favorite_company_item.html" {% endcomment %}
        {% comment %}
        <!-- templates/directory/partials/favorite_company_item.html -->
        {% load rating_tags %}
        <div class="list-group-item mb-3 border rounded shadow-sm favorite-item" data-company-id="{{ item.company.id }}"> {# Add data-id #}
            <div class="row g-3">
                {# Link wrapper is col-md-10 #}
                <a href="{% url 'accounts:public_company_detail' slug=item.company.slug %}" class="directory-item-link-wrapper col-md-10 row g-3 me-0 text-decoration-none">
                    {# Column 1: Logo (col-md-2 within link row) #}
                    <div class="col-md-2 d-flex justify-content-center align-items-start pt-1">
                        <div class="company-logo-container">
                            {% if item.company.info.logo %}
                                <img src="{{ item.company.info.logo.url }}" alt="{{ item.company.name }} logo" class="rounded company-logo"> {# Added company-logo class #}
                            {% else %}
                                <div class="bg-light rounded d-flex align-items-center justify-content-center h-100 logo-placeholder"> {# Added logo-placeholder class #}
                                     <i class="bi bi-building text-muted fs-2"></i>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    {# Column 2: Name, Tags/Categories (col-md-3 within link row) #}
                    <div class="col-md-3">
                        <h5 class="mb-1 h6">
                            {{ item.company.name }}
                        </h5>
                        {# Display tags/categories if available on listing #}
                        {% if item.company.listing %}
                        <div class="mb-2">
                            {% for category in item.company.listing.categories %}
                                <span class="badge bg-secondary tag-badge">{{ category }}</span>
                            {% endfor %}
                            {% for tag in item.company.listing.tags %}
                                <span class="badge bg-light text-dark border tag-badge">{{ tag }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    {# Column 3: Description (col-md-7 within link row) #}
                    <div class="col-md-7">
                        <p class="text-muted mb-0 item-description" style="display: -webkit-box; -webkit-line-clamp: 2; line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">
                            {{ item.company.listing.description|default:item.company.info.description|default:"No description available." }}
                             {% if item.company.listing.description|default:item.company.info.description|length > 100 %}
                                <span class="text-decoration-none small">... Read More</span>
                            {% endif %}
                        </p>
                    </div>
                </a> {# End of clickable area link #}

                {# Column 4: Rating, Buttons & Contact Info (col-md-2) #}
                <div class="col-md-2 d-flex flex-column align-items-end justify-content-start">
                    {# Unlike Button #}
                    <div class="w-100 d-flex justify-content-end mb-1">
                        <button
                            class="like-button btn btn-sm p-1 text-danger" {# Always red on this page #}
                            data-item-id="{{ item.company.id }}"
                            data-item-type="company"
                            title="Unlike"
                            style="background: none; border: none; cursor: pointer; z-index: 10;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-heart-fill" viewBox="0 0 16 16" style="pointer-events: none;">
                                <path fill-rule="evenodd" d="M8 1.314C12.438-3.248 23.534 4.735 8 15-7.534 4.736 3.562-3.248 8 1.314"/>
                            </svg>
                        </button>
                    </div>
                    {# Rating Display - Use listing fields #}
                    {% if item.company.listing %}
                        {% with avg_rating=item.company.listing.avg_rating total_ratings=item.company.listing.total_ratings %}
                            <div class="rating-display-container mb-2 w-100 d-flex justify-content-end" id="rating-display-company-{{ item.company.id }}">
                                {% if avg_rating > 0 %}
                                    {% render_stars avg_rating total_ratings %}
                                {% else %}
                                    <div class="small text-muted fst-italic w-100 text-end" id="no-rating-placeholder-company-{{ item.company.id }}">(No ratings yet)</div>
                                {% endif %}
                            </div>
                        {% endwith %}
                    {% else %}
                        <div class="mb-2 small text-muted fst-italic w-100 text-end">(No rating info)</div>
                    {% endif %}
                    {# Rate Button #}
                    {% if user.is_authenticated and item.company.listing %} {# Only show if listing exists #}
                    <div class="w-100 d-flex justify-content-end mb-1">
                        <button type="button"
                                class="btn btn-outline-secondary btn-sm rate-company-btn"
                                data-bs-toggle="modal"
                                data-bs-target="#ratingModal"
                                data-company-id="{{ item.company.id }}"
                                data-company-name="{{ item.company.name|escapejs }}"
                                style="z-index: 5;">
                            <i class="bi bi-star me-1"></i> Rate
                        </button>
                    </div>
                    <div class="w-100 d-flex justify-content-end">
                        <span class="rating-update-message small text-success mt-1" id="rating-msg-company-{{ item.company.id }}" style="display: none;"></span>
                    </div>
                    {% endif %}
                    {# Contact Info (Optional - Keep or remove based on preference for favorites page) #}
                    {# <div class="mt-auto w-100"> ... contact info ... </div> #}
                </div> {# /col-md-2 #}
            </div> {# /row #}
        </div> {# /list-group-item #}
        {% endcomment %}
        {# --- End of Company Item Partial --- #}
    </div>
</div>

{# Rating Modal Structure (Enhanced) #}
<div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-light">
        <h5 class="modal-title" id="ratingModalLabel">Rate Item</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-4">How would you rate <strong id="modalItemName" class="text-primary">this item</strong>?</p>
        <div class="modal-stars text-center mb-4" style="font-size: 2.5rem;">
            {% for i_int in "12345" %}
            <button class="modal-star-btn btn btn-link text-secondary p-1" data-rating-value="{{ i_int }}" title="Rate {{ i_int }} star{{ i_int|pluralize }}">
                <i class="bi bi-star"></i>
            </button>
            {% endfor %}
        </div>
        <div class="text-center text-muted small mb-3">Click on a star to select your rating</div>
        <div id="modalErrorMsg" class="alert alert-danger small mt-2" style="display: none;"></div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="submitRatingBtn" disabled>
            <i class="bi bi-check-circle me-1"></i> Submit Rating
        </button>
      </div>
    </div>
  </div>
</div>
{# End Rating Modal #}

{# Pagination with items per page #}
{% if page_obj.paginator.count > 0 %}
<div class="mt-4">
    {% include "pagination_with_items_per_page.html" with page_obj=page_obj %}
</div>
{% endif %}
{% csrf_token %} {# Needed for JS #}

</div>

{# --- Modals for Folder Actions --- #}

{# Edit Folder Modal #}
<div class="modal fade" id="editFolderModal" tabindex="-1" aria-labelledby="editFolderModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-light">
        <h5 class="modal-title" id="editFolderModalLabel">
            <i class="bi bi-pencil-square me-2 text-primary"></i>Edit Folder
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="editFolderForm">
          <input type="hidden" id="editFolderIdInput" name="folder_id">
          <div class="mb-3">
            <label for="editFolderNameInput" class="form-label">Folder Name</label>
            <div class="input-group">
              <span class="input-group-text"><i class="bi bi-folder"></i></span>
              <input type="text" class="form-control" id="editFolderNameInput" name="name" required>
            </div>
            <div class="form-text">Choose a descriptive name for your folder</div>
          </div>
          <div id="editFolderErrorMsg" class="alert alert-danger small mt-3" style="display: none;"></div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" form="editFolderForm" class="btn btn-primary" id="saveFolderEditBtn">
            <i class="bi bi-check-circle me-1"></i> Save Changes
        </button>
      </div>
    </div>
  </div>
</div>

{# Delete Folder Confirmation Modal #}
<div class="modal fade" id="deleteFolderModal" tabindex="-1" aria-labelledby="deleteFolderModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-light">
        <h5 class="modal-title" id="deleteFolderModalLabel">
            <i class="bi bi-trash me-2 text-danger"></i>Delete Folder
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <input type="hidden" id="deleteFolderIdInput">
        <div class="alert alert-warning">
            <div class="d-flex">
                <div class="me-3">
                    <i class="bi bi-exclamation-triangle-fill fs-3 text-warning"></i>
                </div>
                <div>
                    <h5 class="alert-heading">Confirm Deletion</h5>
                    <p>Are you sure you want to delete the folder "<strong id="deleteFolderNameSpan"></strong>"?</p>
                    <p class="mb-0">Items in this folder will become uncategorized but will remain in your favorites.</p>
                </div>
            </div>
        </div>
        <div id="deleteFolderErrorMsg" class="alert alert-danger small mt-3" style="display: none;"></div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" id="confirmDeleteFolderBtn">
            <i class="bi bi-trash me-1"></i> Delete Folder
        </button>
      </div>
    </div>
  </div>
</div>
{# Create Folder Modal #}
<div class="modal fade" id="createFolderModal" tabindex="-1" aria-labelledby="createFolderModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-light">
        <h5 class="modal-title" id="createFolderModalLabel">
            <i class="bi bi-folder-plus me-2 text-primary"></i>Create New Folder
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="createFolderForm">
          <input type="hidden" id="createFolderItemTypeInput" name="item_type" value="assistant">
          <div class="mb-3">
            <label for="createFolderNameInput" class="form-label">Folder Name</label>
            <div class="input-group">
              <span class="input-group-text"><i class="bi bi-folder"></i></span>
              <input type="text" class="form-control" id="createFolderNameInput" name="name" required>
            </div>
            <div class="form-text">Choose a descriptive name for your folder</div>
          </div>
          <div id="createFolderErrorMsg" class="alert alert-danger small mt-3" style="display: none;"></div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" form="createFolderForm" class="btn btn-primary" id="createFolderBtn">
            <i class="bi bi-plus-circle me-1"></i> Create Folder
        </button>
      </div>
    </div>
  </div>
</div>
{# --- End Modals --- #}

{% endblock %}

{% block extra_js %}
{# Include the same JS as assistant_list.html for the like buttons, adapted for removal #}
<script>
// Helper function to get CSRF token
function getCsrfToken() {
    const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
    if (!csrfInput) {
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfMeta) return csrfMeta.getAttribute('content');
    }
    return csrfInput ? csrfInput.value : null;
}

// --- Unlike Button Logic (Event Delegation) with Instant Removal ---
function handleUnlikeButtons() {
    const tabContent = document.getElementById('favoritesTabContent');
    if (!tabContent) return;

    tabContent.addEventListener('click', async (event) => {
        const button = event.target.closest('.like-button');

        if (button && (event.target === button || button.contains(event.target))) {
            console.log("Unlike button click detected.");
            event.preventDefault();
            event.stopPropagation();

            const itemId = button.dataset.itemId;
            const itemType = button.dataset.itemType;
            const url = "{% url 'directory:toggle_saved_item' %}";

            // Find all instances of this item on the page (could be in multiple tabs/folders)
            const itemSelector = itemType === 'assistant'
                ? `.favorite-item[data-assistant-id="${itemId}"]`
                : `.favorite-item[data-company-id="${itemId}"]`;
            const itemElements = document.querySelectorAll(itemSelector);

            if (itemElements.length === 0) {
                console.warn('No item elements found to remove');
                return;
            }

            console.log(`Removing favorite: item=${itemId}, type=${itemType}, found ${itemElements.length} instances`);

            // INSTANT VISUAL FEEDBACK: Immediately start fading out all instances
            itemElements.forEach(item => {
                item.style.transition = 'opacity 0.3s ease';
                item.style.opacity = '0';
            });

            // Track which list groups and panes contain these items for empty state handling
            const affectedGroups = new Set();
            const affectedPanes = new Set();

            itemElements.forEach(item => {
                const listGroup = item.closest('.list-group');
                const pane = item.closest('.tab-pane');
                if (listGroup) affectedGroups.add(listGroup);
                if (pane) affectedPanes.add(pane);
            });

            // Send the API request in the background
            try {
                const csrfToken = getCsrfToken();
                if (!csrfToken) {
                    throw new Error("CSRF token not found!");
                }

                // Create the fetch request but don't await it yet
                const fetchPromise = fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({
                        'item_id': itemId,
                        'item_type': itemType
                    })
                });

                // After fade-out animation completes, remove the elements from DOM
                setTimeout(() => {
                    // Remove all instances of the item
                    itemElements.forEach(item => item.remove());

                    // Check if any list groups are now empty and add empty message
                    affectedGroups.forEach(group => {
                        if (group.querySelectorAll('.favorite-item').length === 0) {
                            const folderContainer = group.closest('[data-item-group="true"]');
                            if (folderContainer) {
                                const folderHeader = folderContainer.querySelector('h4');
                                const folderName = folderHeader ? folderHeader.textContent.trim() : 'this folder';

                                // Add empty folder message
                                const emptyMsg = document.createElement('p');
                                emptyMsg.className = 'text-muted small ms-3 empty-folder-message';
                                emptyMsg.innerHTML = `<em>No items in ${folderName}.</em>`;
                                group.appendChild(emptyMsg);
                            }
                        }
                    });

                    // Check if any panes are now completely empty
                    affectedPanes.forEach(pane => {
                        if (pane.querySelectorAll('.favorite-item').length === 0) {
                            // Determine message type based on pane ID
                            let messageType = 'items';
                            if (pane.id === 'assistants-panel') messageType = 'assistants';
                            else if (pane.id === 'community-assistants-panel') messageType = 'community assistants';
                            else if (pane.id === 'companies-panel') messageType = 'companies';

                            // Only add message if one doesn't already exist
                            if (!pane.querySelector('.no-favorites-message')) {
                                // Clear any empty folder containers first
                                pane.querySelectorAll('[data-item-group="true"]').forEach(el => {
                                    if (!el.querySelector('.favorite-item')) {
                                        el.remove();
                                    }
                                });

                                // Add the empty state message
                                const noFavMsg = document.createElement('p');
                                noFavMsg.className = 'text-muted no-favorites-message';
                                noFavMsg.textContent = `You haven't saved any favorite ${messageType} yet.`;
                                pane.appendChild(noFavMsg);
                            }
                        }
                    });
                }, 300); // Match the CSS transition duration

                // Now handle the API response in the background
                fetchPromise.then(async response => {
                    const isJson = response.headers.get('content-type')?.includes('application/json');
                    const data = isJson ? await response.json() : null;

                    if (!response.ok) {
                        const errorMessage = data?.message || `HTTP error ${response.status}`;
                        console.error(`Unlike failed: ${errorMessage}`, data);
                        // Don't throw here - we've already removed the item visually
                    }

                    if (data && data.status === 'success' && !data.saved) {
                        console.log('Favorite removed successfully on server.');
                    } else {
                        console.warn('Unexpected response from server:', data);
                    }
                }).catch(error => {
                    console.error('Error in background API call:', error);
                    // Don't alert the user since we've already removed the item visually
                });

            } catch (error) {
                console.error('Error initiating unlike process:', error);
                // If we can't even start the process, restore the items
                itemElements.forEach(item => {
                    item.style.opacity = '1';
                });
                alert(`An error occurred: ${error.message}`);
            }
        }
    });
}
// --- End Unlike Button Logic ---


// --- Combined Modal Rating Logic (Handles both Assistants and Companies) ---
function handleRatingModal() {
    const ratingModalElement = document.getElementById('ratingModal');
    if (!ratingModalElement) {
        console.error("Rating modal element not found.");
        return;
    }

    let ratingModal = bootstrap.Modal.getInstance(ratingModalElement);
    if (!ratingModal) {
        try {
            ratingModal = new bootstrap.Modal(ratingModalElement);
        } catch (e) {
            console.error("Failed to initialize Bootstrap Modal:", e);
            return;
        }
    }

    const modalTitle = ratingModalElement.querySelector('#ratingModalLabel');
    const modalItemName = ratingModalElement.querySelector('#modalItemName'); // Generic name element
    const modalStarsContainer = ratingModalElement.querySelector('.modal-stars');
    const modalSubmitBtn = ratingModalElement.querySelector('#submitRatingBtn');
    const modalErrorMsg = ratingModalElement.querySelector('#modalErrorMsg');
    let currentItemId = null;
    let currentItemType = null; // 'assistant' or 'company'
    let selectedRating = 0;

    // 1. Populate modal when triggered
    ratingModalElement.addEventListener('show.bs.modal', function (event) {
        const button = event.relatedTarget;
        currentItemId = null; // Reset
        currentItemType = null; // Reset

        let itemName = 'this item';
        let itemType = '';
        let itemId = '';

        if (button && button.classList.contains('rate-assistant-btn')) {
            itemType = 'assistant';
            itemId = button.getAttribute('data-assistant-id');
            itemName = button.getAttribute('data-assistant-name') || 'this assistant';
        } else if (button && button.classList.contains('rate-company-btn')) {
            itemType = 'company';
            itemId = button.getAttribute('data-company-id');
            itemName = button.getAttribute('data-company-name') || 'this company';
        } else {
            console.log("Rating modal triggered by unknown source.");
            return; // Don't proceed if trigger is wrong
        }

        currentItemId = itemId;
        currentItemType = itemType;
        ratingModalElement.dataset.itemId = currentItemId; // Store ID
        ratingModalElement.dataset.itemType = currentItemType; // Store type

        if (modalTitle) modalTitle.textContent = `Rate ${itemName}`;
        if (modalItemName) modalItemName.textContent = itemName;

        // Reset modal state
        selectedRating = 0;
        if (modalErrorMsg) {
            modalErrorMsg.textContent = '';
            modalErrorMsg.style.display = 'none';
        }
        if (modalSubmitBtn) modalSubmitBtn.disabled = true;

        // Reset stars visual state
        if (modalStarsContainer) {
            const stars = modalStarsContainer.querySelectorAll('.modal-star-btn');
            stars.forEach(star => {
                star.classList.remove('selected', 'text-warning');
                star.classList.add('text-secondary');
                const icon = star.querySelector('i');
                if (icon) {
                    icon.classList.remove('bi-star-fill');
                    icon.classList.add('bi-star');
                }
            });
        }
    });

    // 2. Handle star selection
    if (modalStarsContainer) {
        modalStarsContainer.addEventListener('click', function(event) {
            const starButton = event.target.closest('.modal-star-btn');
            if (!starButton) return;

            selectedRating = parseInt(starButton.dataset.ratingValue);
            if (modalSubmitBtn) modalSubmitBtn.disabled = false;
            if (modalErrorMsg) modalErrorMsg.style.display = 'none';

            const stars = modalStarsContainer.querySelectorAll('.modal-star-btn');
            stars.forEach(star => {
                const starValue = parseInt(star.dataset.ratingValue);
                const icon = star.querySelector('i');
                if (icon) {
                    if (starValue <= selectedRating) {
                        star.classList.add('selected', 'text-warning');
                        star.classList.remove('text-secondary');
                        icon.classList.remove('bi-star');
                        icon.classList.add('bi-star-fill');
                    } else {
                        star.classList.remove('selected', 'text-warning');
                        star.classList.add('text-secondary');
                        icon.classList.remove('bi-star-fill');
                        icon.classList.add('bi-star');
                    }
                }
            });
        });
    } else {
         console.error("Modal stars container not found.");
    }


    // 3. Handle rating submission
    if (modalSubmitBtn) {
        modalSubmitBtn.addEventListener('click', async function() {
            const type = ratingModalElement.dataset.itemType;
            const id = ratingModalElement.dataset.itemId;

            if (!type || !id || selectedRating === 0) {
                 if (modalErrorMsg) {
                    modalErrorMsg.textContent = 'Please select a rating (1-5 stars).';
                    modalErrorMsg.style.display = 'block';
                 }
                return;
            }

            const csrfTokenLocal = getCsrfToken();
            let url = '';
            const bodyParams = new URLSearchParams();

            if (type === 'assistant') {
                url = `/directory/rate-assistant/${id}/`;
                bodyParams.append('assistant_id', id);
                bodyParams.append('rating', selectedRating);
            } else if (type === 'company') {
                url = "{% url 'directory:rate_company' %}";
                bodyParams.append('company_id', id);
                bodyParams.append('rating', selectedRating);
            } else {
                console.error("Unknown item type for rating:", type);
                return;
            }

            modalSubmitBtn.disabled = true;
            modalSubmitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';
            if (modalErrorMsg) modalErrorMsg.style.display = 'none';

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfTokenLocal,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: bodyParams
                });

                const data = await response.json();

                if (!response.ok || data.status !== 'success') {
                    throw new Error(data.message || `HTTP error ${response.status}`);
                }

                ratingModal.hide();

                const starsHtml = data.rendered_stars_html;
                const displayContainerId = type === 'assistant' ? `rating-display-${id}` : `rating-display-company-${id}`;
                const noRatingPlaceholderId = type === 'company' ? `no-rating-placeholder-company-${id}` : null; // Only companies might have this placeholder initially

                let containerToUpdate = document.getElementById(displayContainerId);
                if (!containerToUpdate && noRatingPlaceholderId) {
                    containerToUpdate = document.getElementById(noRatingPlaceholderId);
                }

                let updateSuccess = false;
                if (containerToUpdate && starsHtml) {
                    containerToUpdate.outerHTML = starsHtml; // Replace the container or placeholder
                    updateSuccess = true;
                } else {
                     console.error(`[Rating] Could not find container (${displayContainerId}) or received empty stars HTML for ${type} ${id}.`);
                }

                if (updateSuccess) {
                    const rateButtonSelector = type === 'assistant' ? `.rate-assistant-btn[data-assistant-id="${id}"]` : `.rate-company-btn[data-company-id="${id}"]`;
                    const msgSpanId = type === 'assistant' ? `rating-msg-${id}` : `rating-msg-company-${id}`;
                    const rateButton = document.querySelector(rateButtonSelector);
                    const msgSpan = document.getElementById(msgSpanId);

                    if (rateButton) {
                         rateButton.disabled = true;
                         rateButton.innerHTML = '<i class="bi bi-check-lg"></i> Rated';
                    }
                    if (msgSpan) {
                         msgSpan.textContent = 'Thanks!';
                         msgSpan.style.display = 'inline';
                    }
                } else {
                    // Fallback message if UI update failed
                    const msgSpanId = type === 'assistant' ? `rating-msg-${id}` : `rating-msg-company-${id}`;
                    const msgSpan = document.getElementById(msgSpanId);
                     if (msgSpan) {
                         msgSpan.textContent = 'Rated (refresh?)';
                         msgSpan.classList.remove('text-success');
                         msgSpan.classList.add('text-warning');
                         msgSpan.style.display = 'inline';
                     }
                }

            } catch (error) {
                console.error(`Error submitting ${type} rating via modal:`, error);
                 if (modalErrorMsg) {
                    modalErrorMsg.textContent = `Error: ${error.message}`;
                    modalErrorMsg.style.display = 'block';
                 }
            } finally {
                modalSubmitBtn.disabled = false;
                modalSubmitBtn.innerHTML = 'Submit Rating';
                // Clear modal state
                ratingModalElement.dataset.itemType = '';
                ratingModalElement.dataset.itemId = '';
                currentItemId = null;
                currentItemType = null;
            }
        });
    } else {
         console.error("Modal submit button not found.");
    }
}
// --- End Modal Rating Logic ---

// --- Folder Filter Button Logic ---
function handleFolderFilterButtons() {
    const tabContent = document.getElementById('favoritesTabContent');
    if (!tabContent) return;

    tabContent.addEventListener('click', function(event) {
        // Target both buttons and links with the folder-filter-link class
        const filterElement = event.target.closest('.folder-filter-buttons button, .folder-filter-buttons a');
        if (!filterElement) return; // Exit if click wasn't on a filter button or link

        event.preventDefault(); // Prevent default link behavior

        const folderId = filterElement.dataset.folderId;
        const buttonGroup = filterElement.closest('.folder-filter-buttons');
        const activeTabPane = filterElement.closest('.tab-pane'); // Find the parent tab pane

        if (!buttonGroup || !activeTabPane) return;

        // Update button/link active state
        buttonGroup.querySelectorAll('button, a').forEach(el => el.classList.remove('active'));
        filterElement.classList.add('active');

        // Filter items within the active tab pane
        const itemGroups = activeTabPane.querySelectorAll('[data-item-group="true"]');
        itemGroups.forEach(group => {
            if (folderId === 'all' || group.dataset.folderId === folderId) {
                group.style.display = ''; // Show
            } else {
                group.style.display = 'none'; // Hide
            }
        });
    });
}
// --- End Folder Filter Button Logic ---

// --- Folder Edit/Delete Modal Logic ---
function handleFolderActions() {
    const tabContent = document.getElementById('favoritesTabContent');
    const editModalElement = document.getElementById('editFolderModal');
    const deleteModalElement = document.getElementById('deleteFolderModal');

    if (!tabContent || !editModalElement || !deleteModalElement) {
        console.error("Required elements for folder actions not found.");
        return;
    }

    const editModal = new bootstrap.Modal(editModalElement);
    const deleteModal = new bootstrap.Modal(deleteModalElement);

    const editForm = document.getElementById('editFolderForm');
    const editFolderIdInput = document.getElementById('editFolderIdInput');
    const editFolderNameInput = document.getElementById('editFolderNameInput');
    const editFolderErrorMsg = document.getElementById('editFolderErrorMsg');
    const saveFolderEditBtn = document.getElementById('saveFolderEditBtn');

    const deleteFolderIdInput = document.getElementById('deleteFolderIdInput');
    const deleteFolderNameSpan = document.getElementById('deleteFolderNameSpan');
    const deleteFolderErrorMsg = document.getElementById('deleteFolderErrorMsg');
    const confirmDeleteFolderBtn = document.getElementById('confirmDeleteFolderBtn');

    tabContent.addEventListener('click', function(event) {
        const editBtn = event.target.closest('.edit-folder-btn');
        const deleteBtn = event.target.closest('.delete-folder-btn');

        if (editBtn) {
            event.preventDefault();
            const folderId = editBtn.dataset.folderId;
            const folderName = editBtn.dataset.folderName;

            editFolderIdInput.value = folderId;
            editFolderNameInput.value = folderName;
            editFolderErrorMsg.style.display = 'none';
            editFolderErrorMsg.textContent = '';
            editModal.show();
        } else if (deleteBtn) {
            event.preventDefault();
            const folderId = deleteBtn.dataset.folderId;
            const folderName = deleteBtn.dataset.folderName;

            deleteFolderIdInput.value = folderId;
            deleteFolderNameSpan.textContent = folderName;
            deleteFolderErrorMsg.style.display = 'none';
            deleteFolderErrorMsg.textContent = '';
            deleteModal.show();
        }
    });

    // Handle Edit Form Submission
    editForm.addEventListener('submit', async function(event) {
        event.preventDefault();
        const folderId = editFolderIdInput.value;
        const newName = editFolderNameInput.value.trim();
        const csrfToken = getCsrfToken();
        const url = `/directory/favorites/folder/${folderId}/edit/`; // Construct URL

        if (!newName) {
            editFolderErrorMsg.textContent = 'Folder name cannot be empty.';
            editFolderErrorMsg.style.display = 'block';
            return;
        }

        saveFolderEditBtn.disabled = true;
        saveFolderEditBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';
        editFolderErrorMsg.style.display = 'none';

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: new URLSearchParams({ 'name': newName })
            });

            const data = await response.json();

            if (!response.ok || data.status !== 'success') {
                throw new Error(data.message || `HTTP error ${response.status}`);
            }

            // Update the folder name on the page
            const folderLink = document.querySelector(`.folder-filter-link[data-folder-id="${folderId}"]`);
            if (folderLink) {
                folderLink.innerHTML = `<i class="bi bi-folder me-1"></i>${data.new_name}`;
                 // Also update the data-folder-name on the dropdown items if needed for subsequent edits/deletes without reload
                 const dropdownItems = folderLink.closest('.d-inline-block').querySelectorAll('.dropdown-item');
                 dropdownItems.forEach(item => {
                     if(item.classList.contains('edit-folder-btn') || item.classList.contains('delete-folder-btn')) {
                         item.dataset.folderName = data.new_name;
                     }
                 });
            }

            editModal.hide();

        } catch (error) {
            console.error("Error editing folder:", error);
            editFolderErrorMsg.textContent = `Error: ${error.message}`;
            editFolderErrorMsg.style.display = 'block';
        } finally {
            saveFolderEditBtn.disabled = false;
            saveFolderEditBtn.innerHTML = 'Save Changes';
        }
    });

    // Handle Delete Confirmation
    confirmDeleteFolderBtn.addEventListener('click', async function() {
        const folderId = deleteFolderIdInput.value;
        const csrfToken = getCsrfToken();
        const url = `/directory/favorites/folder/${folderId}/delete/`; // Construct URL

        confirmDeleteFolderBtn.disabled = true;
        confirmDeleteFolderBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Deleting...';
        deleteFolderErrorMsg.style.display = 'none';

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                }
                // No body needed for delete usually, ID is in URL
            });

            const data = await response.json();

            if (!response.ok || data.status !== 'success') {
                throw new Error(data.message || `HTTP error ${response.status}`);
            }

            deleteModal.hide();
            // Reload the page to reflect changes (folder removed, items potentially moved)
            window.location.reload();

        } catch (error) {
            console.error("Error deleting folder:", error);
            deleteFolderErrorMsg.textContent = `Error: ${error.message}`;
            deleteFolderErrorMsg.style.display = 'block';
        } finally {
            confirmDeleteFolderBtn.disabled = false;
            confirmDeleteFolderBtn.innerHTML = 'Delete Folder';
        }
    });

}
// --- End Folder Edit/Delete Modal Logic ---


// --- Create Folder Modal Logic ---
function handleCreateFolderButtons() {
    const assistantsTab = document.getElementById('assistants-tab');
    const communityAssistantsTab = document.getElementById('community-assistants-tab');
    const companiesTab = document.getElementById('companies-tab');
    const createFolderModal = document.getElementById('createFolderModal');
    const createFolderForm = document.getElementById('createFolderForm');
    const createFolderItemTypeInput = document.getElementById('createFolderItemTypeInput');
    const createFolderNameInput = document.getElementById('createFolderNameInput');
    const createFolderBtn = document.getElementById('createFolderBtn');
    const createFolderErrorMsg = document.getElementById('createFolderErrorMsg');

    if (!createFolderModal || !createFolderForm || !createFolderItemTypeInput || !createFolderNameInput) {
        console.error("Required elements for create folder functionality not found.");
        return;
    }

    // Set up event listeners for the "Add Folder" buttons
    const addFolderButtons = document.querySelectorAll('.btn-outline-primary[type="button"]');
    addFolderButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Determine the active tab to set the correct item_type
            let itemType = 'assistant'; // Default

            if (companiesTab && companiesTab.classList.contains('active')) {
                itemType = 'company';
            } else if (communityAssistantsTab && communityAssistantsTab.classList.contains('active')) {
                itemType = 'assistant'; // Community assistants use the same folders as regular assistants
            }

            // Set the item type in the form
            createFolderItemTypeInput.value = itemType;

            // Reset the form
            createFolderForm.reset();
            createFolderErrorMsg.style.display = 'none';

            // Show the modal
            const modal = new bootstrap.Modal(createFolderModal);
            modal.show();
        });
    });

    // Handle form submission
    createFolderForm.addEventListener('submit', async function(event) {
        event.preventDefault();

        const folderName = createFolderNameInput.value.trim();
        const itemType = createFolderItemTypeInput.value;
        const csrfToken = getCsrfToken();

        if (!folderName) {
            createFolderErrorMsg.textContent = 'Folder name cannot be empty.';
            createFolderErrorMsg.style.display = 'block';
            return;
        }

        createFolderBtn.disabled = true;
        createFolderBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Creating...';
        createFolderErrorMsg.style.display = 'none';

        try {
            const response = await fetch('/directory/favorites/create-folder/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: new URLSearchParams({
                    'name': folderName,
                    'item_type': itemType
                })
            });

            const data = await response.json();

            if (!response.ok || data.status !== 'success') {
                throw new Error(data.message || `HTTP error ${response.status}`);
            }

            // Reload the page to show the new folder
            window.location.reload();

        } catch (error) {
            console.error("Error creating folder:", error);
            createFolderErrorMsg.textContent = `Error: ${error.message}`;
            createFolderErrorMsg.style.display = 'block';
        } finally {
            createFolderBtn.disabled = false;
            createFolderBtn.innerHTML = '<i class="bi bi-plus-circle me-1"></i> Create Folder';
        }
    });
}
// --- End Create Folder Modal Logic ---

// Initialize handlers when the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM loaded, initializing handlers.");
    handleUnlikeButtons();
    handleRatingModal();
    handleFolderFilterButtons();
    handleFolderActions(); // Initialize folder edit/delete handlers
    handleCreateFolderButtons(); // Initialize create folder handlers

    // --- Revised Tab Click Reset Logic ---
    const assistantTabButton = document.getElementById('assistants-tab');
    const communityAssistantTabButton = document.getElementById('community-assistants-tab');
    const companyTabButton = document.getElementById('companies-tab');
    const baseUrl = "{% url 'directory:my_favorites' %}";

    if (assistantTabButton) {
        assistantTabButton.addEventListener('click', function(event) {
            if (!assistantTabButton.classList.contains('active')) {
                event.preventDefault(); // Prevent Bootstrap's default tab showing
                window.location.href = `${baseUrl}?tab=assistants`; // Redirect to clear filters
            }
        });
    }

    if (communityAssistantTabButton) {
        communityAssistantTabButton.addEventListener('click', function(event) {
            if (!communityAssistantTabButton.classList.contains('active')) {
                event.preventDefault(); // Prevent Bootstrap's default tab showing
                window.location.href = `${baseUrl}?tab=community_assistants`; // Redirect to clear filters
            }
        });
    }

    if (companyTabButton) {
        companyTabButton.addEventListener('click', function(event) {
            if (!companyTabButton.classList.contains('active')) {
                event.preventDefault(); // Prevent Bootstrap's default tab showing
                window.location.href = `${baseUrl}?tab=companies`; // Redirect to clear filters
            }
        });
    }
    // --- End Revised Tab Click Reset Logic ---


    // Ensure Bootstrap JS is loaded for tabs (Keep this check)
    // Note: The actual tab display (making the content visible) will happen on the *next* page load
    // after the redirect, where Django renders the correct 'active' classes based on the 'tab' URL parameter.
    if (typeof bootstrap === 'undefined' || typeof bootstrap.Tab === 'undefined') {
        console.warn('Bootstrap Tab component not found. Tabs might not work.');
    } else {
         // Initialize tabs (redundant if using data-bs-toggle attributes correctly, but safe)
        const triggerTabList = [].slice.call(document.querySelectorAll('#favoritesTab button'));
        triggerTabList.forEach(function (triggerEl) {
            const tabTrigger = new bootstrap.Tab(triggerEl);
            // Optional: Add listener for tab shown event if needed later
            // triggerEl.addEventListener('shown.bs.tab', function (event) { ... });
        });
        console.log("Bootstrap tabs initialized (or assumed initialized via data attributes).");
    }
});
</script>
{% endblock %}
