"""
Script to test community context functionality.
"""

import os
import sys
import django
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'virtualo4.settings')
django.setup()

from django.test import Client
from django.urls import reverse
from assistants.models import Assistant, CommunityContext
from accounts.models import Company
from django.contrib.auth import get_user_model

User = get_user_model()

def test_community_context():
    """Test that community context is being used in the assistant response."""
    # Get a community assistant
    assistant = Assistant.objects.filter(assistant_type='community').first()
    
    if not assistant:
        print("No community assistant found. Please create one first.")
        return
    
    # Get the company
    company = assistant.company
    
    # Get or create a test user
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'is_active': True
        }
    )
    
    if created:
        user.set_password('testpassword')
        user.save()
    
    # Create a test client
    client = Client()
    
    # Log in the user
    client.login(username='testuser', password='testpassword')
    
    # Get the URL for the assistant_interact view
    url = reverse('assistants:interact', kwargs={
        'company_id': company.id,
        'assistant_id': assistant.id
    })
    
    # Create a test message
    message = "Tell me about the community context"
    
    # Create a request with community context enabled
    request_data = {
        'message': message,
        'history': [],
        'use_community_context': True
    }
    
    # Send the request
    response = client.post(
        url,
        data=json.dumps(request_data),
        content_type='application/json'
    )
    
    # Check that the response is successful
    if response.status_code == 200:
        print("Response successful!")
        response_data = json.loads(response.content)
        print(f"Response content: {response_data.get('content')}")
    else:
        print(f"Response failed with status code {response.status_code}")
        print(f"Response content: {response.content}")

if __name__ == '__main__':
    test_community_context()
