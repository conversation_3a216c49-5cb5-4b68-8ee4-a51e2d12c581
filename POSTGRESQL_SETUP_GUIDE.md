# PostgreSQL Setup Guide for Windows

This guide will help you properly install and configure PostgreSQL for your Django project.

## Step 1: Install PostgreSQL

1. Download the PostgreSQL installer for Windows from the [official website](https://www.postgresql.org/download/windows/).
   - Choose the latest version (currently 16.x)
   - Select the Windows x86-64 installer

2. Run the installer and follow these steps:
   - Select components to install:
     - PostgreSQL Server
     - pgAdmin 4 (management tool)
     - Command Line Tools
     - Stack Builder
   - Choose installation directory (default is fine)
   - Choose data directory (default is fine)
   - Set a password for the database superuser (postgres)
     - **IMPORTANT: Remember this password!**
   - Select the port (default is 5432)
   - Select locale (default is fine)

3. Complete the installation
   - Uncheck the option to launch Stack Builder (not needed now)

## Step 2: Verify PostgreSQL Installation

1. Open pgAdmin 4 from the Start menu
   - It will open in your web browser
   - Enter your master password if prompted

2. In the browser interface:
   - Expand "Servers" in the left panel
   - Expand "PostgreSQL XX" (where XX is the version number)
   - You'll be prompted for the password you set during installation
   - If you can connect, PostgreSQL is installed correctly

## Step 3: Create a Database for Your Project

1. In pgAdmin 4:
   - Right-click on "Databases" under your PostgreSQL server
   - Select "Create" > "Database..."
   - Enter "virtualo" as the database name
   - Leave the owner as "postgres"
   - Click "Save"

## Step 4: Update Django Configuration

1. Update your `local_settings.py` file with the correct PostgreSQL credentials:

```python
# Database settings for PostgreSQL
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'virtualo',  # Database name
        'USER': 'postgres',  # PostgreSQL username
        'PASSWORD': 'your_password_here',  # Replace with your actual password
        'HOST': 'localhost',
        'PORT': '5432',
        'CONN_MAX_AGE': 600,  # Keep connections alive for 10 minutes
        'OPTIONS': {
            'connect_timeout': 10,
            'client_encoding': 'UTF8',
        },
    }
}
```

**IMPORTANT:** Replace `'your_password_here'` with the password you set during PostgreSQL installation.

## Step 5: Install Python PostgreSQL Adapter

1. Install the psycopg2 package:
   ```
   pip install psycopg2-binary
   ```

## Step 6: Apply Migrations

1. Run migrations:
   ```
   python manage.py migrate
   ```

2. If you encounter migration errors, try:
   ```
   python manage.py migrate --fake-initial
   ```

## Step 7: Create a Superuser

1. Create a Django superuser:
   ```
   python manage.py createsuperuser
   ```

## Step 8: Run the Development Server

1. Start the Django development server:
   ```
   python manage.py runserver
   ```

## Troubleshooting

### Connection Issues

If you see errors like "password authentication failed" or "no password supplied":

1. Double-check the password in `local_settings.py`
2. Ensure it matches the password you set during PostgreSQL installation
3. Try connecting with pgAdmin 4 to verify the password works

### Database Already Exists

If you see errors about the database already existing:

1. In pgAdmin 4:
   - Right-click on the "virtualo" database
   - Select "Delete/Drop"
   - Check "Drop with CASCADE?"
   - Click "OK"
2. Create the database again as described in Step 3

### Migration Issues

If you encounter migration errors:

1. Try running migrations with the `--fake-initial` flag:
   ```
   python manage.py migrate --fake-initial
   ```

2. If that doesn't work, try running migrations for each app individually:
   ```
   python manage.py migrate auth
   python manage.py migrate contenttypes
   python manage.py migrate sessions
   # ... and so on for each app
   ```

### PostgreSQL Service Not Running

If PostgreSQL isn't running:

1. Open Services (services.msc)
2. Find "postgresql-x64-XX" service (where XX is the version number)
3. Right-click and select "Start"

## Additional Resources

- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Django PostgreSQL Documentation](https://docs.djangoproject.com/en/5.0/ref/databases/#postgresql-notes)
- [psycopg2 Documentation](https://www.psycopg.org/docs/)
