import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import the necessary models
from django.db.models import Q
from assistants.models import Assistant
from accounts.models import Company

# Test the search functionality
def test_search():
    print("Testing search functionality...")
    
    # Test with a sample query
    query = "test"
    
    # Try the search that was causing the error
    try:
        assistant_qs = Assistant.objects.filter(
            Q(name__icontains=query) | Q(description__icontains=query),
            is_public=True,
            is_active=True,
            assistant_type=Assistant.TYPE_GENERAL
        ).select_related('company').distinct()
        
        print(f"Search successful! Found {assistant_qs.count()} assistants.")
        
        # Print the first few results if any
        for assistant in assistant_qs[:5]:
            print(f"- {assistant.name} (Type: {assistant.assistant_type})")
            
    except Exception as e:
        print(f"Error during search: {e}")
        
    print("Test completed.")

if __name__ == "__main__":
    test_search()
