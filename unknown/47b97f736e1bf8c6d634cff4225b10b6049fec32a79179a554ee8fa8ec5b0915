{% extends 'base/layout.html' %}
{% load static %}

{% block title %}Change Password - 24seven{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="form-container mt-4">
                <h2 class="text-center mb-4">Change Password</h2>

                {% if form.errors %}
                    <div class="alert alert-danger">
                        <h5>Please correct the following errors:</h5>
                        <ul class="mb-0">
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    <li>{{ field|title }}: {{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                <p class="text-muted mb-4">
                    To change your password, please enter your current password and then your new password twice to verify it.
                </p>

                <form method="post">
                    {% csrf_token %}

                    <div class="mb-3">
                        <label for="id_old_password" class="form-label">Current Password</label>
                        <input type="password" name="old_password" id="id_old_password" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label for="id_new_password1" class="form-label">New Password</label>
                        <input type="password" name="new_password1" id="id_new_password1" class="form-control" required>
                        {% if form.new_password1.help_text %}
                            <small class="form-text text-muted">
                                <ul class="mb-0">
                                    <li>Your password can't be too similar to your other personal information.</li>
                                    <li>Your password must contain at least 8 characters.</li>
                                    <li>Your password can't be a commonly used password.</li>
                                    <li>Your password can't be entirely numeric.</li>
                                </ul>
                            </small>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="id_new_password2" class="form-label">Confirm New Password</label>
                        <input type="password" name="new_password2" id="id_new_password2" class="form-control" required>
                        {% if form.new_password2.help_text %}
                            <small class="form-text text-muted">
                                {{ form.new_password2.help_text }}
                            </small>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Change Password</button>
                    </div>
                </form>

                <hr>

                <div class="text-center">
                    <a href="{% url 'accounts:dashboard' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
