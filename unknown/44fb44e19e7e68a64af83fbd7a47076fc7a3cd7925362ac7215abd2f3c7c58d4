# accounts/permissions.py

# Permissions specifically for the Company Owner
# Based on OWNER_PERMS_COMPANY from migration 0009
OWNER_PERMS_COMPANY = [
    # Admin Permissions
    'accounts.change_company_settings',
    'accounts.manage_billing',
    'accounts.manage_directory_listing',
    'accounts.manage_members',
    'accounts.manage_invites_links',
    'accounts.view_company_activity',
    'accounts.manage_folder_access', # Can manage folder access *for* memberships
    'accounts.change_membership_role', # Can change roles *of* memberships
    'accounts.manage_company_assistants', # Can manage assistants within the company
    # Corrected app label for folder permissions
    'accounts.add_assistantfolder',
    'accounts.change_assistantfolder',
    'accounts.delete_assistantfolder',
    'accounts.view_assistantfolder',
    # Assistant permissions remain under 'assistants' app
    'assistants.add_assistant',
    'assistants.change_assistant',
    'assistants.delete_assistant',
    'assistants.view_assistant_usage',
    'assistants.view_assistant_analytics',
    'assistants.access_all_private',
    'assistants.create_assistant_token',
    # Owner-Specific Permissions
    'accounts.delete_company_object', # Owner can delete company
]

# Define other permission sets if needed elsewhere
# ADMIN_PERMS_COMPANY = [...]
# MEMBER_PERMS_COMPANY = [...]
# VIEWER_PERMS_COMPANY = [...]
# FOLDER_ACCESS_PERMS = [...]
