/**
 * Company Detail Contact Colors CSS
 * Improves the visibility of contact information in company detail pages,
 * especially for dark mode
 */

/* Contact information styling - Light mode */
.card-body .contact-info li {
  margin-bottom: 0.8rem;
  display: flex;
  align-items: flex-start;
}

.card-body .contact-info li i {
  margin-right: 0.75rem;
  margin-top: 0.2rem;
  color: #0d6efd;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.card-body .contact-info .contact-text {
  color: #212529;
  font-weight: 500;
  font-size: 0.95rem;
}

/* Dark mode contact information styling */
[data-theme="dark"] .card-body .contact-info li i {
  color: #5a9cff;
  text-shadow: 0 0 5px rgba(90, 156, 255, 0.5);
}

[data-theme="dark"] .card-body .contact-info .contact-text {
  color: #e6e6e6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Specific styling for different contact types */
/* Location icon and text */
.card-body .contact-info li:has(i.bi-geo-alt) i {
  color: #dc3545;
}

[data-theme="dark"] .card-body .contact-info li:has(i.bi-geo-alt) i {
  color: #ff6b6b;
  text-shadow: 0 0 5px rgba(255, 107, 107, 0.5);
}

/* Phone icon and text */
.card-body .contact-info li:has(i.bi-telephone) i {
  color: #198754;
}

[data-theme="dark"] .card-body .contact-info li:has(i.bi-telephone) i {
  color: #4ade80;
  text-shadow: 0 0 5px rgba(74, 222, 128, 0.5);
}

/* Email icon and text */
.card-body .contact-info li:has(i.bi-envelope) i {
  color: #6f42c1;
}

[data-theme="dark"] .card-body .contact-info li:has(i.bi-envelope) i {
  color: #c084fc;
  text-shadow: 0 0 5px rgba(192, 132, 252, 0.5);
}

/* Website icon and text */
.card-body .contact-info li:has(i.bi-link-45deg) i {
  color: #0dcaf0;
}

[data-theme="dark"] .card-body .contact-info li:has(i.bi-link-45deg) i {
  color: #67e8f9;
  text-shadow: 0 0 5px rgba(103, 232, 249, 0.5);
}

/* Fallback for browsers that don't support :has() */
.card-body .contact-info li i.bi-geo-alt {
  color: #dc3545;
}

.card-body .contact-info li i.bi-telephone {
  color: #198754;
}

.card-body .contact-info li i.bi-envelope {
  color: #6f42c1;
}

.card-body .contact-info li i.bi-link-45deg {
  color: #0dcaf0;
}

[data-theme="dark"] .card-body .contact-info li i.bi-geo-alt {
  color: #ff6b6b;
  text-shadow: 0 0 5px rgba(255, 107, 107, 0.5);
}

[data-theme="dark"] .card-body .contact-info li i.bi-telephone {
  color: #4ade80;
  text-shadow: 0 0 5px rgba(74, 222, 128, 0.5);
}

[data-theme="dark"] .card-body .contact-info li i.bi-envelope {
  color: #c084fc;
  text-shadow: 0 0 5px rgba(192, 132, 252, 0.5);
}

[data-theme="dark"] .card-body .contact-info li i.bi-link-45deg {
  color: #67e8f9;
  text-shadow: 0 0 5px rgba(103, 232, 249, 0.5);
}
