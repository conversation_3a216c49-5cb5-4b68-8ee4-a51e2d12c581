"""
Test script for email functionality.
"""

import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from accounts.email_utils import send_html_email, send_signin_approval_email, send_team_invitation_email
from accounts.auth_utils import store_signin_approval, verify_signin_token
from accounts.models import Company, CompanyInvitation
from site_settings.models import SiteConfiguration

User = get_user_model()

def test_email_config():
    """Test email configuration."""
    from django.conf import settings
    print(f"Email settings:")
    print(f"EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
    print(f"EMAIL_HOST: {settings.EMAIL_HOST}")
    print(f"EMAIL_PORT: {settings.EMAIL_PORT}")
    print(f"EMAIL_USE_SSL: {getattr(settings, 'EMAIL_USE_SSL', False)}")
    print(f"EMAIL_USE_TLS: {getattr(settings, 'EMAIL_USE_TLS', False)}")
    print(f"EMAIL_HOST_USER: {settings.EMAIL_HOST_USER}")
    print(f"DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}")

def test_send_html_email():
    """Test sending HTML email."""
    try:
        result = send_html_email(
            subject='Test HTML Email from 24seven',
            to_email='<EMAIL>',  # Replace with a test email if needed
            template_html='accounts/email/signin_approval.html',
            template_txt='accounts/email/signin_approval.txt',
            context={
                'user': {'get_full_name': lambda: 'Test User', 'username': 'testuser'},
                'approval_url': 'https://example.com/approve',
                'expiry_hours': 24,
                'now': timezone.now(),
            }
        )
        print(f"HTML email sent successfully: {result}")
    except Exception as e:
        print(f"Error sending HTML email: {e}")

def test_signin_approval_email():
    """Test sending sign-in approval email."""
    try:
        # Get or create a test user
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',  # Replace with a test email if needed
                'is_active': True,
            }
        )

        # Generate approval URL
        approval_url = 'https://example.com/approve-signin/test-token'

        # Send approval email
        result = send_signin_approval_email(user, approval_url, 24)
        print(f"Sign-in approval email sent successfully: {result}")
    except Exception as e:
        print(f"Error sending sign-in approval email: {e}")

def test_team_invitation_email():
    """Test sending team invitation email."""
    try:
        # Get or create a test user and company
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'is_active': True,
            }
        )

        company, created = Company.objects.get_or_create(
            name='Test Company',
            defaults={
                'owner': user,
                'is_active': True,
            }
        )

        # Create a test invitation with a unique token
        import uuid
        unique_token = str(uuid.uuid4())

        # Check if invitation already exists
        existing_invitation = CompanyInvitation.objects.filter(
            company=company,
            email='<EMAIL>'
        ).first()

        if existing_invitation:
            # Update existing invitation
            existing_invitation.token = unique_token
            existing_invitation.expires_at = timezone.now() + timezone.timedelta(days=7)
            existing_invitation.metadata = {'message': 'This is a test invitation message.'}
            existing_invitation.save()
            invitation = existing_invitation
        else:
            # Create new invitation
            invitation = CompanyInvitation.objects.create(
                company=company,
                email='<EMAIL>',  # Replace with a test email if needed
                invited_by=user,
                token=unique_token,
                expires_at=timezone.now() + timezone.timedelta(days=7),
                metadata={'message': 'This is a test invitation message.'}
            )

        # Get site configuration
        site_config = SiteConfiguration.objects.first()

        # Send invitation email
        result = send_team_invitation_email(invitation, site_config)
        print(f"Team invitation email sent successfully: {result}")
    except Exception as e:
        print(f"Error sending team invitation email: {e}")

def test_auth_utils():
    """Test authentication utilities."""
    try:
        # Get or create a test user
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'is_active': True,
            }
        )

        # Store sign-in approval
        token = store_signin_approval(user, 24)
        print(f"Sign-in approval token generated: {token}")

        # Verify token
        verified_user = verify_signin_token(token)
        if verified_user:
            print(f"Token verified successfully for user: {verified_user.username}")
        else:
            print("Token verification failed")
    except Exception as e:
        print(f"Error testing auth utils: {e}")

if __name__ == '__main__':
    print("Testing email functionality...")
    test_email_config()

    # Test auth utils
    print("\nTesting authentication utilities...")
    test_auth_utils()

    # Test sign-in approval email
    print("\nTesting sign-in approval email...")
    test_signin_approval_email()

    # Test team invitation email
    print("\nTesting team invitation email...")
    test_team_invitation_email()
