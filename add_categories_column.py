import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

# SQL to add the categories column to the accounts_companyinformation table
sql = """
ALTER TABLE "accounts_companyinformation" 
ADD COLUMN "categories" text NOT NULL DEFAULT '';
"""

# Execute the SQL
with connection.cursor() as cursor:
    cursor.execute(sql)

print("Categories column added successfully!")
