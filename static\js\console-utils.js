/**
 * Console utilities for cross-browser compatibility
 * Provides fallbacks for browsers that don't support certain console features
 */

// Check if we're in Edge/IE and provide fallbacks
(function() {
    // Create safe console methods
    window.safeConsole = {
        log: function() {
            if (typeof console !== 'undefined' && console.log) {
                console.log.apply(console, arguments);
            }
        },
        error: function() {
            if (typeof console !== 'undefined' && console.error) {
                console.error.apply(console, arguments);
            }
        },
        warn: function() {
            if (typeof console !== 'undefined' && console.warn) {
                console.warn.apply(console, arguments);
            }
        },
        info: function() {
            if (typeof console !== 'undefined' && console.info) {
                console.info.apply(console, arguments);
            }
        }
    };

    // Check if we're in Edge/IE by looking for specific features
    const isEdgeOrIE = /Edge\/|Trident\/|MSIE /.test(navigator.userAgent);
    
    // If CaptLog doesn't exist or we're in Edge/IE, create a safe version
    if (isEdgeOrIE || typeof CaptLog === 'undefined') {
        window.CaptLog = {
            error: function(msg) {
                safeConsole.error('[CaptLog]', msg);
                return { 
                    toSpecial: function() { return this; }
                };
            },
            info: function(msg) {
                safeConsole.info('[CaptLog]', msg);
                return { 
                    toSpecial: function() { return this; }
                };
            }
        };
    }
})();
