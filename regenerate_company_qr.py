#!/usr/bin/env python
"""
Script to regenerate QR codes for companies.
This script will regenerate the QR code for a specific company or all companies.
"""

import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.urls import reverse
from accounts.models import Company
from utils.qr_generator import generate_model_qr_code

def regenerate_company_qr(company_id=None):
    """
    Regenerate QR code for a specific company or all companies.
    
    Args:
        company_id (int, optional): The ID of the company to regenerate the QR code for.
                                   If None, regenerate for all companies.
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if company_id:
            # Regenerate for a specific company
            try:
                company = Company.objects.get(id=company_id)
                companies = [company]
                print(f"Regenerating QR code for company: {company.name} (ID: {company.id})")
            except Company.DoesNotExist:
                print(f"Error: Company with ID {company_id} not found")
                return False
        else:
            # Regenerate for all companies
            companies = Company.objects.all()
            print(f"Regenerating QR codes for all {companies.count()} companies")
        
        success_count = 0
        error_count = 0
        
        for company in companies:
            try:
                # Store the old QR code path to delete it later
                old_qr_path = company.qr_code.path if company.qr_code else None
                
                # Generate QR code using the company's public detail URL
                url_path = reverse('accounts:public_company_detail', kwargs={'slug': company.slug})
                print(f"Using URL path: {url_path}")
                
                success = generate_model_qr_code(company, url_path, field_name='qr_code')
                
                if success:
                    # Save the company with the new QR code
                    company.save(update_fields=['qr_code'])
                    success_count += 1
                    print(f"✅ Successfully regenerated QR code for company: {company.name}")
                    print(f"QR code URL: {company.qr_code.url}")
                    
                    # Delete the old file if it exists and is different from the new one
                    if old_qr_path and os.path.exists(old_qr_path) and old_qr_path != company.qr_code.path:
                        try:
                            os.remove(old_qr_path)
                            print(f"  Deleted old QR code: {old_qr_path}")
                        except OSError as e:
                            print(f"  Could not delete old QR code: {old_qr_path} - {e}")
                else:
                    error_count += 1
                    print(f"❌ Failed to regenerate QR code for company: {company.name}")
            
            except Exception as e:
                error_count += 1
                print(f"❌ Error regenerating QR code for company {company.name}: {e}")
        
        print(f"\nSummary: {success_count} QR codes regenerated successfully, {error_count} errors")
        return success_count > 0
    
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    # Check if a company ID was provided
    company_id = None
    if len(sys.argv) > 1:
        try:
            company_id = int(sys.argv[1])
        except ValueError:
            print(f"Error: Invalid company ID: {sys.argv[1]}")
            sys.exit(1)
    
    success = regenerate_company_qr(company_id)
    sys.exit(0 if success else 1)
