/**
 * Contact Information Tables CSS
 * Special styling for contact information tables in chat bubbles
 */

/* Base styling for contact info tables */
.message-content table.contact-info {
  border: none !important;
  background: transparent !important;
  margin: 0.5rem 0 !important;
  width: 100% !important;
  table-layout: auto !important;
}

/* Remove borders from contact info table cells */
.message-content table.contact-info td,
.message-content table.contact-info th {
  border: none !important;
  padding: 0.5rem !important;
  vertical-align: top !important;
  background: transparent !important;
}

/* Style section headers in contact tables */
.message-content table.contact-info td strong,
.message-content table.contact-info th strong,
.message-content table.contact-info td b,
.message-content table.contact-info th b {
  color: #3b7dd8 !important;
  font-weight: 600 !important;
  display: block !important;
  margin-bottom: 0.25rem !important;
}

/* Style for contact info sections */
.message-content table.contact-info td:first-child {
  font-weight: 600 !important;
  min-width: 120px !important;
}

/* Responsive layout for contact tables */
@media (max-width: 768px) {
  .message-content table.contact-info {
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
  }
  
  .message-content table.contact-info tr {
    display: flex !important;
    flex-wrap: wrap !important;
    margin-bottom: 0.5rem !important;
    width: 100% !important;
  }
  
  .message-content table.contact-info td {
    flex: 1 1 200px !important;
    min-width: 0 !important;
    padding: 0.25rem 0.5rem !important;
  }
}

/* Dark mode support */
[data-theme="dark"] .message-content table.contact-info td strong,
[data-theme="dark"] .message-content table.contact-info th strong,
[data-theme="dark"] .message-content table.contact-info td b,
[data-theme="dark"] .message-content table.contact-info th b {
  color: #5a9cff !important;
}

/* Special styling for specific contact sections */
.message-content table.contact-info td:contains("CHAT"),
.message-content table.contact-info td:contains("CORPORATE OFFICE"),
.message-content table.contact-info td:contains("MEDIA & PRESS") {
  font-weight: 600 !important;
  color: #3b7dd8 !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
  padding-bottom: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}

/* Style for email links in contact tables */
.message-content table.contact-info a[href^="mailto:"] {
  color: #3b7dd8 !important;
  text-decoration: none !important;
  border-bottom: 1px dotted #3b7dd8 !important;
}

.message-content table.contact-info a[href^="mailto:"]:hover {
  text-decoration: none !important;
  border-bottom: 1px solid #3b7dd8 !important;
}

/* Dark mode support for email links */
[data-theme="dark"] .message-content table.contact-info a[href^="mailto:"] {
  color: #5a9cff !important;
  border-bottom: 1px dotted #5a9cff !important;
}

[data-theme="dark"] .message-content table.contact-info a[href^="mailto:"]:hover {
  border-bottom: 1px solid #5a9cff !important;
}

/* Fix for tables with layout issues */
.message-content > table.contact-info,
.message-content > p > table.contact-info {
  display: table !important;
  width: 100% !important;
}

/* Fix for nested tables */
.message-content table.contact-info table {
  width: 100% !important;
  margin: 0 !important;
  border: none !important;
}

/* Fix for tables without proper structure */
.message-content table.contact-info:not(:has(tbody)) {
  display: block !important;
  width: 100% !important;
}

.message-content table.contact-info:not(:has(tbody)) tr {
  display: flex !important;
  flex-wrap: wrap !important;
  margin-bottom: 0.5rem !important;
}

.message-content table.contact-info:not(:has(tbody)) td {
  flex: 1 1 200px !important;
  min-width: 0 !important;
}

/* Specific styling for location information */
.message-content table.contact-info td:contains("Location"),
.message-content table.contact-info td:contains("Address") {
  font-weight: 600 !important;
}

/* Specific styling for hours information */
.message-content table.contact-info td:contains("hours") {
  white-space: nowrap !important;
}
