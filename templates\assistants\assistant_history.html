{% extends "assistants/base.html" %}
{% load static account_tags %} {# Load tags if needed #}

{% block title %}Chat History for {{ assistant.name }}{% endblock %}

{% block head_extra %}
{# Basic styling for stars #}
<style>
.rating-stars { display: inline-flex; direction: rtl; } /* RTL for hover effect */
.rating-stars input[type="radio"] { display: none; } /* Hide radio buttons */
.rating-stars label {
    font-size: 1.5em; /* Adjust size */
    color: #ddd; /* Default star color */
    cursor: pointer;
    padding: 0 0.1em;
    transition: color 0.2s ease-in-out;
}
/* Hover effect: color stars up to the hovered one */
.rating-stars:not(:hover) input[type="radio"]:checked ~ label,
.rating-stars:hover label:hover ~ label,
.rating-stars label:hover {
    color: #f5c518; /* Gold color for selected/hovered */
}
/* Color already selected stars */
.rating-stars input[type="radio"]:checked ~ label {
    color: #f5c518;
}
.rating-status { font-size: 0.9em; margin-left: 10px; color: #6c757d; }
</style>
{% endblock %}

{% block page_header %}
    <h1 class="h2">Chat History for {{ assistant.name }}</h1>
{% endblock %}

{% block main_content %}
<div class="card">
    <div class="card-body">
        {% if page_obj.object_list %}
            {% for interaction in page_obj %}
                <div class="mb-4 p-3 border rounded {% if interaction.user %}bg-light{% else %}bg-white{% endif %}">
                    <p class="mb-1"><strong>{% if interaction.user %}{{ interaction.user.get_full_name|default:interaction.user.username }}{% else %}User{% endif %}:</strong></p>
                    <p class="mb-2 ms-3">{{ interaction.prompt|linebreaksbr }}</p>

                    <p class="mb-1"><strong>{{ assistant.name }}:</strong></p>
                    <p class="mb-2 ms-3">{{ interaction.response|linebreaksbr }}</p>

                    <div class="d-flex justify-content-between align-items-center text-muted small mt-2">
                        <span>{{ interaction.created_at|date:"Y-m-d H:i:s" }}</span>
                        <div class="d-flex align-items-center">
                            {# --- Rating Stars --- #}
                            {% if user.is_authenticated and interaction.user == user %} {# Only allow user to rate their own interactions #}
                            <div class="rating-form me-3" data-interaction-id="{{ interaction.id }}">
                                <div class="rating-stars">
                                    {% for i in "54321"|make_list %}
                                        <input type="radio" id="star{{ i }}-{{ interaction.id }}" name="rating-{{ interaction.id }}" value="{{ i }}" {% if interaction.rating == i|add:"0" %}checked{% endif %}>
                                        <label for="star{{ i }}-{{ interaction.id }}" title="{{ i }} stars">&#9733;</label>
                                    {% endfor %}
                                </div>
                                <span class="rating-status" id="status-{{ interaction.id }}">
                                    {% if interaction.rating %}Rated {{ interaction.rating }}/5{% else %}Rate this response{% endif %}
                                </span>
                            </div>
                            {% elif interaction.rating %}
                                <span class="me-3">Rating: {{ interaction.rating }}/5</span>
                            {% endif %}
                            {# --- End Rating Stars --- #}
                            
                            <span class="me-3">Duration: {{ interaction.duration|floatformat:2 }}s</span>
                            <span>Tokens: {{ interaction.token_count }}</span>
                        </div>
                    </div>
                </div>
                {% if not forloop.last %}<hr class="my-4">{% endif %}
            {% endfor %}

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="Interaction history pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">Previous</span>
                            </li>
                        {% endif %}

                        {% for i in page_obj.paginator.page_range %}
                            {% if page_obj.number == i %}
                                <li class="page-item active" aria-current="page">
                                    <span class="page-link">{{ i }}</span>
                                </li>
                            {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">Next</span>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}

        {% else %}
            <p class="text-center text-muted">No chat history found for this assistant.</p>
        {% endif %}
    </div>
</div>

{% csrf_token %} {# Add CSRF token once for the page if not in base template #}
{% endblock %}


{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', () => {
    const ratingForms = document.querySelectorAll('.rating-form');
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

    ratingForms.forEach(form => {
        const interactionId = form.dataset.interactionId;
        const stars = form.querySelectorAll('input[type="radio"]');
        const statusEl = form.querySelector('.rating-status');

        stars.forEach(star => {
            star.addEventListener('change', async (event) => {
                const rating = event.target.value;
                
                // Update status immediately for feedback
                statusEl.textContent = 'Saving...';

                try {
                    const response = await fetch("{% url 'assistants:rate_interaction' company_id=company.id assistant_id=assistant.id %}", {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-CSRFToken': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest' 
                        },
                        body: `interaction_id=${interactionId}&rating=${rating}`
                    });

                    if (!response.ok) {
                         const errorData = await response.json().catch(() => ({ message: 'Server error' }));
                         throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();

                    if (data.status === 'success') {
                        statusEl.textContent = `Rated ${rating}/5`;
                        // Ensure correct stars remain visually checked (though radio buttons handle this)
                        stars.forEach(s => s.checked = (s.value === rating)); 
                    } else {
                        console.error('Error submitting rating:', data.message);
                        statusEl.textContent = `Error: ${data.message}`;
                    }
                } catch (error) {
                    console.error('Failed to submit rating:', error);
                    statusEl.textContent = `Error: ${error.message}`;
                    // Revert UI if needed, or keep 'Error' message
                }
            });
        });
    });
});
</script>
{% endblock %}
