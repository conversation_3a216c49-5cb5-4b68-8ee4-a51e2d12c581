{% extends "base/layout.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Superadmin Dashboard" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link href="{% static 'css/superadmin.css' %}" rel="stylesheet">
<link href="{% static 'css/superadmin-enhanced.css' %}" rel="stylesheet">
<style>
  /* Force transparent background on breadcrumb */
  .breadcrumb,
  nav[aria-label="breadcrumb"],
  ol.breadcrumb,
  .breadcrumb-item {
    background-color: transparent !important;
    background: transparent !important;
    border: none !important;
    --bs-breadcrumb-bg: transparent !important;
    --ef-background-alt: transparent !important;
  }
</style>
{% endblock %}


{% block body_class %}superadmin-body{% endblock %}

{% block content %}
<!-- Sidebar Overlay (Mobile) -->
<div class="sidebar-overlay" id="sidebarOverlay"></div>

<!-- Sidebar -->
<div class="superadmin-sidebar" id="superadminSidebar">
    <!-- Sidebar Toggle Button -->
    <button id="sidebarToggle" class="sidebar-toggle">
        <i class="bi bi-list"></i>
    </button>

    <a class="sidebar-brand" href="{% url 'superadmin:dashboard' %}">
        <i class="bi bi-shield-lock"></i>
        <span>{% trans "Superadmin" %}</span>
    </a>

    <div class="sidebar-divider"></div>

    <div class="sidebar-heading">
        {% trans "Core" %}
    </div>

    <nav class="nav flex-column">
        <div class="nav-item">
            <a class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}"
               href="{% url 'superadmin:dashboard' %}">
                <i class="bi bi-speedometer2"></i>
                <span>{% trans "Dashboard" %}</span>
            </a>
        </div>

        <div class="sidebar-divider"></div>

        <div class="sidebar-heading">
            {% trans "Management" %}
        </div>

        <div class="nav-item">
            <a class="nav-link {% if request.resolver_match.url_name == 'company_list' and not request.GET.entity_type == 'community' %}active{% endif %}"
               href="{% url 'superadmin:company_list' %}?entity_type=company">
                <i class="bi bi-building"></i>
                <span>{% trans "Companies" %}</span>
                {% if pending_company_approvals > 0 %}
                <span class="badge rounded-pill bg-danger ms-auto">{{ pending_company_approvals }}</span>
                {% endif %}
            </a>
        </div>

        <div class="nav-item">
            <a class="nav-link {% if request.resolver_match.url_name == 'company_list' and request.GET.entity_type == 'community' %}active{% endif %}"
               href="{% url 'superadmin:company_list' %}?entity_type=community">
                <i class="bi bi-people"></i>
                <span>{% trans "Communities" %}</span>
                {% if pending_community_approvals > 0 %}
                <span class="badge rounded-pill bg-danger ms-auto">{{ pending_community_approvals }}</span>
                {% endif %}
            </a>
        </div>

        <div class="nav-item">
            <a class="nav-link {% if request.resolver_match.url_name == 'assistant_list' %}active{% endif %}"
               href="{% url 'superadmin:assistant_list' %}">
                <i class="bi bi-robot"></i>
                <span>{% trans "Assistants" %}</span>
                {% if pending_assistant_tier_changes > 0 %}
                <span class="badge rounded-pill bg-warning ms-auto">{{ pending_assistant_tier_changes }}</span>
                {% endif %}
            </a>
        </div>

        <div class="nav-item">
            <a class="nav-link {% if request.resolver_match.url_name == 'community_assistant_list' %}active{% endif %}"
               href="{% url 'superadmin:community_assistant_list' %}">
                <i class="bi bi-people"></i>
                <span>{% trans "Community Assistants" %}</span>
            </a>
        </div>

        <div class="sidebar-divider"></div>

        <div class="sidebar-heading">
            {% trans "System" %}
        </div>

        <div class="nav-item">
            <a class="nav-link" href="{% url 'admin:index' %}">
                <i class="bi bi-gear"></i>
                <span>{% trans "Django Admin" %}</span>
            </a>
        </div>

        <div class="nav-item">
            <a class="nav-link" href="{% url 'home' %}">
                <i class="bi bi-house"></i>
                <span>{% trans "Back to Site" %}</span>
            </a>
        </div>
    </nav>
</div>

<!-- Main Content -->
<div class="superadmin-content" id="superadminContent">
    <!-- Page Header -->
    <div class="superadmin-header">
        <div>
            <h1>{% block page_title %}{{ title|default:"Superadmin" }}{% endblock %}</h1>
            <div style="background-color: transparent !important; border: none !important;">
                <nav aria-label="breadcrumb" class="superadmin-breadcrumb-nav" style="background: transparent !important; border: none !important;">
                    <ol class="breadcrumb superadmin-breadcrumb" style="background: transparent !important; border: none !important; --ef-background-alt: transparent !important;">
                        <li class="breadcrumb-item"><a href="{% url 'superadmin:dashboard' %}">{% trans "Superadmin" %}</a></li>
                        {% block breadcrumbs %}
                        <li class="breadcrumb-item active" aria-current="page">{% trans "Dashboard" %}</li>
                        {% endblock %}
                    </ol>
                </nav>
            </div>
        </div>
        <div class="d-flex align-items-center">
            <div class="dropdown me-3">
                <button class="btn btn-admin-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-person-circle me-2"></i>
                    <span>{{ request.user.get_full_name|default:request.user.username }}</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                    <li><a class="dropdown-item" href="{% url 'home' %}"><i class="bi bi-person me-2"></i>{% trans "Profile" %}</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="{% url 'accounts:logout' %}"><i class="bi bi-box-arrow-right me-2"></i>{% trans "Logout" %}</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Page Content -->
    <div class="superadmin-container">
        {% block superadmin_content %}
        {# Specific superadmin page content goes here #}
        {% endblock superadmin_content %}
    </div>
</div>
{% endblock content %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sidebar Toggle Functionality
    const sidebarToggle = document.getElementById('sidebarToggle');
    const superadminSidebar = document.getElementById('superadminSidebar');
    const superadminContent = document.getElementById('superadminContent');
    const sidebarOverlay = document.getElementById('sidebarOverlay');
    const body = document.body;

    // Function to toggle sidebar
    function toggleSidebar() {
        if (window.innerWidth < 992) {
            // Mobile behavior
            body.classList.toggle('sidebar-mobile-open');
        } else {
            // Desktop behavior
            body.classList.toggle('sidebar-collapsed');
        }
    }

    // Toggle sidebar when button is clicked
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
    }

    // Close sidebar when overlay is clicked (mobile)
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', function() {
            body.classList.remove('sidebar-mobile-open');
        });
    }

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 992) {
            body.classList.remove('sidebar-mobile-open');
        }
    });

    // Initialize editable fields
    const editableFields = document.querySelectorAll('.editable-field');

    editableFields.forEach(field => {
        const displaySpan = field.querySelector('span');
        const inputField = field.querySelector('input');

        if (!displaySpan || !inputField) return;

        // Double-click to edit
        field.addEventListener('dblclick', () => {
            field.classList.add('editing');
            inputField.focus();
        });

        // Save on enter or blur
        inputField.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                field.classList.remove('editing');
                displaySpan.textContent = inputField.value || 'N/A';
                // Here you would typically save the change via AJAX
            }
        });

        inputField.addEventListener('blur', () => {
            field.classList.remove('editing');
            displaySpan.textContent = inputField.value || 'N/A';
            // Here you would typically save the change via AJAX
        });
    });
});
</script>
{% block page_js %}{% endblock %}
{% endblock extra_js %}
