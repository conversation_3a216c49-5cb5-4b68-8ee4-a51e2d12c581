"""
<PERSON><PERSON><PERSON> to fix owner permissions on assistants.

This script ensures that all company owners have the correct permissions on their assistants.
"""

import os
import django
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import django
django.setup()

from django.contrib.auth.models import User
from guardian.shortcuts import assign_perm, get_perms
from accounts.models import Company
from assistants.models import Assistant

def fix_owner_assistant_permissions():
    """
    Ensure all company owners have the correct permissions on their assistants.
    """
    print("Fixing owner permissions on assistants...")
    
    # Get all companies
    companies = Company.objects.all()
    print(f"Found {companies.count()} companies")
    
    # Define the permissions that owners should have on assistants
    assistant_perms = [
        'assistants.view_assistant',
        'assistants.change_assistant',
        'assistants.delete_assistant',
        'assistants.view_assistant_usage',
        'assistants.view_assistant_analytics'
    ]
    
    # Process each company
    for company in companies:
        owner = company.owner
        if not owner:
            print(f"  Warning: Company '{company.name}' (ID: {company.id}) has no owner. Skipping.")
            continue
        
        print(f"Processing company '{company.name}' (ID: {company.id}) with owner '{owner.username}'")
        
        # Get all assistants for this company
        assistants = Assistant.objects.filter(company=company)
        print(f"  Found {assistants.count()} assistants")
        
        # Process each assistant
        for assistant in assistants:
            print(f"  Processing assistant '{assistant.name}' (ID: {assistant.id})")
            
            # Get current permissions for the owner on this assistant
            current_perms = get_perms(owner, assistant)
            print(f"    Current permissions: {current_perms}")
            
            # Assign any missing permissions
            for perm in assistant_perms:
                if perm.split('.')[-1] not in current_perms:
                    try:
                        assign_perm(perm, owner, assistant)
                        print(f"    Assigned '{perm}' to owner")
                    except Exception as e:
                        print(f"    Error assigning '{perm}' to owner: {e}")
            
            # Verify permissions after assignment
            final_perms = get_perms(owner, assistant)
            print(f"    Final permissions: {final_perms}")
    
    print("Done fixing owner permissions on assistants.")

if __name__ == "__main__":
    fix_owner_assistant_permissions()
