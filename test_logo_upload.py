import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.core.files import File
from assistants.models import Assistant

def test_logo_upload():
    # Get the assistant
    assistant = Assistant.objects.get(id=24)
    
    # Print the current logo state
    print("Before upload:")
    print(f"Logo: {assistant.logo.name if assistant.logo else 'None'}")
    print(f"Logo URL: {assistant.logo_url()}")
    print(f"Get Logo URL: {assistant.get_logo_url()}")
    
    # Upload a new logo
    with open('media/test_image.png', 'rb') as f:
        assistant.logo.save('test_logo_upload.png', File(f), save=True)
    
    # Refresh from the database
    assistant.refresh_from_db()
    
    # Print the new logo state
    print("\nAfter upload:")
    print(f"Logo: {assistant.logo.name if assistant.logo else 'None'}")
    print(f"Logo URL: {assistant.logo_url()}")
    print(f"Get Logo URL: {assistant.get_logo_url()}")
    
    return assistant

if __name__ == '__main__':
    test_logo_upload()
