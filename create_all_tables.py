import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection
from django.conf import settings

# Get database settings
db_settings = settings.DATABASES['default']

# Connect directly to PostgreSQL
conn = psycopg2.connect(
    dbname=db_settings['NAME'],
    user=db_settings['USER'],
    password=db_settings['PASSWORD'],
    host=db_settings['HOST'],
    port=db_settings['PORT']
)
conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
cursor = conn.cursor()

# Create accounts_company table
print("Creating accounts_company table...")
cursor.execute("""
CREATE TABLE IF NOT EXISTS "accounts_company" (
    "id" serial NOT NULL PRIMARY KEY,
    "name" varchar(200) NOT NULL,
    "slug" varchar(200) NOT NULL UNIQUE,
    "entity_type" varchar(20) NOT NULL,
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
    "qr_code" varchar(100) NULL,
    "tier" varchar(10) NOT NULL,
    "is_featured" boolean NOT NULL,
    "is_active" boolean NOT NULL,
    "tier_expiry_date" timestamp with time zone NULL,
    "featured_expiry_date" timestamp with time zone NULL,
    "owner_id" integer NOT NULL
);
""")
print("accounts_company table created successfully!")

# Create directory_companycategory table
print("Creating directory_companycategory table...")
cursor.execute("""
CREATE TABLE IF NOT EXISTS "directory_companycategory" (
    "id" serial NOT NULL PRIMARY KEY,
    "name" varchar(100) NOT NULL UNIQUE,
    "slug" varchar(110) NOT NULL UNIQUE
);
""")
print("directory_companycategory table created successfully!")

# Create directory_companylisting table
print("Creating directory_companylisting table...")
cursor.execute("""
CREATE TABLE IF NOT EXISTS "directory_companylisting" (
    "id" serial NOT NULL PRIMARY KEY,
    "is_listed" boolean NOT NULL,
    "featured" boolean NOT NULL,
    "description" text NOT NULL,
    "website" varchar(200) NOT NULL,
    "social_links" jsonb NOT NULL,
    "tags" jsonb NOT NULL,
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
    "avg_rating" numeric(3, 2) NOT NULL,
    "total_ratings" integer NOT NULL,
    "company_id" integer NOT NULL
);
""")
print("directory_companylisting table created successfully!")

# Create directory_companylisting_categories table
print("Creating directory_companylisting_categories table...")
cursor.execute("""
CREATE TABLE IF NOT EXISTS "directory_companylisting_categories" (
    "id" serial NOT NULL PRIMARY KEY,
    "companylisting_id" integer NOT NULL,
    "companycategory_id" integer NOT NULL,
    CONSTRAINT "directory_companylisting_categories_companylisting_id_companycategory_id_key" UNIQUE ("companylisting_id", "companycategory_id")
);
""")
print("directory_companylisting_categories table created successfully!")

# Create indexes
print("Creating indexes...")
cursor.execute("""
CREATE INDEX IF NOT EXISTS "directory_companylisting_company_id_idx" ON "directory_companylisting" ("company_id");
CREATE INDEX IF NOT EXISTS "directory_companylisting_categories_companylisting_id_idx" ON "directory_companylisting_categories" ("companylisting_id");
CREATE INDEX IF NOT EXISTS "directory_companylisting_categories_companycategory_id_idx" ON "directory_companylisting_categories" ("companycategory_id");
""")
print("Indexes created successfully!")

# Close the connection
cursor.close()
conn.close()

print("All tables created successfully!")
