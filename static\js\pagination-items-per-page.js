document.addEventListener('DOMContentLoaded', function() {
  const itemsPerPageSelect = document.getElementById('items-per-page');
  if (itemsPerPageSelect) {
    itemsPerPageSelect.addEventListener('change', function() {
      const urlParams = new URLSearchParams(window.location.search);
      urlParams.set('items_per_page', this.value);
      urlParams.delete('page'); // Reset to first page when changing items per page
      window.location.search = urlParams.toString();
    });
  }
});
