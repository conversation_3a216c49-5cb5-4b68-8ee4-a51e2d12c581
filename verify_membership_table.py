import os
import django
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import the models
try:
    from accounts.models import Membership
    from django.contrib.auth import get_user_model
    from accounts.models import Company

    print("Successfully imported models")
    User = get_user_model()
except Exception as e:
    print(f"Error importing models: {e}")
    sys.exit(1)

def test_query():
    """Test querying the Membership model with date_joined field."""
    print("Testing Membership query with date_joined field...")

    # Get all memberships
    try:
        memberships = Membership.objects.all()
        print(f"Found {memberships.count()} memberships")
    except Exception as e:
        print(f"Error querying memberships: {e}")
        return

    if memberships.exists():
        membership = memberships.first()
        print(f"Testing with membership: {membership.user.username} - {membership.company.name}")
        print(f"Date joined: {membership.date_joined}")
    else:
        print("No memberships found in the database")

        # Try to create a test membership if there are users and companies
        users = User.objects.all()
        companies = Company.objects.all()

        if users.exists() and companies.exists():
            user = users.first()
            company = companies.first()

            try:
                membership = Membership.objects.create(
                    user=user,
                    company=company
                )
                print(f"Created test membership: {membership.user.username} - {membership.company.name}")
                print(f"Date joined: {membership.date_joined}")

                # Clean up
                membership.delete()
                print("Deleted test membership")
            except Exception as e:
                print(f"Error creating membership: {e}")
        else:
            print("No users or companies found to create a test membership")

if __name__ == "__main__":
    test_query()
