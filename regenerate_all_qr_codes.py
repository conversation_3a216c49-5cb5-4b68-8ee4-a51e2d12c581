#!/usr/bin/env python
"""
Script to regenerate QR codes for all models that use them.
This script will regenerate QR codes for companies, assistants, and registration links.
"""

import os
import sys
import django
import time

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.urls import reverse
from accounts.models import Company, RegistrationLink
from assistants.models import Assistant
from utils.qr_generator import generate_model_qr_code

def regenerate_all_qr_codes(dry_run=False):
    """
    Regenerate QR codes for all models that use them.
    
    Args:
        dry_run (bool, optional): If True, don't actually regenerate QR codes,
                                 just print what would be done.
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        print("=== QR Code Regeneration ===")
        
        # Track statistics
        total_regenerated = 0
        total_skipped = 0
        total_errors = 0
        
        # Process Companies
        companies = Company.objects.all()
        print(f"\nProcessing {companies.count()} Companies...")
        
        for i, company in enumerate(companies, 1):
            print(f"  [{i}/{companies.count()}] Company: {company.name} (ID: {company.id})")
            
            try:
                # Store the old QR code path to delete it later
                old_qr_path = company.qr_code.path if company.qr_code else None
                
                # Generate QR code using the company's public detail URL
                url_path = reverse('accounts:public_company_detail', kwargs={'slug': company.slug})
                
                if not dry_run:
                    success = generate_model_qr_code(company, url_path, field_name='qr_code')
                    
                    if success:
                        # Save the company with the new QR code
                        company.save(update_fields=['qr_code'])
                        total_regenerated += 1
                        print(f"    ✅ QR code regenerated successfully")
                        
                        # Delete the old file if it exists and is different from the new one
                        if old_qr_path and os.path.exists(old_qr_path) and old_qr_path != company.qr_code.path:
                            try:
                                os.remove(old_qr_path)
                                print(f"    Deleted old QR code: {old_qr_path}")
                            except OSError as e:
                                print(f"    Could not delete old QR code: {old_qr_path} - {e}")
                    else:
                        total_skipped += 1
                        print(f"    ❌ Failed to regenerate QR code")
                else:
                    print(f"    [DRY RUN] Would regenerate QR code for URL: {url_path}")
                    total_regenerated += 1
            
            except Exception as e:
                total_errors += 1
                print(f"    ❌ Error: {e}")
            
            # Small delay to prevent overwhelming the system
            time.sleep(0.1)
        
        # Process Assistants
        assistants = Assistant.objects.all()
        print(f"\nProcessing {assistants.count()} Assistants...")
        
        for i, assistant in enumerate(assistants, 1):
            print(f"  [{i}/{assistants.count()}] Assistant: {assistant.name} (ID: {assistant.id})")
            
            try:
                # Store the old QR code path to delete it later
                old_qr_path = assistant.qr_code.path if assistant.qr_code else None
                
                # Generate QR code using the assistant's chat URL
                url_path = reverse('assistants:assistant_chat', kwargs={'slug': assistant.slug})
                
                if not dry_run:
                    success = generate_model_qr_code(assistant, url_path, field_name='qr_code')
                    
                    if success:
                        # Save the assistant with the new QR code
                        assistant.save(update_fields=['qr_code'])
                        total_regenerated += 1
                        print(f"    ✅ QR code regenerated successfully")
                        
                        # Delete the old file if it exists and is different from the new one
                        if old_qr_path and os.path.exists(old_qr_path) and old_qr_path != assistant.qr_code.path:
                            try:
                                os.remove(old_qr_path)
                                print(f"    Deleted old QR code: {old_qr_path}")
                            except OSError as e:
                                print(f"    Could not delete old QR code: {old_qr_path} - {e}")
                    else:
                        total_skipped += 1
                        print(f"    ❌ Failed to regenerate QR code")
                else:
                    print(f"    [DRY RUN] Would regenerate QR code for URL: {url_path}")
                    total_regenerated += 1
            
            except Exception as e:
                total_errors += 1
                print(f"    ❌ Error: {e}")
            
            # Small delay to prevent overwhelming the system
            time.sleep(0.1)
        
        # Process Registration Links
        links = RegistrationLink.objects.all()
        print(f"\nProcessing {links.count()} Registration Links...")
        
        for i, link in enumerate(links, 1):
            print(f"  [{i}/{links.count()}] Registration Link: {link.token} (ID: {link.id})")
            
            try:
                # Store the old QR code path to delete it later
                old_qr_path = link.qr_code.path if link.qr_code else None
                
                # Get the absolute URL for the link
                url_path = link.get_absolute_url()
                
                if not dry_run:
                    success = generate_model_qr_code(link, url_path, field_name='qr_code')
                    
                    if success:
                        # Save the link with the new QR code
                        link.save(update_fields=['qr_code'])
                        total_regenerated += 1
                        print(f"    ✅ QR code regenerated successfully")
                        
                        # Delete the old file if it exists and is different from the new one
                        if old_qr_path and os.path.exists(old_qr_path) and old_qr_path != link.qr_code.path:
                            try:
                                os.remove(old_qr_path)
                                print(f"    Deleted old QR code: {old_qr_path}")
                            except OSError as e:
                                print(f"    Could not delete old QR code: {old_qr_path} - {e}")
                    else:
                        total_skipped += 1
                        print(f"    ❌ Failed to regenerate QR code")
                else:
                    print(f"    [DRY RUN] Would regenerate QR code for URL: {url_path}")
                    total_regenerated += 1
            
            except Exception as e:
                total_errors += 1
                print(f"    ❌ Error: {e}")
            
            # Small delay to prevent overwhelming the system
            time.sleep(0.1)
        
        # Print summary
        print("\n=== Summary ===")
        print(f"Total QR codes regenerated: {total_regenerated}")
        print(f"Total QR codes skipped: {total_skipped}")
        print(f"Total errors: {total_errors}")
        
        return total_errors == 0
    
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    # Check if dry run flag was provided
    dry_run = False
    if len(sys.argv) > 1 and sys.argv[1].lower() in ('--dry-run', '-d'):
        dry_run = True
        print("Running in DRY RUN mode - no changes will be made")
    
    success = regenerate_all_qr_codes(dry_run)
    sys.exit(0 if success else 1)
