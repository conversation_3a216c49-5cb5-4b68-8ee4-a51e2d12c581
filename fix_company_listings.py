"""
Sc<PERSON>t to fix company listings in the directory.
This script will:
1. Ensure all companies have a CompanyInformation record
2. Ensure all companies have a CompanyListing record
3. Sync the visibility settings between CompanyInformation and CompanyListing
"""
import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from accounts.models import Company, CompanyInformation
from directory.models import CompanyListing, DirectorySettings
from django.db import transaction

def fix_company_listings():
    """Fix company listings in the directory."""
    print("\n=== Fixing Company Listings ===\n")
    
    # Get all companies
    companies = Company.objects.all()
    print(f"Found {companies.count()} companies")
    
    # Track counts for reporting
    info_created = 0
    listing_created = 0
    listing_updated = 0
    
    # Process each company
    for company in companies:
        print(f"\nProcessing company: {company.name} (ID: {company.id})")
        
        # Ensure CompanyInformation exists
        try:
            with transaction.atomic():
                info, info_created_now = CompanyInformation.objects.get_or_create(
                    company=company,
                    defaults={
                        'mission': '',
                        'description': '',
                        'website': '',
                        'contact_email': '',
                        'contact_phone': '',
                        'timezone': 'UTC',
                        'language': 'en',
                        'list_in_directory': True,  # Default to visible
                        'address_line1': '',
                        'address_line2': '',
                        'city': '',
                        'postal_code': '',
                        'country': '',
                        'industry': '',
                        'size': ''
                    }
                )
                
                if info_created_now:
                    info_created += 1
                    print(f"  Created CompanyInformation for {company.name}")
                else:
                    print(f"  CompanyInformation already exists for {company.name}")
                
                # Ensure CompanyListing exists and is synced with CompanyInformation
                listing, listing_created_now = CompanyListing.objects.get_or_create(
                    company=company,
                    defaults={
                        'is_listed': info.list_in_directory,
                        'featured': company.is_featured,
                        'description': info.description,
                        'website': info.website
                    }
                )
                
                if listing_created_now:
                    listing_created += 1
                    print(f"  Created CompanyListing for {company.name}")
                else:
                    print(f"  CompanyListing already exists for {company.name}")
                
                # Ensure listing.is_listed matches info.list_in_directory
                if listing.is_listed != info.list_in_directory:
                    old_value = listing.is_listed
                    listing.is_listed = info.list_in_directory
                    listing.save()
                    listing_updated += 1
                    print(f"  Updated CompanyListing.is_listed from {old_value} to {info.list_in_directory}")
        
        except Exception as e:
            print(f"  Error processing company {company.name}: {e}")
    
    # Print summary
    print("\n=== Summary ===")
    print(f"Total companies processed: {companies.count()}")
    print(f"CompanyInformation records created: {info_created}")
    print(f"CompanyListing records created: {listing_created}")
    print(f"CompanyListing records updated: {listing_updated}")

if __name__ == "__main__":
    fix_company_listings()
