/**
 * Enhanced Theme Switcher - Dark Mode Only
 * Dark mode is now the only theme option
 */

document.addEventListener('DOMContentLoaded', function() {
    // Always use dark mode
    const theme = 'dark';

    // Store the theme preference
    localStorage.setItem('theme', theme);

    // Apply the theme on initial load with transition disabled
    document.documentElement.classList.add('no-transition');
    applyTheme(theme);

    // Force a reflow to ensure the no-transition class takes effect
    document.documentElement.offsetHeight;

    // Remove the no-transition class after a short delay
    setTimeout(() => {
        document.documentElement.classList.remove('no-transition');
    }, 50);

    // Function to apply the selected theme
    function applyTheme(theme) {
        // Always use dark mode
        theme = 'dark';

        // Set the data-theme attribute on both document element and body
        document.documentElement.setAttribute('data-theme', theme);
        document.body.setAttribute('data-theme', theme);

        // Store the theme preference
        localStorage.setItem('theme', theme);

        // Update meta theme-color for browser UI
        const metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (metaThemeColor) {
            metaThemeColor.setAttribute('content', '#121212');
        }

        // Force body background color for dark mode
        document.body.style.backgroundColor = '#121212';
        document.body.style.backgroundImage = 'radial-gradient(circle at 25% 25%, rgba(30, 30, 30, 0.2) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(20, 20, 20, 0.2) 0%, transparent 50%), linear-gradient(to bottom, #121212, #0a0a0a)';
        document.body.style.backgroundAttachment = 'fixed';
        document.body.style.color = '#ffffff';

        // Remove theme toggle button
        const themeToggleBtn = document.querySelector('.theme-toggle-btn');
        if (themeToggleBtn) {
            themeToggleBtn.remove();
        }

        // Dispatch a custom event that other scripts can listen for
        document.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));

        // Apply specific fixes for TinyMCE if it exists
        if (window.tinymce) {
            applyTinyMCETheme(theme);
        }

        // Force immediate application of styles to key elements
        applyThemeToElements(theme);
    }

    // Function to apply theme to specific elements that might not be caught by CSS
    function applyThemeToElements(theme) {
        // Always apply dark mode styles
        // Apply to main content area
        const mainContent = document.querySelector('main');
        if (mainContent) {
            mainContent.style.backgroundColor = '#121212';
            mainContent.style.color = '#ffffff';
        }

        // Apply to assistant header - only if it exists
        const assistantHeader = document.querySelector('.general-assistant-header, .assistant-header');
        if (assistantHeader) {
            assistantHeader.style.background = 'linear-gradient(135deg, #1e1e1e 0%, #**********%)';
            assistantHeader.style.backgroundColor = '#1e1e1e';
            assistantHeader.style.borderBottom = '1px solid #333333';
            assistantHeader.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)';
            assistantHeader.style.display = 'block';
            assistantHeader.style.backdropFilter = 'blur(10px)';
            assistantHeader.style.webkitBackdropFilter = 'blur(10px)';
        }

        // Style header headings - only if assistantHeader exists
        if (assistantHeader) {
            const companyName = assistantHeader.querySelector('h4');
            if (companyName) {
                companyName.style.color = '#0066ff';
                companyName.style.textShadow = '0 1px 2px rgba(0, 0, 0, 0.2)';
            }

            const assistantName = assistantHeader.querySelector('h5');
            if (assistantName) {
                assistantName.style.color = '#ffffff';
                assistantName.style.textShadow = '0 1px 1px rgba(0, 0, 0, 0.1)';
            }
        }

        // Apply to chat container - match assistant header
        const chatContainer = document.querySelector('.chat-container, .general-chat-container');
        if (chatContainer) {
            chatContainer.style.background = 'linear-gradient(135deg, #1e1e1e 0%, #**********%)';
            chatContainer.style.backgroundColor = '#1e1e1e';
            chatContainer.style.border = '1px solid #333333';
            chatContainer.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)';
            chatContainer.style.borderRadius = '12px';
            chatContainer.style.overflow = 'hidden';
            chatContainer.style.display = 'block';
            chatContainer.style.backdropFilter = 'blur(10px)';
            chatContainer.style.webkitBackdropFilter = 'blur(10px)';
        }

        // Apply to filter form
        const filterForm = document.querySelector('.filter-form, form.filter-form');
        if (filterForm) {
            filterForm.style.backgroundColor = '#1a1a1a';
            filterForm.style.background = '#1a1a1a';
            filterForm.style.border = '1px solid #333333';
            filterForm.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
            filterForm.style.color = '#ffffff';
            filterForm.style.display = 'block';

            // Style input group text
            const inputGroupText = filterForm.querySelector('.input-group-text');
            if (inputGroupText) {
                inputGroupText.style.backgroundColor = '#252525';
                inputGroupText.style.borderColor = '#444444';
                inputGroupText.style.color = '#ffffff';
            }

            // Style input
            const input = filterForm.querySelector('.form-control');
            if (input) {
                input.style.backgroundColor = '#252525';
                input.style.borderColor = '#444444';
                input.style.color = '#ffffff';
            }

            // Style search button
            const searchButton = filterForm.querySelector('.btn-primary');
            if (searchButton) {
                searchButton.style.backgroundColor = '#0077ff';
                searchButton.style.borderColor = '#0066dd';
                searchButton.style.color = '#ffffff';
            }

            // Style clear button
            const clearButton = filterForm.querySelector('.btn-outline-secondary');
            if (clearButton) {
                clearButton.style.borderColor = '#444444';
                clearButton.style.color = '#ffffff';
            }
        }

        // Apply to chat box - match overall theme
        const chatBox = document.querySelector('.chat-box, .general-chat-box, #chat-box');
        if (chatBox) {
            chatBox.style.background = '#1a1a1a';
            chatBox.style.backgroundColor = '#1a1a1a';
            chatBox.style.border = 'none';
            chatBox.style.boxShadow = 'inset 0 2px 10px rgba(0, 0, 0, 0.15)';
            chatBox.style.padding = '1.5rem';

            // Force immediate update of chat bubbles
            const event = new CustomEvent('themeChanged', { detail: { theme: 'dark' } });
            document.dispatchEvent(event);
        }

        // Apply to cards
        document.querySelectorAll('.card, .directory-card').forEach(card => {
            // Skip cards in the hero section which are handled by homepage-dark-mode.js
            if (card.closest('.hero-section')) return;

            card.style.backgroundColor = '#252525';
            card.style.background = 'linear-gradient(145deg, #1e1e1e, #252525)';
            card.style.borderColor = '#333333';
            card.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)';
            card.style.color = '#ffffff';
        });

        // Apply to buttons
        document.querySelectorAll('.btn-primary').forEach(btn => {
            // Skip buttons in the hero section which are handled by homepage-dark-mode.js
            if (btn.closest('.hero-section')) return;

            btn.style.background = 'linear-gradient(to bottom, #0077ff, #0055cc)';
            btn.style.borderColor = 'transparent';
            btn.style.color = '#ffffff';
            btn.style.boxShadow = '0 4px 10px rgba(0, 102, 255, 0.3)';
        });

        // Apply to white sections
        document.querySelectorAll('section, .container, .container-fluid').forEach(section => {
            // Skip sections that are specifically handled by homepage-dark-mode.js
            if (section.classList.contains('hero-section') ||
                section.classList.contains('features-section') ||
                section.classList.contains('cta-section') ||
                section.closest('.hero-section') ||
                section.closest('.features-section') ||
                section.closest('.cta-section')) {
                return;
            }

            if (getComputedStyle(section).backgroundColor === 'rgb(255, 255, 255)' ||
                getComputedStyle(section).backgroundColor === '#ffffff') {
                section.style.backgroundColor = '#121212';
                section.style.color = '#ffffff';
            }
        });

        // Apply to body if it has bg-light class
        if (document.body.classList.contains('bg-light')) {
            document.body.style.backgroundColor = '#121212';
            document.body.style.color = '#ffffff';
        }
    }

    // Function to update the theme toggle button - Removed as we only use dark mode
    function updateThemeToggleButton(theme) {
        // Remove any theme toggle buttons
        const themeToggleBtn = document.querySelector('.theme-toggle-btn');
        if (themeToggleBtn) {
            themeToggleBtn.remove();
        }
    }

    // Function to create the theme toggle button - DEPRECATED
    // This function is now handled by theme-toggle-button.js
    function createThemeToggleButton() {
        // Function body removed to prevent duplicate buttons
    }

    // Function to apply theme to TinyMCE instances
    function applyTinyMCETheme(theme) {
        if (!window.tinymce) return;

        // Apply to all TinyMCE instances
        tinymce.editors.forEach(editor => {
            try {
                // If editor is initialized
                if (editor.initialized) {
                    const editorContainer = editor.getContainer();
                    if (editorContainer) {
                        // Always use dark mode
                        editorContainer.classList.add('tox-tinymce--dark');
                    }

                    // Try to refresh the UI
                    editor.ui.styleSheetLoader.unload('dark-mode-styles');
                    // Always load dark mode styles
                    editor.ui.styleSheetLoader.load('dark-mode-styles', '/static/css/tinymce-dark-mode.css');
                }
            } catch (e) {
                // Silently handle errors applying theme to TinyMCE
            }
        });
    }
});

// Add CSS for smooth transitions
document.addEventListener('DOMContentLoaded', function() {
    // Create a style element for transitions
    const style = document.createElement('style');
    style.textContent = `
        /* Smooth theme transitions */
        :root {
            transition: all 0.3s ease;
        }

        /* Disable transitions temporarily when switching themes */
        .no-transition,
        .no-transition * {
            transition: none !important;
        }

        /* Hide theme toggle button */
        .theme-toggle-btn {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            pointer-events: none !important;
        }
    `;
    document.head.appendChild(style);
});
