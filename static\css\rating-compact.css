/**
 * Compact Rating Display
 * Makes ratings display more compact to fit on a single line
 */

/* Make star rating more compact */
.star-rating {
    display: inline-flex !important;
    align-items: center !important;
    white-space: nowrap !important; /* Prevent wrapping */
    font-size: 0.75em !important; /* Smaller font size */
    line-height: 1 !important; /* Tighter line height */
    max-width: 100% !important; /* Ensure it fits in container */
}

.star-rating .stars {
    display: inline-flex !important;
    align-items: center !important;
    flex-shrink: 0 !important; /* Don't shrink stars */
}

.star-rating .stars i {
    font-size: 0.85em !important; /* Smaller stars */
    margin: 0 !important; /* No margin between stars */
    padding: 0 !important; /* No padding */
}

.star-rating .rating-count {
    margin-left: 0.2em !important; /* Reduced margin */
    font-size: 0.75em !important; /* Smaller text */
    white-space: nowrap !important; /* Prevent wrapping */
    overflow: hidden !important; /* Hide overflow */
    text-overflow: ellipsis !important; /* Add ellipsis if needed */
}

/* Ensure rating container doesn't wrap */
.rating-display-container {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important; /* Align to the right */
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 100% !important;
}
