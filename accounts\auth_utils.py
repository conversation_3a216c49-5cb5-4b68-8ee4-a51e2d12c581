"""
Authentication utility functions for the accounts app.
This module provides functions for handling sign-in approvals and other authentication-related tasks.
"""

import logging
import uuid
import os
import json
from datetime import timed<PERSON><PERSON>
from django.conf import settings
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.cache import cache, caches
from django.urls import reverse

from .email_utils import send_signin_approval_email, get_site_url

User = get_user_model()
logger = logging.getLogger(__name__)

# Cache keys
SIGNIN_APPROVAL_PREFIX = 'signin_approval:'
SIGNIN_ATTEMPTS_PREFIX = 'signin_attempts:'

# Try to get the session_tokens cache, fall back to default cache if not available
try:
    token_cache = caches['session_tokens']
except Exception as e:
    logger.warning(f"Could not get session_tokens cache: {e}. Using default cache.")
    token_cache = cache

# File-based token storage as fallback (for cPanel environments where cache might not persist)
TOKEN_FILE_DIR = os.path.join(settings.BASE_DIR, 'token_storage')
os.makedirs(TOKEN_FILE_DIR, exist_ok=True)

def generate_signin_token():
    """Generate a unique token for sign-in approval."""
    return str(uuid.uuid4())

def store_signin_approval(user, expiry_hours=24):
    """
    Store a sign-in approval request and send an approval email.

    Args:
        user: User object
        expiry_hours: Hours until the approval link expires

    Returns:
        str: The approval token
    """
    # Generate a unique token
    token = generate_signin_token()

    # Store in cache with expiration
    cache_key = f"{SIGNIN_APPROVAL_PREFIX}{token}"
    expiry_seconds = expiry_hours * 3600
    expiry_time = timezone.now() + timedelta(seconds=expiry_seconds)

    # Store token data
    token_data = {
        'user_id': user.id,
        'username': user.username,
        'email': user.email,
        'expiry': expiry_time.isoformat(),
        'created': timezone.now().isoformat(),
    }

    # Store in cache
    token_cache.set(cache_key, token_data, expiry_seconds)

    # Also store in file as backup (for cPanel environments)
    try:
        token_file = os.path.join(TOKEN_FILE_DIR, f"{token}.json")
        with open(token_file, 'w') as f:
            json.dump(token_data, f)
    except Exception as e:
        logger.warning(f"Could not write token to file: {e}")

    # Generate approval URL
    site_url = get_site_url()
    approval_url = f"{site_url}{reverse('accounts:approve_signin', kwargs={'token': token})}"

    # Send approval email
    send_signin_approval_email(user, approval_url, expiry_hours)

    logger.info(f"Created sign-in token for user {user.username} (ID: {user.id})")
    return token

def verify_signin_token(token):
    """
    Verify a sign-in approval token.

    Args:
        token: The token to verify

    Returns:
        User or None: The user if token is valid, None otherwise
    """
    if not token:
        logger.warning("Empty token provided for verification")
        return None

    cache_key = f"{SIGNIN_APPROVAL_PREFIX}{token}"

    # Try to get token data from cache
    token_data = token_cache.get(cache_key)

    # If not in cache, try to get from file
    if not token_data:
        try:
            token_file = os.path.join(TOKEN_FILE_DIR, f"{token}.json")
            if os.path.exists(token_file):
                with open(token_file, 'r') as f:
                    token_data = json.load(f)
                logger.info(f"Retrieved token data from file: {token}")
        except Exception as e:
            logger.warning(f"Could not read token from file: {e}")

    if token_data:
        try:
            # Check if token is expired
            if 'expiry' in token_data:
                expiry_time = timezone.datetime.fromisoformat(token_data['expiry'])
                if timezone.now() > expiry_time:
                    logger.warning(f"Token {token} has expired")
                    # Clean up expired token
                    token_cache.delete(cache_key)
                    try:
                        token_file = os.path.join(TOKEN_FILE_DIR, f"{token}.json")
                        if os.path.exists(token_file):
                            os.remove(token_file)
                    except Exception as e:
                        logger.warning(f"Could not delete expired token file: {e}")
                    return None

            # Get user ID from token data
            user_id = token_data.get('user_id')
            if not user_id:
                logger.warning(f"Token {token} has no user ID")
                return None

            # Get user from database
            user = User.objects.get(id=user_id)

            # Delete the token after use
            token_cache.delete(cache_key)
            try:
                token_file = os.path.join(TOKEN_FILE_DIR, f"{token}.json")
                if os.path.exists(token_file):
                    os.remove(token_file)
            except Exception as e:
                logger.warning(f"Could not delete token file: {e}")

            logger.info(f"Successfully verified token for user {user.username} (ID: {user.id})")
            return user
        except User.DoesNotExist:
            logger.warning(f"User with ID {user_id} not found for token {token}")
        except Exception as e:
            logger.error(f"Error verifying token {token}: {e}")
    else:
        logger.warning(f"Token {token} not found in cache or file storage")

    return None

def track_signin_attempt(username_or_email, ip_address):
    """
    Track sign-in attempts for rate limiting.

    Args:
        username_or_email: Username or email used in the attempt
        ip_address: IP address of the request

    Returns:
        int: Number of attempts in the last hour
    """
    # Track by username/email
    user_key = f"{SIGNIN_ATTEMPTS_PREFIX}user:{username_or_email}"
    user_attempts = cache.get(user_key, 0)
    cache.set(user_key, user_attempts + 1, 3600)  # 1 hour expiry

    # Track by IP address
    ip_key = f"{SIGNIN_ATTEMPTS_PREFIX}ip:{ip_address}"
    ip_attempts = cache.get(ip_key, 0)
    cache.set(ip_key, ip_attempts + 1, 3600)  # 1 hour expiry

    return max(user_attempts, ip_attempts) + 1

def should_require_approval(user, ip_address):
    """
    Determine if a sign-in should require approval.

    Args:
        user: User object
        ip_address: IP address of the request

    Returns:
        bool: True if approval should be required, False otherwise
    """
    # Always require approval for admin users
    if user.is_staff or user.is_superuser:
        return True

    # Check if this is a new IP address for this user
    ip_key = f"user:{user.id}:ip:{ip_address}"
    if not cache.get(ip_key):
        # Store this IP as known for this user (for 30 days)
        cache.set(ip_key, True, 60 * 60 * 24 * 30)
        # New IP should require approval
        return True

    # Check number of recent sign-in attempts
    attempts = track_signin_attempt(user.username, ip_address)
    if attempts > 5:  # Require approval after 5 attempts
        return True

    # Default: don't require approval
    return False
