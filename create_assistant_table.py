import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection
from django.conf import settings

# Get database settings
db_settings = settings.DATABASES['default']

# Connect directly to PostgreSQL
conn = psycopg2.connect(
    dbname=db_settings['NAME'],
    user=db_settings['USER'],
    password=db_settings['PASSWORD'],
    host=db_settings['HOST'],
    port=db_settings['PORT']
)
conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
cursor = conn.cursor()

# Create assistants_assistantfolder table first
print("Creating assistants_assistantfolder table...")
cursor.execute("""
CREATE TABLE IF NOT EXISTS "assistants_assistantfolder" (
    "id" serial NOT NULL PRIMARY KEY,
    "name" varchar(100) NOT NULL,
    "description" text NOT NULL,
    "order" integer NOT NULL,
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
    "company_id" integer NOT NULL REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED,
    "parent_id" integer NULL REFERENCES "assistants_assistantfolder" ("id") DEFERRABLE INITIALLY DEFERRED
);
""")
print("assistants_assistantfolder table created successfully!")

# Create assistants_assistant table
print("Creating assistants_assistant table...")
cursor.execute("""
CREATE TABLE IF NOT EXISTS "assistants_assistant" (
    "id" serial NOT NULL PRIMARY KEY,
    "name" varchar(100) NOT NULL,
    "persona_name" varchar(100) NOT NULL,
    "slug" varchar(100) NOT NULL UNIQUE,
    "description" text NOT NULL,
    "assistant_type" varchar(20) NOT NULL,
    "model" varchar(50) NOT NULL,
    "temperature" double precision NOT NULL,
    "max_tokens" integer NOT NULL,
    "system_prompt" text NOT NULL,
    "greeting_message" text NOT NULL,
    "knowledge_base" varchar(100) NULL,
    "custom_css" text NOT NULL,
    "website_data" jsonb NOT NULL,
    "extra_context" text NOT NULL,
    "is_active" boolean NOT NULL,
    "is_public" boolean NOT NULL,
    "qr_code" varchar(100) NULL,
    "total_interactions" integer NOT NULL,
    "average_rating" double precision NULL,
    "last_trained" timestamp with time zone NULL,
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
    "company_id" integer NOT NULL REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED,
    "created_by_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED,
    "folder_id" integer NULL REFERENCES "assistants_assistantfolder" ("id") DEFERRABLE INITIALLY DEFERRED,
    "logo" varchar(100) NULL,
    "tier" varchar(10) NOT NULL,
    "is_featured" boolean NOT NULL,
    "requested_tier" varchar(10) NULL,
    "tier_change_pending" boolean NOT NULL,
    "tier_expiry_date" timestamp with time zone NULL,
    "featured_expiry_date" timestamp with time zone NULL,
    "requested_tier_duration" varchar(10) NULL,
    "requested_featured_duration" varchar(10) NULL,
    "avatar" varchar(100) NULL,
    "linked_company_id" integer NULL REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED,
    "saved_suggestions" jsonb NOT NULL,
    "show_sidebar" boolean NOT NULL,
    "show_sidebar_public" boolean NOT NULL
);
""")
print("assistants_assistant table created successfully!")

# Create indexes
print("Creating indexes...")
cursor.execute("""
CREATE INDEX IF NOT EXISTS "assistants_assistant_company_id_idx" ON "assistants_assistant" ("company_id");
CREATE INDEX IF NOT EXISTS "assistants_assistant_created_by_id_idx" ON "assistants_assistant" ("created_by_id");
CREATE INDEX IF NOT EXISTS "assistants_assistant_folder_id_idx" ON "assistants_assistant" ("folder_id");
CREATE INDEX IF NOT EXISTS "assistants_assistant_linked_company_id_idx" ON "assistants_assistant" ("linked_company_id");
CREATE INDEX IF NOT EXISTS "assistants_assistant_company_id_assistant_type_idx" ON "assistants_assistant" ("company_id", "assistant_type");
CREATE INDEX IF NOT EXISTS "assistants_assistant_company_id_is_active_idx" ON "assistants_assistant" ("company_id", "is_active");
""")
print("Indexes created successfully!")

# Close the connection
cursor.close()
conn.close()

print("Assistant tables created successfully!")
