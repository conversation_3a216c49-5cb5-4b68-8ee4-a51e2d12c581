#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to fix the tiny letter A issue in QR codes on cPanel.
This issue occurs when the font loading fails and falls back to the default font.

This script will:
1. Test font loading and identify the issue
2. Create a custom font solution that works on cPanel
3. Update the QR generator to handle font fallback better
4. Regenerate QR codes with the proper letter size

Run this script on your cPanel server to fix the tiny letter A issue.
"""

import os
import sys
import django
import shutil
import re
from PIL import Image, ImageDraw, ImageFont

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import QR code generation functions
from utils.qr_generator import generate_qr_with_a, load_font
from accounts.models import Company

def test_font_loading():
    """Test font loading and identify the issue."""
    print("=== Testing Font Loading ===")
    
    # Test loading Arial Black at different sizes
    sizes = [20, 36, 50, 72]
    
    for size in sizes:
        print(f"\nTesting font size {size}:")
        font = load_font("Arial Black", size)
        
        # Check if it's the default font
        default_font = ImageFont.load_default()
        
        # Create test images to compare
        test_img1 = Image.new('RGB', (100, 100), 'white')
        test_img2 = Image.new('RGB', (100, 100), 'white')
        
        draw1 = ImageDraw.Draw(test_img1)
        draw2 = ImageDraw.Draw(test_img2)
        
        draw1.text((10, 10), "A", font=font, fill='black')
        draw2.text((10, 10), "A", font=default_font, fill='black')
        
        # Get text dimensions
        try:
            bbox1 = draw1.textbbox((0, 0), "A", font=font)
            width1 = bbox1[2] - bbox1[0]
            height1 = bbox1[3] - bbox1[1]
        except AttributeError:
            width1, height1 = draw1.textsize("A", font=font)
        
        try:
            bbox2 = draw2.textbbox((0, 0), "A", font=default_font)
            width2 = bbox2[2] - bbox2[0]
            height2 = bbox2[3] - bbox2[1]
        except AttributeError:
            width2, height2 = draw2.textsize("A", font=default_font)
        
        print(f"  Loaded font dimensions: {width1}x{height1}")
        print(f"  Default font dimensions: {width2}x{height2}")
        
        if width1 == width2 and height1 == height2:
            print(f"  ❌ Font loading failed - using default font")
        else:
            print(f"  ✅ Font loading successful")

def create_enhanced_font_loader():
    """Create an enhanced font loader that works better on cPanel."""
    print("\n=== Creating Enhanced Font Loader ===")
    
    # Read the current qr_generator.py file
    qr_generator_path = os.path.join('utils', 'qr_generator.py')
    
    if not os.path.exists(qr_generator_path):
        print(f"Error: QR generator file not found at {qr_generator_path}")
        return False
    
    # Create backup
    backup_path = os.path.join('utils', 'qr_generator.py.bak')
    shutil.copy2(qr_generator_path, backup_path)
    print(f"Created backup: {backup_path}")
    
    # Enhanced font loading function
    enhanced_font_loader = '''
def load_font_enhanced(font_name, font_size):
    """
    Enhanced font loader that works better on cPanel.
    If no suitable font is found, creates a scaled version of the default font.
    """
    print(f"Enhanced font loading: '{font_name}' at size {font_size}")
    
    # Try the original load_font function first
    font = load_font(font_name, font_size)
    
    # Test if we got the default font
    default_font = ImageFont.load_default()
    
    # Create test images to compare
    test_img1 = Image.new('RGB', (100, 100), 'white')
    test_img2 = Image.new('RGB', (100, 100), 'white')
    
    draw1 = ImageDraw.Draw(test_img1)
    draw2 = ImageDraw.Draw(test_img2)
    
    draw1.text((10, 10), "A", font=font, fill='black')
    draw2.text((10, 10), "A", font=default_font, fill='black')
    
    # Get text dimensions
    try:
        bbox1 = draw1.textbbox((0, 0), "A", font=font)
        width1 = bbox1[2] - bbox1[0]
        height1 = bbox1[3] - bbox1[1]
    except AttributeError:
        width1, height1 = draw1.textsize("A", font=font)
    
    try:
        bbox2 = draw2.textbbox((0, 0), "A", font=default_font)
        width2 = bbox2[2] - bbox2[0]
        height2 = bbox2[3] - bbox2[1]
    except AttributeError:
        width2, height2 = draw2.textsize("A", font=default_font)
    
    # If dimensions are the same, we're using the default font
    if width1 == width2 and height1 == height2:
        print(f"Font loading failed, creating custom font solution")
        
        # Calculate scale factor to achieve desired size
        target_size = font_size
        current_size = max(width1, height1)
        
        if current_size > 0:
            scale_factor = target_size / current_size
            print(f"Scaling factor: {scale_factor}")
            
            # For cPanel, we'll use a different approach
            # Create a larger image and scale the text
            if scale_factor > 1:
                # Create a custom font class that scales the default font
                class ScaledFont:
                    def __init__(self, base_font, scale):
                        self.base_font = base_font
                        self.scale = scale
                    
                    def getsize(self, text):
                        try:
                            bbox = ImageDraw.Draw(Image.new('RGB', (1, 1))).textbbox((0, 0), text, font=self.base_font)
                            width = bbox[2] - bbox[0]
                            height = bbox[3] - bbox[1]
                        except AttributeError:
                            width, height = ImageDraw.Draw(Image.new('RGB', (1, 1))).textsize(text, font=self.base_font)
                        return (int(width * self.scale), int(height * self.scale))
                
                scaled_font = ScaledFont(default_font, scale_factor)
                print(f"Created scaled font with factor {scale_factor}")
                return scaled_font
    
    print(f"Using loaded font successfully")
    return font
'''
    
    try:
        # Read the file
        with open(qr_generator_path, 'r') as f:
            content = f.read()
        
        # Add the enhanced font loader after the original load_font function
        # Find the end of the load_font function
        load_font_end = content.find('return ImageFont.load_default()')
        if load_font_end != -1:
            # Find the end of the line
            line_end = content.find('\n', load_font_end)
            if line_end != -1:
                # Insert the enhanced font loader
                new_content = content[:line_end+1] + enhanced_font_loader + content[line_end+1:]
                
                # Write the updated content
                with open(qr_generator_path, 'w') as f:
                    f.write(new_content)
                
                print("Enhanced font loader added successfully")
                return True
        
        print("Could not find insertion point for enhanced font loader")
        return False
        
    except Exception as e:
        print(f"Error adding enhanced font loader: {e}")
        return False

def update_qr_generation_to_use_enhanced_loader():
    """Update the QR generation function to use the enhanced font loader."""
    print("\n=== Updating QR Generation Function ===")
    
    qr_generator_path = os.path.join('utils', 'qr_generator.py')
    
    try:
        # Read the file
        with open(qr_generator_path, 'r') as f:
            content = f.read()
        
        # Replace load_font calls with load_font_enhanced calls in the generate_qr_with_a function
        # Find the generate_qr_with_a function
        func_start = content.find('def generate_qr_with_a(')
        if func_start != -1:
            # Find the next function definition to limit our search
            next_func = content.find('\ndef ', func_start + 1)
            if next_func == -1:
                next_func = len(content)
            
            # Extract the function content
            func_content = content[func_start:next_func]
            
            # Replace load_font calls with load_font_enhanced calls
            updated_func_content = func_content.replace('load_font("Arial Black"', 'load_font_enhanced("Arial Black"')
            
            # Replace in the main content
            new_content = content[:func_start] + updated_func_content + content[next_func:]
            
            # Write the updated content
            with open(qr_generator_path, 'w') as f:
                f.write(new_content)
            
            print("Updated QR generation function to use enhanced font loader")
            return True
        
        print("Could not find generate_qr_with_a function")
        return False
        
    except Exception as e:
        print(f"Error updating QR generation function: {e}")
        return False

def regenerate_qr_codes():
    """Regenerate QR codes for all companies and assistants."""
    print("\n=== Regenerating QR Codes ===")
    
    try:
        # Get all companies
        companies = Company.objects.all()
        print(f"Found {companies.count()} companies")
        
        success_count = 0
        error_count = 0
        
        for company in companies:
            print(f"Regenerating QR code for company: {company.name}")
            
            try:
                # Generate URL path for the company
                url_path = f"/accounts/company/{company.slug}/"
                
                # Import the function to generate QR codes
                from accounts.utils import generate_qr_code
                
                # Generate the QR code
                success = generate_qr_code(company, url_path, field_name='qr_code')
                
                if success:
                    company.save(update_fields=['qr_code'])
                    print(f"✅ Successfully regenerated QR code for: {company.name}")
                    success_count += 1
                else:
                    print(f"❌ Failed to regenerate QR code for: {company.name}")
                    error_count += 1
            except Exception as e:
                print(f"❌ Error regenerating QR code for {company.name}: {e}")
                error_count += 1
        
        print(f"\nCompany QR codes: {success_count} successful, {error_count} failed")
        
        # Also regenerate assistant QR codes
        try:
            from assistants.models import Assistant
            assistants = Assistant.objects.all()
            print(f"Found {assistants.count()} assistants")
            
            for assistant in assistants:
                print(f"Regenerating QR code for assistant: {assistant.name}")
                
                try:
                    url_path = f"/assistant/assistant/{assistant.slug}/chat/"
                    from accounts.utils import generate_qr_code
                    success = generate_qr_code(assistant, url_path, field_name='qr_code')
                    
                    if success:
                        assistant.save(update_fields=['qr_code'])
                        print(f"✅ Successfully regenerated QR code for: {assistant.name}")
                        success_count += 1
                    else:
                        print(f"❌ Failed to regenerate QR code for: {assistant.name}")
                        error_count += 1
                except Exception as e:
                    print(f"❌ Error regenerating QR code for {assistant.name}: {e}")
                    error_count += 1
            
            print(f"Assistant QR codes: {success_count} successful, {error_count} failed")
            
        except ImportError:
            print("Assistant model not found, skipping assistant QR codes")
        
        return success_count > 0
        
    except Exception as e:
        print(f"Error regenerating QR codes: {e}")
        return False

def main():
    """Run the cPanel font fix script."""
    print("=== cPanel QR Code Font Fix Tool ===")
    print("This tool will fix the tiny letter A issue in QR codes on cPanel.")
    
    # Test current font loading
    test_font_loading()
    
    # Create enhanced font loader
    if create_enhanced_font_loader():
        # Update QR generation to use enhanced loader
        if update_qr_generation_to_use_enhanced_loader():
            # Regenerate QR codes
            regenerate_qr_codes()
        else:
            print("Failed to update QR generation function")
    else:
        print("Failed to create enhanced font loader")
    
    print("\n=== Fix Complete ===")
    print("Please check the regenerated QR codes to ensure they have the proper letter 'A' size.")

if __name__ == "__main__":
    main()
