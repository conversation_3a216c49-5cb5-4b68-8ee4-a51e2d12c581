from .models import DirectorySettings
import logging

def directory_settings_context(request):
    """
    Adds directory settings to the template context.
    Makes the directory settings available in all templates.
    """
    logger = logging.getLogger('directory')
    
    try:
        settings = DirectorySettings.load()
        logger.debug(f"Directory settings context processor loaded: hide_standard_tier_companies={settings.hide_standard_tier_companies}, hide_standard_tier_assistants={settings.hide_standard_tier_assistants}")
        
        return {
            'hide_standard_tier_companies': settings.hide_standard_tier_companies,
            'hide_standard_tier_assistants': settings.hide_standard_tier_assistants,
            'directory_settings': settings,
        }
    except Exception as e:
        logger.error(f"Error loading directory settings in context processor: {e}")
        return {
            'hide_standard_tier_companies': False,
            'hide_standard_tier_assistants': False,
            'directory_settings': None,
        }
