{% if contexts %}
    {% for context in contexts %}
        <div class="card mb-3 border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div class="d-flex align-items-center">
                        {% if context.created_by.profile.avatar %}
                            <img src="{{ context.created_by.profile.avatar.url }}" alt="{{ context.created_by.username }}" class="rounded-circle me-2" width="32" height="32">
                        {% else %}
                            <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                <i class="bi bi-person-fill"></i>
                            </div>
                        {% endif %}
                        <div>
                            <span class="fw-bold">{{ context.created_by.get_full_name|default:context.created_by.username }}</span>
                            <small class="text-muted d-block">{{ context.created_at|timesince }} ago</small>
                        </div>
                    </div>
                    <div>
                        <span class="badge bg-primary me-1"><i class="bi bi-lightning-charge"></i> {{ context.times_used }} uses</span>
                        <span class="badge bg-success"><i class="bi bi-hand-thumbs-up"></i> <span class="upvote-count">{{ context.upvote_count }}</span> upvotes</span>
                    </div>
                </div>

                <h5 class="card-title">{{ context.title|default:"Untitled Context" }}</h5>
                <div class="card-text mb-3 context-content" data-context-id="{{ context.id }}">
                    <div class="content-preview">{{ context.text_content|safe|truncatewords_html:50 }}</div>
                    <div class="content-full" style="display: none;">{{ context.text_content|safe }}</div>
                </div>
                <div class="d-flex justify-content-between mb-3">
                    <div>
                        <button class="btn btn-sm {% if context.id in upvoted_contexts %}btn-success{% else %}btn-outline-success{% endif %} upvote-btn"
                                data-context-id="{{ context.id }}"
                                data-url="{% url 'assistants:upvote_context' assistant.company.id assistant.id context.id %}">
                            <i class="bi bi-hand-thumbs-up me-1"></i>
                            <span class="upvote-text">{% if context.id in upvoted_contexts %}Upvoted{% else %}Upvote{% endif %}</span>
                        </button>
                    </div>
                    <div>
                        <button class="btn btn-sm btn-link text-decoration-none show-more-btn" data-context-id="{{ context.id }}">
                            <i class="bi bi-chevron-down me-1"></i> Show more
                        </button>
                        {% if context.created_by == request.user %}
                            <a href="{% url 'assistants:edit_context' assistant.company.id assistant.id context.id %}" class="btn btn-sm btn-outline-secondary ms-2">
                                <i class="bi bi-pencil me-1"></i> Edit
                            </a>
                            <form method="post" action="{% url 'assistants:delete_context' assistant.company.id assistant.id context.id %}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this context? This action cannot be undone.')">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-sm btn-outline-danger ms-2">
                                    <i class="bi bi-trash me-1"></i> Delete
                                </button>
                            </form>
                        {% endif %}
                    </div>
                </div>

                {% if context.images.exists %}
                    <div class="mb-3">
                        {% for img in context.images.all %}
                            <img src="{{ img.image.url }}" alt="{{ img.caption|default:'Context image' }}" class="img-fluid rounded mb-2" style="max-height: 300px;">
                        {% endfor %}
                    </div>
                {% endif %}

                {% if context.keywords %}
                    <div class="mb-2">
                        {% for keyword in context.keywords %}
                            <span class="badge bg-light text-dark me-1">{{ keyword }}</span>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>
    {% endfor %}

    <div class="text-center mt-3">
        <a href="{% url 'assistants:contexts' assistant.company.id assistant.id %}" class="btn btn-outline-primary">
            <i class="bi bi-collection me-1"></i> View All Contributions
        </a>
    </div>
{% else %}
    <div class="alert alert-info">
        <i class="bi bi-info-circle-fill me-2"></i>
        No contexts found. Be the first to contribute knowledge!
    </div>
{% endif %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show more/less functionality for context content
    const showMoreButtons = document.querySelectorAll('.show-more-btn');

    showMoreButtons.forEach(button => {
        button.addEventListener('click', function() {
            const contextId = this.getAttribute('data-context-id');
            const contentContainer = document.querySelector(`.context-content[data-context-id="${contextId}"]`);
            const preview = contentContainer.querySelector('.content-preview');
            const fullContent = contentContainer.querySelector('.content-full');

            if (preview.style.display !== 'none') {
                preview.style.display = 'none';
                fullContent.style.display = 'block';
                this.innerHTML = '<i class="bi bi-chevron-up me-1"></i> Show less';
            } else {
                preview.style.display = 'block';
                fullContent.style.display = 'none';
                this.innerHTML = '<i class="bi bi-chevron-down me-1"></i> Show more';
            }
        });
    });

    // Upvote functionality
    const upvoteButtons = document.querySelectorAll('.upvote-btn');

    upvoteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const contextId = this.getAttribute('data-context-id');
            const url = this.getAttribute('data-url');
            const upvoteText = this.querySelector('.upvote-text');
            const upvoteCountElement = this.closest('.card').querySelector('.upvote-count');
            const upvoteCount = parseInt(upvoteCountElement.textContent);

            // Get CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

            // Send AJAX request to upvote
            fetch(url, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Update the button appearance
                    if (data.upvote_status === 'added') {
                        button.classList.remove('btn-outline-success');
                        button.classList.add('btn-success');
                        upvoteText.textContent = 'Upvoted';
                    } else {
                        button.classList.remove('btn-success');
                        button.classList.add('btn-outline-success');
                        upvoteText.textContent = 'Upvote';
                    }

                    // Update the upvote count
                    upvoteCountElement.textContent = data.upvote_count;
                }
            })
            .catch(error => {
                console.error('Error upvoting context:', error);
            });
        });
    });
});
</script>
