/**
 * Assistants List Dark Mode CSS
 * Specific dark mode styling for the assistants list page
 */

/* Main container styling */
[data-theme="dark"] .container {
  background-color: #121212;
  color: #ffffff;
}

/* Specific styling for the assistants list page */
[data-theme="dark"] body[data-theme="dark"] {
  background-color: #121212;
  color: #ffffff;
  background-image: radial-gradient(circle at 25% 25%, rgba(30, 30, 30, 0.2) 0%, transparent 50%),
                    radial-gradient(circle at 75% 75%, rgba(20, 20, 20, 0.2) 0%, transparent 50%),
                    linear-gradient(to bottom, #121212, #0a0a0a);
  background-attachment: fixed;
}

/* Page header styling */
[data-theme="dark"] h1.h2,
[data-theme="dark"] h2.h3,
[data-theme="dark"] h3.h5 {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] p.text-muted {
  color: #aaaaaa !important;
}

/* Debug button styling */
[data-theme="dark"] .btn-outline-secondary {
  color: #cccccc;
  border-color: #555555;
  background-color: transparent;
}

[data-theme="dark"] .btn-outline-secondary:hover {
  background-color: #333333;
  color: #ffffff;
}

/* Create button styling */
[data-theme="dark"] .btn-primary {
  background: linear-gradient(to bottom, #0077ff, #0055cc);
  border: none;
  color: white;
  box-shadow: 0 4px 10px rgba(0, 102, 255, 0.3);
  transition: all 0.3s ease;
}

[data-theme="dark"] .btn-primary:hover {
  background: linear-gradient(to bottom, #0088ff, #0066dd);
  box-shadow: 0 6px 15px rgba(0, 102, 255, 0.4);
  transform: translateY(-2px);
}

[data-theme="dark"] .btn-primary:active {
  background: linear-gradient(to bottom, #0055cc, #0044aa);
  box-shadow: 0 2px 5px rgba(0, 102, 255, 0.3);
  transform: translateY(1px);
}

/* Folder filter links */
[data-theme="dark"] .btn-link {
  color: #0088ff;
}

[data-theme="dark"] .btn-link:hover {
  color: #6a93ff;
}

[data-theme="dark"] .btn-link.active {
  color: #ffffff;
  background-color: rgba(0, 119, 255, 0.2);
  border-radius: 4px;
}

/* Add folder button */
[data-theme="dark"] .btn-sm.btn-outline-secondary {
  color: #cccccc;
  border-color: #555555;
  background-color: transparent;
}

[data-theme="dark"] .btn-sm.btn-outline-secondary:hover {
  background-color: #333333;
  color: #ffffff;
}

/* Tier section styling */
[data-theme="dark"] .tier-section {
  background-color: #1a1a1a;
  border: 1px solid #333333;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .tier-section h3 {
  color: #ffffff;
  border-bottom: 1px solid #333333;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

/* List group items (assistant cards) */
[data-theme="dark"] .list-group-item,
[data-theme="dark"] .directory-card {
  background-color: #252525;
  border: 1px solid #333333;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  color: #ffffff;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  margin-bottom: 15px;
  overflow: hidden;
}

[data-theme="dark"] .list-group-item:hover,
[data-theme="dark"] .directory-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

/* Logo container styling */
[data-theme="dark"] .logo-container {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

/* Override specific inline styles for logo containers */
[data-theme="dark"] [style*="background-color: white"] .logo-container,
[data-theme="dark"] .logo-container[style*="background-color: white"] {
  background-color: #1a1a1a !important;
}

/* Logo placeholder styling */
[data-theme="dark"] .logo-placeholder {
  background-color: rgba(26, 26, 26, 0.5) !important;
  color: #0077ff !important;
}

/* Override specific inline styles for logo placeholders */
[data-theme="dark"] [style*="background-color: rgba(240, 248, 255"] .logo-placeholder,
[data-theme="dark"] .logo-placeholder[style*="background-color: rgba(240, 248, 255"] {
  background-color: rgba(26, 26, 26, 0.5) !important;
}

/* Assistant name styling */
[data-theme="dark"] .assistant-name a {
  color: #ffffff;
  text-decoration: none;
}

[data-theme="dark"] .assistant-name a:hover {
  color: #0088ff;
}

/* Assistant meta information */
[data-theme="dark"] .assistant-meta {
  color: #cccccc;
}

/* Badge styling */
[data-theme="dark"] .badge.bg-secondary {
  background-color: #444444 !important;
}

[data-theme="dark"] .badge.bg-success {
  background-color: #198754 !important;
}

[data-theme="dark"] .badge.community-badge {
  background-color: #6c757d !important;
}

/* Tier badge styling */
[data-theme="dark"] .tier-badge {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .tier-badge.tier-gold {
  background-color: #ffd700;
  color: #000000;
}

[data-theme="dark"] .tier-badge.tier-silver {
  background-color: #c0c0c0;
  color: #000000;
}

[data-theme="dark"] .tier-badge.tier-bronze {
  background-color: #cd7f32;
  color: #ffffff;
}

/* Action buttons styling */
[data-theme="dark"] .btn-outline-primary {
  color: #0088ff;
  border-color: #0088ff;
  background-color: transparent;
}

[data-theme="dark"] .btn-outline-primary:hover {
  background-color: rgba(0, 136, 255, 0.2);
  color: #ffffff;
}

[data-theme="dark"] .btn-outline-info {
  color: #0dcaf0;
  border-color: #0dcaf0;
  background-color: transparent;
}

[data-theme="dark"] .btn-outline-info:hover {
  background-color: rgba(13, 202, 240, 0.2);
  color: #ffffff;
}

[data-theme="dark"] .btn-outline-danger {
  color: #dc3545;
  border-color: #dc3545;
  background-color: transparent;
}

[data-theme="dark"] .btn-outline-danger:hover {
  background-color: rgba(220, 53, 69, 0.2);
  color: #ffffff;
}

/* Modal styling */
[data-theme="dark"] .modal-content {
  background-color: #1a1a1a;
  border: 1px solid #333333;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .modal-header {
  border-bottom: 1px solid #333333;
}

[data-theme="dark"] .modal-footer {
  border-top: 1px solid #333333;
}

[data-theme="dark"] .modal-title {
  color: #ffffff;
}

[data-theme="dark"] .close {
  color: #ffffff;
}

/* Form controls in modals */
[data-theme="dark"] .form-control {
  background-color: #252525;
  border: 1px solid #333333;
  color: #ffffff;
}

[data-theme="dark"] .form-control:focus {
  border-color: #0077ff;
  box-shadow: 0 0 0 3px rgba(0, 119, 255, 0.25);
}

[data-theme="dark"] .form-label {
  color: #ffffff;
}

/* Empty state message */
[data-theme="dark"] .text-muted {
  color: #aaaaaa !important;
}

/* Fix for any hardcoded background colors */
[data-theme="dark"] [style*="background-color: white"],
[data-theme="dark"] [style*="background-color: #fff"],
[data-theme="dark"] [style*="background-color: rgb(255, 255, 255)"],
[data-theme="dark"] [style*="background-color: rgba(255, 255, 255"] {
  background-color: #1a1a1a !important;
}

/* Fix for any hardcoded text colors */
[data-theme="dark"] [style*="color: black"],
[data-theme="dark"] [style*="color: #000"],
[data-theme="dark"] [style*="color: rgb(0, 0, 0)"],
[data-theme="dark"] [style*="color: rgba(0, 0, 0"] {
  color: #ffffff !important;
}
