#!/usr/bin/env python
"""
Test script to verify that password reset emails use localhost in DEBUG mode.
"""

import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.forms import PasswordResetForm
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes

User = get_user_model()

def test_password_reset_domain():
    """Test that password reset emails use localhost domain in DEBUG mode."""
    print("Testing password reset domain configuration...")
    
    # Check current DEBUG setting
    print(f"DEBUG setting: {settings.DEBUG}")
    
    # Get or create a test user
    try:
        user, created = User.objects.get_or_create(
            username='testuser_reset',
            defaults={
                'email': '<EMAIL>',
                'is_active': True,
            }
        )
        
        if created:
            print(f"Created test user: {user.username}")
        else:
            print(f"Using existing test user: {user.username}")
        
        # Create a password reset form
        form = PasswordResetForm({'email': user.email})
        
        if form.is_valid():
            print("Password reset form is valid")
            
            # Determine expected domain based on DEBUG setting
            if settings.DEBUG:
                expected_domain = 'localhost:8000'
                expected_protocol = 'http'
            else:
                # In production, it would use the site domain
                from django.contrib.sites.models import Site
                current_site = Site.objects.get_current()
                expected_domain = current_site.domain
                expected_protocol = 'https' if getattr(settings, 'SECURE_SSL_REDIRECT', False) else 'http'
            
            print(f"Expected domain: {expected_domain}")
            print(f"Expected protocol: {expected_protocol}")
            
            # Generate the password reset URL manually to show what it would look like
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            token = default_token_generator.make_token(user)
            reset_url = f"{expected_protocol}://{expected_domain}/accounts/reset/{uid}/{token}/"
            
            print(f"Generated password reset URL: {reset_url}")
            
            # Verify the URL uses localhost in DEBUG mode
            if settings.DEBUG:
                if reset_url.startswith("http://localhost:8000"):
                    print("✅ SUCCESS: Password reset URL correctly uses localhost in DEBUG mode")
                    return True
                else:
                    print(f"❌ FAILURE: Password reset URL should start with 'http://localhost:8000'")
                    return False
            else:
                print("ℹ️  INFO: Not in DEBUG mode, using production domain")
                return True
                
        else:
            print(f"❌ ERROR: Password reset form is not valid: {form.errors}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: Failed to test password reset: {e}")
        return False

def test_custom_password_reset_view():
    """Test that our custom password reset view is properly configured."""
    print("\nTesting custom password reset view configuration...")
    
    try:
        from accounts.auth_views import CustomPasswordResetView
        print("✅ CustomPasswordResetView imported successfully")
        
        # Check if the view has the correct form_valid method
        if hasattr(CustomPasswordResetView, 'form_valid'):
            print("✅ CustomPasswordResetView has form_valid method")
        else:
            print("❌ CustomPasswordResetView missing form_valid method")
            return False
            
        # Check URL configuration
        from django.urls import reverse
        try:
            password_reset_url = reverse('accounts:password_reset')
            print(f"✅ Password reset URL resolved: {password_reset_url}")
        except Exception as e:
            print(f"❌ Error resolving password reset URL: {e}")
            return False
            
        return True
        
    except ImportError as e:
        print(f"❌ ERROR: Failed to import CustomPasswordResetView: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("TESTING PASSWORD RESET LOCALHOST CONFIGURATION")
    print("=" * 60)
    
    # Test 1: Password reset domain logic
    domain_test_passed = test_password_reset_domain()
    
    # Test 2: Custom view configuration
    view_test_passed = test_custom_password_reset_view()
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    if domain_test_passed:
        print("✅ Password reset domain logic works correctly")
    else:
        print("❌ Password reset domain logic has issues")
        
    if view_test_passed:
        print("✅ Custom password reset view is properly configured")
    else:
        print("❌ Custom password reset view has configuration issues")
    
    if domain_test_passed and view_test_passed:
        print("\n🎯 RESULT: Password reset emails should now use localhost:8000 in development")
    else:
        print("\n⚠️  WARNING: Some tests failed - please check the configuration")

if __name__ == '__main__':
    main()
