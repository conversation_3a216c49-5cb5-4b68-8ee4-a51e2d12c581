/**
 * Remove Purple Space CSS
 * Ensures there's no purple space/column on the right side of message content
 */

/* Force message content to fill entire width - Mobile only */
@media (max-width: 768px) {
  .message-content,
  .message-content.tinymce-content,
  .tinymce-content.message-content,
  span.message-content,
  div.message-content,
  span.message-content.tinymce-content,
  div.message-content.tinymce-content,
  span.tinymce-content.message-content,
  div.tinymce-content.message-content {
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    display: block !important;
    box-sizing: border-box !important;
    margin: 0 !important;
    padding-right: 1.2rem !important;
    padding-left: 1.2rem !important;
    overflow-x: hidden !important;
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    word-break: normal !important;
  }

  /* For user messages on mobile, make them 80% width */
  .user-message .message-content,
  .user-message .message-content.tinymce-content,
  .user-message .tinymce-content.message-content {
    width: 80% !important;
    min-width: 80% !important;
    max-width: 80% !important;
  }

  /* For assistant messages on mobile, make them 95% width */
  .assistant-message .message-content,
  .assistant-message .message-content.tinymce-content,
  .assistant-message .tinymce-content.message-content {
    width: 95% !important;
    min-width: 95% !important;
    max-width: 95% !important;
  }
}

/* Mobile-only styles */
@media (max-width: 768px) {
  /* Force all direct children to fill width */
  .message-content > *,
  .message-content.tinymce-content > *,
  .tinymce-content.message-content > * {
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
  }

  /* Force paragraphs to fill width */
  .message-content p,
  .tinymce-content p {
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
    display: block !important;
  }

  /* Force tables to fill width */
  .message-content table,
  .tinymce-content table {
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    table-layout: fixed !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-right: 0 !important;
    box-sizing: border-box !important;
    display: table !important;
  }

  /* Force message container to fill width */
  .message {
    width: 100% !important;
    max-width: 100% !important;
    display: flex !important;
    box-sizing: border-box !important;
    padding: 0 !important;
  }

  /* Force user message alignment */
  .user-message {
    justify-content: flex-end !important;
    width: 100% !important;
  }

  /* Force assistant message alignment */
  .assistant-message {
    justify-content: flex-start !important;
    width: 100% !important;
  }
}

/* Mobile-only styles for background and pseudo-elements */
@media (max-width: 768px) {
  /* Ensure no background colors or borders that might show purple */
  .message-content,
  .message-content.tinymce-content,
  .tinymce-content.message-content {
    background-clip: padding-box !important;
  }

  /* Remove any pseudo-elements that might add gradients or backgrounds */
  .message-content::before,
  .message-content::after,
  .message-content.tinymce-content::before,
  .message-content.tinymce-content::after,
  .tinymce-content.message-content::before,
  .tinymce-content.message-content::after {
    display: none !important;
    content: none !important;
    background: none !important;
    background-image: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
  }
}
