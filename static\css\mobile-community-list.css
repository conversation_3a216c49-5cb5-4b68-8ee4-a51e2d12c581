/**
 * Mobile Community Assistants List Improvements
 * Enhanced mobile experience for the community assistants list
 */

/* Base improvements for all screen sizes */
.directory-card {
  transition: all 0.3s ease !important;
  overflow: hidden !important;
}

.directory-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
}

.logo-container {
  transition: all 0.3s ease !important;
  overflow: hidden !important;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  /* Optimize directory cards */
  .directory-card {
    padding: 1rem !important;
    margin-bottom: 1rem !important;
    border-radius: 0.75rem !important;
  }

  /* Optimize logo container */
  .logo-container {
    height: 100px !important;
    width: 100px !important;
    min-height: 100px !important;
    min-width: 100px !important;
    max-height: 100px !important;
    max-width: 100px !important;
    margin: 0 auto 1rem auto !important;
  }

  /* Optimize directory card layout */
  .directory-card .row {
    flex-direction: column !important;
  }

  /* Optimize logo column */
  .directory-item-link-wrapper .col-md-2 {
    width: 100% !important;
    display: flex !important;
    justify-content: center !important;
    margin-bottom: 1rem !important;
    padding: 0 !important;
  }

  /* Optimize name and company column */
  .directory-item-link-wrapper .col-md-3 {
    width: 100% !important;
    text-align: center !important;
    padding: 0 !important;
    margin-bottom: 0.5rem !important;
  }

  /* Optimize description column */
  .directory-item-link-wrapper .col-md-7 {
    width: 100% !important;
    text-align: center !important;
    padding: 0 !important;
  }

  /* Optimize action buttons column */
  .directory-card .col-md-2.text-end {
    width: 100% !important;
    text-align: center !important;
    margin-top: 1rem !important;
    padding: 0 !important;
  }

  /* Optimize tier badge */
  .tier-badge {
    position: absolute !important;
    top: 0.5rem !important;
    left: 0.5rem !important;
    font-size: 0.7rem !important;
    padding: 0.25rem 0.5rem !important;
    z-index: 10 !important;
  }

  /* Optimize featured badge */
  .featured-badge {
    position: absolute !important;
    top: 0.5rem !important;
    right: 0.5rem !important;
    font-size: 0.7rem !important;
    padding: 0.25rem 0.5rem !important;
    z-index: 10 !important;
  }

  /* Optimize filter form */
  .filter-form {
    padding: 1rem !important;
    margin-bottom: 1.5rem !important;
    border-radius: 0.75rem !important;
  }

  /* Optimize filter form inputs */
  .filter-form .form-control {
    font-size: 16px !important; /* Prevents iOS zoom on focus */
    padding: 0.5rem 0.75rem !important;
  }

  /* Optimize filter form buttons */
  .filter-form .btn {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.9rem !important;
    margin-top: 0.5rem !important;
    width: 100% !important;
  }

  /* Optimize tier sections */
  .tier-section {
    padding: 1rem !important;
    margin-bottom: 1.5rem !important;
    border-radius: 0.75rem !important;
  }

  /* Optimize tier section headings */
  .tier-section h3 {
    font-size: 1.25rem !important;
    margin-bottom: 1rem !important;
    text-align: center !important;
  }

  /* Optimize featured carousel */
  .featured-carousel-container {
    padding: 1rem 0 !important;
    margin-bottom: 1.5rem !important;
  }

  /* Optimize featured carousel items */
  .featured-carousel-item {
    margin: 0 0.5rem !important;
  }

  /* Optimize featured carousel item wrapper */
  .featured-item-wrapper {
    width: 180px !important;
  }
}

/* Very small screens (up to 576px) */
@media (max-width: 576px) {
  /* Further optimize logo container */
  .logo-container {
    height: 80px !important;
    width: 80px !important;
    min-height: 80px !important;
    min-width: 80px !important;
    max-height: 80px !important;
    max-width: 80px !important;
  }

  /* Further optimize logo placeholder */
  .logo-container .logo-placeholder i {
    font-size: 50px !important;
  }

  /* Further optimize directory card */
  .directory-card {
    padding: 0.75rem !important;
  }

  /* Further optimize filter form */
  .filter-form {
    padding: 0.75rem !important;
  }

  /* Further optimize tier sections */
  .tier-section {
    padding: 0.75rem !important;
  }

  /* Further optimize tier section headings */
  .tier-section h3 {
    font-size: 1.1rem !important;
  }

  /* Optimize filter form layout */
  .filter-form .row {
    flex-direction: column !important;
  }

  .filter-form .col-md-8,
  .filter-form .col-md-4 {
    width: 100% !important;
    margin-bottom: 0.5rem !important;
  }
}

/* Tablet optimizations (between 768px and 992px) */
@media (min-width: 769px) and (max-width: 991.98px) {
  /* Optimize directory cards for tablets */
  .directory-card {
    padding: 1.25rem !important;
    margin-bottom: 1.5rem !important;
    border-radius: 0.75rem !important;
  }

  /* Optimize logo container for tablets */
  .logo-container {
    height: 90px !important;
    width: 90px !important;
    min-height: 90px !important;
    min-width: 90px !important;
    max-height: 90px !important;
    max-width: 90px !important;
    margin-bottom: 0 !important;
  }

  /* Adjust row layout for tablets - not fully stacked but optimized */
  .directory-card .row {
    flex-wrap: wrap !important;
  }

  /* Optimize logo column for tablets */
  .directory-item-link-wrapper .col-md-2 {
    width: 25% !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 0 0.5rem !important;
  }

  /* Optimize name and company column for tablets */
  .directory-item-link-wrapper .col-md-3 {
    width: 75% !important;
    padding: 0 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  /* Optimize description column for tablets */
  .directory-item-link-wrapper .col-md-7 {
    width: 100% !important;
    padding: 0 0.5rem !important;
    clear: both !important;
  }

  /* Optimize action buttons column for tablets */
  .directory-card .col-md-2.text-end {
    width: 100% !important;
    text-align: right !important;
    margin-top: 1rem !important;
    padding: 0 0.5rem !important;
  }

  /* Optimize tier badge for tablets */
  .tier-badge {
    position: absolute !important;
    top: 0.5rem !important;
    left: 0.5rem !important;
    font-size: 0.75rem !important;
    padding: 0.25rem 0.5rem !important;
    z-index: 10 !important;
  }

  /* Optimize featured badge for tablets */
  .featured-badge {
    position: absolute !important;
    top: 0.5rem !important;
    right: 0.5rem !important;
    font-size: 0.75rem !important;
    padding: 0.25rem 0.5rem !important;
    z-index: 10 !important;
  }

  /* Optimize filter form for tablets */
  .filter-form {
    padding: 1.25rem !important;
    margin-bottom: 1.5rem !important;
    border-radius: 0.75rem !important;
  }

  /* Optimize filter form inputs for tablets */
  .filter-form .form-control {
    font-size: 16px !important;
    padding: 0.5rem 0.75rem !important;
  }

  /* Optimize filter form buttons for tablets */
  .filter-form .btn {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.9rem !important;
    margin-top: 0 !important;
  }

  /* Optimize tier sections for tablets */
  .tier-section {
    padding: 1.25rem !important;
    margin-bottom: 1.5rem !important;
    border-radius: 0.75rem !important;
  }

  /* Optimize tier section headings for tablets */
  .tier-section h3 {
    font-size: 1.35rem !important;
    margin-bottom: 1.25rem !important;
  }

  /* Optimize featured carousel for tablets */
  .featured-carousel-container {
    padding: 1.25rem 0 !important;
    margin-bottom: 1.5rem !important;
  }

  /* Optimize featured carousel items for tablets */
  .featured-carousel-item {
    margin: 0 0.75rem !important;
  }

  /* Optimize featured carousel item wrapper for tablets */
  .featured-item-wrapper {
    width: 200px !important;
  }

  /* Ensure proper spacing between elements */
  .directory-card h5,
  .directory-card h6 {
    margin-bottom: 0.5rem !important;
  }

  .directory-card p {
    margin-bottom: 0.75rem !important;
  }
}

/* Dark mode optimizations for mobile */
@media (max-width: 768px) {
  [data-theme="dark"] .filter-form {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
  }

  [data-theme="dark"] .tier-section {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
  }

  [data-theme="dark"] .directory-card {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
  }
}

/* Dark mode optimizations for tablets */
@media (min-width: 769px) and (max-width: 991.98px) {
  [data-theme="dark"] .filter-form {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
  }

  [data-theme="dark"] .tier-section {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
  }

  [data-theme="dark"] .directory-card {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
  }
}
