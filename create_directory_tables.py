import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection
from django.conf import settings

# Get database settings
db_settings = settings.DATABASES['default']

# Connect directly to PostgreSQL
conn = psycopg2.connect(
    dbname=db_settings['NAME'],
    user=db_settings['USER'],
    password=db_settings['PASSWORD'],
    host=db_settings['HOST'],
    port=db_settings['PORT']
)
conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
cursor = conn.cursor()

# Check if the directory_companycategory table exists
cursor.execute("""
SELECT EXISTS (
   SELECT FROM information_schema.tables 
   WHERE table_schema = 'public'
   AND table_name = 'directory_companycategory'
);
""")
companycategory_exists = cursor.fetchone()[0]

# Create the directory_companycategory table if it doesn't exist
if not companycategory_exists:
    print("Creating directory_companycategory table...")
    cursor.execute("""
    CREATE TABLE "directory_companycategory" (
        "id" serial NOT NULL PRIMARY KEY,
        "name" varchar(100) NOT NULL UNIQUE,
        "slug" varchar(110) NOT NULL UNIQUE
    );
    """)
    print("directory_companycategory table created successfully!")
else:
    print("directory_companycategory table already exists.")

# Check if the directory_companylisting table exists
cursor.execute("""
SELECT EXISTS (
   SELECT FROM information_schema.tables 
   WHERE table_schema = 'public'
   AND table_name = 'directory_companylisting'
);
""")
companylisting_exists = cursor.fetchone()[0]

# Create the directory_companylisting table if it doesn't exist
if not companylisting_exists:
    print("Creating directory_companylisting table...")
    cursor.execute("""
    CREATE TABLE "directory_companylisting" (
        "id" serial NOT NULL PRIMARY KEY,
        "is_listed" boolean NOT NULL,
        "featured" boolean NOT NULL,
        "description" text NOT NULL,
        "website" varchar(200) NOT NULL,
        "social_links" jsonb NOT NULL,
        "tags" jsonb NOT NULL,
        "created_at" timestamp with time zone NOT NULL DEFAULT now(),
        "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
        "avg_rating" numeric(3, 2) NOT NULL,
        "total_ratings" integer NOT NULL,
        "company_id" integer NOT NULL REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED
    );
    """)
    print("directory_companylisting table created successfully!")
else:
    print("directory_companylisting table already exists.")

# Check if the directory_companylisting_categories table exists
cursor.execute("""
SELECT EXISTS (
   SELECT FROM information_schema.tables 
   WHERE table_schema = 'public'
   AND table_name = 'directory_companylisting_categories'
);
""")
companylisting_categories_exists = cursor.fetchone()[0]

# Create the directory_companylisting_categories table if it doesn't exist
if not companylisting_categories_exists:
    print("Creating directory_companylisting_categories table...")
    cursor.execute("""
    CREATE TABLE "directory_companylisting_categories" (
        "id" serial NOT NULL PRIMARY KEY,
        "companylisting_id" integer NOT NULL,
        "companycategory_id" integer NOT NULL,
        CONSTRAINT "directory_companylisting_categories_companylisting_id_companycategory_id_key" UNIQUE ("companylisting_id", "companycategory_id"),
        CONSTRAINT "directory_companylisting_categories_companylisting_id_fkey" FOREIGN KEY ("companylisting_id") REFERENCES "directory_companylisting" ("id") DEFERRABLE INITIALLY DEFERRED,
        CONSTRAINT "directory_companylisting_categories_companycategory_id_fkey" FOREIGN KEY ("companycategory_id") REFERENCES "directory_companycategory" ("id") DEFERRABLE INITIALLY DEFERRED
    );
    """)
    print("directory_companylisting_categories table created successfully!")
else:
    print("directory_companylisting_categories table already exists.")

# Create indexes
print("Creating indexes...")
cursor.execute("""
CREATE INDEX IF NOT EXISTS "directory_companylisting_company_id_idx" ON "directory_companylisting" ("company_id");
CREATE INDEX IF NOT EXISTS "directory_companylisting_categories_companylisting_id_idx" ON "directory_companylisting_categories" ("companylisting_id");
CREATE INDEX IF NOT EXISTS "directory_companylisting_categories_companycategory_id_idx" ON "directory_companylisting_categories" ("companycategory_id");
""")
print("Indexes created successfully!")

# Close the connection
cursor.close()
conn.close()

print("All directory tables created successfully!")
