#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to generate a test login link for local testing.
This script creates a login token for a specified user and prints the login URL.
"""

import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
os.environ['CPANEL_ENV'] = 'True'
django.setup()

from django.contrib.auth import get_user_model
from accounts.auth_utils import store_signin_approval
from accounts.email_utils import get_site_url

User = get_user_model()

def generate_login_link(username=None, email=None):
    """Generate a login link for the specified user."""
    # Find the user
    if username:
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            print(f"Error: User with username '{username}' not found.")
            return None
    elif email:
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            print(f"Error: User with email '{email}' not found.")
            return None
    else:
        print("Error: You must specify either a username or an email.")
        return None
    
    # Generate a token
    token = store_signin_approval(user, 24)
    
    # Generate the login URL
    site_url = get_site_url()
    login_url = f"{site_url}/accounts/approve-signin/{token}/"
    
    return {
        'user': user,
        'token': token,
        'login_url': login_url
    }

def main():
    """Main function."""
    print("=== Login Link Generator ===")
    
    # Check if username or email is provided as command line argument
    if len(sys.argv) > 1:
        if '@' in sys.argv[1]:
            email = sys.argv[1]
            username = None
        else:
            username = sys.argv[1]
            email = None
    else:
        # Ask for username or email
        print("Enter username or email of the user to generate a login link for:")
        user_input = input("> ").strip()
        
        if '@' in user_input:
            email = user_input
            username = None
        else:
            username = user_input
            email = None
    
    # Generate the login link
    result = generate_login_link(username, email)
    
    if result:
        print("\n=== Login Link Generated ===")
        print(f"User: {result['user'].username} ({result['user'].email})")
        print(f"Token: {result['token']}")
        print(f"Login URL: {result['login_url']}")
        print("\nCopy and paste this URL into your browser to test the login link.")
        print("Note: This link will expire in 24 hours.")
    
if __name__ == "__main__":
    main()
