"""
Directory test script to test directory-related functionality.
"""

import os
import django
import uuid
import json
import time

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.test import Client
from django.urls import reverse
from django.contrib.auth.models import User
from accounts.models import Company
from assistants.models import Assistant
from directory.models import CompanyListing, DirectoryRating, SavedItem, FavoriteFolder

def test_directory_listing():
    """Test directory listing functionality."""
    print("Testing directory listing...")

    # Create client
    client = Client()

    # Test assistant directory page
    response = client.get(reverse('directory:assistant_list'))
    assert response.status_code == 200, "Assistant directory page should load"

    # Test company directory page
    response = client.get(reverse('directory:company_list'))
    assert response.status_code == 200, "Company directory page should load"

    # Test community assistant directory page
    response = client.get(reverse('assistants:community_assistants_list'))
    assert response.status_code == 200, "Community assistant directory page should load"

    print("Directory listing test passed!")
    return True

def test_directory_filtering():
    """Test directory filtering functionality."""
    print("Testing directory filtering...")

    # Create client
    client = Client()

    # Test filtering by tier
    response = client.get(reverse('directory:assistant_list') + '?tier=Gold')
    assert response.status_code == 200, "Filtering by tier should work"

    # Test filtering by category
    response = client.get(reverse('directory:assistant_list') + '?category=1')
    assert response.status_code == 200, "Filtering by category should work"

    # Test filtering by search query
    response = client.get(reverse('directory:assistant_list') + '?q=test')
    assert response.status_code == 200, "Filtering by search query should work"

    # Test combined filters
    response = client.get(reverse('directory:assistant_list') + '?tier=Gold&q=test')
    assert response.status_code == 200, "Combined filters should work"

    print("Directory filtering test passed!")
    return True

def test_rating_system():
    """Test rating system functionality."""
    print("Testing rating system...")

    # Create a test user
    username = f"rating_test_user_{uuid.uuid4().hex[:8]}"
    password = "RatingTest123!"
    user = User.objects.create_user(username=username, password=password)

    # Create a test company
    company_name = f"Rating Test Company {uuid.uuid4().hex[:8]}"
    company = Company.objects.create(
        name=company_name,
        owner=user,
        is_active=True,
        is_featured=True,
        tier='Gold'
    )

    # Create a test assistant
    assistant_name = f"Rating Test Assistant {uuid.uuid4().hex[:8]}"
    assistant = Assistant.objects.create(
        name=assistant_name,
        company=company,
        assistant_type=Assistant.TYPE_GENERAL,
        is_public=True,
        is_active=True,
        tier='Gold'
    )

    # Create client and login
    client = Client()
    client.login(username=username, password=password)

    # Test rating an assistant
    response = client.post(reverse('directory:rate_assistant', kwargs={'assistant_id': assistant.id}), {
        'rating': 5,
        'comment': 'Great assistant!'
    })

    # Should redirect or return JSON response after successful rating
    assert response.status_code in [200, 302], "Rating submission should be successful"

    # Check if rating was created
    assert DirectoryRating.objects.filter(assistant=assistant, user=user).exists(), "Rating should be created"

    # Get the rating
    rating = DirectoryRating.objects.get(assistant=assistant, user=user)

    # Check rating properties
    assert rating.rating == 5, "Rating value should be 5"
    assert rating.comment == 'Great assistant!', "Comment should be correct"

    print("Rating system test passed!")
    return True

def test_favorites_functionality():
    """Test favorites functionality."""
    print("Testing favorites functionality...")

    # Create a test user
    username = f"favorites_test_user_{uuid.uuid4().hex[:8]}"
    password = "FavoritesTest123!"
    user = User.objects.create_user(username=username, password=password)

    # Create a test company
    company_name = f"Favorites Test Company {uuid.uuid4().hex[:8]}"
    company = Company.objects.create(
        name=company_name,
        owner=user,
        is_active=True
    )

    # Create a test assistant
    assistant_name = f"Favorites Test Assistant {uuid.uuid4().hex[:8]}"
    assistant = Assistant.objects.create(
        name=assistant_name,
        company=company,
        assistant_type=Assistant.TYPE_GENERAL,
        is_public=True,
        is_active=True
    )

    # Create client and login
    client = Client()
    client.login(username=username, password=password)

    # Test creating a favorites folder
    folder_name = f"Test Folder {uuid.uuid4().hex[:8]}"
    response = client.post(reverse('directory:create_folder_and_save'), {
        'folder_name': folder_name,
        'item_type': 'assistant',
        'item_id': assistant.id
    })

    # Should return JSON response after successful folder creation
    assert response.status_code == 200, "Folder creation should be successful"

    # Check if folder was created
    assert FavoriteFolder.objects.filter(name=folder_name, user=user).exists(), "Folder should be created"

    # Get the folder
    folder = FavoriteFolder.objects.get(name=folder_name, user=user)

    # Test saving an assistant to favorites
    response = client.post(reverse('directory:add_item_to_folder'), {
        'item_type': 'assistant',
        'item_id': assistant.id,
        'folder_id': folder.id
    })

    # Should return JSON response after successful save
    assert response.status_code == 200, "Saving to favorites should be successful"

    # Check if saved item was created
    assert SavedItem.objects.filter(
        user=user,
        folder=folder,
        item_type='assistant',
        assistant_id=assistant.id
    ).exists(), "Saved item should be created"

    # Test favorites page
    response = client.get(reverse('directory:my_favorites'))
    assert response.status_code == 200, "Favorites page should load"

    print("Favorites functionality test passed!")
    return True

def test_pagination():
    """Test pagination functionality."""
    print("Testing pagination...")

    # Create client
    client = Client()

    # Test pagination on assistant directory
    response = client.get(reverse('directory:assistant_list') + '?page=1')
    assert response.status_code == 200, "First page should load"

    # Test pagination with items per page
    response = client.get(reverse('directory:assistant_list') + '?page=1&per_page=10')
    assert response.status_code == 200, "Pagination with items per page should work"

    # Test pagination on company directory
    response = client.get(reverse('directory:company_list') + '?page=1')
    assert response.status_code == 200, "First page of company directory should load"

    # Test pagination on community assistant directory
    response = client.get(reverse('assistants:community_assistants_list') + '?page=1')
    assert response.status_code == 200, "First page of community assistant directory should load"

    print("Pagination test passed!")
    return True

def run_all_directory_tests():
    """Run all directory tests."""
    print("Running all directory tests...")

    results = []
    results.append(test_directory_listing())
    results.append(test_directory_filtering())
    results.append(test_rating_system())
    results.append(test_favorites_functionality())
    results.append(test_pagination())

    # Return True only if all tests passed
    return all(results)

if __name__ == "__main__":
    run_all_directory_tests()
