#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to generate a sample QR code using the company QR code generation code.
This script will generate a sample QR code with the letter "A" perfectly centered.
"""

import os
import sys
import django
from PIL import Image

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from utils.qr_generator import generate_qr_with_a

def generate_sample_qr_code():
    """
    Generate a sample QR code using the company QR code generation code.
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        print("Generating sample QR code using company QR code generation code...")
        
        # Generate a sample QR code with a test URL
        test_url = "https://example.com/sample-qr-code"
        qr_img = generate_qr_with_a(test_url, letter="A")
        
        # Save the QR code
        output_path = "sample_qr_code.png"
        qr_img.save(output_path)
        
        # Get file info
        file_size = os.path.getsize(output_path)
        img_width, img_height = qr_img.size
        
        print(f"Sample QR code generated successfully!")
        print(f"File: {output_path}")
        print(f"Size: {file_size} bytes")
        print(f"Dimensions: {img_width}x{img_height} pixels")
        
        return True
    
    except Exception as e:
        print(f"Error generating sample QR code: {e}")
        return False

if __name__ == "__main__":
    success = generate_sample_qr_code()
    sys.exit(0 if success else 1)
