/**
 * Touch-Friendly CSS
 * Improves touch interactions for mobile devices
 */

/* Base touch improvements for all screen sizes */
@media (max-width: 991.98px) {
  /* Increase touch target sizes */
  .btn,
  .nav-link,
  .dropdown-item,
  .form-control,
  .form-select,
  input[type="checkbox"],
  input[type="radio"],
  .form-check-label,
  .navbar-toggler {
    min-height: 44px !important; /* Apple's recommended minimum touch target size */
    min-width: 44px !important;
    line-height: 1.5 !important;
    padding: 0.5rem 1rem !important;
  }

  /* Adjust padding for form controls */
  .form-control,
  .form-select {
    padding: 0.5rem 0.75rem !important;
    font-size: 16px !important; /* Prevents iOS zoom on focus */
  }

  /* Improve checkbox and radio touch targets */
  .form-check {
    padding-left: 2rem !important;
    min-height: 44px !important;
    display: flex !important;
    align-items: center !important;
  }

  .form-check-input {
    width: 1.25rem !important;
    height: 1.25rem !important;
    margin-top: 0 !important;
    margin-left: -2rem !important;
  }

  /* Improve dropdown menus */
  .dropdown-menu {
    padding: 0.5rem 0 !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  }

  .dropdown-item {
    padding: 0.75rem 1.5rem !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
  }

  .dropdown-item:last-child {
    border-bottom: none !important;
  }

  /* Improve buttons */
  .btn {
    padding: 0.5rem 1rem !important;
    font-size: 1rem !important;
    border-radius: 0.375rem !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
  }

  .btn:active {
    transform: scale(0.98) !important;
  }

  /* Improve navbar */
  .navbar-toggler {
    padding: 0.5rem !important;
    border: none !important;
    border-radius: 0.375rem !important;
  }

  .navbar-toggler:focus {
    box-shadow: none !important;
    outline: none !important;
  }

  /* Improve card interactions */
  .card,
  .list-group-item {
    transition: all 0.2s ease !important;
  }

  .card:active,
  .list-group-item:active {
    transform: scale(0.98) !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
  }

  /* Improve links */
  a {
    transition: all 0.2s ease !important;
  }

  a:active {
    opacity: 0.8 !important;
  }

  /* Improve modals */
  .modal-dialog {
    margin: 0.5rem !important;
    max-width: calc(100% - 1rem) !important;
  }

  .modal-content {
    border-radius: 0.5rem !important;
    border: none !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  }

  .modal-header,
  .modal-footer {
    padding: 1rem !important;
  }

  .modal-body {
    padding: 1.5rem 1rem !important;
  }

  /* Improve tables */
  .table th,
  .table td {
    padding: 0.75rem !important;
  }

  /* Improve pagination */
  .pagination .page-link {
    min-height: 44px !important;
    min-width: 44px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
}

/* Small mobile devices (up to 576px) */
@media (max-width: 576px) {
  /* Further optimize buttons */
  .btn {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.95rem !important;
  }

  /* Further optimize form controls */
  .form-control,
  .form-select {
    padding: 0.5rem 0.75rem !important;
    font-size: 16px !important;
  }

  /* Further optimize modals */
  .modal-header,
  .modal-footer {
    padding: 0.75rem !important;
  }

  .modal-body {
    padding: 1rem !important;
  }

  /* Optimize button groups */
  .btn-group {
    display: flex !important;
    flex-wrap: wrap !important;
  }

  .btn-group > .btn {
    flex: 1 1 auto !important;
    margin-bottom: 0.25rem !important;
  }

  /* Optimize form rows */
  .row {
    margin-left: -0.5rem !important;
    margin-right: -0.5rem !important;
  }

  .col,
  [class*="col-"] {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }
}

/* Tablet-specific touch optimizations (between 768px and 992px) */
@media (min-width: 769px) and (max-width: 991.98px) {
  /* Optimize directory cards for tablet touch */
  .directory-card {
    cursor: pointer !important;
    transition: all 0.2s ease !important;
  }

  .directory-card:active {
    transform: scale(0.99) !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
  }

  /* Optimize buttons for tablet touch */
  .btn {
    padding: 0.5rem 1rem !important;
    font-size: 1rem !important;
    min-height: 44px !important;
  }

  /* Optimize form controls for tablet touch */
  .form-control,
  .form-select {
    min-height: 44px !important;
    font-size: 16px !important;
    padding: 0.5rem 0.75rem !important;
  }

  /* Ensure proper spacing for touch targets */
  .directory-item-link-wrapper .col-md-2,
  .directory-item-link-wrapper .col-md-3,
  .directory-item-link-wrapper .col-md-7 {
    padding: 0.5rem !important;
  }

  /* Optimize action buttons for tablet touch */
  .directory-card .col-md-2.text-end .btn {
    margin-left: 0.5rem !important;
    min-width: 44px !important;
  }

  /* Optimize filter form for tablet touch */
  .filter-form .form-control,
  .filter-form .btn {
    min-height: 44px !important;
  }
}

/* Add active state styles for touch interactions */
.touch-active {
  transform: scale(0.98) !important;
  opacity: 0.9 !important;
  transition: all 0.2s ease !important;
}
