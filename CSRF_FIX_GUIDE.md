# CSRF Fix Guide

This guide explains how to fix CSRF verification issues in your Django application.

## Changes Made

We've made the following changes to fix the CSRF verification issue:

1. **Updated CSRF Settings**:
   - Added `CSRF_COOKIE_HTTPONLY = False` to allow JavaScript to access the CSRF cookie
   - Added `CSRF_USE_SESSIONS = False` to store CSRF token in cookie (not session)
   - Added `CSRF_COOKIE_SAMESITE = 'Lax'` to allow CSRF cookie to be sent in same-site requests
   - Added `CSRF_TRUSTED_ORIGINS` with your development URLs

2. **Set DEBUG to True for Development**:
   - This helps with debugging and provides more detailed error messages

## How to Test

1. Restart your Django development server
2. Clear your browser cookies for your site
3. Try submitting a form again

## For Production Deployment

When deploying to production, make these additional changes:

1. **Add Your Production Domain to CSRF_TRUSTED_ORIGINS**:
   ```python
   CSRF_TRUSTED_ORIGINS = ['https://yourdomain.com', 'https://www.yourdomain.com']
   ```

2. **Set DEBUG to False**:
   ```python
   DEBUG = False
   ```

## Troubleshooting

If you're still experiencing CSRF issues:

### Check Your Templates

Make sure all your forms include the CSRF token:

```html
<form method="post">
    {% csrf_token %}
    <!-- form fields -->
    <button type="submit">Submit</button>
</form>
```

### Check for Cross-Domain Issues

If you're accessing your site from a different domain than it's hosted on, you may need to add that domain to `CSRF_TRUSTED_ORIGINS`.

### Check Browser Cookies

Make sure your browser is accepting cookies from your site.

### Check for Middleware Issues

Ensure the CSRF middleware is enabled in your settings:

```python
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',  # This should be present
    # ...
]
```

## Additional Resources

- [Django CSRF Protection Documentation](https://docs.djangoproject.com/en/stable/ref/csrf/)
- [Django Security Best Practices](https://docs.djangoproject.com/en/stable/topics/security/)
