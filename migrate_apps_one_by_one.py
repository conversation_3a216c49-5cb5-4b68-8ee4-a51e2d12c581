import os
import sys
import subprocess

def run_migrate_command(app_name=None, fake=False):
    """Run the migrate command for a specific app or all apps."""
    command = [sys.executable, "manage.py", "migrate"]
    
    if app_name:
        command.append(app_name)
    
    if fake:
        command.append("--fake")
    
    print(f"Running: {' '.join(command)}")
    
    try:
        result = subprocess.run(
            command,
            check=False,  # Don't raise an exception on non-zero exit
            capture_output=True,
            text=True
        )
        
        print(f"Exit code: {result.returncode}")
        if result.stdout:
            print(f"STDOUT: {result.stdout}")
        if result.stderr:
            print(f"STDERR: {result.stderr}")
        
        return result.returncode == 0
    except Exception as e:
        print(f"Error running command: {e}")
        return False

def main():
    """Main function to run migrations for each app."""
    # List of Django apps in the project
    apps = [
        "contenttypes",
        "auth",
        "admin",
        "sessions",
        "sites",
        "guardian",
        "impersonate",
        "accounts",
        "assistants",
        "content",
        "directory",
        "site_settings",
    ]
    
    # First try to run migrations for the core Django apps
    print("\nMigrating core Django apps first...")
    for app in ["contenttypes", "auth", "admin", "sessions", "sites"]:
        print(f"\nMigrating {app}...")
        success = run_migrate_command(app, fake=True)
        if not success:
            print(f"Failed to migrate {app} with --fake. Trying without --fake...")
            success = run_migrate_command(app)
            if not success:
                print(f"Failed to migrate {app}. Continuing with next app...")
    
    # Then try to run migrations for the third-party apps
    print("\nMigrating third-party apps...")
    for app in ["guardian", "impersonate"]:
        print(f"\nMigrating {app}...")
        success = run_migrate_command(app, fake=True)
        if not success:
            print(f"Failed to migrate {app} with --fake. Trying without --fake...")
            success = run_migrate_command(app)
            if not success:
                print(f"Failed to migrate {app}. Continuing with next app...")
    
    # Finally, run migrations for the project apps
    print("\nMigrating project apps...")
    for app in ["accounts", "assistants", "content", "directory", "site_settings"]:
        print(f"\nMigrating {app}...")
        success = run_migrate_command(app, fake=True)
        if not success:
            print(f"Failed to migrate {app} with --fake. Trying without --fake...")
            success = run_migrate_command(app)
            if not success:
                print(f"Failed to migrate {app}. Continuing with next app...")
    
    # Run a final migrate command to catch any remaining migrations
    print("\nRunning final migration to catch any remaining migrations...")
    run_migrate_command()
    
    print("\nMigration process completed.")

if __name__ == "__main__":
    main()
