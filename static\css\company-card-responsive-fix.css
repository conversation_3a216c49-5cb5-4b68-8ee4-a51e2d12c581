/**
 * Company Card Responsive Fix CSS
 * Comprehensive fixes for company cards in tablet and landscape modes
 */

/* ===== TABLET MODE FIXES (769px - 991.98px) ===== */
@media (min-width: 769px) and (max-width: 991.98px) {
  /* Improve overall card layout */
  .directory-card {
    display: flex !important;
    flex-direction: column !important;
    height: auto !important;
    min-height: 0 !important;
    padding: 1rem !important;
  }

  /* Fix row layout to use available space better */
  .directory-card .row {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    width: 100% !important;
    height: auto !important;
    gap: 0.75rem !important;
  }

  /* Adjust link wrapper width */
  .directory-item-link-wrapper {
    width: 75% !important;
    min-width: 75% !important;
    max-width: 75% !important;
    margin-right: 0 !important;
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
  }

  /* Optimize logo column */
  .directory-item-link-wrapper .col-md-3 {
    width: 90px !important;
    min-width: 90px !important;
    max-width: 90px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 0 !important;
  }

  /* Adjust logo container size */
  .logo-container {
    height: 80px !important;
    width: 80px !important;
    min-height: 80px !important;
    min-width: 80px !important;
    max-height: 80px !important;
    max-width: 80px !important;
    margin: 0 !important;
  }

  /* Optimize name and company column */
  .directory-item-link-wrapper .col-md-4 {
    width: 35% !important;
    min-width: 35% !important;
    max-width: 35% !important;
    padding: 0 0.5rem !important;
  }

  /* Optimize description column */
  .directory-item-link-wrapper .col-md-5 {
    width: 45% !important;
    min-width: 45% !important;
    max-width: 45% !important;
    padding: 0 0.5rem !important;
  }

  /* Adjust contact info column */
  .directory-card .col-md-2.d-flex.flex-column.justify-content-center {
    width: 25% !important;
    min-width: 25% !important;
    max-width: 25% !important;
    padding: 0 0.5rem !important;
    overflow: visible !important;
  }

  /* Improve contact info styling */
  .contact-info {
    margin-bottom: 0 !important;
    padding: 0 !important;
    font-size: 0.85rem !important;
  }

  .contact-info li {
    margin-bottom: 0.5rem !important;
    display: flex !important;
    align-items: flex-start !important;
    white-space: normal !important;
    overflow: visible !important;
  }

  .contact-info .contact-text {
    color: #ffffff !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    white-space: normal !important;
    overflow: visible !important;
    word-break: break-word !important;
  }

  .contact-info i {
    color: #5a9cff !important;
    text-shadow: 0 0 5px rgba(90, 156, 255, 0.3) !important;
    font-size: 1rem !important;
    margin-right: 0.5rem !important;
    width: auto !important;
    min-width: 16px !important;
  }

  /* Specific icon colors for different contact types */
  .contact-info .address-item i {
    color: #ff6b6b !important; /* Bright red for location */
  }

  .contact-info .phone-item i {
    color: #4ade80 !important; /* Bright green for phone */
  }

  .contact-info .email-item i {
    color: #c084fc !important; /* Bright purple for email */
  }

  .contact-info .website-item i {
    color: #67e8f9 !important; /* Bright cyan for website */
  }
}

/* ===== LANDSCAPE MODE FIXES (max-width: 991.98px and orientation: landscape) ===== */
@media (max-width: 991.98px) and (orientation: landscape) {
  /* Improve overall card layout */
  .directory-card {
    display: flex !important;
    flex-direction: column !important;
    height: auto !important;
    min-height: 0 !important;
    padding: 0.75rem !important;
    margin-bottom: 1rem !important;
  }

  /* Fix row layout to use available space better */
  .directory-card .row {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    width: 100% !important;
    height: auto !important;
    gap: 0.5rem !important;
    padding-top: 0.25rem !important;
  }

  /* Adjust link wrapper width */
  .directory-item-link-wrapper {
    width: 70% !important;
    min-width: 70% !important;
    max-width: 70% !important;
    margin-right: 0 !important;
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
  }

  /* Optimize logo column */
  .directory-item-link-wrapper .col-md-3 {
    width: 70px !important;
    min-width: 70px !important;
    max-width: 70px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 0 !important;
  }

  /* Adjust logo container size */
  .logo-container {
    height: 60px !important;
    width: 60px !important;
    min-height: 60px !important;
    min-width: 60px !important;
    max-height: 60px !important;
    max-width: 60px !important;
    margin: 0 !important;
  }

  /* Optimize name and company column */
  .directory-item-link-wrapper .col-md-4 {
    width: 30% !important;
    min-width: 30% !important;
    max-width: 30% !important;
    padding: 0 0.5rem !important;
  }

  /* Optimize description column */
  .directory-item-link-wrapper .col-md-5 {
    width: 40% !important;
    min-width: 40% !important;
    max-width: 40% !important;
    padding: 0 0.5rem !important;
  }

  /* Adjust contact info column */
  .directory-card .col-md-2.d-flex.flex-column.justify-content-center {
    width: 30% !important;
    min-width: 30% !important;
    max-width: 30% !important;
    padding: 0 0.5rem !important;
    overflow: visible !important;
  }

  /* Improve contact info styling */
  .contact-info {
    margin-bottom: 0 !important;
    padding: 0 !important;
    font-size: 0.8rem !important;
  }

  .contact-info li {
    margin-bottom: 0.4rem !important;
    display: flex !important;
    align-items: flex-start !important;
    white-space: normal !important;
    overflow: visible !important;
  }

  .contact-info .contact-text {
    color: #ffffff !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    white-space: normal !important;
    overflow: visible !important;
    word-break: break-word !important;
  }

  .contact-info i {
    color: #5a9cff !important;
    text-shadow: 0 0 5px rgba(90, 156, 255, 0.3) !important;
    font-size: 0.9rem !important;
    margin-right: 0.4rem !important;
    width: auto !important;
    min-width: 14px !important;
  }

  /* Specific icon colors for different contact types */
  .contact-info .address-item i {
    color: #ff6b6b !important; /* Bright red for location */
  }

  .contact-info .phone-item i {
    color: #4ade80 !important; /* Bright green for phone */
  }

  .contact-info .email-item i {
    color: #c084fc !important; /* Bright purple for email */
  }

  .contact-info .website-item i {
    color: #67e8f9 !important; /* Bright cyan for website */
  }
}
