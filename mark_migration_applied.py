"""
Script to mark a migration as applied without running it.
"""
import os
import django
from django.db import connections
from django.db.migrations.recorder import MigrationRecorder

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

def mark_migration_applied(app, name):
    """Mark a migration as applied without running it."""
    print(f"Marking migration {app}.{name} as applied...")

    # Get the migration recorder
    connection = connections['default']
    recorder = MigrationRecorder(connection)

    # Check if the migration is already applied
    migration_exists = recorder.migration_qs.filter(
        app=app,
        name=name
    ).exists()

    if not migration_exists:
        # Mark the migration as applied
        recorder.record_applied(app, name)
        print(f"Migration {app}.{name} marked as applied.")
    else:
        print(f"Migration {app}.{name} is already applied.")

if __name__ == "__main__":
    # Mark the problematic migrations as applied
    mark_migration_applied('accounts', '0004_companyinformation_categories_and_more')

    print("\nNext steps:")
    print("1. Run 'python manage.py migrate' to apply the remaining migrations.")
    print("2. If you encounter more issues, you may need to mark additional migrations")
