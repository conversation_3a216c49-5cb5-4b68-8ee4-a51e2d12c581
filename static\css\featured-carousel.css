/* Continuous Scrolling Featured Carousel Styles */
.featured-carousel-container {
    width: 100%;
    overflow: hidden;
    position: relative;
    padding: 30px 0 70px 0; /* More padding at bottom for item info */
    margin-bottom: 30px;
}

.featured-carousel-items {
    display: flex;
    animation: scroll 60s linear infinite; /* Fixed animation duration */
    width: max-content;
    min-height: 300px; /* Ensure minimum height */
    animation-play-state: running; /* Default to running */
    padding: 10px 0; /* Add padding to prevent items from being cut off */
    transition: animation-play-state 0.3s ease; /* Smooth transition for animation state */
}

.featured-carousel-item {
    flex: 0 0 auto;
    margin: 0 30px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    transition: transform 0.3s ease, filter 0.3s ease;
    z-index: 1;
}

.featured-carousel-item:hover {
    transform: scale(1.05);
    z-index: 10; /* Bring hovered item to front */
    filter: brightness(1.05);
}

.featured-item-wrapper {
    position: relative; /* For absolute positioning of like button */
    width: 240px; /* Default width for desktop */
    min-height: auto; /* Allow height to adapt to content */
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #fff; /* Ensure background is white */
    border-radius: 8px; /* Add rounded corners */
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1); /* Add shadow for depth */
    padding: 15px; /* Add padding for spacing */
    transition: all 0.3s ease; /* Smooth transition for hover effects */
    overflow: visible; /* Ensure content is not cut off */
}

.featured-item-wrapper:hover {
    transform: translateY(-5px); /* Slight lift on hover */
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15); /* Enhanced shadow on hover */
}

.featured-carousel-item a {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    text-decoration: none;
    width: 100%; /* Take full width of parent */
    color: #333;
    transition: all 0.3s ease;
}

.featured-carousel-item a:hover {
    color: #0d6efd;
    text-decoration: none;
    transform: translateY(-5px);
}

.featured-carousel-item .logo-container {
    height: 180px; /* Large square size */
    width: 180px; /* Equal to height for perfect square */
    min-height: 180px; /* Ensure minimum height */
    min-width: 180px; /* Ensure minimum width */
    max-height: 180px; /* Ensure maximum height */
    max-width: 180px; /* Ensure maximum width */
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    border-radius: 0.25rem; /* Very slight rounding for square appearance */
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 8px 15px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.08);
    position: relative; /* For absolute positioning of placeholder */
    aspect-ratio: 1/1; /* Enforce 1:1 aspect ratio */
    margin: 0 auto 20px; /* Add bottom margin */
    z-index: 2;
    flex-shrink: 0; /* Prevent logo from shrinking */
}

.featured-carousel-item .logo-container:hover {
    transform: scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25), 0 10px 20px rgba(0, 0, 0, 0.15);
}

.featured-carousel-item .logo-container img {
    max-height: 100%;
    max-width: 100%;
    height: 100%;
    width: 100%;
    object-fit: contain;
    display: block;
    padding: 10px; /* Increased padding to prevent image from touching container edges */
    transition: all 0.3s ease;
}

.featured-carousel-item .item-info {
    text-align: center;
    width: 100%;
}

.featured-carousel-item .item-info h5 {
    color: #0d6efd;
    font-size: 1.3rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 0.5rem;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    letter-spacing: -0.01em;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.featured-carousel-item .item-info p {
    font-size: 0.9rem;
    line-height: 1.5;
    color: #495057;
    margin-bottom: 0.5rem;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

.featured-carousel-item .item-info p i {
    color: #0d6efd;
    margin-right: 0.25rem;
}

.featured-carousel-item .logo-placeholder {
    color: #0d6efd; /* Brighter blue color */
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    overflow: visible; /* Allow icon to overflow slightly if needed */
    padding: 0; /* Remove any padding that might reduce space */
    background-color: rgba(240, 248, 255, 0.5); /* Light blue background */
}

.featured-carousel-item .logo-placeholder i {
    font-size: 140px; /* Increased size for better visibility */
    line-height: 1; /* Normal line height */
    display: block; /* Prevent any inline spacing issues */
    text-align: center; /* Center the icon */
    margin: 0; /* Remove any margins */
    padding: 0; /* Remove any padding */
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Add shadow for depth */
}

.featured-carousel-item .star-rating {
    display: inline-flex;
    align-items: center;
    font-size: 1.2em;
}

.featured-carousel-item .star-rating .bi-star-fill {
    color: #ffc107;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
    margin: 0 1px;
}

.featured-carousel-item .star-rating .bi-star {
    color: #dee2e6;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
    margin: 0 1px;
}

.featured-carousel-item .star-rating .rating-count {
    font-size: 0.9em;
    margin-left: 0.5em;
    color: #495057;
    font-weight: 500;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

/* Like button styling */
.featured-carousel-item .like-button {
    background-color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.08);
    width: 36px;
    height: 36px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    z-index: 10;
    position: absolute;
    top: 10px;
    right: 10px;
}

.featured-carousel-item .like-button:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.featured-carousel-item .like-button.text-danger {
    background-color: #fff0f0;
}

.featured-carousel-item .like-button.text-danger:hover {
    background-color: #ffe0e0;
}

/* Animation for continuous scrolling */
@keyframes scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(calc(-50% - 30px)); /* Adjust for margin */
    }
}

/* Ensure animation is defined globally as well */
@-webkit-keyframes scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(calc(-50% - 30px)); /* Adjust for margin */
    }
}

/* Fallback animation for browsers that don't support calc */
@keyframes scroll-fallback {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

@-webkit-keyframes scroll-fallback {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* Debug styles - commented out for production
.featured-carousel-container {
    border: 3px solid red;
    background-color: rgba(240, 248, 255, 0.5);
    padding: 20px;
    margin: 20px 0;
}

.featured-carousel-items {
    border: 2px dashed blue;
    padding: 10px;
}

.featured-carousel-item {
    border: 2px solid green;
    background-color: rgba(255, 255, 240, 0.5);
    padding: 10px;
}
*/

/* Responsive adjustments */
/* Tablet adjustments (between 768px and 992px) */
@media (min-width: 769px) and (max-width: 991.98px) {
    .featured-carousel-container {
        padding: 20px 0 50px 0; /* Reduced padding */
    }

    .featured-carousel-item {
        margin: 0 20px;
    }

    .featured-item-wrapper {
        width: 220px;
    }

    .featured-carousel-item .logo-container {
        height: 160px;
        width: 160px;
        min-height: 160px;
        min-width: 160px;
        max-height: 160px;
        max-width: 160px;
        margin-bottom: 15px;
    }

    .featured-carousel-item .logo-placeholder i {
        font-size: 120px;
    }

    .featured-carousel-item .item-info h5 {
        font-size: 1.2rem;
    }

    /* Adjust animation speed for better performance on tablets */
    .featured-carousel-items {
        animation-duration: 80s; /* Slower animation for tablets */
    }
}

/* Mobile adjustments (up to 768px) */
@media (max-width: 768px) {
    .featured-carousel-container {
        padding: 10px 0; /* Minimal padding */
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%; /* Take full width */
        height: auto; /* Allow height to adapt to content */
        overflow: hidden; /* Hide overflow items */
        margin: 0 auto;
        position: relative;
    }

    /* Center the featured items and make them follow each other */
    .featured-carousel-items {
        display: flex;
        animation: scroll 60s linear infinite; /* Original animation speed */
        width: max-content !important; /* Ensure width accommodates all items */
        justify-content: flex-start; /* Start from the beginning */
        min-height: auto; /* Allow height to adapt to content */
        align-items: center;
    }

    /* Make featured items match tier card style */
    .featured-carousel-item {
        margin: 0 15px; /* Reduced spacing between items */
        width: 95vw; /* Take up most of the viewport width */
        max-width: 95vw; /* Maximum width for larger phones */
        min-width: 320px; /* Minimum width for smaller phones */
        transform: none !important; /* No scaling - use actual size */
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .featured-item-wrapper {
        width: 100%; /* Take full width of the item */
        padding: 25px; /* Increased padding */
        margin: 0 auto;
        border-radius: 8px; /* Match tier card style */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Match tier card shadow */
        display: flex;
        flex-direction: column;
        align-items: center;
        transition: all 0.3s ease;
        background-color: #222222; /* Dark background like tier cards */
        border: 1px solid #333333; /* Dark border like tier cards */
        min-width: 300px; /* Ensure minimum width for square appearance */
    }

    /* Logo container styled like tier cards */
    .featured-carousel-item .logo-container {
        height: 240px; /* Much larger logo size */
        width: 240px;
        min-height: 240px;
        min-width: 240px;
        max-height: 240px;
        max-width: 240px;
        margin: 0 auto 15px auto;
        border-radius: 4px; /* Match tier card image style */
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        border: 1px solid #333333;
    }

    /* Logo placeholder icon */
    .featured-carousel-item .logo-placeholder i {
        font-size: 180px; /* Much larger size to match larger logo container */
        color: #0d6efd;
    }

    /* Text styling to match tier cards */
    .featured-carousel-item .item-info h5 {
        font-size: 1.2rem; /* Match tier card title size */
        margin-bottom: 0.5rem;
        text-align: center;
        font-weight: 600; /* Semi-bold text */
        color: #4da3ff; /* Blue color like in tier cards */
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .featured-carousel-item .item-info p {
        font-size: 0.9rem; /* Match tier card description size */
        margin-bottom: 0.5rem;
        text-align: center;
        color: #adb5bd; /* Light gray for better contrast on dark background */
        width: 100%;
        white-space: normal; /* Allow wrapping */
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2; /* Show 2 lines */
        line-clamp: 2; /* Standard property for compatibility */
        -webkit-box-orient: vertical;
        line-height: 1.3;
    }

    /* Community badge styling */
    .featured-carousel-item .badge {
        background-color: #333333;
        color: #adb5bd;
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        margin-bottom: 10px;
    }

    /* Rating text styling */
    .featured-carousel-item .rating-text {
        font-size: 0.8rem;
        color: #adb5bd;
        margin-top: 5px;
        text-align: right;
        width: 100%;
    }

    /* Like button styling */
    .featured-carousel-item .like-button {
        width: 40px; /* Match tier card like button */
        height: 40px;
        bottom: 15px;
        right: 15px;
        top: auto; /* Override top positioning */
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #333333;
        border: 1px solid #444444;
        color: #ffffff;
    }

    /* Chat and Rate buttons */
    .featured-carousel-item .btn-primary {
        background-color: #0d6efd;
        border-color: #0d6efd;
        color: white;
        width: 100%;
        margin-bottom: 10px;
        border-radius: 4px;
        padding: 8px 16px;
        font-size: 0.9rem;
    }

    .featured-carousel-item .btn-secondary {
        background-color: #333333;
        border-color: #444444;
        color: white;
        width: 100%;
        border-radius: 4px;
        padding: 8px 16px;
        font-size: 0.9rem;
    }

    /* Featured badge styling */
    .featured-carousel-item .featured-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: #198754;
        color: white;
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
    }

    /* Ensure the featured section takes up appropriate vertical space */
    .featured-section {
        min-height: 500px; /* Ensure enough vertical space */
        padding: 15px 10px; /* Appropriate padding */
        margin-bottom: 20px;
    }
}

/* Small mobile devices (up to 576px) */
@media (max-width: 576px) {
    .featured-carousel-container {
        padding: 10px 0; /* Appropriate padding */
        max-width: 100%; /* Take full width of the screen */
    }

    /* Make featured items properly sized for small screens */
    .featured-carousel-item {
        margin: 0 10px; /* Reduced spacing between items */
        width: 95vw; /* Take up most of the viewport width */
        max-width: 95vw; /* Maximum width for small phones */
        min-width: 300px; /* Minimum width for very small phones */
        transform: none !important; /* No scaling - use actual size */
    }

    .featured-item-wrapper {
        width: 100%; /* Take full width */
        padding: 20px; /* Increased padding */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Match tier card shadow */
        border-radius: 8px; /* Match tier card style */
        min-width: 320px; /* Ensure minimum width for square appearance */
        min-height: 360px; /* Ensure minimum height for square appearance */
    }

    /* Logo container styled like tier cards */
    .featured-carousel-item .logo-container {
        height: 200px; /* Much larger size for very small screens */
        width: 200px;
        min-height: 200px;
        min-width: 200px;
        max-height: 200px;
        max-width: 200px;
        margin: 0 auto 12px auto;
        border-radius: 4px; /* Match tier card image style */
    }

    .featured-carousel-item .logo-placeholder i {
        font-size: 160px; /* Much larger icon size */
    }

    /* Text styling to match tier cards */
    .featured-carousel-item .item-info h5 {
        font-size: 1.1rem; /* Match tier card title size */
        margin-bottom: 0.4rem;
        font-weight: 600; /* Semi-bold text */
    }

    .featured-carousel-item .item-info p {
        font-size: 0.85rem; /* Match tier card description size */
        margin-bottom: 0.4rem;
        line-height: 1.2;
    }

    /* Community badge styling */
    .featured-carousel-item .badge {
        font-size: 0.75rem;
        padding: 0.2rem 0.4rem;
        margin-bottom: 8px;
    }

    /* Rating text styling */
    .featured-carousel-item .rating-text {
        font-size: 0.75rem;
        margin-top: 4px;
    }

    /* Smaller like button */
    .featured-carousel-item .like-button {
        width: 36px;
        height: 36px;
        bottom: 12px;
        right: 12px;
    }

    /* Chat and Rate buttons */
    .featured-carousel-item .btn-primary,
    .featured-carousel-item .btn-secondary {
        padding: 6px 12px;
        font-size: 0.85rem;
        margin-bottom: 8px;
    }

    /* Featured badge styling */
    .featured-carousel-item .featured-badge {
        font-size: 0.75rem;
        padding: 0.2rem 0.4rem;
        top: 8px;
        right: 8px;
    }

    /* Keep original animation speed */
    .featured-carousel-items {
        animation-duration: 60s; /* Original animation speed */
        min-height: auto; /* Allow height to adapt to content */
    }

    /* Ensure the featured section takes up appropriate vertical space */
    .featured-section {
        min-height: 450px; /* Appropriate vertical space */
        padding: 12px 5px; /* Appropriate padding */
    }
}

/* Dark mode styles for featured carousel */
/* These styles are now redundant since we're using dark mode by default for mobile */
[data-theme="dark"] .featured-item-wrapper {
    background-color: #222222 !important;
    border-color: #333333 !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] .featured-carousel-item .logo-container {
    background-color: #252525 !important;
    border-color: #333333 !important;
}

[data-theme="dark"] .featured-carousel-item .item-info h5 {
    color: #4da3ff !important; /* Lighter blue for dark mode */
}

[data-theme="dark"] .featured-carousel-item .item-info p {
    color: #adb5bd !important;
}

[data-theme="dark"] .featured-carousel-item .logo-placeholder {
    background-color: rgba(13, 110, 253, 0.1) !important;
}

[data-theme="dark"] .featured-carousel-item .logo-placeholder i {
    color: #4da3ff !important; /* Lighter blue for dark mode */
}

[data-theme="dark"] .featured-carousel-item .like-button {
    background-color: #333333 !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

[data-theme="dark"] .featured-carousel-item .like-button.text-danger {
    background-color: rgba(220, 53, 69, 0.2) !important;
}

[data-theme="dark"] .featured-carousel-item .btn-primary {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
}

[data-theme="dark"] .featured-carousel-item .btn-secondary {
    background-color: #333333 !important;
    border-color: #444444 !important;
}

[data-theme="dark"] .featured-carousel-item .badge {
    background-color: #333333 !important;
    color: #adb5bd !important;
}

/* Light mode styles for featured carousel on mobile */
@media (max-width: 768px) {
    [data-theme="light"] .featured-item-wrapper {
        background-color: #ffffff !important;
        border-color: #dee2e6 !important;
    }

    [data-theme="light"] .featured-carousel-item .logo-container {
        background-color: #f8f9fa !important;
        border-color: #dee2e6 !important;
    }

    [data-theme="light"] .featured-carousel-item .item-info h5 {
        color: #0d6efd !important;
    }

    [data-theme="light"] .featured-carousel-item .item-info p {
        color: #495057 !important;
    }

    [data-theme="light"] .featured-carousel-item .badge {
        background-color: #e9ecef !important;
        color: #495057 !important;
    }

    [data-theme="light"] .featured-carousel-item .like-button {
        background-color: #f8f9fa !important;
        border-color: #dee2e6 !important;
        color: #212529 !important;
    }

    [data-theme="light"] .featured-carousel-item .btn-secondary {
        background-color: #6c757d !important;
        border-color: #6c757d !important;
    }
}
