{% comment %}
This template tag displays a warning alert for entities that are pending approval.
Usage: {% include 'accounts/tags/pending_approval_alert.html' with entity_type='company' entity_name=company.name %}
Parameters:
- entity_type: The type of entity (company, community, assistant)
- entity_name: The name of the entity
{% endcomment %}

<div class="alert alert-warning d-flex align-items-center mb-4" role="alert">
    <div class="d-flex align-items-center">
        <i class="bi bi-exclamation-triangle-fill fs-4 me-3"></i>
        <div>
            <h5 class="alert-heading mb-1">Pending Approval</h5>
            <p class="mb-0">
                Your {{ entity_type }} <strong>{{ entity_name }}</strong> is currently pending approval by an administrator.
                {% if entity_type == 'company' or entity_type == 'community' %}
                Once approved, you'll be able to fully use all features.
                {% elif entity_type == 'assistant' %}
                Once approved, your assistant will be available for use.
                {% endif %}
            </p>
        </div>
    </div>
</div>
