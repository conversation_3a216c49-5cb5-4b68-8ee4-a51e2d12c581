"""
<PERSON><PERSON><PERSON> to check the status of migrations.
"""
import os
import django
from django.db import connections
from django.db.migrations.recorder import MigrationRecorder

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

def check_migrations(app=None):
    """Check the status of migrations."""
    print("Checking migration status...")
    
    # Get the migration recorder
    connection = connections['default']
    recorder = MigrationRecorder(connection)
    
    # Get all applied migrations
    applied_migrations = recorder.migration_qs.all()
    
    # Filter by app if specified
    if app:
        applied_migrations = applied_migrations.filter(app=app)
    
    # Sort by app and name
    applied_migrations = applied_migrations.order_by('app', 'name')
    
    # Print the results
    print(f"\nApplied migrations ({applied_migrations.count()}):")
    for migration in applied_migrations:
        print(f"[X] {migration.app}.{migration.name}")
    
    print("\nMigration check completed.")

if __name__ == "__main__":
    # Check all migrations
    check_migrations()
    
    # Check specific app migrations
    # check_migrations('accounts')
