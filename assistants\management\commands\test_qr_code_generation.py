from django.core.management.base import BaseCommand
from django.urls import reverse
from assistants.models import Assistant
from accounts.models import Company
from accounts.utils import generate_qr_code
import time

class Command(BaseCommand):
    help = 'Test QR code generation with capital A in the middle'

    def handle(self, *args, **options):
        self.stdout.write("Testing QR code generation with capital 'A' in the middle...")
        
        # Test company QR code generation
        companies = Company.objects.all()[:1]
        if not companies:
            self.stdout.write(self.style.ERROR("No companies found. Please create a company first."))
            return
        
        company = companies[0]
        self.stdout.write(f"Using company: {company.name} (ID: {company.id})")
        
        # Generate QR code for company
        url_path = f"/accounts/company/{company.slug}/"
        success = generate_qr_code(company, url_path, field_name='qr_code')
        
        if success:
            # Save the company with the new QR code
            company.save(update_fields=['qr_code'])
            self.stdout.write(self.style.SUCCESS(
                f"Successfully generated QR code for company: {company.name}"
            ))
            self.stdout.write(f"QR code URL: {company.qr_code.url}")
        else:
            self.stdout.write(self.style.ERROR(
                f"Failed to generate QR code for company: {company.name}"
            ))
        
        # Test assistant QR code generation
        assistants = Assistant.objects.all()[:1]
        if not assistants:
            self.stdout.write(self.style.ERROR("No assistants found. Please create an assistant first."))
            return
        
        assistant = assistants[0]
        self.stdout.write(f"Using assistant: {assistant.name} (ID: {assistant.id})")
        
        # Generate QR code for assistant
        url_path = f"/assistant/assistant/{assistant.slug}/chat/"
        success = generate_qr_code(assistant, url_path, field_name='qr_code')
        
        if success:
            # Save the assistant with the new QR code
            assistant.save(update_fields=['qr_code'])
            self.stdout.write(self.style.SUCCESS(
                f"Successfully generated QR code for assistant: {assistant.name}"
            ))
            self.stdout.write(f"QR code URL: {assistant.qr_code.url}")
        else:
            self.stdout.write(self.style.ERROR(
                f"Failed to generate QR code for assistant: {assistant.name}"
            ))
        
        self.stdout.write(self.style.SUCCESS("QR code generation test completed!"))
