import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

# SQL to add the show_sidebar column to the assistants_assistant table
sql = """
ALTER TABLE "assistants_assistant" 
ADD COLUMN IF NOT EXISTS "show_sidebar" boolean NOT NULL DEFAULT true;
"""

# Execute the SQL
with connection.cursor() as cursor:
    cursor.execute(sql)

# SQL to add the show_sidebar_public column to the assistants_assistant table
sql_public = """
ALTER TABLE "assistants_assistant" 
ADD COLUMN IF NOT EXISTS "show_sidebar_public" boolean NOT NULL DEFAULT true;
"""

# Execute the SQL
with connection.cursor() as cursor:
    cursor.execute(sql_public)

print("Added show_sidebar and show_sidebar_public columns to assistants_assistant table successfully!")
