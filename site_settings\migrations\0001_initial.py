# Generated by Django 5.2.1 on 2025-05-20 05:57

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="SiteConfiguration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "site_name",
                    models.<PERSON>r<PERSON>ield(
                        default="Company Assistant Platform", max_length=255
                    ),
                ),
                (
                    "default_assistant_logo",
                    models.ImageField(
                        blank=True,
                        help_text="Default logo for assistants if they don't have one specifically uploaded. Displayed in chat.",
                        null=True,
                        upload_to="site_defaults/",
                    ),
                ),
            ],
            options={
                "verbose_name": "Site Configuration",
                "verbose_name_plural": "Site Configuration",
            },
        ),
    ]
