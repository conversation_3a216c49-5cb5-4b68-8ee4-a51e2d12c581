import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.auth.models import User
from django.db import IntegrityError

# Superuser details
username = "admin"
email = "<EMAIL>"
password = "admin123"

print(f"Creating/updating superuser '{username}'...")

try:
    # Check if user already exists
    if User.objects.filter(username=username).exists():
        user = User.objects.get(username=username)
        user.email = email
        user.set_password(password)
        user.is_staff = True
        user.is_superuser = True
        user.save()
        print(f"Superuser '{username}' updated successfully!")
    else:
        User.objects.create_superuser(username=username, email=email, password=password)
        print(f"Superuser '{username}' created successfully!")
    
    print(f"Username: {username}")
    print(f"Email: {email}")
    print(f"Password: {password}")
    print("\nYou can now log in to the admin interface at http://127.0.0.1:8000/admin/")
    
except IntegrityError as e:
    print(f"Error: {e}")
except Exception as e:
    print(f"An unexpected error occurred: {e}")
