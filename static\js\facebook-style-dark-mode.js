/**
 * Facebook Style Dark Mode Handler
 * Ensures dark mode is properly applied to Facebook-style elements
 * and prevents white flash when navigating between pages
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if dark mode is active
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
    
    // Apply dark mode styles to Facebook-style elements
    applyDarkModeToFacebookStyle(isDarkMode);
    
    // Listen for theme changes
    document.addEventListener('themeChanged', function(e) {
        const isDarkMode = e.detail.theme === 'dark';
        applyDarkModeToFacebookStyle(isDarkMode);
    });
});

/**
 * Apply dark mode styles to Facebook-style elements
 * @param {boolean} isDarkMode - Whether dark mode is active
 */
function applyDarkModeToFacebookStyle(isDarkMode) {
    // Check if the page has Facebook-style elements
    const hasFacebookStyle = document.body.classList.contains('facebook-style');
    if (!hasFacebookStyle) return;
    
    // Apply dark mode styles immediately to prevent white flash
    if (isDarkMode) {
        // Override body background
        document.body.style.backgroundColor = '#121212';
        document.body.style.background = '#121212';
        document.body.style.color = '#ffffff';
        
        // Override navbar
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            navbar.style.backgroundColor = '#121212';
            navbar.style.background = '#121212';
            navbar.style.borderBottom = '1px solid #333333';
        }
        
        // Override cards
        document.querySelectorAll('.card').forEach(card => {
            card.style.backgroundColor = '#1e1e1e';
            card.style.background = '#1e1e1e';
            card.style.borderColor = '#333333';
            card.style.color = '#ffffff';
        });
        
        // Override card headers
        document.querySelectorAll('.card-header').forEach(header => {
            header.style.backgroundColor = '#252525';
            header.style.background = '#252525';
            header.style.borderBottom = '1px solid #333333';
        });
        
        // Override card bodies
        document.querySelectorAll('.card-body').forEach(body => {
            body.style.backgroundColor = '#1e1e1e';
            body.style.background = '#1e1e1e';
        });
        
        // Override list group items
        document.querySelectorAll('.list-group-item').forEach(item => {
            item.style.backgroundColor = '#1e1e1e';
            item.style.background = '#1e1e1e';
            item.style.borderColor = '#333333';
            item.style.color = '#ffffff';
        });
        
        // Override alerts
        document.querySelectorAll('.alert-info').forEach(alert => {
            alert.style.backgroundColor = '#1e3a5f';
            alert.style.background = '#1e3a5f';
            alert.style.borderColor = '#264b7a';
            alert.style.color = '#ffffff';
        });
        
        document.querySelectorAll('.alert-warning').forEach(alert => {
            alert.style.backgroundColor = '#3a3000';
            alert.style.background = '#3a3000';
            alert.style.borderColor = '#4d4000';
            alert.style.color = '#ffffff';
        });
        
        // Override text colors
        document.querySelectorAll('.text-muted').forEach(text => {
            text.style.color = '#aaaaaa';
        });
        
        // Override buttons
        document.querySelectorAll('.btn-light').forEach(btn => {
            btn.style.backgroundColor = '#252525';
            btn.style.background = '#252525';
            btn.style.borderColor = '#333333';
            btn.style.color = '#ffffff';
        });
        
        // Override form elements
        document.querySelectorAll('input, textarea, select').forEach(input => {
            input.style.backgroundColor = '#252525';
            input.style.background = '#252525';
            input.style.borderColor = '#333333';
            input.style.color = '#ffffff';
        });
        
        // Override any elements with bg-light class
        document.querySelectorAll('.bg-light').forEach(el => {
            el.style.backgroundColor = '#1e1e1e';
            el.style.background = '#1e1e1e';
        });
    }
}
