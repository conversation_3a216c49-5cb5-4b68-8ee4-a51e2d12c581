# Migration Reset and Recreation Guide

This guide explains how to reset and recreate all migrations in your Django project. This is useful when you have migration conflicts or want to start with a clean migration history.

## Prerequisites

- Make sure you have a backup of your database if you need to preserve data
- Ensure your virtual environment is activated
- Make sure you have the correct database settings in your `settings.py` file

## Available Scripts

### 1. For Existing Tables (Recommended)

If your database already has tables created but you want to reset migrations:

```bash
python fix_migrations_with_existing_tables.py
```

This script will:
1. Delete all migration files
2. Reset the migration history in the database
3. Recreate migrations for all apps
4. Apply migrations with the `--fake` flag to handle existing tables

### 2. For Fresh Databases

If you're starting with a fresh database:

```bash
python reset_and_recreate_migrations.py
```

This script will:
1. Delete all migration files
2. Reset the migration history in the database
3. Recreate and apply migrations for all apps

### 3. Individual Scripts

If you prefer to run the steps individually, you can use these scripts:

#### Delete Migration Files

```bash
python delete_migrations.py
```

This script will delete all migration files from the following apps:
- accounts
- assistants
- content
- directory
- site_settings
- superadmin

#### Reset Migration History

```bash
python reset_migrations.py
```

This script will reset the migration history in the database by deleting all records from the `django_migrations` table.

#### Recreate and Apply Migrations

```bash
python recreate_migrations.py
```

This script will:
1. Create new migrations for all apps
2. Apply migrations in the correct order:
   - Core Django apps first
   - Third-party apps second
   - Custom apps last

## Troubleshooting

### "Relation Already Exists" Errors

If you see errors like `relation "django_content_type" already exists` or `relation "site_settings_siteconfiguration" already exists`:

1. This means the tables already exist in the database, but Django is trying to create them again
2. Use the `fix_migrations_with_existing_tables.py` script which applies migrations with the `--fake` flag
3. Alternatively, run migrations manually with the `--fake` flag:
   ```bash
   python manage.py migrate --fake-initial
   python manage.py migrate --fake
   ```

### Database Connection Issues

If you encounter database connection issues, make sure:
- Your database server is running
- Your database settings in `settings.py` are correct
- You have the necessary database drivers installed (e.g., psycopg2 for PostgreSQL)

### Migration Errors

If you encounter migration errors:
1. Check the error message for specific issues
2. Make sure all apps are properly installed
3. Check for circular dependencies in your models

### Model Changes Not Reflected

If your model changes are not reflected in the migrations:
1. Make sure you've saved all your model files
2. Check for syntax errors in your models
3. Try running `python manage.py makemigrations app_name --verbosity 3` to see detailed output

## Best Practices for Future Migrations

1. **Create migrations for each app separately**:
   ```bash
   python manage.py makemigrations app_name
   ```

2. **Apply migrations in the correct order**:
   ```bash
   python manage.py migrate contenttypes
   python manage.py migrate auth
   # ... other core apps
   python manage.py migrate
   ```

3. **Name complex migrations**:
   ```bash
   python manage.py makemigrations app_name --name descriptive_name
   ```

4. **Test migrations before applying them to production**:
   ```bash
   python manage.py migrate --plan
   ```

5. **Use squashing for large migration sets**:
   ```bash
   python manage.py squashmigrations app_name start_migration_name end_migration_name
   ```
