{% load static %}
{% load account_tags %}

<div class="card border-0 shadow-sm h-100 member-card">
    <!-- Member Header -->
    <div class="card-body p-4">
        <div class="text-center mb-3">
            <!-- Avatar -->
            <div class="position-relative d-inline-block mb-3">
                {% if member.avatar %}
                    <img src="{{ member.avatar.url }}" 
                         alt="{{ member.get_full_name }}"
                         class="rounded-circle"
                         width="80" height="80">
                {% else %}
                    <img src="{% gravatar_url member.email 80 %}"
                         alt="{{ member.get_full_name }}"
                         class="rounded-circle"
                         width="80" height="80">
                {% endif %}

                <!-- Online Status -->
                {% if member.is_online %}
                    <span class="position-absolute bottom-0 end-0 translate-middle p-2 bg-success border border-white rounded-circle"
                          data-bs-toggle="tooltip"
                          title="Online"></span>
                {% endif %}
            </div>

            <!-- Member Info -->
            <h6 class="mb-1">
                {{ member.get_full_name|default:member.username }}
                {% if member == company.owner %}
                    <i class="bi bi-star-fill text-warning ms-1" 
                       data-bs-toggle="tooltip" 
                       title="Company Owner"></i>
                {% endif %}
            </h6>
            <p class="text-muted small mb-2">
                {{ member.email }}
            </p>
            
            <!-- Role Badge -->
            {{ member|company_role:company|role_badge }}
        </div>

        <!-- Member Details -->
        {% if member.bio %}
            <p class="small text-muted mb-0">
                {{ member.bio|truncatewords:30 }}
            </p>
        {% endif %}

        <!-- Member Stats -->
        <div class="row g-2 text-center mt-3">
            <div class="col-4">
                <div class="p-2 border rounded bg-light">
                    <div class="h6 mb-0">{{ member.contributions_count }}</div>
                    <small class="text-muted">Posts</small>
                </div>
            </div>
            <div class="col-4">
                <div class="p-2 border rounded bg-light">
                    <div class="h6 mb-0">{{ member.tasks_count }}</div>
                    <small class="text-muted">Tasks</small>
                </div>
            </div>
            <div class="col-4">
                <div class="p-2 border rounded bg-light">
                    <div class="h6 mb-0">{{ member.days_active }}</div>
                    <small class="text-muted">Days</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Member Actions -->
    {% if user == company.owner and member != company.owner %}
        <div class="member-actions">
            <div class="dropdown">
                <button class="btn btn-light btn-sm" 
                        type="button" 
                        data-bs-toggle="dropdown"
                        aria-expanded="false">
                    <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    {% if member|company_role:company != 'admin' %}
                        <li>
                            <form method="post" 
                                  action="{% url 'accounts:promote_member' company.id member.id %}">
                                {% csrf_token %}
                                <button type="submit" class="dropdown-item">
                                    <i class="bi bi-arrow-up-circle me-2"></i>
                                    Promote to Admin
                                </button>
                            </form>
                        </li>
                    {% else %}
                        <li>
                            <form method="post" 
                                  action="{% url 'accounts:demote_member' company.id member.id %}">
                                {% csrf_token %}
                                <button type="submit" class="dropdown-item">
                                    <i class="bi bi-arrow-down-circle me-2"></i>
                                    Demote to Member
                                </button>
                            </form>
                        </li>
                    {% endif %}
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <form method="post" 
                              action="{% url 'accounts:remove_member' company.id member.id %}"
                              onsubmit="return confirm('Are you sure you want to remove this member?');">
                            {% csrf_token %}
                            <button type="submit" class="dropdown-item text-danger">
                                <i class="bi bi-person-x me-2"></i>
                                Remove Member
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>
    {% endif %}

    <!-- Card Footer -->
    <div class="card-footer bg-transparent border-0 p-4 pt-0">
        <div class="d-flex align-items-center text-muted small">
            <i class="bi bi-clock me-2"></i>
            Last active: {{ member.last_active|timesince }} ago
        </div>
    </div>

    <!-- Direct Message Button -->
    {% if member != user %}
        <div class="position-absolute bottom-0 end-0 p-3">
            <button type="button" 
                    class="btn btn-primary btn-sm btn-icon"
                    onclick="location.href='{% url 'messages:compose' %}?to={{ member.username }}'">
                <i class="bi bi-chat"></i>
            </button>
        </div>
    {% endif %}
</div>
