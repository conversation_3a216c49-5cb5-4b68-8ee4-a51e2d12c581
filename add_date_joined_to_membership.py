import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

print("Starting script to add date_joined column to accounts_membership table...")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

# SQL to add the date_joined column to the accounts_membership table
add_column_sql = """
ALTER TABLE accounts_membership 
ADD COLUMN IF NOT EXISTS date_joined timestamp with time zone NOT NULL DEFAULT now();
"""

# SQL to drop the role column if it exists (since it's been removed from the model)
drop_role_column_sql = """
ALTER TABLE accounts_membership 
DROP COLUMN IF EXISTS role;
"""

# SQL to drop the created_at column if it exists (since it's not in the model)
drop_created_at_column_sql = """
ALTER TABLE accounts_membership 
DROP COLUMN IF EXISTS created_at;
"""

# SQL to drop the updated_at column if it exists (since it's not in the model)
drop_updated_at_column_sql = """
ALTER TABLE accounts_membership 
DROP COLUMN IF EXISTS updated_at;
"""

# SQL to drop the unique constraint that includes role
drop_constraint_sql = """
ALTER TABLE accounts_membership 
DROP CONSTRAINT IF EXISTS accounts_membership_company_id_user_id_role_unique;
"""

# SQL to add the new unique constraint without role
add_constraint_sql = """
ALTER TABLE accounts_membership 
DROP CONSTRAINT IF EXISTS accounts_membership_company_id_user_id_unique;

ALTER TABLE accounts_membership 
ADD CONSTRAINT accounts_membership_company_id_user_id_unique 
UNIQUE (company_id, user_id);
"""

# Connect to the database
try:
    print("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(**db_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    
    # Add the date_joined column
    print("Adding date_joined column to accounts_membership table...")
    cursor.execute(add_column_sql)
    print("Column added successfully!")
    
    # Drop the role column
    print("Dropping role column from accounts_membership table...")
    cursor.execute(drop_role_column_sql)
    print("Column dropped successfully!")
    
    # Drop the created_at column
    print("Dropping created_at column from accounts_membership table...")
    cursor.execute(drop_created_at_column_sql)
    print("Column dropped successfully!")
    
    # Drop the updated_at column
    print("Dropping updated_at column from accounts_membership table...")
    cursor.execute(drop_updated_at_column_sql)
    print("Column dropped successfully!")
    
    # Drop the old unique constraint
    print("Dropping old unique constraint...")
    cursor.execute(drop_constraint_sql)
    print("Constraint dropped successfully!")
    
    # Add the new unique constraint
    print("Adding new unique constraint...")
    cursor.execute(add_constraint_sql)
    print("Constraint added successfully!")
    
    # Close the connection
    cursor.close()
    conn.close()
    print("Database connection closed.")
    print("Table modification completed successfully!")
    
except Exception as e:
    print(f"Error: {e}")
