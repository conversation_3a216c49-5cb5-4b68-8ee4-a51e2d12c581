/**
 * Category Dropdowns - Handles industry and category selection with multi-select support
 *
 * This script loads categories from the category.json file and creates
 * dynamic dropdowns for industry and category selection.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log("Category Dropdowns Multi-Select script loaded");

    // Global variables
    let categoriesData = {};
    let activeDropdown = null;
    let selectedCategories = [];

    // Load categories data
    fetch('/static/json/category.json')
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to load categories data');
            }
            return response.json();
        })
        .then(data => {
            categoriesData = data;
            console.log("Categories loaded:", Object.keys(categoriesData).length);
            initializeDropdowns();
        })
        .catch(error => {
            console.error("Error loading categories:", error);
            // Fallback data
            categoriesData = {
                "Technology": ["Software", "Hardware", "Cloud Computing"],
                "Healthcare": ["Hospitals", "Pharmaceuticals", "Medical Devices"],
                "Candy & Soda": ["Candy and other confectionery", "Bottled-canned soft drinks", "Flavoring syrup", "Potato chips", "Manufactured ice"]
            };
            console.log("Using fallback data");
            initializeDropdowns();
        });

    /**
     * Initialize all dropdown components
     */
    function initializeDropdowns() {
        setupIndustryDropdowns();
        setupCategoryDropdowns();

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.dropdown-container')) {
                closeAllDropdowns();
            }
        });

        // Close dropdowns when pressing escape
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeAllDropdowns();
            }
        });
    }

    /**
     * Set up industry dropdown components
     */
    function setupIndustryDropdowns() {
        const industryContainers = document.querySelectorAll('.industry-dropdown-container');

        industryContainers.forEach(container => {
            const input = container.querySelector('input[type="text"]');
            const hiddenInput = container.querySelector('input[type="hidden"]');
            const dropdownList = container.querySelector('.dropdown-list');

            if (!input || !dropdownList) return;

            // Create industry options
            populateIndustryDropdown(dropdownList);

            // Show dropdown on focus
            input.addEventListener('focus', function() {
                closeAllDropdowns();
                showDropdown(dropdownList);
                activeDropdown = dropdownList;
            });

            // Filter options on input
            input.addEventListener('input', function() {
                filterDropdownOptions(input.value, dropdownList);
                showDropdown(dropdownList);

                // Update hidden input value
                if (hiddenInput) {
                    hiddenInput.value = input.value;
                }
            });

            // Check if industry is already selected
            if (input.value) {
                updateCategoryDropdowns(input.value);
            }
        });
    }

    /**
     * Set up category dropdown components
     */
    function setupCategoryDropdowns() {
        const categoryContainers = document.querySelectorAll('.category-dropdown-container');

        categoryContainers.forEach(container => {
            const input = container.querySelector('input[type="text"]');
            const hiddenInput = container.querySelector('input[type="hidden"]');
            const dropdownList = container.querySelector('.dropdown-list');

            if (!input || !dropdownList) return;

            // Initialize selected categories from input value
            if (input.value) {
                selectedCategories = input.value.split(',').map(cat => cat.trim()).filter(cat => cat);
                updateCategoryDisplay(input, hiddenInput);
            }

            // Show dropdown on focus
            input.addEventListener('focus', function() {
                closeAllDropdowns();
                showDropdown(dropdownList);
                activeDropdown = dropdownList;
            });

            // Handle input changes
            input.addEventListener('input', function() {
                // Only update if the user is typing a new category
                if (!dropdownList.classList.contains('show')) {
                    return;
                }

                filterDropdownOptions(input.value, dropdownList);
                showDropdown(dropdownList);
            });
        });
    }

    /**
     * Populate industry dropdown with options
     */
    function populateIndustryDropdown(dropdownList) {
        // Clear existing options
        dropdownList.innerHTML = '';

        // Add industry options
        Object.keys(categoriesData).forEach(industry => {
            const option = document.createElement('div');
            option.className = 'dropdown-item';
            option.textContent = industry;

            option.addEventListener('click', function(event) {
                event.stopPropagation();
                const container = dropdownList.closest('.dropdown-container');
                const input = container.querySelector('input[type="text"]');
                const hiddenInput = container.querySelector('input[type="hidden"]');

                if (input) {
                    input.value = industry;
                }

                if (hiddenInput) {
                    hiddenInput.value = industry;
                }

                hideDropdown(dropdownList);

                // Update category dropdowns
                updateCategoryDropdowns(industry);
            });

            dropdownList.appendChild(option);
        });
    }

    /**
     * Update category dropdowns based on selected industry
     */
    function updateCategoryDropdowns(industry) {
        console.log("Updating categories for industry:", industry);

        const categoryContainers = document.querySelectorAll('.category-dropdown-container');

        categoryContainers.forEach(container => {
            const input = container.querySelector('input[type="text"]');
            const hiddenInput = container.querySelector('input[type="hidden"]');
            const dropdownList = container.querySelector('.dropdown-list');

            if (!input || !dropdownList) return;

            // Clear existing options
            dropdownList.innerHTML = '';

            // Get categories for selected industry
            const categories = categoriesData[industry] || [];
            console.log("Found categories:", categories.length);

            // Add category options
            categories.forEach(category => {
                const option = document.createElement('div');
                option.className = 'dropdown-item';
                option.textContent = category;
                
                // Check if this category is already selected
                if (selectedCategories.includes(category)) {
                    option.classList.add('selected');
                }

                option.addEventListener('click', function(event) {
                    event.stopPropagation();
                    
                    // Toggle selection
                    const index = selectedCategories.indexOf(category);
                    if (index === -1) {
                        selectedCategories.push(category);
                        option.classList.add('selected');
                    } else {
                        selectedCategories.splice(index, 1);
                        option.classList.remove('selected');
                    }
                    
                    updateCategoryDisplay(input, hiddenInput);
                });

                dropdownList.appendChild(option);
            });
        });
    }

    /**
     * Update the display of selected categories
     */
    function updateCategoryDisplay(input, hiddenInput) {
        if (input) {
            input.value = selectedCategories.join(', ');
        }
        
        if (hiddenInput) {
            hiddenInput.value = selectedCategories.join(', ');
        }
    }

    /**
     * Filter dropdown options based on input value
     */
    function filterDropdownOptions(value, dropdownList) {
        const searchText = value.toLowerCase();

        // Filter options
        const options = dropdownList.querySelectorAll('.dropdown-item');
        let hasVisibleOptions = false;

        options.forEach(option => {
            if (option.textContent.toLowerCase().includes(searchText)) {
                option.style.display = 'block';
                hasVisibleOptions = true;
            } else {
                option.style.display = 'none';
            }
        });

        // If no options match, hide dropdown
        if (!hasVisibleOptions && searchText) {
            hideDropdown(dropdownList);
        }
    }

    /**
     * Show dropdown list
     */
    function showDropdown(dropdownList) {
        dropdownList.style.display = 'block';
        dropdownList.classList.add('show');

        // Ensure dropdown is visible within viewport
        const rect = dropdownList.getBoundingClientRect();
        const viewportHeight = window.innerHeight;

        if (rect.bottom > viewportHeight) {
            dropdownList.style.maxHeight = (viewportHeight - rect.top - 20) + 'px';
        }
    }

    /**
     * Hide dropdown list
     */
    function hideDropdown(dropdownList) {
        dropdownList.style.display = 'none';
        dropdownList.classList.remove('show');
    }

    /**
     * Close all open dropdowns
     */
    function closeAllDropdowns() {
        document.querySelectorAll('.dropdown-list').forEach(list => {
            hideDropdown(list);
        });
        activeDropdown = null;
    }
});
