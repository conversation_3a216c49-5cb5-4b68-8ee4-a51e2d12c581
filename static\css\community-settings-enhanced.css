/**
 * Enhanced Community Settings CSS
 * Improved styling for community settings page with dark mode support
 */

/* Card styling */
.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Card headers with gradient */
.card-header {
  background: linear-gradient(135deg, var(--bs-primary-rgb, 13, 110, 253) 0%, rgba(var(--bs-primary-rgb, 13, 110, 253), 0.7) 100%);
  color: white;
  padding: 1rem;
  border-radius: 0.5rem 0.5rem 0 0;
}

[data-theme="dark"] .card-header {
  background: linear-gradient(135deg, #1a3a6c 0%, #2c5eaa 100%);
  color: white;
}

/* Form controls */
.form-control, .form-select {
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  height: auto;
  font-size: 1rem;
  line-height: 1.5;
  width: 100%;
}

.form-control:focus, .form-select:focus {
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb, 13, 110, 253), 0.25);
  border-color: #0d6efd;
}

/* Fix for squished inputs */
.input-group {
  flex-wrap: nowrap;
}

.input-group .form-control {
  flex: 1 1 auto;
  width: 1%;
}

/* Spacing between form elements */
.form-label {
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-text {
  margin-top: 0.25rem;
  font-size: 0.875rem;
}

/* Row spacing */
.row.g-4 {
  --bs-gutter-y: 1.5rem;
}

/* Dark mode specific styling */
[data-theme="dark"] .card {
  background-color: #1e1e1e;
  border-color: #333;
}

[data-theme="dark"] .card-body {
  background-color: #1e1e1e;
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
  background-color: #252525;
  border-color: #444;
  color: #fff;
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
  background-color: #252525;
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

[data-theme="dark"] .form-check-input {
  background-color: #252525;
  border-color: #444;
}

[data-theme="dark"] .form-text {
  color: #aaa;
}

/* Input groups in dark mode */
[data-theme="dark"] .input-group-text {
  background-color: #252525;
  border-color: #444;
  color: #fff;
}

/* QR code container in dark mode */
[data-theme="dark"] .bg-light {
  background-color: #252525 !important;
}

/* Dropdown styling */
.dropdown-container {
  position: relative;
  width: 100%;
  margin-bottom: 0;
  z-index: 1050; /* Base z-index for all dropdowns */
}

.dropdown-container input {
  width: 100%;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #ced4da;
  background-color: #fff;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1051; /* Higher than container */
}

.dropdown-container input:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  outline: none;
}

.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 9999; /* Very high z-index to ensure it's above everything */
  display: none;
  max-height: 200px;
  overflow-y: auto;
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  margin-top: 0.25rem;
  padding: 0.5rem 0;
}

.dropdown-list.show {
  display: block;
}

.dropdown-item {
  padding: 0.5rem 1rem;
  cursor: pointer;
  color: #212529;
  transition: background-color 0.2s ease;
  position: relative;
  z-index: 10000; /* Even higher to ensure clickability */
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

/* When dropdown is open, increase container z-index */
.dropdown-container:focus-within {
  z-index: 9000; /* Very high when focused/active */
}

/* Dark mode dropdown styling */
[data-theme="dark"] .dropdown-container input {
  background-color: #252525;
  border-color: #444;
  color: #fff;
}

[data-theme="dark"] .dropdown-container input:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

[data-theme="dark"] .dropdown-list {
  background-color: #252525;
  border-color: #444;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .dropdown-item {
  color: #fff;
}

[data-theme="dark"] .dropdown-item:hover,
[data-theme="dark"] .dropdown-item:focus {
  background-color: #333;
  color: #fff;
}

/* Arrow indicator for dropdowns */
.dropdown-container::after {
  content: '';
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #6c757d;
  pointer-events: none;
  z-index: 1052; /* Above input */
}

/* Category dropdown specific styles */
.category-dropdown-container {
  position: relative;
  z-index: 1060; /* Higher than industry dropdown */
}

/* Button styling */
.btn {
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn:active {
  transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .card {
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 768px) {
  .btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1rem;
  }
}
