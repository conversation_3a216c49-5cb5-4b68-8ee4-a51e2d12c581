{% extends 'base/layout.html' %}
{% load static %}
{% load account_tags %}
{% load permission_tags %} {# Load permission tags #}

{% block title %}Dashboard - {{ active_company.name }} - 24seven{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Welcome Section -->
    <div class="row align-items-center mb-4">
        <div class="col d-flex align-items-center"> {# Use flexbox for alignment #}
            {# Display Company Logo if available, positioned to the left #}
            {% if active_company.info.logo and active_company.info.logo.url %}
                <img src="{{ active_company.info.logo.url }}"
                     alt="{{ active_company.name }} Logo"
                     class="rounded-circle me-3" {# Margin end for spacing #}
                     style="width: 40px; height: 40px; object-fit: cover;"> {# Set fixed size #}
            {% endif %}
            <div> {# Wrap text elements #}
                <h1 class="h3 mb-0">Welcome, {{ user.get_full_name|default:user.username }}</h1>
                <p class="text-muted mb-0">
                    {{ active_company.name }} Dashboard
                </p>
            </div>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                {# Use the new 'check_perm' tag with app_label.codename format #}
                {% check_perm user 'accounts.change_company_settings' company as can_edit_settings %}
                {% if can_edit_settings %}
                    <a href="{% url 'accounts:company_settings' company.id %}" class="btn btn-light"> {# Use company.id #}
                        <i class="bi bi-gear"></i> Settings
                    </a>
                {% endif %}
                {# Hide Team button if user is in 'Company Members' group #}
                {% if not is_company_member %}
                <a href="{% url 'accounts:company_team' company.id %}" class="btn btn-light"> {# Use company from view context #}
                    <i class="bi bi-people"></i> Team
                </a>
                {% endif %}
                {# Ensure this link is present and uses company.id from view context #}
                <a href="{% url 'assistants:list' company_id=company.id %}" class="btn btn-light">
                    <i class="bi bi-robot"></i> Manage Assistants
                </a>
                {# Removed Directory Settings link as it's admin-only #}
            </div>
        </div>
    </div>

    {% load account_tags %}
    {% get_pending_companies user as pending_companies %}
    {% get_pending_communities user as pending_communities %}
    {% get_pending_assistants user as pending_assistants %}

    <!-- Show a single consolidated notification for all pending items -->
    <div id="pending-notifications-container">
        {% include 'accounts/tags/consolidated_simple_notification.html' with pending_companies=pending_companies pending_communities=pending_communities pending_assistants=pending_assistants %}
    </div>

    <!-- Timeframe Selector -->
    <div class="row mb-3">
        <div class="col-md-8">
            {# Placeholder for potential future charts or main content #}
        </div>
        <div class="col-md-4 text-md-end">
            <div class="btn-group btn-group-sm" role="group" aria-label="Timeframe selection">
                <a href="{% url 'accounts:dashboard' %}?period=day" class="btn {% if selected_period == 'day' %}btn-primary{% else %}btn-outline-secondary{% endif %}">Day</a>
                <a href="{% url 'accounts:dashboard' %}?period=week" class="btn {% if selected_period == 'week' %}btn-primary{% else %}btn-outline-secondary{% endif %}">Week</a>
                <a href="{% url 'accounts:dashboard' %}?period=month" class="btn {% if selected_period == 'month' %}btn-primary{% else %}btn-outline-secondary{% endif %}">Month</a>
                <a href="{% url 'accounts:dashboard' %}?period=all" class="btn {% if selected_period == 'all' or not selected_period %}btn-primary{% else %}btn-outline-secondary{% endif %}">All Time</a>
            </div>
        </div>
    </div>

    <!-- Metrics Row -->
    <div class="row g-3 mb-4">
        <!-- Active Assistants -->
        <div class="col-md-6 col-lg-3">
            <div class="card h-100 border-0 shadow-sm metric-card metric-primary">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 p-3 rounded">
                                <i class="bi bi-robot text-primary"></i> {# Changed icon #}
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="h6 text-muted mb-0">Active Assistants</h3> {# Renamed label #}
                            <h4 class="h2 mb-0">{{ active_assistants }}</h4> {# Use correct variable #}
                        </div>
                    </div>
                    {# Removed placeholder trend indicator #}
                </div>
            </div>
        </div>

        <!-- Team Members -->
        <div class="col-md-6 col-lg-3">
            <div class="card h-100 border-0 shadow-sm metric-card metric-success">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 p-3 rounded">
                                <i class="bi bi-people text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="h6 text-muted mb-0">Team Members</h3>
                            <h4 class="h2 mb-0">{{ total_members }}</h4> {# Use correct variable #}
                        </div>
                    </div>
                    {% if pending_invitations %}
                        <div class="text-muted small">
                            {{ pending_invitations }} pending invitation{{ pending_invitations|pluralize }}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Total Content -->
        <div class="col-md-6 col-lg-3">
            <div class="card h-100 border-0 shadow-sm metric-card metric-info">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-opacity-10 p-3 rounded">
                                <i class="bi bi-files text-info"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="h6 text-muted mb-0">Total Content</h3>
                            <h4 class="h2 mb-0">{{ total_content }}</h4>
                        </div>
                    </div>
                    <div class="text-muted small">
                        Across all categories
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity Log Entries -->
        <div class="col-md-6 col-lg-3">
            <div class="card h-100 border-0 shadow-sm metric-card metric-warning">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-opacity-10 p-3 rounded">
                                <i class="bi bi-lightning text-warning"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="h6 text-muted mb-0">Activity Log Entries</h3> {# Renamed label #}
                            <h4 class="h2 mb-0">{{ total_activity_log_entries }}</h4> {# Use correct variable #}
                        </div>
                    </div>
                    <div class="text-muted small">
                        All time
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% if active_company.entity_type == 'community' %}
    <!-- Community Assistant Section -->
    <div class="row g-3 mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">Community Assistant</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 p-3 rounded">
                                <i class="bi bi-people-fill text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <p class="mb-2">Your community assistant allows users to contribute knowledge and improve answers over time.</p>
                            <div class="d-flex flex-wrap gap-2">
                                {% for assistant in community_assistants %}
                                    <a href="{% url 'assistants:assistant_chat' company.id assistant.id %}" class="btn btn-success">
                                        <i class="bi bi-chat-dots me-2"></i> Chat with Community Assistant
                                    </a>
                                    <a href="{% url 'assistants:contexts' company.id assistant.id %}" class="btn btn-outline-success">
                                        <i class="bi bi-collection me-2"></i> View Contexts
                                    </a>
                                    <a href="{% url 'assistants:flagged_questions' company.id assistant.id %}" class="btn btn-outline-success">
                                        <i class="bi bi-flag me-2"></i> Flagged Questions
                                    </a>
                                {% empty %}
                                    <p class="text-muted">No community assistant available. Please <a href="{{ site_config.contact_url|default:'/contact/' }}">contact support</a>.</p>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Assistant Analytics Row -->
    <div class="row g-3 mb-4">
        <h4 class="mb-3 mt-2 text-muted">Assistant Performance Overview <span class="small text-muted">({{ selected_period|default:"All Time"|capfirst }})</span></h4>
        <!-- Total Assistant Interactions -->
        <div class="col-md-6 col-lg-4">
            <div class="card h-100 border-0 shadow-sm metric-card metric-secondary">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-secondary bg-opacity-10 p-3 rounded">
                                <i class="bi bi-robot text-secondary"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="h6 text-muted mb-0">Total Assistant Interactions</h3>
                            <h4 class="h2 mb-0">{{ dashboard_analytics.total_assistant_interactions|default:"0" }}</h4>
                        </div>
                    </div>
                    <div class="text-muted small">
                        Across all assistants
                    </div>
                </div>
            </div>
        </div>

        <!-- Average Assistant Rating -->
        <div class="col-md-6 col-lg-4">
            <div class="card h-100 border-0 shadow-sm metric-card metric-success"> {# Reusing success color for rating #}
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 p-3 rounded">
                                <i class="bi bi-star-half text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="h6 text-muted mb-0">Average Assistant Rating</h3>
                            <h4 class="h2 mb-0">
                                {% if dashboard_analytics.average_assistant_rating is not None %}
                                    {{ dashboard_analytics.average_assistant_rating|floatformat:1 }} <span class="fs-5 text-muted">/ 5</span>
                                {% else %}
                                    N/A
                                {% endif %}
                            </h4>
                        </div>
                    </div>
                     <div class="text-muted small">
                        Based on user feedback
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Assistant Tokens Used -->
        <div class="col-md-6 col-lg-4">
            <div class="card h-100 border-0 shadow-sm metric-card metric-info"> {# Reusing info color #}
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-opacity-10 p-3 rounded">
                                <i class="bi bi-cpu text-info"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="h6 text-muted mb-0">Total Assistant Tokens</h3>
                            <h4 class="h2 mb-0">{{ dashboard_analytics.total_assistant_tokens|default:"0" }}</h4>
                        </div>
                    </div>
                     <div class="text-muted small">
                        Total usage across assistants
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row g-4">
        <!-- Recent Activity -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-transparent border-0">
                    <h5 class="mb-0">Recent Activity</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for activity in recent_activity %}
                            {% activity_item activity %}
                        {% empty %}
                            <div class="list-group-item text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-clock h3 mb-2"></i>
                                    <p>No recent activity</p>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
                {% if recent_activity %}
                    <div class="card-footer bg-transparent border-0 text-center">
                        {# Link to the internal company detail page which shows more activity #}
                        <a href="{% url 'accounts:company_detail' company_id=company.id %}" class="btn btn-link text-muted">View All Activity</a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Team Overview -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Team Overview</h5>
                    <a href="{% url 'accounts:company_team' company.id %}" class="btn btn-sm btn-light"> {# Use company from view context #}
                        Manage
                    </a>
                </div>
                <div class="card-body">
                    {# Display Company Logo at the top of the card #}
                    <div class="text-center mb-3">
                         {% if active_company.info.logo and active_company.info.logo.url %}
                            <img src="{{ active_company.info.logo.url }}"
                                 alt="{{ active_company.name }} Logo"
                                 class="img-thumbnail rounded-circle"
                                 style="width: 60px; height: 60px; object-fit: cover;">
                        {% else %}
                             <i class="bi bi-building display-6 text-muted"></i>
                         {% endif %}
                    </div>

                    {# Removed Member Avatars Section #}

                    <!-- Quick Stats -->
                    <div class="row g-2 text-center">
                        <div class="col-4">
                            <div class="p-3 border rounded bg-light">
                                <div class="h4 mb-0">{{ total_members }}</div> {# Use correct total_members count #}
                                <small class="text-muted">Members</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-3 border rounded bg-light">
                                <div class="h4 mb-0">{{ active_members }}</div>
                                <small class="text-muted">Active</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-3 border rounded bg-light">
                                <div class="h4 mb-0">{{ pending_invitations }}</div>
                                <small class="text-muted">Pending</small>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltips = document.querySelectorAll('[title]');
    tooltips.forEach(el => {
        new bootstrap.Tooltip(el);
    });

    // Fade in metrics on scroll
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    });

    document.querySelectorAll('.metric-card').forEach(card => {
        observer.observe(card);
    });
});
</script>
{% endblock %}
