import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

print("Starting Interaction table creation script...")

# SQL to create the assistants_interaction table
interaction_sql = """
CREATE TABLE IF NOT EXISTS "assistants_interaction" (
    "id" serial NOT NULL PRIMARY KEY,
    "prompt" text NOT NULL,
    "response" text NOT NULL,
    "context" text NOT NULL,
    "rating" integer NULL,
    "duration" double precision NOT NULL,
    "token_count" integer NOT NULL,
    "use_community_context" boolean NOT NULL DEFAULT false,
    "created_at" timestamp with time zone NOT NULL,
    "assistant_id" integer NOT NULL REFERENCES "assistants_assistant" ("id") DEFERRABLE INITIALLY DEFERRED,
    "user_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# SQL to create indexes
indexes_sql = """
CREATE INDEX IF NOT EXISTS "assistants_interaction_assistant_id_created_at_idx" ON "assistants_interaction" ("assistant_id", "created_at");
CREATE INDEX IF NOT EXISTS "assistants_interaction_user_id_created_at_idx" ON "assistants_interaction" ("user_id", "created_at");
"""

# SQL to create the many-to-many relationship table for used_contexts
used_contexts_sql = """
CREATE TABLE IF NOT EXISTS "assistants_interaction_used_contexts" (
    "id" serial NOT NULL PRIMARY KEY,
    "interaction_id" integer NOT NULL,
    "communitycontext_id" integer NOT NULL,
    CONSTRAINT "assistants_interaction_used_contexts_interaction_id_communitycontext_id_key" UNIQUE ("interaction_id", "communitycontext_id"),
    CONSTRAINT "assistants_interaction_used_contexts_interaction_id_fkey" FOREIGN KEY ("interaction_id") REFERENCES "assistants_interaction" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# Execute the SQL
try:
    with connection.cursor() as cursor:
        print("Creating assistants_interaction table...")
        cursor.execute(interaction_sql)
        print("Interaction table created successfully!")

        print("Creating indexes...")
        cursor.execute(indexes_sql)
        print("Indexes created successfully!")

        print("Creating assistants_interaction_used_contexts table...")
        cursor.execute(used_contexts_sql)
        print("Interaction used_contexts table created successfully!")

    print("Tables created successfully!")
except Exception as e:
    print(f"Error creating tables: {e}")
