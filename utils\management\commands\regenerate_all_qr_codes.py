from django.core.management.base import BaseCommand
from django.apps import apps
from django.db.models import FileField
import os
import time
from utils.qr_generator import generate_model_qr_code

class Command(BaseCommand):
    help = 'Regenerates all QR codes in the system to ensure consistent design'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be regenerated without actually regenerating',
        )

    def handle(self, *args, **options):
        dry_run = options.get('dry_run', False)
        
        # Get all models that might have QR codes
        models_with_qr = []
        
        # Look for models with a field named 'qr_code'
        for app_config in apps.get_app_configs():
            for model in app_config.get_models():
                for field in model._meta.get_fields():
                    if isinstance(field, FileField) and field.name == 'qr_code':
                        models_with_qr.append(model)
        
        if not models_with_qr:
            self.stdout.write(self.style.WARNING('No models with QR code fields found.'))
            return
        
        total_regenerated = 0
        total_skipped = 0
        
        # Process each model
        for model in models_with_qr:
            model_name = model.__name__
            self.stdout.write(f"Processing {model_name} objects...")
            
            # Get all instances of this model
            instances = model.objects.all()
            count = instances.count()
            
            if count == 0:
                self.stdout.write(f"  No {model_name} instances found.")
                continue
            
            self.stdout.write(f"  Found {count} {model_name} instances.")
            
            # Process each instance
            for i, instance in enumerate(instances):
                # Show progress
                if i % 10 == 0 or i == count - 1:
                    self.stdout.write(f"  Processing {i+1}/{count}...", ending='\r')
                
                # Check if instance has a QR code
                if not instance.qr_code:
                    total_skipped += 1
                    continue
                
                # Regenerate QR code
                if not dry_run:
                    # Store the old QR code path to delete it later
                    old_qr_path = instance.qr_code.path if instance.qr_code else None
                    
                    # Generate new QR code
                    success = generate_model_qr_code(instance)
                    
                    if success:
                        # Save the instance to persist the new QR code
                        instance.save()
                        total_regenerated += 1
                        
                        # Delete the old file if it exists and is different from the new one
                        if old_qr_path and os.path.exists(old_qr_path) and old_qr_path != instance.qr_code.path:
                            try:
                                os.remove(old_qr_path)
                            except OSError:
                                self.stdout.write(self.style.WARNING(f"  Could not delete old QR code: {old_qr_path}"))
                    else:
                        total_skipped += 1
                else:
                    # In dry run mode, just count
                    total_regenerated += 1
                
                # Small delay to prevent overwhelming the system
                time.sleep(0.01)
            
            self.stdout.write("")  # New line after progress
        
        # Summary
        if dry_run:
            self.stdout.write(self.style.SUCCESS(f"DRY RUN: Would regenerate {total_regenerated} QR codes. {total_skipped} would be skipped."))
        else:
            self.stdout.write(self.style.SUCCESS(f"Successfully regenerated {total_regenerated} QR codes. {total_skipped} were skipped."))
