"""
<PERSON><PERSON><PERSON> to fix the missing accounts_membership_accessible_folders table issue.
This script will:
1. Check if the table exists
2. If not, mark the problematic migration as applied without running it
3. Continue with the remaining migrations
"""
import os
import django
from django.db import connections
from django.db.migrations.recorder import MigrationRecorder

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

def fix_migration():
    """Fix the migration issue with accounts_membership_accessible_folders."""
    print("Starting migration fix...")
    
    # Get the migration recorder
    connection = connections['default']
    cursor = connection.cursor()
    recorder = MigrationRecorder(connection)
    
    # Check if the table exists
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = 'accounts_membership_accessible_folders'
        );
    """)
    table_exists = cursor.fetchone()[0]
    
    if not table_exists:
        print("Table 'accounts_membership_accessible_folders' does not exist.")
        print("This is expected since we're trying to remove it.")
        
        # Mark the migration as applied
        migration_exists = recorder.migration_qs.filter(
            app='accounts',
            name='0010_remove_registrationlink_role_remove_membership_role_and_more'
        ).exists()
        
        if not migration_exists:
            print("Marking migration '0010_remove_registrationlink_role_remove_membership_role_and_more' as applied...")
            recorder.record_applied('accounts', '0010_remove_registrationlink_role_remove_membership_role_and_more')
            print("Migration marked as applied.")
        else:
            print("Migration is already marked as applied.")
    else:
        print("Table 'accounts_membership_accessible_folders' exists.")
        print("This is unexpected. The migration should be able to remove it.")
    
    print("\nMigration fix completed.")
    print("\nNext steps:")
    print("1. Run 'python manage.py migrate' to apply the remaining migrations.")

if __name__ == "__main__":
    fix_migration()
