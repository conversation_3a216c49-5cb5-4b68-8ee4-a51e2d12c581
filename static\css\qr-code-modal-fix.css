/**
 * QR Code Modal Fixes
 * Enhances the appearance of QR code modals, especially in dark mode
 */

/* QR code container in dark mode */
[data-theme="dark"] .bg-light {
  background-color: #252525 !important;
}

/* Modal styling for dark mode */
[data-theme="dark"] .modal-content {
  background-color: #1e1e1e;
  border-color: #333;
}

/* QR Code modal specific header styling */
#qrCodeModal .modal-header {
  position: relative;
  padding-right: 3rem; /* Extra space for the close button */
}

[data-theme="dark"] #qrCodeModal .modal-header {
  background-color: #252525; /* Slightly lighter than modal background */
  border-bottom-color: #333;
}

[data-theme="dark"] .modal-header {
  border-bottom-color: #333;
}

[data-theme="dark"] .modal-footer {
  border-top-color: #333;
}

/* Improved close button visibility in dark mode */
[data-theme="dark"] .btn-close {
  filter: invert(1) grayscale(100%) brightness(400%); /* Increased brightness for better visibility */
  opacity: 0.9; /* Higher default opacity */
  background-color: rgba(255, 255, 255, 0.2); /* More visible background */
  border-radius: 50%; /* Make it circular */
  padding: 0.5rem; /* Larger clickable area */
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3); /* Add a light border glow */
  transform: scale(1.2); /* Make it slightly larger */
  transition: all 0.2s ease-in-out; /* Smooth transition for all properties */
}

[data-theme="dark"] .btn-close:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.3); /* Brighter on hover */
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.4); /* Enhanced glow on hover */
  transform: scale(1.3); /* Grow slightly on hover */
}

/* Improved close button visibility in light mode */
#qrCodeModal .btn-close {
  background-color: rgba(0, 0, 0, 0.1); /* Subtle background */
  border-radius: 50%; /* Make it circular */
  padding: 0.5rem; /* Larger clickable area */
  opacity: 0.7; /* Higher default opacity */
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1); /* Add a subtle border */
  transform: scale(1.1); /* Make it slightly larger */
  transition: all 0.2s ease-in-out; /* Smooth transition for all properties */
}

#qrCodeModal .btn-close:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.2); /* Darker on hover */
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.2); /* Enhanced border on hover */
  transform: scale(1.2); /* Grow slightly on hover */
}

/* QR code image container */
[data-theme="dark"] .qr-code-container {
  background-color: #252525;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 1rem;
}

/* QR code image background */
[data-theme="dark"] .qr-code-container .bg-light {
  background-color: #ffffff !important; /* Keep white background for QR code readability */
  border: 1px solid #333;
}
