from django.db import models
from django.core.exceptions import ValidationError

class SiteConfiguration(models.Model):
    """
    Singleton model to hold site-wide configuration settings,
    editable via the Django admin interface.
    """
    site_name = models.CharField(max_length=255, default='24seven Platform')
    default_assistant_logo = models.ImageField(
        upload_to='site_defaults/',
        blank=True,
        null=True,
        help_text="Default logo for assistants if they don't have one specifically uploaded. Displayed in chat.",
    )
    learn_ai_url = models.URLField(
        max_length=255,
        blank=True,
        null=True,
        help_text="URL for the 'Learn AI' button on the homepage.",
    )
    contact_url = models.URLField(
        max_length=255,
        blank=True,
        null=True,
        help_text="URL for the 'Contact' links in the header and footer. If not set, the default contact page will be used.",
    )
    # Add other global settings here as needed in the future

    class Meta:
        verbose_name = "Site Configuration"
        verbose_name_plural = "Site Configuration"

    def __str__(self):
        return "Site Configuration"

    def save(self, *args, **kwargs):
        """Ensure only one instance of SiteConfiguration exists."""
        if not self.pk and SiteConfiguration.objects.exists():
            # Prevent creation of a new instance if one already exists
            raise ValidationError('There can be only one SiteConfiguration instance. Please edit the existing one.')
        super().save(*args, **kwargs)

    @classmethod
    def load(cls):
        """Load the singleton instance, creating it if it doesn't exist."""
        obj, _ = cls.objects.get_or_create(pk=1)
        return obj
