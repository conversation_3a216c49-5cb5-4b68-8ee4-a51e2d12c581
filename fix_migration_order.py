"""
<PERSON><PERSON><PERSON> to fix migration order issues in PostgreSQL.
This script specifically addresses the issue with assistants.0012_alter_assistant_options
being applied before its dependency assistants.0011_remove_assistant_featured_autoplay_and_more.
"""
import os
import django
import psycopg2
from django.conf import settings

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Get database connection info from settings
db_settings = settings.DATABASES['default']

def fix_migration_order():
    """Fix the order of migrations in the database."""
    print("Connecting to PostgreSQL database...")
    try:
        # Connect to PostgreSQL
        conn_params = {
            'dbname': db_settings['NAME'],
            'user': db_settings['USER'],
            'host': db_settings['HOST'],
            'port': db_settings['PORT']
        }
        
        # Only add password if it's not empty
        if db_settings['PASSWORD']:
            conn_params['password'] = db_settings['PASSWORD']
            
        conn = psycopg2.connect(**conn_params)

        # Create a cursor
        cursor = conn.cursor()

        # Check if django_migrations table exists
        cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_name = 'django_migrations'
        )
        """)

        table_exists = cursor.fetchone()[0]

        if not table_exists:
            print("The django_migrations table doesn't exist yet. No need to fix migration order.")
            return

        # Check if the problematic migrations exist
        cursor.execute("""
        SELECT id, app, name, applied
        FROM django_migrations
        WHERE (app = 'assistants' AND name = '0012_alter_assistant_options')
           OR (app = 'assistants' AND name = '0011_remove_assistant_featured_autoplay_and_more')
        ORDER BY applied
        """)
        
        migrations = cursor.fetchall()
        
        if len(migrations) < 2:
            print("The problematic migrations don't exist in the database.")
            return
            
        # Check if they're in the wrong order
        wrong_order = False
        migration_0012_id = None
        migration_0011_id = None
        
        for migration in migrations:
            if migration[1] == 'assistants' and migration[2] == '0012_alter_assistant_options':
                migration_0012_id = migration[0]
            elif migration[1] == 'assistants' and migration[2] == '0011_remove_assistant_featured_autoplay_and_more':
                migration_0011_id = migration[0]
        
        if migration_0012_id is not None and migration_0011_id is not None:
            cursor.execute("""
            SELECT id FROM django_migrations
            WHERE id = %s AND applied < (SELECT applied FROM django_migrations WHERE id = %s)
            """, [migration_0012_id, migration_0011_id])
            
            wrong_order = cursor.fetchone() is not None
        
        if not wrong_order:
            print("The migrations are already in the correct order.")
            return
            
        print("Fixing migration order...")
        
        # Delete both migrations
        cursor.execute("""
        DELETE FROM django_migrations
        WHERE (app = 'assistants' AND name = '0012_alter_assistant_options')
           OR (app = 'assistants' AND name = '0011_remove_assistant_featured_autoplay_and_more')
        """)
        
        # Re-insert them in the correct order
        cursor.execute("""
        INSERT INTO django_migrations (app, name, applied)
        VALUES ('assistants', '0011_remove_assistant_featured_autoplay_and_more', NOW())
        """)
        
        cursor.execute("""
        INSERT INTO django_migrations (app, name, applied)
        VALUES ('assistants', '0012_alter_assistant_options', NOW() + INTERVAL '1 second')
        """)
        
        # Commit the changes
        conn.commit()
        print("Migration order fixed successfully.")

    except Exception as e:
        print(f"An error occurred: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        # Close the connection
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    fix_migration_order()
