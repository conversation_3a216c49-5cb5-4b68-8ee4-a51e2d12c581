import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

print("Starting script to check content_contentimage table structure...")

# Check if the table exists
with connection.cursor() as cursor:
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = 'content_contentimage'
        );
    """)
    table_exists = cursor.fetchone()[0]
    
    if table_exists:
        print("The content_contentimage table exists!")
        
        # Check the table structure
        cursor.execute("""
            SELECT column_name, data_type, character_maximum_length
            FROM information_schema.columns
            WHERE table_name = 'content_contentimage'
            ORDER BY ordinal_position;
        """)
        columns = cursor.fetchall()
        
        print("\nTable structure:")
        print("Column Name | Data Type | Max Length")
        print("-" * 50)
        for column in columns:
            print(f"{column[0]} | {column[1]} | {column[2]}")
    else:
        print("WARNING: The content_contentimage table does NOT exist!")
