/**
 * Message Content Fix CSS
 * Ensures content inside message bubbles fills the available space
 */

/* Base message content styling - ensure content fills the bubble */
.message-content,
.message-content.tinymce-content,
.tinymce-content.message-content,
span.message-content,
div.message-content,
span.message-content.tinymce-content,
div.message-content.tinymce-content,
span.tinymce-content.message-content,
div.tinymce-content.message-content {
  width: 100% !important;
  max-width: 100% !important;
  display: block !important; /* Change from inline-block to block */
  box-sizing: border-box !important;
  overflow-wrap: break-word !important;
  word-wrap: break-word !important;
  word-break: normal !important;
  min-width: 100% !important; /* Ensure minimum width is 100% */
  flex: 1 1 auto !important; /* Allow flex growing */
  margin: 0 !important; /* Remove any margins */
  padding-right: 0 !important; /* Ensure no right padding */
}

/* Ensure all direct children of message content expand to fill width */
.message-content > *,
.message-content.tinymce-content > *,
.tinymce-content.message-content > * {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 100% !important;
  box-sizing: border-box !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* Exception for inline elements that shouldn't be displayed as block */
.message-content a,
.message-content strong,
.message-content em,
.message-content span:not(.message-content):not(.tinymce-content),
.tinymce-content a,
.tinymce-content strong,
.tinymce-content em,
.tinymce-content span:not(.message-content):not(.tinymce-content) {
  width: auto !important;
  min-width: 0 !important;
  max-width: 100% !important;
  display: inline !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Specific styling for paragraphs, divs, and spans */
.message-content p,
.message-content div,
.message-content span:not(.message-content),
.tinymce-content p,
.tinymce-content div,
.tinymce-content span:not(.tinymce-content) {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 100% !important;
  display: block !important;
  box-sizing: border-box !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* Ensure tables expand to fill the width */
.message-content table,
.tinymce-content table {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 100% !important;
  table-layout: fixed !important; /* Fixed table layout for better control */
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  box-sizing: border-box !important;
}

/* Ensure table cells wrap content properly */
.message-content table td,
.message-content table th,
.tinymce-content table td,
.tinymce-content table th {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Ensure images are responsive and fill width */
.message-content img,
.tinymce-content img {
  max-width: 100% !important;
  height: auto !important;
  width: 100% !important;
  display: block !important;
  margin: 0.5rem 0 !important;
  box-sizing: border-box !important;
}

/* Ensure code blocks and pre elements fill width */
.message-content pre,
.message-content code,
.tinymce-content pre,
.tinymce-content code {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 100% !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  box-sizing: border-box !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* Ensure lists fill width */
.message-content ul,
.message-content ol,
.tinymce-content ul,
.tinymce-content ol {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 100% !important;
  box-sizing: border-box !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Ensure list items fill width */
.message-content li,
.tinymce-content li {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 100% !important;
  box-sizing: border-box !important;
  margin-right: 0 !important;
}

/* Ensure headings fill width */
.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6,
.tinymce-content h1,
.tinymce-content h2,
.tinymce-content h3,
.tinymce-content h4,
.tinymce-content h5,
.tinymce-content h6 {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 100% !important;
  box-sizing: border-box !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* Ensure blockquotes fill width */
.message-content blockquote,
.tinymce-content blockquote {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 100% !important;
  box-sizing: border-box !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding-left: 1rem !important;
  padding-right: 0 !important;
  border-left: 3px solid #ccc !important;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .message-content,
  .tinymce-content {
    width: 100% !important;
    max-width: 100% !important;
  }
}

@media (max-width: 768px) {
  .message-content,
  .tinymce-content {
    width: 100% !important;
    max-width: 100% !important;
  }

  /* User messages should be 80% width on mobile */
  .user-message .message-content,
  .user-message .tinymce-content {
    width: 80% !important;
    max-width: 80% !important;
    min-width: 80% !important;
  }

  /* Assistant messages should be 95% width on mobile */
  .assistant-message .message-content,
  .assistant-message .tinymce-content {
    width: 95% !important;
    max-width: 95% !important;
    min-width: 95% !important;
  }
}

@media (max-width: 576px) {
  .message-content,
  .tinymce-content {
    width: 100% !important;
    max-width: 100% !important;
  }

  /* User messages should be 80% width on small mobile */
  .user-message .message-content,
  .user-message .tinymce-content {
    width: 80% !important;
    max-width: 80% !important;
    min-width: 80% !important;
  }

  /* Assistant messages should be 95% width on small mobile */
  .assistant-message .message-content,
  .assistant-message .tinymce-content {
    width: 95% !important;
    max-width: 95% !important;
    min-width: 95% !important;
  }
}
