/**
 * HTTP Protocol Fix
 * This script ensures that all URLs are served via HTTP instead of HTTPS
 * when running in development mode with Django's development server
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('HTTP Protocol Fix: Loaded');
    
    // Check if we're in development mode (not on a secure connection)
    const isDevelopment = window.location.hostname === 'localhost' || 
                          window.location.hostname === '127.0.0.1' ||
                          window.location.hostname.startsWith('192.168.');
    
    if (isDevelopment) {
        console.log('HTTP Protocol Fix: Development environment detected');
        
        // Fix the current URL if it's using HTTPS
        if (window.location.protocol === 'https:') {
            console.log('HTTP Protocol Fix: Converting current page from HTTPS to HTTP');
            // Replace https:// with http:// in the current URL
            const newUrl = window.location.href.replace('https://', 'http://');
            window.location.replace(newUrl);
        }
        
        // Fix all links on the page to use HTTP instead of HTTPS
        document.querySelectorAll('a[href^="https://"]').forEach(link => {
            // Only modify links to the same domain
            if (link.hostname === window.location.hostname) {
                const oldHref = link.href;
                link.href = oldHref.replace('https://', 'http://');
                console.log(`HTTP Protocol Fix: Changed link from ${oldHref} to ${link.href}`);
            }
        });
        
        // Fix all form actions to use HTTP instead of HTTPS
        document.querySelectorAll('form[action^="https://"]').forEach(form => {
            // Only modify forms submitting to the same domain
            const formUrl = new URL(form.action);
            if (formUrl.hostname === window.location.hostname) {
                const oldAction = form.action;
                form.action = oldAction.replace('https://', 'http://');
                console.log(`HTTP Protocol Fix: Changed form action from ${oldAction} to ${form.action}`);
            }
        });
        
        // Intercept form submissions to ensure they use HTTP
        document.addEventListener('submit', function(e) {
            const form = e.target;
            if (form.action && form.action.startsWith('https://')) {
                // Only modify forms submitting to the same domain
                const formUrl = new URL(form.action);
                if (formUrl.hostname === window.location.hostname) {
                    e.preventDefault();
                    const newAction = form.action.replace('https://', 'http://');
                    console.log(`HTTP Protocol Fix: Intercepted form submission, changing from ${form.action} to ${newAction}`);
                    form.action = newAction;
                    form.submit();
                }
            }
        }, true);
        
        // Intercept AJAX requests to ensure they use HTTP
        const originalFetch = window.fetch;
        window.fetch = function(url, options) {
            if (typeof url === 'string' && url.startsWith('https://')) {
                // Only modify requests to the same domain
                try {
                    const requestUrl = new URL(url);
                    if (requestUrl.hostname === window.location.hostname) {
                        const newUrl = url.replace('https://', 'http://');
                        console.log(`HTTP Protocol Fix: Changed fetch URL from ${url} to ${newUrl}`);
                        return originalFetch(newUrl, options);
                    }
                } catch (e) {
                    // If URL parsing fails, proceed with original URL
                }
            }
            return originalFetch(url, options);
        };
        
        // Also intercept XMLHttpRequest
        const originalOpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
            let modifiedUrl = url;
            if (typeof url === 'string' && url.startsWith('https://')) {
                try {
                    const requestUrl = new URL(url, window.location.href);
                    if (requestUrl.hostname === window.location.hostname) {
                        modifiedUrl = url.replace('https://', 'http://');
                        console.log(`HTTP Protocol Fix: Changed XHR URL from ${url} to ${modifiedUrl}`);
                    }
                } catch (e) {
                    // If URL parsing fails, proceed with original URL
                }
            }
            return originalOpen.call(this, method, modifiedUrl, async, user, password);
        };
    }
});
