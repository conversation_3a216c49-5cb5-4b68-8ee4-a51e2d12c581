import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

print("Starting script to check accounts_companyinvitation table structure...")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

# Connect to the database
try:
    print("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(**db_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    
    # Check if the table exists
    print("Checking if accounts_companyinvitation table exists...")
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = 'accounts_companyinvitation'
        );
    """)
    table_exists = cursor.fetchone()[0]
    
    if table_exists:
        print("The accounts_companyinvitation table exists!")
        
        # Check the table structure
        print("Checking accounts_companyinvitation table structure...")
        cursor.execute("""
            SELECT column_name, data_type, character_maximum_length
            FROM information_schema.columns
            WHERE table_name = 'accounts_companyinvitation'
            ORDER BY ordinal_position;
        """)
        columns = cursor.fetchall()
        
        print("\nTable structure:")
        print("Column Name | Data Type | Max Length")
        print("-" * 50)
        for column in columns:
            print(f"{column[0]} | {column[1]} | {column[2]}")
    else:
        print("WARNING: The accounts_companyinvitation table does NOT exist!")
    
    # Close the connection
    cursor.close()
    conn.close()
    print("\nDatabase connection closed.")
    
except Exception as e:
    print(f"Error: {e}")
