# Generated by Django 5.2.1 on 2025-05-20 05:56

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("accounts", "0002_initial"),
        ("assistants", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Content",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("slug", models.SlugField(max_length=200)),
                (
                    "content_type",
                    models.CharField(
                        choices=[
                            ("document", "Document"),
                            ("wiki", "Wiki Page"),
                            ("faq", "FAQ"),
                            ("policy", "Policy"),
                        ],
                        default="document",
                        max_length=20,
                    ),
                ),
                ("body", models.TextField()),
                ("summary", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_public", models.BooleanField(default=False)),
                ("is_archived", models.BooleanField(default=False)),
                (
                    "assistant",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="content",
                        to="assistants.assistant",
                    ),
                ),
                (
                    "author",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="authored_content",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="content",
                        to="accounts.company",
                    ),
                ),
            ],
            options={
                "ordering": ["-updated_at"],
            },
        ),
        migrations.CreateModel(
            name="ContentImage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("image", models.ImageField(upload_to="content/images/")),
                ("alt_text", models.CharField(blank=True, max_length=200)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "content",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="images",
                        to="content.content",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ContentVersion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("body", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("change_summary", models.CharField(blank=True, max_length=200)),
                (
                    "content",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="versions",
                        to="content.content",
                    ),
                ),
                (
                    "edited_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="content_edits",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "get_latest_by": "created_at",
            },
        ),
        migrations.AddIndex(
            model_name="content",
            index=models.Index(
                fields=["company", "content_type"],
                name="content_con_company_2fb228_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="content",
            index=models.Index(
                fields=["company", "is_public"], name="content_con_company_ae5089_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="content",
            index=models.Index(
                fields=["company", "is_archived"], name="content_con_company_860096_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="content",
            unique_together={("company", "slug")},
        ),
    ]
