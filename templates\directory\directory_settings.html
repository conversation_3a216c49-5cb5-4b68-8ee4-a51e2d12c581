{% extends "base/layout.html" %}
{% load crispy_forms_tags %}

{% block title %}{{ page_title|default:"Directory Settings" }}{% endblock %}

{% block page_header %}
    <h1 class="h2">{{ page_title|default:"Directory Settings" }}</h1>
{% endblock %}

{% block main_content %}
<div class="card">
    <div class="card-header">
        Featured Assistants Carousel Settings
    </div>
    <div class="card-body">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}

        <form method="post">
            {% csrf_token %}
            <p class="small text-muted">Configure the appearance and behavior of the featured assistants carousel on the main directory page.</p>

            <div class="row">
                <div class="col-md-6 mb-3">
                    {{ form.featured_scroll_direction|as_crispy_field }}
                </div>
                <div class="col-md-6 mb-3">
                    {{ form.featured_transition_effect|as_crispy_field }}
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-3">
                    {{ form.featured_visible_count|as_crispy_field }}
                </div>
                <div class="col-md-4 mb-3 pt-4"> {# Align checkbox vertically #}
                    {{ form.featured_autoplay|as_crispy_field }}
                </div>
                <div class="col-md-4 mb-3">
                    {{ form.featured_autoplay_delay|as_crispy_field }}
                </div>
            </div>

            <hr class="my-4">

            <h5 class="mb-3">Standard Tier Visibility</h5>
            <div class="mb-3">
                 {{ form.hide_standard_tier_assistants|as_crispy_field }}
            </div>
            <div class="mb-3">
                 {{ form.hide_standard_tier_companies|as_crispy_field }}
            </div>

            <div class="mt-3">
                <button type="submit" class="btn btn-primary">Save Settings</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
