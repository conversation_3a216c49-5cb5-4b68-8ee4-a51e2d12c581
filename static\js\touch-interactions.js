/**
 * Touch Interactions JavaScript
 * Enhances touch interactions for mobile devices
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('[Touch Interactions] Initializing touch interactions');

    // Check if we're on a mobile device
    const isMobile = window.innerWidth <= 768 || 'ontouchstart' in window;

    // Check if we're in landscape mode
    const isLandscape = window.innerWidth > window.innerHeight;

    if (isMobile) {
        console.log('[Touch Interactions] Mobile device detected');

        if (isLandscape) {
            console.log('[Touch Interactions] Landscape mode detected initially');
        }

        // Add touch feedback to interactive elements
        addTouchFeedback();

        // Fix iOS double-tap zoom
        fixDoubleTapZoom();

        // Fix iOS 100vh issue
        fixIOS100vh();

        // Fix iOS input focus scrolling
        fixIOSInputFocus();

        // Apply landscape-specific optimizations if needed
        if (isLandscape) {
            optimizeTouchForLandscape();
        }

        // Listen for orientation changes
        window.addEventListener('orientationchange', function() {
            console.log('[Touch Interactions] Orientation changed');
            setTimeout(function() {
                fixIOS100vh();

                // Apply landscape-specific touch optimizations
                if (window.innerWidth > window.innerHeight) {
                    console.log('[Touch Interactions] Landscape mode detected');
                    optimizeTouchForLandscape();
                }
            }, 300);
        });

        // Listen for window resize to handle landscape mode changes
        window.addEventListener('resize', function() {
            console.log('[Touch Interactions] Window resized');
            setTimeout(function() {
                // Check if we're in landscape mode after resize
                if (window.innerWidth > window.innerHeight) {
                    console.log('[Touch Interactions] Landscape mode detected after resize');
                    optimizeTouchForLandscape();
                }
            }, 300);
        });
    }

    /**
     * Adds touch feedback to interactive elements
     */
    function addTouchFeedback() {
        console.log('[Touch Interactions] Adding touch feedback');

        // Elements that should have touch feedback
        const interactiveElements = document.querySelectorAll(
            'a, button, .btn, .nav-link, .dropdown-item, .card, .list-group-item, ' +
            '.directory-card, .featured-carousel-item, .tier-section, .filter-form'
        );

        interactiveElements.forEach(function(element) {
            // Add touch-start feedback
            element.addEventListener('touchstart', function(e) {
                // Don't add feedback to elements with disabled attribute
                if (element.hasAttribute('disabled') || element.classList.contains('disabled')) {
                    return;
                }

                // Add touch-active class
                element.classList.add('touch-active');

                // Store the original transform and opacity
                element.dataset.originalTransform = element.style.transform || '';
                element.dataset.originalOpacity = element.style.opacity || '';

                // Apply touch feedback
                element.style.transform = 'scale(0.98)';
                element.style.opacity = '0.9';
            }, { passive: true });

            // Remove touch feedback on touch end
            element.addEventListener('touchend', function() {
                // Restore original styles
                element.style.transform = element.dataset.originalTransform || '';
                element.style.opacity = element.dataset.originalOpacity || '';

                // Remove touch-active class
                element.classList.remove('touch-active');
            }, { passive: true });

            // Remove touch feedback on touch cancel
            element.addEventListener('touchcancel', function() {
                // Restore original styles
                element.style.transform = element.dataset.originalTransform || '';
                element.style.opacity = element.dataset.originalOpacity || '';

                // Remove touch-active class
                element.classList.remove('touch-active');
            }, { passive: true });
        });
    }

    /**
     * Fixes iOS double-tap zoom
     */
    function fixDoubleTapZoom() {
        console.log('[Touch Interactions] Fixing double-tap zoom');

        // Add touch-action: manipulation to all interactive elements
        const interactiveElements = document.querySelectorAll(
            'a, button, .btn, .nav-link, .dropdown-item, .card, .list-group-item, ' +
            '.directory-card, .featured-carousel-item, .tier-section, .filter-form'
        );

        interactiveElements.forEach(function(element) {
            element.style.touchAction = 'manipulation';
        });
    }

    /**
     * Fixes iOS 100vh issue
     */
    function fixIOS100vh() {
        console.log('[Touch Interactions] Fixing iOS 100vh issue');

        // Set CSS variable for viewport height
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);

        // Apply the custom viewport height to relevant elements
        const fullHeightElements = document.querySelectorAll('.chat-box, .sidebar, #chat-sidebar');
        fullHeightElements.forEach(function(element) {
            element.style.height = 'calc(var(--vh, 1vh) * 100)';
        });
    }

    /**
     * Fixes iOS input focus scrolling
     */
    function fixIOSInputFocus() {
        console.log('[Touch Interactions] Fixing iOS input focus scrolling');

        // Get all input elements
        const inputElements = document.querySelectorAll('input, textarea');

        inputElements.forEach(function(input) {
            // When an input is focused
            input.addEventListener('focus', function() {
                // Add padding to the bottom of the body to ensure the input is visible
                document.body.style.paddingBottom = '150px';

                // Scroll to the input after a short delay
                setTimeout(function() {
                    input.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 300);
            });

            // When an input loses focus
            input.addEventListener('blur', function() {
                // Remove the padding
                document.body.style.paddingBottom = '';
            });
        });
    }

    /**
     * Optimizes touch interactions for landscape mode
     */
    function optimizeTouchForLandscape() {
        console.log('[Touch Interactions] Optimizing touch for landscape mode');

        // Adjust touch targets for landscape mode
        const touchTargets = document.querySelectorAll(
            '.btn, .nav-link, .dropdown-item, .form-control, .form-select, ' +
            'input[type="checkbox"], input[type="radio"], .form-check-label, .navbar-toggler'
        );

        touchTargets.forEach(function(element) {
            // Make touch targets slightly smaller in landscape to fit better
            element.style.minHeight = '40px';
            element.style.minWidth = '40px';
            element.style.padding = '0.4rem 0.8rem';
        });

        // Optimize action buttons in landscape mode
        const actionButtons = document.querySelectorAll('.directory-card .col-md-2.text-end .btn, .directory-card .col-md-4.text-end .btn');
        actionButtons.forEach(function(button) {
            button.style.margin = '0.25rem';
            button.style.padding = '0.4rem';
        });

        // Adjust form controls for landscape mode
        const formControls = document.querySelectorAll('.form-control, .form-select');
        formControls.forEach(function(control) {
            control.style.padding = '0.4rem 0.6rem';
        });

        // Optimize contact information display for landscape
        const contactInfo = document.querySelectorAll('.contact-info');
        contactInfo.forEach(function(info) {
            info.style.display = 'block';
            info.style.marginBottom = '0';
            info.style.fontSize = '0.75rem';

            // Optimize contact info list items
            const contactItems = info.querySelectorAll('li');
            contactItems.forEach(function(item) {
                item.style.marginBottom = '0.25rem';
                item.style.whiteSpace = 'nowrap';
                item.style.overflow = 'hidden';
                item.style.textOverflow = 'ellipsis';
                item.style.maxWidth = '100%';
            });

            // Optimize contact info icons
            const contactIcons = info.querySelectorAll('i');
            contactIcons.forEach(function(icon) {
                icon.style.fontSize = '0.8rem';
                icon.style.width = '12px';
                icon.style.textAlign = 'center';
                icon.style.marginRight = '0.25rem';
            });

            // Optimize contact text
            const contactTexts = info.querySelectorAll('.contact-text');
            contactTexts.forEach(function(text) {
                text.style.display = 'inline';
                text.style.color = '#cccccc';
            });
        });

        // Position like, QR code and share buttons for landscape
        const actionButtonsContainer = document.querySelectorAll('.action-buttons-container');
        actionButtonsContainer.forEach(function(container) {
            container.style.display = 'flex';
            container.style.flexDirection = 'column';
            container.style.alignItems = 'flex-end';
            container.style.justifyContent = 'center';
        });
    }
});
