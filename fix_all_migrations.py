"""
<PERSON><PERSON>t to fix all migration issues by:
1. Modifying migration files to handle the NavigationItem model
2. Faking all migrations in the database
"""
import os
import sys
import re
import django
from django.db import connection
from django.core.management import call_command

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

def fix_migration_file(file_path):
    """Fix a migration file by modifying it directly."""
    print(f"Checking migration file: {file_path}")
    try:
        # Read the file
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check if the file contains references to NavigationItem
        if 'NavigationItem' in content or 'navigationitem' in content.lower():
            print(f"Found NavigationItem references in {file_path}")
            
            # Add a do_nothing function if it doesn't exist
            if 'def do_nothing(' not in content:
                do_nothing_func = '''
def do_nothing(apps, schema_editor):
    """
    This function does nothing.
    We're using it because the model doesn't exist in the state.
    """
    pass
'''
                # Add the function after the imports
                content = re.sub(r'(from django.db import migrations.*?\n)',
                                r'\1\n' + do_nothing_func,
                                content,
                                flags=re.DOTALL)
            
            # Comment out operations that reference NavigationItem
            content = re.sub(r'(\s+migrations\.AddField\([^)]*?[\'"]navigationitem[\'"][^)]*?\),)',
                            r'\n        # Commented out to fix migration issues\n        # \1\n        migrations.RunPython(do_nothing, do_nothing),',
                            content)
            
            content = re.sub(r'(\s+migrations\.AlterField\([^)]*?[\'"]navigationitem[\'"][^)]*?\),)',
                            r'\n        # Commented out to fix migration issues\n        # \1\n        migrations.RunPython(do_nothing, do_nothing),',
                            content)
            
            content = re.sub(r'(\s+migrations\.RemoveField\([^)]*?[\'"]navigationitem[\'"][^)]*?\),)',
                            r'\n        # Commented out to fix migration issues\n        # \1\n        migrations.RunPython(do_nothing, do_nothing),',
                            content)
            
            content = re.sub(r'(\s+migrations\.CreateModel\(\s*name=[\'"]NavigationItem[\'"][^)]*?\s+\),)',
                            r'\n        # Commented out to fix migration issues\n        # \1\n        migrations.RunPython(do_nothing, do_nothing),',
                            content,
                            flags=re.DOTALL)
            
            # Write the modified content back to the file
            with open(file_path, 'w') as f:
                f.write(content)
            
            print(f"Fixed migration file: {file_path}")
            return True
        return True
    except Exception as e:
        print(f"Error fixing migration file {file_path}: {e}")
        return False

def find_migration_files():
    """Find all migration files in the project."""
    print("Finding migration files...")
    try:
        migration_files = []
        
        # Look for migration files in all app directories
        for root, dirs, files in os.walk('.'):
            if 'migrations' in root and '__pycache__' not in root:
                for file in files:
                    if file.endswith('.py') and not file.startswith('__'):
                        migration_files.append(os.path.join(root, file))
        
        return migration_files
    except Exception as e:
        print(f"Error finding migration files: {e}")
        return []

def fake_all_migrations():
    """Fake all migrations in the database."""
    print("Faking all migrations...")
    try:
        call_command('migrate', '--fake')
        print("All migrations faked successfully.")
        return True
    except Exception as e:
        print(f"Error faking migrations: {e}")
        return False

def main():
    """Main function to fix all migration issues."""
    print("Starting migration fix...")
    
    # Find migration files
    migration_files = find_migration_files()
    print(f"Found {len(migration_files)} migration files.")
    
    # Fix each migration file
    for file_path in migration_files:
        fix_migration_file(file_path)
    
    # Fake all migrations
    fake_all_migrations()
    
    print("\nNext steps:")
    print("1. Run 'python manage.py runserver' to start the server")
    print("2. If you still encounter issues, try 'python manage.py migrate --fake-initial'")

if __name__ == "__main__":
    main()
