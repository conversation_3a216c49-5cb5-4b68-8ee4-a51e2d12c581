from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    help = 'Checks and fixes the accounts_companyinvitation table structure'

    def handle(self, *args, **options):
        self.stdout.write('Checking accounts_companyinvitation table structure...')
        
        # Check if the table exists
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'accounts_companyinvitation'
                );
            """)
            table_exists = cursor.fetchone()[0]
            
            if table_exists:
                self.stdout.write(self.style.SUCCESS('The accounts_companyinvitation table exists!'))
            else:
                self.stdout.write(self.style.WARNING('The accounts_companyinvitation table does NOT exist!'))
                
                # Create the table
                self.stdout.write('Creating accounts_companyinvitation table...')
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS "accounts_companyinvitation" (
                        "id" serial NOT NULL PRIMARY KEY,
                        "email" varchar(254) NOT NULL,
                        "token" varchar(100) NOT NULL UNIQUE,
                        "status" varchar(20) NOT NULL,
                        "invited_at" timestamp with time zone NOT NULL,
                        "expires_at" timestamp with time zone NOT NULL,
                        "accepted_at" timestamp with time zone NULL,
                        "company_id" integer NOT NULL REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED,
                        "invited_by_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
                    );
                """)
                self.stdout.write(self.style.SUCCESS('Table created successfully!'))
                
        self.stdout.write(self.style.SUCCESS('Table check and fix completed successfully!'))
