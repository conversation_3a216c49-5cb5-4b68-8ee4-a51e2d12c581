import os
import uuid
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.utils.text import slugify

def handle_image_upload(uploaded_file, item_unique_id, field_type, index=None, gallery_index=None):
    """
    Saves an uploaded image file to the 'uploads/' directory within MEDIA_ROOT.

    Args:
        uploaded_file: The InMemoryUploadedFile object.
        item_unique_id: The unique_id of the NavigationItem (e.g., 'about_us_123').
        field_type: 'main_image' or 'gallery'.
        index: The index for multi-entry items (e.g., product 0, 1, 2). Optional.
        gallery_index: The index within a gallery upload. Optional.

    Returns:
        The relative path to the saved file within MEDIA_ROOT (e.g., 'uploads/about_us_123_main_image_uuid.jpg').
        Returns None if the file is not an image or an error occurs.
    """
    if not uploaded_file or not uploaded_file.content_type.startswith("image/"):
        print(f"Skipping non-image file: {uploaded_file.name if uploaded_file else 'None'}")
        return None

    # Sanitize original filename and get extension
    original_filename = uploaded_file.name
    filename_base, file_extension = os.path.splitext(original_filename)
    safe_filename_base = slugify(filename_base)
    safe_extension = file_extension.lower()

    # Construct a unique filename
    unique_id = uuid.uuid4().hex[:8] # Add a short unique ID to prevent collisions
    filename_parts = [item_unique_id, field_type]
    if index is not None:
        filename_parts.append(str(index))
    if gallery_index is not None:
        filename_parts.append(str(gallery_index))
    filename_parts.append(safe_filename_base)
    filename_parts.append(unique_id)

    filename = "_".join(filter(None, filename_parts)) + safe_extension
    upload_dir = 'uploads' # Subdirectory within MEDIA_ROOT
    file_path = os.path.join(upload_dir, filename)

    try:
        # Use default_storage to save the file
        saved_path = default_storage.save(file_path, ContentFile(uploaded_file.read()))
        print(f"Saved image to: {saved_path}")
        # default_storage.save might modify the path slightly (e.g., add underscores)
        # Return the actual path returned by the storage backend
        return saved_path
    except Exception as e:
        print(f"Error saving image {original_filename} to {file_path}: {e}")
        return None