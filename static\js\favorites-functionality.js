/**
 * Favorites Functionality
 * Global JavaScript for handling favorites/likes functionality across the site
 */

// Store current item details for modal actions
// These variables are declared globally for the module
var currentModalItemId = null;
var currentModalItemType = null;

// Initialize when the document is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Favorites Functionality: Initializing');

    initFavoriteButtons();
    initFolderModal();

    console.log('Favorites Functionality: Initialized');
});

/**
 * Initialize all favorite buttons on the page
 */
function initFavoriteButtons() {
    // First, ensure all heart icons are properly styled based on their state
    const allLikeButtons = document.querySelectorAll('.like-button, .btn-like, .btn-favorite, .favorite-button');
    allLikeButtons.forEach(button => {
        // Check if the button is in liked state
        const isLiked = button.classList.contains('text-danger');

        // Apply appropriate styling
        if (isLiked) {
            button.classList.add('text-danger');
            button.classList.remove('text-secondary');
        } else {
            button.classList.remove('text-danger');
            button.classList.add('text-secondary');
        }
    });

    // Find all favorite buttons
    const favoriteButtons = document.querySelectorAll('.like-button, .favorite-btn, [data-action="favorite"], button[data-item-type]');

    favoriteButtons.forEach(button => {
        // Skip if already initialized
        if (button.dataset.favoritesInitialized === 'true') {
            return;
        }

        // Remove any existing event listeners
        const newButton = button.cloneNode(true);
        if (button.parentNode) {
            button.parentNode.replaceChild(newButton, button);
        }

        // Add new event listener
        newButton.addEventListener('click', async function(event) {
            event.preventDefault();
            event.stopPropagation();

            console.log('Favorites Functionality: Button clicked');

            const itemId = this.getAttribute('data-item-id');
            const itemType = this.getAttribute('data-item-type');

            if (!itemId || !itemType) {
                console.error('Favorites Functionality: Missing item ID or type');
                return;
            }

            try {
                await handleFavoriteClick(this, itemId, itemType);
            } catch (error) {
                console.error('Favorites Functionality: Error handling click', error);
                alert(`An error occurred: ${error.message}`);
            }
        });

        // Mark as initialized
        newButton.dataset.favoritesInitialized = 'true';
    });
}

/**
 * Handle click on a favorite button
 */
async function handleFavoriteClick(button, itemId, itemType) {
    console.log(`Favorite button clicked: ID=${itemId}, Type=${itemType}`);

    // Set these variables globally so they're available for the modal actions
    currentModalItemId = itemId;
    currentModalItemType = itemType;

    // Get CSRF token
    const csrfToken = getCsrfToken();
    if (!csrfToken) {
        console.error('CSRF token not found');
        alert('Error: CSRF token not found. Please refresh the page and try again.');
        return;
    }

    // Show loading state on the button
    const originalButtonHTML = button.innerHTML;
    button.disabled = true;
    if (button.querySelector('i')) {
        button.querySelector('i').className = 'bi bi-hourglass-split';
    }

    try {
        // Prepare form data
        const formData = new URLSearchParams();
        formData.append('item_id', itemId);
        formData.append('item_type', itemType);

        console.log(`Sending toggle request with params: item_id=${itemId}, item_type=${itemType}`);

        // Send request
        const response = await fetch('/directory/favorites/toggle/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
        }

        const data = await response.json();
        console.log('Toggle response:', data);

        if (data.status === 'success' && data.action === 'unfavorited') {
            // Item was unfavorited
            console.log('Item unfavorited, updating heart icon');
            updateHeartIcon(button, false);

            // Check if we're on the My Favorites page and remove the item if needed
            // More robust check for the favorites page URL
            if (window.location.pathname.startsWith('/directory/favorites/') || window.location.pathname.includes('/my-favorites') || window.location.pathname.includes('/my_favorites')) {
                console.log('[DEBUG] Pathname (' + window.location.pathname + ') matches favorites page. Calling removeItemFromFavoritesPage.');
                removeItemFromFavoritesPage(itemId, itemType);
            } else {
                console.log('[DEBUG] Pathname (' + window.location.pathname + ') does NOT match favorites page. Not calling removeItemFromFavoritesPage.');
            }
        } else if (data.status === 'options') {
            // Item is not saved, show folder options modal
            console.log('Showing folder options modal');

            // Show the folder options modal
            const folderModalElement = document.getElementById('folderOptionsModal');
            if (folderModalElement) {
                // Make sure we have a Bootstrap Modal instance
                let folderModal = bootstrap.Modal.getInstance(folderModalElement);
                if (!folderModal) {
                    folderModal = new bootstrap.Modal(folderModalElement);
                }

                // Populate and show the modal
                populateAndShowFolderModal(data);
                folderModal.show();

                console.log('Folder modal shown');
            } else {
                // Fallback: If modal doesn't exist, just mark as liked
                console.warn("Folder modal not found, performing basic like.");
                await saveToFavorites(); // Save without folder
                updateHeartIcon(button, true);
            }
        } else {
            throw new Error(data.message || 'Unknown error');
        }
    } catch (error) {
        console.error('Error handling favorite click:', error);
        alert(`Error: ${error.message || 'An unknown error occurred'}. Please try again.`);
    } finally {
        // Restore button state
        button.innerHTML = originalButtonHTML;
        button.disabled = false;
    }
}

/**
 * Update the heart icon appearance
 * @param {HTMLElement} button - The button element to update
 * @param {boolean} isSaved - Whether the item is saved/favorited
 */
function updateHeartIcon(button, isSaved) {
    if (!button) {
        console.error('updateHeartIcon: Button is null or undefined');
        return;
    }

    console.log(`Updating heart icon: isSaved=${isSaved}, button=`, button);

    try {
        // Get the item ID and type from the button
        const itemId = button.getAttribute('data-item-id');
        const itemType = button.getAttribute('data-item-type');

        // Update button classes
        if (isSaved) {
            button.classList.remove('text-secondary');
            button.classList.add('text-danger');
            button.title = 'Remove from Favorites';
            button.setAttribute('data-favorited', 'true');
        } else {
            button.classList.remove('text-danger');
            button.classList.add('text-secondary');
            button.title = 'Add to Favorites';
            button.setAttribute('data-favorited', 'false');
        }

        // Check for Bootstrap icon (i tag)
        let icon = button.querySelector('i.bi-heart, i.bi-heart-fill');

        // If no icon found, check for SVG
        if (!icon) {
            icon = button.querySelector('svg');
        }

        if (icon) {
            if (icon.tagName === 'I') {
                // Handle Bootstrap icon classes
                if (isSaved) {
                    icon.classList.remove('bi-heart');
                    icon.classList.add('bi-heart-fill');
                    button.classList.add('text-danger');
                    button.classList.remove('text-secondary');

                    // Add pulse animation class for better feedback
                    icon.classList.add('pulse-heart');
                    setTimeout(() => {
                        icon.classList.remove('pulse-heart');
                    }, 800);
                } else {
                    icon.classList.remove('bi-heart-fill', 'pulse-heart');
                    icon.classList.add('bi-heart');
                    button.classList.remove('text-danger');
                    button.classList.add('text-secondary');
                }
            } else if (icon.tagName === 'SVG') {
                // For SVG icons, update the button class which controls the color
                if (isSaved) {
                    button.classList.add('text-danger');
                    button.classList.remove('text-secondary');
                } else {
                    button.classList.remove('text-danger');
                    button.classList.add('text-secondary');
                }
                console.log('SVG icon found, color updated via button class');
            }
        } else {
            console.warn('No icon found in button, creating one');
            // Create an icon if none exists
            icon = document.createElement('i');
            icon.className = isSaved ? 'bi bi-heart-fill' : 'bi bi-heart';
            button.prepend(icon);
        }

        // Remove all text nodes (don't add new text)
        Array.from(button.childNodes).forEach(node => {
            if (node.nodeType === Node.TEXT_NODE) {
                button.removeChild(node);
            }
        });

        // Add ripple effect for better visual feedback
        button.classList.add('ripple');
        setTimeout(() => {
            button.classList.remove('ripple');
        }, 500);

        // Add a visual feedback animation with smooth transition
        button.style.transition = 'transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), color 0.3s ease';
        button.style.transform = 'scale(1.2)';

        // Add a heart particle effect for better visual feedback
        if (isSaved) {
            // Use enhanced particles if available, otherwise fall back to original
            if (typeof createEnhancedHeartParticles === 'function') {
                createEnhancedHeartParticles(button);
            } else {
                createHeartParticles(button);
            }
        }

        setTimeout(() => {
            button.style.transform = 'scale(1)';
        }, 300);

        // IMPORTANT: Update all other buttons with the same item ID and type
        if (itemId && itemType) {
            // Find all other like buttons for the same item
            const allButtons = document.querySelectorAll(`.like-button[data-item-id="${itemId}"][data-item-type="${itemType}"]`);

            allButtons.forEach(otherButton => {
                // Skip the button we just updated
                if (otherButton === button) return;

                console.log(`Syncing state to other button:`, otherButton);

                // Update the other button's state to match
                if (isSaved) {
                    otherButton.classList.remove('text-secondary');
                    otherButton.classList.add('text-danger');
                    otherButton.title = 'Remove from Favorites';
                    otherButton.setAttribute('data-favorited', 'true');

                    // Update icon if it exists
                    const otherIcon = otherButton.querySelector('i.bi-heart, i.bi-heart-fill');
                    if (otherIcon) {
                        otherIcon.classList.remove('bi-heart');
                        otherIcon.classList.add('bi-heart-fill');
                    }

                    // Remove any text nodes
                    Array.from(otherButton.childNodes).forEach(node => {
                        if (node.nodeType === Node.TEXT_NODE) {
                            otherButton.removeChild(node);
                        }
                    });
                } else {
                    otherButton.classList.remove('text-danger');
                    otherButton.classList.add('text-secondary');
                    otherButton.title = 'Add to Favorites';
                    otherButton.setAttribute('data-favorited', 'false');

                    // Update icon if it exists
                    const otherIcon = otherButton.querySelector('i.bi-heart, i.bi-heart-fill');
                    if (otherIcon) {
                        otherIcon.classList.remove('bi-heart-fill');
                        otherIcon.classList.add('bi-heart');
                    }

                    // Remove any text nodes
                    Array.from(otherButton.childNodes).forEach(node => {
                        if (node.nodeType === Node.TEXT_NODE) {
                            otherButton.removeChild(node);
                        }
                    });
                }
            });
        }

        console.log('Heart icon updated successfully');
    } catch (error) {
        console.error('Error updating heart icon:', error);
    }
}

/**
 * Initialize the folder modal
 */
function initFolderModal() {
    console.log('Initializing folder modal');
    const folderModal = document.getElementById('folderOptionsModal');
    if (!folderModal) {
        console.error('Folder modal element not found in the DOM');
        return;
    }
    console.log('Found folder modal element:', folderModal);

    const saveWithoutFolderBtn = document.getElementById('saveWithoutFolderBtn');
    const createFolderToggleBtn = document.getElementById('createFolderToggleBtn');
    const createFolderForm = document.getElementById('createFolderForm');
    const newFolderNameInput = document.getElementById('newFolderName');
    const createAndSaveBtn = document.getElementById('createAndSaveBtn');
    const folderOptionsList = document.getElementById('folderOptionsList');
    const folderModalErrorMsg = document.getElementById('folderModalErrorMsg');

    // Log which elements were found for debugging
    console.log('Modal button elements found:', {
        saveWithoutFolderBtn: !!saveWithoutFolderBtn,
        createFolderToggleBtn: !!createFolderToggleBtn,
        createFolderForm: !!createFolderForm,
        newFolderNameInput: !!newFolderNameInput,
        createAndSaveBtn: !!createAndSaveBtn,
        folderOptionsList: !!folderOptionsList,
        folderModalErrorMsg: !!folderModalErrorMsg
    });

    // Try to find buttons by class if ID not found
    let saveBtn = saveWithoutFolderBtn;
    if (!saveBtn) {
        const btnByClass = document.querySelector('.save-without-folder-btn');
        if (btnByClass) {
            console.log('Found save button by class instead of ID');
            saveBtn = btnByClass;
        }
    }

    // Reset modal when it's hidden
    folderModal.addEventListener('hidden.bs.modal', function() {
        if (createFolderForm) createFolderForm.style.display = 'none';
        if (newFolderNameInput) newFolderNameInput.value = '';
        if (folderModalErrorMsg) folderModalErrorMsg.style.display = 'none';

        // Reset current item variables
        currentModalItemId = null;
        currentModalItemType = null;
    });

    // Toggle create folder form
    if (createFolderToggleBtn && createFolderForm) {
        createFolderToggleBtn.addEventListener('click', function() {
            createFolderForm.style.display = createFolderForm.style.display === 'none' ? 'block' : 'none';
            if (createFolderForm.style.display === 'block') {
                newFolderNameInput.focus();
            }
        });
    }

    // Save without folder button
    if (saveBtn) {
        console.log('Adding click event to save button:', saveBtn);
        saveBtn.addEventListener('click', async function() {
            console.log('Save button clicked');
            await saveToFavorites();
        });
    } else {
        console.warn('Save button not found in the DOM');
    }

    // Create and save button
    if (createAndSaveBtn && newFolderNameInput) {
        createAndSaveBtn.addEventListener('click', async function() {
            const folderName = newFolderNameInput.value.trim();
            if (!folderName) {
                if (folderModalErrorMsg) {
                    folderModalErrorMsg.textContent = 'Please enter a folder name.';
                    folderModalErrorMsg.style.display = 'block';
                }
                return;
            }
            await saveToFavorites(folderName);
        });

        // Also handle Enter key in the input field
        newFolderNameInput.addEventListener('keyup', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                createAndSaveBtn.click();
            }
        });
    }

    // Handle folder click in the list
    if (folderOptionsList) {
        folderOptionsList.addEventListener('click', async function(event) {
            const folderItem = event.target.closest('.list-group-item');
            if (!folderItem) return;

            const folderId = folderItem.getAttribute('data-folder-id');
            if (folderId) {
                await saveToFavorites(null, folderId);
            }
        });
    }
}

/**
 * Populate and show the folder modal with enhanced styling
 * @param {Object} data - The data containing folders and item information
 */
function populateAndShowFolderModal(data) {
    console.log('Populating folder modal with data:', data);

    // Find all possible modal title elements (different templates might use different IDs)
    const modalTitle = document.getElementById('folderModalTitle');
    const modalItemName = document.getElementById('modalItemName');
    const modalFolderNameItemName = document.getElementById('modalFolderNameItemName');

    // Log which elements were found for debugging
    console.log('Modal elements found:', {
        modalTitle: !!modalTitle,
        modalItemName: !!modalItemName,
        modalFolderNameItemName: !!modalFolderNameItemName
    });

    // Find all possible folder list elements - use let instead of const since we might reassign it
    let folderOptionsList = document.getElementById('folderOptionsList') ||
                           document.getElementById('folderList');

    const createFolderForm = document.getElementById('createFolderForm');
    const newFolderNameInput = document.getElementById('newFolderName');
    const folderModalErrorMsg = document.getElementById('folderModalErrorMsg');

    // If we can't find the folder list, try to create one
    if (!folderOptionsList) {
        console.warn('Folder options list element not found, attempting to create one');
        const modalBody = document.querySelector('#folderOptionsModal .modal-body');
        if (modalBody) {
            // Create a container for the folder list
            const folderListContainer = document.createElement('div');
            folderListContainer.id = 'folderOptionsList';
            folderListContainer.className = 'list-group mb-3';

            // Find where to insert it (after the intro paragraph)
            const introParagraph = modalBody.querySelector('p');
            if (introParagraph) {
                introParagraph.after(folderListContainer);
            } else {
                modalBody.prepend(folderListContainer);
            }

            // Now we have a folder list element
            const newFolderOptionsList = document.getElementById('folderOptionsList');
            if (newFolderOptionsList) {
                console.log('Successfully created folder options list element');
                // Use the newly created element
                folderOptionsList = newFolderOptionsList;
            }
        }
    }

    // Reset modal state
    if (createFolderForm) createFolderForm.style.display = 'none';
    if (newFolderNameInput) newFolderNameInput.value = '';
    if (folderModalErrorMsg) folderModalErrorMsg.style.display = 'none';

    // Set item name in all possible elements
    const itemName = data.item_name || 'this item';

    if (modalTitle) {
        modalTitle.textContent = `Save ${itemName} to:`;
    }

    if (modalItemName) {
        modalItemName.textContent = itemName;
    }

    if (modalFolderNameItemName) {
        modalFolderNameItemName.textContent = itemName;
    }

    // Populate folders list
    if (folderOptionsList) {
        // Clear existing content
        folderOptionsList.innerHTML = '';

        // First, check if there are any existing elements in the modal that we should remove
        const modalBody = document.querySelector('#folderOptionsModal .modal-body');
        if (modalBody) {
            // Get all the elements we need to keep
            const introText = modalBody.querySelector('p:first-child');
            const errorMsg = modalBody.querySelector('#folderModalErrorMsg');

            // Save the original content
            const originalIntro = introText ? introText.cloneNode(true) : null;
            const originalError = errorMsg ? errorMsg.cloneNode(true) : null;

            // Clear everything from the modal body
            modalBody.innerHTML = '';

            // Add back the intro text if it existed
            if (originalIntro) {
                modalBody.appendChild(originalIntro);
            }

            // Add back the error message if it existed
            if (originalError) {
                modalBody.appendChild(originalError);
            }

            // Create a new folder options list element
            const newFolderOptionsList = document.createElement('div');
            newFolderOptionsList.id = 'folderOptionsList';
            newFolderOptionsList.className = 'list-group mb-3';
            modalBody.appendChild(newFolderOptionsList);

            // Update our reference to the folder options list
            folderOptionsList = newFolderOptionsList;
        }

        // Create the main container for the modal content
        const modalContentContainer = document.createElement('div');
        modalContentContainer.className = 'folder-modal-content';

        // Create the folder list section
        const folderListSection = document.createElement('div');
        folderListSection.className = 'folder-list-section mb-3';

        // Add a heading for the folders section
        const foldersHeading = document.createElement('div');
        foldersHeading.className = 'fw-bold mb-2';
        foldersHeading.textContent = 'Select a folder:';
        folderListSection.appendChild(foldersHeading);

        // Create a container for the folder buttons with max height and scrolling
        const folderContainer = document.createElement('div');
        folderContainer.className = 'folder-list-container';
        folderContainer.style.maxHeight = '200px';
        folderContainer.style.overflowY = 'auto';
        folderContainer.style.border = '1px solid rgba(0,0,0,0.125)';
        folderContainer.style.borderRadius = '0.25rem';

        if (data.folders && data.folders.length > 0) {
            console.log(`Adding ${data.folders.length} folders to the list`);

            // Add each folder as a button with enhanced styling
            data.folders.forEach(folder => {
                const item = document.createElement('button');
                item.className = 'list-group-item list-group-item-action d-flex align-items-center';
                item.setAttribute('data-folder-id', folder.id);
                item.style.border = 'none';
                item.style.borderBottom = '1px solid rgba(0,0,0,0.125)';

                const folderIcon = document.createElement('i');
                folderIcon.className = 'bi bi-folder me-2';
                item.appendChild(folderIcon);

                const folderName = document.createElement('span');
                folderName.textContent = folder.name;
                folderName.className = 'flex-grow-1';
                item.appendChild(folderName);

                // Add a subtle arrow icon to indicate it's clickable
                const arrowIcon = document.createElement('i');
                arrowIcon.className = 'bi bi-chevron-right text-muted';
                arrowIcon.style.fontSize = '0.8rem';
                item.appendChild(arrowIcon);

                folderContainer.appendChild(item);
            });
        } else {
            // No folders message
            const noFolders = document.createElement('div');
            noFolders.className = 'text-muted small p-2 text-center';
            noFolders.textContent = 'No folders yet. Create one or save without a folder.';
            folderContainer.appendChild(noFolders);
        }

        folderListSection.appendChild(folderContainer);

        // Add the folder list section to the main container
        modalContentContainer.appendChild(folderListSection);

        // Create the "Create new folder" section
        const createFolderSection = document.createElement('div');
        createFolderSection.className = 'create-folder-section mb-3';

        // Create folder toggle button
        const createFolderToggle = document.createElement('button');
        createFolderToggle.id = 'createFolderToggleBtn';
        createFolderToggle.className = 'btn btn-outline-secondary w-100 d-flex align-items-center justify-content-center';
        createFolderToggle.innerHTML = '<i class="bi bi-folder-plus me-2"></i> Create New Folder';
        createFolderSection.appendChild(createFolderToggle);

        // Create folder form (initially hidden)
        const newFolderForm = document.createElement('div');
        newFolderForm.id = 'createFolderForm';
        newFolderForm.className = 'mt-3';
        newFolderForm.style.display = 'none';

        // Create form content
        newFolderForm.innerHTML = `
            <div class="input-group mb-2">
                <input type="text" id="newFolderName" class="form-control" placeholder="Folder name">
                <button id="createAndSaveBtn" class="btn btn-success">
                    <i class="bi bi-check-lg"></i> Create & Save
                </button>
            </div>
        `;

        createFolderSection.appendChild(newFolderForm);

        // Add the create folder section to the main container
        modalContentContainer.appendChild(createFolderSection);

        // Create the quick save button section (at the bottom)
        const quickSaveSection = document.createElement('div');
        quickSaveSection.className = 'quick-save-section';

        // Add a divider
        const divider = document.createElement('hr');
        divider.className = 'my-3';
        quickSaveSection.appendChild(divider);

        // Create the "Save without folder" button
        const saveWithoutFolderBtn = document.createElement('button');
        saveWithoutFolderBtn.id = 'saveWithoutFolderBtn';
        saveWithoutFolderBtn.className = 'btn btn-primary w-100 save-without-folder-btn';
        saveWithoutFolderBtn.innerHTML = '<i class="bi bi-heart-fill me-2"></i> Save to Favorites';
        quickSaveSection.appendChild(saveWithoutFolderBtn);

        // Add the quick save section to the main container
        modalContentContainer.appendChild(quickSaveSection);

        // Add the main container to the folder options list
        folderOptionsList.appendChild(modalContentContainer);

        // Add event listeners for the new elements

        // Save without folder button
        const saveBtn = document.getElementById('saveWithoutFolderBtn');
        if (saveBtn) {
            // Remove any existing event listeners by cloning and replacing
            const newSaveBtn = saveBtn.cloneNode(true);
            saveBtn.parentNode.replaceChild(newSaveBtn, saveBtn);
            newSaveBtn.addEventListener('click', () => saveToFavorites());
        }

        // Create folder toggle button
        const newToggleBtn = document.getElementById('createFolderToggleBtn');
        const newCreateForm = document.getElementById('createFolderForm');
        const newFolderInput = document.getElementById('newFolderName');
        const newCreateBtn = document.getElementById('createAndSaveBtn');

        if (newToggleBtn && newCreateForm) {
            // Remove any existing event listeners by cloning and replacing
            const newToggle = newToggleBtn.cloneNode(true);
            newToggleBtn.parentNode.replaceChild(newToggle, newToggleBtn);

            newToggle.addEventListener('click', () => {
                newCreateForm.style.display = newCreateForm.style.display === 'none' ? 'block' : 'none';
                if (newCreateForm.style.display === 'block' && newFolderInput) {
                    newFolderInput.focus();
                }
            });
        }

        if (newCreateBtn && newFolderInput) {
            // Remove any existing event listeners by cloning and replacing
            const newCreate = newCreateBtn.cloneNode(true);
            newCreateBtn.parentNode.replaceChild(newCreate, newCreateBtn);

            newCreate.addEventListener('click', () => {
                const folderName = newFolderInput.value.trim();
                if (folderName) {
                    saveToFavorites(folderName);
                } else {
                    const errorMsg = document.getElementById('folderModalErrorMsg');
                    if (errorMsg) {
                        errorMsg.textContent = 'Please enter a folder name';
                        errorMsg.style.display = 'block';
                        errorMsg.className = 'alert alert-danger mt-2';
                    }
                }
            });

            // Handle Enter key
            newFolderInput.addEventListener('keyup', (event) => {
                if (event.key === 'Enter') {
                    newCreate.click();
                }
            });
        }

        // Add click event listeners to folder items
        const folderItems = folderOptionsList.querySelectorAll('[data-folder-id]');
        folderItems.forEach(item => {
            item.addEventListener('click', () => {
                const folderId = item.getAttribute('data-folder-id');
                if (folderId) {
                    saveToFavorites(null, folderId);
                }
            });
        });
    } else {
        console.error('Could not find or create folder options list element');
    }

    // Make sure the error message element exists
    if (!folderModalErrorMsg) {
        const modalBody = document.querySelector('#folderOptionsModal .modal-body');
        if (modalBody) {
            const errorMsg = document.createElement('div');
            errorMsg.id = 'folderModalErrorMsg';
            errorMsg.className = 'alert alert-danger mt-3';
            errorMsg.style.display = 'none';
            modalBody.appendChild(errorMsg);
            console.log('Created missing error message element');
        }
    }
}

/**
 * Save an item to favorites
 */
async function saveToFavorites(folderName = null, folderId = null) {
    console.log('saveToFavorites called with:', { folderName, folderId });

    if (!currentModalItemId || !currentModalItemType) {
        console.error("No item selected for saving. Current values:", { currentModalItemId, currentModalItemType });
        alert("Error: No item selected for saving. Please try again.");
        return;
    }

    console.log(`Saving item to favorites: ID=${currentModalItemId}, Type=${currentModalItemType}, Folder Name=${folderName}, Folder ID=${folderId}`);

    const csrfToken = getCsrfToken();
    if (!csrfToken) {
        const folderModalErrorMsg = document.getElementById('folderModalErrorMsg');
        if (folderModalErrorMsg) {
            folderModalErrorMsg.textContent = 'CSRF token not found. Please refresh the page.';
            folderModalErrorMsg.style.display = 'block';
        }
        return;
    }

    // Disable buttons during save
    let saveWithoutFolderBtn = document.getElementById('saveWithoutFolderBtn');
    if (!saveWithoutFolderBtn) {
        // Try to find by class as fallback
        saveWithoutFolderBtn = document.querySelector('.save-without-folder-btn');
    }
    const createAndSaveBtn = document.getElementById('createAndSaveBtn');
    const folderModalErrorMsg = document.getElementById('folderModalErrorMsg');
    const createFolderToggleBtn = document.getElementById('createFolderToggleBtn');

    // Disable all buttons during the save operation
    if (saveWithoutFolderBtn) saveWithoutFolderBtn.disabled = true;
    if (createAndSaveBtn) createAndSaveBtn.disabled = true;
    if (createFolderToggleBtn) createFolderToggleBtn.disabled = true;
    if (folderModalErrorMsg) folderModalErrorMsg.style.display = 'none';

    // Show loading indicator
    const saveButtonOriginalText = saveWithoutFolderBtn ? saveWithoutFolderBtn.innerHTML : '';
    const createButtonOriginalText = createAndSaveBtn ? createAndSaveBtn.innerHTML : '';

    if (saveWithoutFolderBtn) {
        saveWithoutFolderBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';
    }

    if (createAndSaveBtn && folderName) {
        createAndSaveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Creating...';
    }

    // Prepare request data
    let url = '/directory/favorites/save-no-folder/';
    let bodyParams = new URLSearchParams();
    bodyParams.append('item_id', currentModalItemId);
    bodyParams.append('item_type', currentModalItemType);

    if (folderName) {
        url = '/directory/favorites/create-folder-save/';
        bodyParams.append('folder_name', folderName);
    } else if (folderId) {
        url = '/directory/favorites/add-to-folder/';
        bodyParams.append('folder_id', folderId);
    }

    // Add CSRF token to the body parameters
    bodyParams.append('csrfmiddlewaretoken', csrfToken);

    console.log('Using URL path:', url);

    console.log(`Sending request to ${url} with params:`, Object.fromEntries(bodyParams));

    try {
        console.log(`Making POST request to ${url} with CSRF token: ${csrfToken ? 'Present' : 'Missing!'}`);

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: bodyParams,
            credentials: 'same-origin' // Include cookies in the request
        });

        console.log(`Response status: ${response.status}`);

        // Check if the response is JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            console.error('Response is not JSON:', contentType);
            throw new Error(`Server returned non-JSON response: ${contentType}`);
        }

        const data = await response.json();
        console.log('Response data:', data);

        if (!response.ok) {
            throw new Error(`HTTP error ${response.status}: ${data.message || 'Unknown error'}`);
        }

        if (data.status !== 'success') {
            throw new Error(data.message || 'Server returned unsuccessful status');
        }

        console.log('Save successful!', data);

        // Success! Update the heart icon on the page and close modal
        // Try multiple selectors to find the button
        let originalButton = null;

        // First try the most specific selector
        originalButton = document.querySelector(`.like-button[data-item-id="${currentModalItemId}"][data-item-type="${currentModalItemType}"]`);

        // If not found, try a more general selector
        if (!originalButton) {
            originalButton = document.querySelector(`button[data-item-id="${currentModalItemId}"][data-item-type="${currentModalItemType}"]`);
        }

        // If still not found, try looking in specific containers
        if (!originalButton) {
            // Try in the main list container
            const listContainer = document.getElementById('assistant-list-container') ||
                                 document.getElementById('company-list-container');
            if (listContainer) {
                originalButton = listContainer.querySelector(`[data-item-id="${currentModalItemId}"][data-item-type="${currentModalItemType}"]`);
            }

            // Try in the featured carousel
            if (!originalButton) {
                const featuredCarousel = document.getElementById('featuredAssistantsCarousel') ||
                                        document.querySelector('.featured-carousel-items');
                if (featuredCarousel) {
                    originalButton = featuredCarousel.querySelector(`[data-item-id="${currentModalItemId}"][data-item-type="${currentModalItemType}"]`);
                }
            }
        }

        if (originalButton) {
            console.log('Updating heart icon for button:', originalButton);
            updateHeartIcon(originalButton, true);
        } else {
            console.warn('Could not find original button to update heart icon - will refresh page to show updated state');
            // If we can't find the button, we'll reload the page to show the updated state
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }

        // Find and hide the folder modal
        const folderModalElement = document.getElementById('folderOptionsModal');
        if (folderModalElement) {
            try {
                // Try to get the Bootstrap modal instance
                let folderModal = bootstrap.Modal.getInstance(folderModalElement);

                // If no instance exists, create one
                if (!folderModal) {
                    folderModal = new bootstrap.Modal(folderModalElement);
                }

                console.log('Hiding folder modal');
                folderModal.hide();

                // Also try the jQuery way as a fallback
                if (typeof $ !== 'undefined') {
                    $(folderModalElement).modal('hide');
                }

                // Force hide with a timeout as a last resort
                setTimeout(() => {
                    folderModalElement.style.display = 'none';
                    folderModalElement.classList.remove('show');
                    document.body.classList.remove('modal-open');
                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }
                }, 300);

            } catch (modalError) {
                console.error('Error hiding modal:', modalError);
                // Force hide as a fallback
                folderModalElement.style.display = 'none';
                folderModalElement.classList.remove('show');
                document.body.classList.remove('modal-open');
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }
            }
        } else {
            console.warn('Could not find folder modal element to hide');
        }

        // Show success message
        const successMessage = document.createElement('div');
        successMessage.className = 'alert alert-success position-fixed top-0 start-50 translate-middle-x mt-3';
        successMessage.style.zIndex = '9999';
        successMessage.innerHTML = '<i class="bi bi-check-circle-fill me-2"></i> Added to favorites successfully!';
        document.body.appendChild(successMessage);

        // Remove the success message after 3 seconds
        setTimeout(() => {
            successMessage.remove();
        }, 3000);

    } catch (error) {
        console.error('Error saving to favorites:', error);
        const errorMessage = error.message || 'An error occurred while saving.';

        // Find the error message element
        const folderModalErrorMsg = document.getElementById('folderModalErrorMsg');

        // Create a detailed error message with debugging info
        const detailedError = `Error: ${errorMessage}\n\nDetails: Item ID: ${currentModalItemId}, Type: ${currentModalItemType}\nURL: ${url}`;
        console.error(detailedError);

        if (folderModalErrorMsg) {
            // Show error in the modal
            folderModalErrorMsg.textContent = errorMessage;
            folderModalErrorMsg.style.display = 'block';
            folderModalErrorMsg.classList.add('alert', 'alert-danger', 'mt-2');
            console.log('Error message displayed in modal');

            // Make sure the modal is visible
            const folderModalElement = document.getElementById('folderOptionsModal');
            if (folderModalElement) {
                try {
                    const folderModal = bootstrap.Modal.getInstance(folderModalElement) || new bootstrap.Modal(folderModalElement);
                    folderModal.show();
                } catch (modalError) {
                    console.error('Error showing modal for error message:', modalError);
                }
            }
        } else {
            console.error('folderModalErrorMsg element not found');

            // Show a styled error alert instead of a basic alert
            const errorAlert = document.createElement('div');
            errorAlert.className = 'alert alert-danger position-fixed top-0 start-50 translate-middle-x mt-3';
            errorAlert.style.zIndex = '9999';
            errorAlert.innerHTML = `<i class="bi bi-exclamation-triangle-fill me-2"></i> Error saving to favorites: ${errorMessage}`;
            document.body.appendChild(errorAlert);

            // Remove the error alert after 5 seconds
            setTimeout(() => {
                errorAlert.remove();
            }, 5000);
        }
    } finally {
        // Re-enable buttons and restore original text
        if (saveWithoutFolderBtn) {
            saveWithoutFolderBtn.disabled = false;
            saveWithoutFolderBtn.innerHTML = saveButtonOriginalText;
        }
        if (createAndSaveBtn) {
            createAndSaveBtn.disabled = false;
            createAndSaveBtn.innerHTML = createButtonOriginalText;
        }
        if (createFolderToggleBtn) {
            createFolderToggleBtn.disabled = false;
        }
    }
}

/**
 * Create heart particle effects around a button
 * @param {HTMLElement} button - The button to create particles around
 */
function createHeartParticles(button) {
    if (!button) return;

    // Get button position
    const rect = button.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    // Create a container for all particles to improve performance
    const particleContainer = document.createElement('div');
    particleContainer.style.position = 'fixed';
    particleContainer.style.left = '0';
    particleContainer.style.top = '0';
    particleContainer.style.width = '100%';
    particleContainer.style.height = '100%';
    particleContainer.style.pointerEvents = 'none';
    particleContainer.style.zIndex = '9999';
    particleContainer.style.overflow = 'hidden';
    document.body.appendChild(particleContainer);

    // Create 7 heart particles (increased for better effect)
    const colors = ['#ff3366', '#ff6b98', '#ff4d79'];

    for (let i = 0; i < 7; i++) {
        const particle = document.createElement('div');
        particle.innerHTML = '<i class="bi bi-heart-fill"></i>';
        particle.style.position = 'absolute';
        particle.style.left = `${centerX}px`;
        particle.style.top = `${centerY}px`;
        particle.style.color = colors[Math.floor(Math.random() * colors.length)];
        particle.style.fontSize = `${10 + Math.floor(Math.random() * 6)}px`;
        particle.style.pointerEvents = 'none';
        particle.style.opacity = '1';
        particle.style.transform = 'translate(-50%, -50%)';
        particle.style.transition = 'all 0.8s cubic-bezier(0.165, 0.84, 0.44, 1)';

        particleContainer.appendChild(particle);

        // Random direction with improved animation curve
        const angle = Math.random() * Math.PI * 2;
        const distance = 25 + Math.random() * 40;
        const destX = centerX + Math.cos(angle) * distance;
        const destY = centerY + Math.sin(angle) * distance;

        // Random rotation for more natural movement
        const rotation = -30 + Math.random() * 60;

        // Animate with slight delay for each particle
        setTimeout(() => {
            particle.style.transform = `translate(${destX - centerX}px, ${destY - centerY}px) rotate(${rotation}deg) scale(${Math.random() * 0.5 + 0.5})`;
            particle.style.opacity = '0';
        }, i * 40); // Staggered timing
    }

    // Remove particle container after animation
    setTimeout(() => {
        particleContainer.remove();
    }, 1000);
}

/**
 * Remove an item from the My Favorites page
 * @param {string} itemId - The ID of the item to remove
 * @param {string} itemType - The type of the item to remove (assistant or company)
 */
function removeItemFromFavoritesPage(itemId, itemType) {
    console.log(`[DEBUG] removeItemFromFavoritesPage START - Item ID: ${itemId}, Type: ${itemType}`);

    // Find all instances of this item on the page (could be in multiple tabs/folders)
    const itemSelector = itemType === 'assistant'
        ? `.favorite-item[data-assistant-id="${itemId}"]`
        : `.favorite-item[data-company-id="${itemId}"]`;
    console.log(`[DEBUG] removeItemFromFavoritesPage - Selector: ${itemSelector}`);
    const itemElements = document.querySelectorAll(itemSelector);

    if (itemElements.length === 0) {
        console.warn(`[DEBUG] removeItemFromFavoritesPage - No item elements found with selector: ${itemSelector}`);
        console.log(`[DEBUG] removeItemFromFavoritesPage END - No items removed.`);
        return;
    }

    console.log(`[DEBUG] removeItemFromFavoritesPage - Found ${itemElements.length} instance(s) of ${itemType} ${itemId} to remove.`);

    // Track which list groups and panes contain these items for empty state handling
    const affectedGroups = new Set();
    const affectedPanes = new Set();

    // Track containers before removing
    itemElements.forEach(item => {
        const listGroup = item.closest('.list-group');
        const pane = item.closest('.tab-pane');
        if (listGroup) affectedGroups.add(listGroup);
        if (pane) affectedPanes.add(pane);
        console.log(`[DEBUG] removeItemFromFavoritesPage - Identified item for removal:`, item);
    });

    // Immediately remove the elements from DOM
    itemElements.forEach((item, index) => {
        console.log(`[DEBUG] removeItemFromFavoritesPage - BEFORE removing element ${index + 1}:`, item);
        item.remove();
        console.log(`[DEBUG] removeItemFromFavoritesPage - AFTER removing element ${index + 1}.`);
    });

    console.log(`[DEBUG] removeItemFromFavoritesPage - All identified elements removed. Proceeding to check empty states.`);

    // Check if any list groups are now empty and add empty message
    affectedGroups.forEach(group => {
        if (group.querySelectorAll('.favorite-item').length === 0) {
            const folderContainer = group.closest('[data-item-group="true"]');
            if (folderContainer) {
                const folderHeader = folderContainer.querySelector('h4');
                const folderName = folderHeader ? folderHeader.textContent.trim() : 'this folder';

                // Add empty folder message if not already present
                if (!group.querySelector('.empty-folder-message')) {
                    const emptyMsg = document.createElement('p');
                    emptyMsg.className = 'text-muted small ms-3 empty-folder-message';
                    emptyMsg.innerHTML = `<em>No items in ${folderName}.</em>`;
                    emptyMsg.style.animation = 'fadeIn 0.5s ease forwards';
                    group.appendChild(emptyMsg);
                }
            }
        }
    });

    // Check if any panes are now completely empty
    affectedPanes.forEach(pane => {
        if (pane.querySelectorAll('.favorite-item').length === 0) {
            // Determine message type based on pane ID
            let messageType = 'items';
            if (pane.id === 'assistants-panel') messageType = 'assistants';
            else if (pane.id === 'community-assistants-panel') messageType = 'community assistants';
            else if (pane.id === 'companies-panel') messageType = 'companies';

            // Only add message if one doesn't already exist
            if (!pane.querySelector('.no-favorites-message')) {
                // Clear any empty folder containers first
                pane.querySelectorAll('[data-item-group="true"]').forEach(el => {
                    if (!el.querySelector('.favorite-item')) {
                        el.remove();
                    }
                });

                // Add the empty state message
                const noFavMsg = document.createElement('p');
                noFavMsg.className = 'text-muted no-favorites-message';
                noFavMsg.textContent = `You haven't saved any favorite ${messageType} yet.`;
                noFavMsg.style.animation = 'fadeIn 0.5s ease forwards';
                pane.appendChild(noFavMsg);
            }
        }
    });
    console.log(`[DEBUG] removeItemFromFavoritesPage END - Finished processing.`);
}

/**
 * Get CSRF token from the page
 * @returns {string|null} The CSRF token or null if not found
 */
function getCsrfToken() {
    // Try to get from input
    const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
    if (csrfInput) {
        console.log('Found CSRF token in input element');
        return csrfInput.value;
    }

    // Try to get from meta tag
    const csrfMeta = document.querySelector('meta[name="csrf-token"]');
    if (csrfMeta) {
        console.log('Found CSRF token in meta tag');
        return csrfMeta.getAttribute('content');
    }

    // Try to get from cookie as a last resort
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.startsWith('csrftoken=')) {
            console.log('Found CSRF token in cookie');
            return cookie.substring('csrftoken='.length, cookie.length);
        }
    }

    console.error('CSRF token not found in input, meta tag, or cookies');
    return null;
}
/**
 * Initialize folder filter functionality on the My Favorites page
 */
function initFolderFilters() {
    // Select both buttons and anchor tags with data-folder-id
    const folderFilterElements = document.querySelectorAll('.folder-filter-buttons .btn[data-folder-id], .folder-filter-buttons a[data-folder-id]');
    if (!folderFilterElements.length) {
        console.log('Favorites Functionality: No folder filter elements found on this page.');
        return;
    }
    console.log(`Favorites Functionality: Initializing ${folderFilterElements.length} folder filter elements.`);

    folderFilterElements.forEach(element => {
        element.addEventListener('click', async function(event) {
            event.preventDefault();
            const folderId = this.getAttribute('data-folder-id');
            const activeTabPane = this.closest('.tab-pane');
            if (!activeTabPane) {
                console.error('Favorites Functionality: Could not determine active tab pane for folder filter.');
                return;
            }
            const itemType = getItemTypeFromTabPane(activeTabPane);
            if (!itemType) {
                console.error('Favorites Functionality: Could not determine item type from tab pane.');
                return;
            }

            console.log(`Favorites Functionality: Folder element clicked. Folder ID: ${folderId}, Item Type: ${itemType}`);
            await loadFilteredFavorites(folderId, itemType, activeTabPane, this);
        });
    });
}

/**
 * Determines the item type based on the active tab pane.
 * @param {HTMLElement} tabPane - The active tab pane element.
 * @returns {string|null} 'assistant', 'community_assistant', 'company', or null.
 */
function getItemTypeFromTabPane(tabPane) {
    if (tabPane.id === 'assistants-panel') {
        return 'assistant';
    } else if (tabPane.id === 'community-assistants-panel') {
        return 'community_assistant';
    } else if (tabPane.id === 'companies-panel') {
        return 'company';
    }
    return null;
}

/**
 * Loads filtered favorite items via AJAX.
 * @param {string} folderId - The ID of the folder to filter by (or 'all' or 'uncategorized').
 * @param {string} itemType - The type of items ('assistant', 'community_assistant', 'company').
 * @param {HTMLElement} tabPane - The active tab pane element.
 * @param {HTMLElement} clickedButton - The folder button that was clicked.
 */
async function loadFilteredFavorites(folderId, itemType, tabPane, clickedButton) {
    console.log(`[DEBUG] loadFilteredFavorites START - Folder ID: ${folderId}, Item Type: ${itemType}, Tab ID: ${tabPane.id}`);
    console.log(`[DEBUG] Button clicked:`, clickedButton);
    console.log(`[DEBUG] Button data attributes:`, clickedButton.dataset);

    // Construct the correct pluralized ID for the items container
    let pluralizedItemTypeKey;
    if (itemType === 'company') {
        pluralizedItemTypeKey = 'companies';
    } else {
        // For 'assistant' -> 'assistants'
        // For 'community_assistant' -> 'community-assistants'
        pluralizedItemTypeKey = itemType.replace('_', '-') + 's';
    }
    const itemsContainerId = `favorite-${pluralizedItemTypeKey}-items-container`;
    console.log(`[DEBUG] Looking for container with ID: ${itemsContainerId}`);
    const itemsContainer = tabPane.querySelector(`#${itemsContainerId}`);

    if (!itemsContainer) {
        console.error(`[DEBUG] loadFilteredFavorites - Items container #${itemsContainerId} not found in tab:`, tabPane.id);
        console.log(`[DEBUG] Tab pane HTML:`, tabPane.innerHTML);
        console.log(`[DEBUG] All containers in tab:`, Array.from(tabPane.querySelectorAll('[id]')).map(el => el.id));
        console.log(`[DEBUG] loadFilteredFavorites END - Container not found.`);
        return;
    }

    // Add a loading indicator
    console.log(`[DEBUG] loadFilteredFavorites - BEFORE setting loading HTML for ${itemsContainerId}`);
    itemsContainer.innerHTML = '<div class="text-center p-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div> <p class="mt-2">Loading favorites...</p></div>';
    console.log(`[DEBUG] loadFilteredFavorites - AFTER setting loading HTML. Loading favorites for folder ${folderId}, type ${itemType} into ${itemsContainerId}`);

    try {
        const csrfToken = getCsrfToken();
        if (!csrfToken) {
            console.error('Favorites Functionality: CSRF token not found for folder filter.');
            itemsContainer.innerHTML = '<p class="text-danger text-center">Error: Security token missing. Please refresh the page.</p>';
            return;
        }

        const url = `/directory/favorites/filter-by-folder/?folder_id=${folderId}&item_type=${itemType}`;
        console.log(`[DEBUG] Making AJAX request to: ${url}`);

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': csrfToken
            }
        });

        console.log(`[DEBUG] Response status:`, response.status);
        console.log(`[DEBUG] Response headers:`, Object.fromEntries([...response.headers]));

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[DEBUG] Error response body:`, errorText);
            throw new Error(`HTTP error ${response.status}: ${errorText}`);
        }

        const htmlResponse = await response.text();
        console.log(`[DEBUG] Response length:`, htmlResponse.length);
        console.log(`[DEBUG] Response preview:`, htmlResponse.substring(0, 200) + '...');
        console.log(`[DEBUG] loadFilteredFavorites - BEFORE setting response HTML for ${itemsContainerId}`);
        itemsContainer.innerHTML = htmlResponse;
        console.log(`[DEBUG] loadFilteredFavorites - AFTER setting response HTML. Successfully loaded and replaced content for ${itemsContainerId}`);

        // Re-initialize favorite buttons and rating modals if they are part of the loaded HTML
        console.log(`[DEBUG] loadFilteredFavorites - Calling initFavoriteButtons() after loading content.`);
        initFavoriteButtons(); // Make sure new like buttons work
        // If you have rating modals or other dynamic elements, re-initialize them here too.
        // e.g., if rating modals are loaded with items:
        // if (typeof initRatingModal === 'function') { initRatingModal(); }


        // Update active state for folder buttons and links within the same tab
        const allFolderElementsInTab = tabPane.querySelectorAll('.folder-filter-buttons .btn[data-folder-id], .folder-filter-buttons a[data-folder-id]');
        allFolderElementsInTab.forEach(element => {
            element.classList.remove('active', 'fw-bold', 'text-primary');
            element.classList.add('text-decoration-none'); // Ensure non-active are just links
        });
        if (clickedButton) {
            clickedButton.classList.add('active', 'fw-bold', 'text-primary');
            clickedButton.classList.remove('text-decoration-none');
            console.log('Favorites Functionality: Active state updated for element:', clickedButton);
        }

    } catch (error) {
        console.error('[DEBUG] loadFilteredFavorites - Error loading filtered favorites:', error);
        itemsContainer.innerHTML = `<p class="text-danger text-center">Error loading items: ${error.message}. Please try again.</p>`;
    }
    console.log(`[DEBUG] loadFilteredFavorites END - Finished processing for Folder ID: ${folderId}, Item Type: ${itemType}`);
}

// Modify the DOMContentLoaded to also initialize folder filters
document.addEventListener('DOMContentLoaded', function() {
    // The existing initFavoriteButtons() and initFolderModal() calls are already in the
    // original DOMContentLoaded listener at the top of the file.
    // We only need to add the new initFolderFilters() call.
    // The console logs are also already present.
    initFolderFilters();
});
