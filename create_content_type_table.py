import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

# SQL to create the django_content_type table
sql = """
CREATE TABLE IF NOT EXISTS "django_content_type" (
    "id" serial NOT NULL PRIMARY KEY,
    "app_label" varchar(100) NOT NULL,
    "model" varchar(100) NOT NULL,
    CONSTRAINT "django_content_type_app_label_model_76bd3d3b_uniq" UNIQUE ("app_label", "model")
);
"""

# Execute the SQL
with connection.cursor() as cursor:
    cursor.execute(sql)

print("Content type table created successfully!")
