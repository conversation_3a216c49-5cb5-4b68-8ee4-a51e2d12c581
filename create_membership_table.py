import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

# SQL to create the accounts_membership table
sql = """
CREATE TABLE IF NOT EXISTS "accounts_membership" (
    "id" serial NOT NULL PRIMARY KEY,
    "date_joined" timestamp with time zone NOT NULL DEFAULT now(),
    "company_id" integer NOT NULL,
    "user_id" integer NOT NULL,
    CONSTRAINT "accounts_membership_company_id_user_id_unique" UNIQUE ("company_id", "user_id"),
    CONSTRAINT "accounts_membership_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT "accounts_membership_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# SQL to create the accounts_membership_accessible_folders table (if needed)
accessible_folders_sql = """
CREATE TABLE IF NOT EXISTS "accounts_membership_accessible_folders" (
    "id" serial NOT NULL PRIMARY KEY,
    "membership_id" integer NOT NULL,
    "assistantfolder_id" integer NOT NULL,
    CONSTRAINT "accounts_membership_accessible_folders_membership_id_assistantfolder_id_unique" UNIQUE ("membership_id", "assistantfolder_id"),
    CONSTRAINT "accounts_membership_accessible_folders_membership_id_fkey" FOREIGN KEY ("membership_id") REFERENCES "accounts_membership" ("id") DEFERRABLE INITIALLY DEFERRED,
    CONSTRAINT "accounts_membership_accessible_folders_assistantfolder_id_fkey" FOREIGN KEY ("assistantfolder_id") REFERENCES "assistants_assistantfolder" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# Execute the SQL
try:
    with connection.cursor() as cursor:
        print("Creating accounts_membership table...")
        cursor.execute(sql)

        print("Creating accounts_membership_accessible_folders table...")
        cursor.execute(accessible_folders_sql)

    print("Membership tables created successfully!")
except Exception as e:
    print(f"Error creating tables: {e}")
