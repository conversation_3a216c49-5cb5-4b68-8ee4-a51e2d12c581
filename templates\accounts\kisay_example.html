{% extends 'base/layout.html' %}
{% load static account_tags rating_tags %}

{% block title %}Kisay - Company Overview{% endblock %}

{% block head_extra %}
<style>
    .company-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem;
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        margin-bottom: 1.5rem;
    }

    .company-logo {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 1rem;
    }

    .company-logo img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .company-info h1 {
        margin-bottom: 0;
        font-size: 1.5rem;
    }

    .company-actions {
        display: flex;
        align-items: center;
    }

    .rating-display {
        margin-right: 1rem;
    }

    .like-button {
        background: none;
        border: none;
        cursor: pointer;
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    .like-button.favorited {
        color: #dc3545;
    }

    .like-button:not(.favorited) {
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <!-- Company Header with Rating and Like Buttons -->
            <div class="company-header">
                <div class="d-flex align-items-center">
                    <!-- Company Logo -->
                    <div class="company-logo">
                        <img src="{% static 'img/kisay-logo.png' %}" alt="Kisay Logo" onerror="this.src='{% static 'img/default-company.png' %}'">
                    </div>

                    <!-- Company Info -->
                    <div class="company-info">
                        <h1>Kisay</h1>
                        <p class="text-muted small mb-0">Beverage Company</p>
                    </div>
                </div>

                <!-- Company Actions -->
                <div class="company-actions">
                    <!-- Rating Display -->
                    <div class="rating-display me-3">
                        <div id="rating-display-1">
                            {% render_stars 4.5 12 %}
                        </div>
                        {% if user.is_authenticated %}
                            <button type="button" class="btn btn-sm btn-link text-decoration-none p-0 mt-1"
                                    data-bs-toggle="modal" data-bs-target="#ratingModal"
                                    data-company-id="1"
                                    data-company-name="Kisay">
                                <i class="bi bi-star me-1"></i>Rate
                            </button>
                        {% endif %}
                    </div>

                    <!-- Like Button -->
                    {% if user.is_authenticated %}
                        <button
                            class="like-button btn btn-sm p-1 text-secondary"
                            data-item-id="1"
                            data-item-type="company"
                            title="Add to Favorites"
                            style="background: none; border: none;">
                            <i class="bi bi-heart me-1"></i>
                            Favorite
                        </button>
                    {% endif %}

                    <!-- Share Button -->
                    <button class="btn btn-sm btn-outline-secondary ms-2" id="shareButton">
                        <i class="bi bi-share me-1"></i> Share
                    </button>

                    <!-- QR Code Button -->
                    <button class="btn btn-sm btn-outline-secondary ms-2" data-bs-toggle="modal" data-bs-target="#qrCodeModal">
                        <i class="bi bi-qr-code me-1"></i> QR
                    </button>
                </div>
            </div>

            <!-- Company Description -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="card-title">About Kisay</h5>
                    <p class="card-text">Kisay is a leading beverage company specializing in natural fruit juices, offering a wide range of healthy and refreshing drinks made from locally sourced ingredients.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Rating Modal -->
<div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="ratingModalLabel">Rate <span id="modalCompanyName">Kisay</span></h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="ratingForm">
          {% csrf_token %}
          <p class="mb-3">How would you rate your experience with <strong id="modalCompanyNameInner">Kisay</strong>?</p>

          <div class="modal-stars text-center mb-4">
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="1">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="2">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="3">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="4">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="5">
              <i class="bi bi-star text-secondary"></i>
            </button>
          </div>

          <div id="modalErrorMsg" class="alert alert-danger" style="display: none;"></div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="submitRatingBtn" disabled>Submit Rating</button>
      </div>
    </div>
  </div>
</div>

<!-- Folder Options Modal -->
<div class="modal fade" id="folderOptionsModal" tabindex="-1" aria-labelledby="folderOptionsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="folderOptionsModalLabel">Add to Favorites</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Add <strong id="modalFolderNameItemName">Kisay</strong> to favorites:</p>

        <!-- Option 1: Save without folder -->
        <div class="d-grid mb-3">
            <button type="button" class="btn btn-outline-primary text-start" id="saveNoFolderBtn">
                <i class="bi bi-bookmark-heart me-2"></i>Save (Uncategorized)
            </button>
        </div>

        <hr>

        <!-- Option 2: Add to existing folder -->
        <div id="existingFoldersSection" class="mb-3" style="display: none;">
            <label for="selectFolder" class="form-label small mb-1">Add to existing folder:</label>
            <div class="input-group">
                <span class="input-group-text"><i class="bi bi-folder-symlink"></i></span>
                <select class="form-select" id="selectFolder">
                    <option selected disabled value="">Choose folder...</option>
                    <!-- Options will be populated by JS -->
                </select>
                <button class="btn btn-primary" type="button" id="addToFolderBtn" disabled>
                    <i class="bi bi-plus-lg"></i> Add
                </button>
            </div>
        </div>

        <!-- Option 3: Create new folder -->
        <div>
            <label for="newFolderName" class="form-label small mb-1">Or create a new folder:</label>
            <div class="input-group">
                 <span class="input-group-text"><i class="bi bi-folder-plus"></i></span>
                <input type="text" class="form-control" id="newFolderName" placeholder="New folder name...">
                <button class="btn btn-success" type="button" id="createFolderAndSaveBtn" disabled>
                   <i class="bi bi-check-lg"></i> Create & Save
                </button>
            </div>
        </div>

        <div id="folderModalErrorMsg" class="text-danger small mt-3" style="display: none;"></div>
      </div>
    </div>
  </div>
</div>

<!-- QR Code Modal -->
<div class="modal fade" id="qrCodeModal" tabindex="-1" aria-labelledby="qrCodeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-sm modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="qrCodeModalLabel">Scan QR Code</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body text-center">
        <p class="text-muted">QR code not available</p>
        <p class="mt-2 small text-muted">Scan to visit company page</p>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Helper function to get CSRF token
function getCsrfToken() {
    const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
    if (!csrfInput) {
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfMeta) return csrfMeta.getAttribute('content');
    }
    return csrfInput ? csrfInput.value : null;
}

// --- Favorite Button & Folder Modal Logic ---
function handleFavoriteButtons() {
    const likeButton = document.querySelector('.like-button');
    const folderModalElement = document.getElementById('folderOptionsModal');
    let folderModal = null;

    if (folderModalElement) {
        folderModal = new bootstrap.Modal(folderModalElement);
    } else {
        console.error("Folder options modal element not found.");
        return;
    }

    if (!likeButton) {
        console.log("Like button not found on this page.");
        return;
    }

    // Store current item details for modal actions
    let currentModalItemId = likeButton.dataset.itemId;
    let currentModalItemType = likeButton.dataset.itemType;

    // --- Main Click Handler (Like Button) ---
    likeButton.addEventListener('click', async (event) => {
        event.preventDefault();
        event.stopPropagation();

        const csrfToken = getCsrfToken();
        if (!csrfToken) {
            console.error("CSRF token not found!");
            alert("Action failed. Please refresh.");
            return;
        }

        const itemId = currentModalItemId;
        const itemType = currentModalItemType;
        const url = "/directory/toggle_saved_item/"; // Example URL

        try {
            // For demo purposes, just toggle the button state
            if (likeButton.classList.contains('text-danger')) {
                updateHeartIcon(likeButton, false);
            } else {
                // Show folder options modal
                populateAndShowFolderModal({
                    item_name: "Kisay",
                    folders: [
                        { id: 1, name: "Favorite Companies" },
                        { id: 2, name: "Beverages" }
                    ]
                });
            }
        } catch (error) {
            console.error('Error handling favorite click:', error);
            alert(`An error occurred: ${error.message}`);
        }
    });

    // --- Helper: Update Heart Icon ---
    function updateHeartIcon(button, isSaved) {
        if (!button) return;
        if (isSaved) {
            button.classList.remove('text-secondary');
            button.classList.add('text-danger');
            button.title = 'Unlike';
            button.querySelector('i').classList.remove('bi-heart');
            button.querySelector('i').classList.add('bi-heart-fill');
            button.querySelector('i').nextSibling.textContent = ' Favorited';
        } else {
            button.classList.remove('text-danger');
            button.classList.add('text-secondary');
            button.title = 'Like';
            button.querySelector('i').classList.remove('bi-heart-fill');
            button.querySelector('i').classList.add('bi-heart');
            button.querySelector('i').nextSibling.textContent = ' Favorite';
        }
    }

    // --- Helper: Populate and Show Folder Modal ---
    function populateAndShowFolderModal(data) {
        if (!folderModalElement || !folderModal) return;

        // Set item name
        const itemNameElement = folderModalElement.querySelector('#modalFolderNameItemName');
        if (itemNameElement) itemNameElement.textContent = data.item_name || 'this item';

        // Populate existing folders dropdown
        const selectFolder = folderModalElement.querySelector('#selectFolder');
        const existingFoldersSection = folderModalElement.querySelector('#existingFoldersSection');
        const addToFolderBtn = folderModalElement.querySelector('#addToFolderBtn');

        selectFolder.innerHTML = '<option selected disabled value="">Choose folder...</option>'; // Reset options
        if (data.folders && data.folders.length > 0) {
            data.folders.forEach(folder => {
                const option = document.createElement('option');
                option.value = folder.id;
                option.textContent = folder.name;
                selectFolder.appendChild(option);
            });
            existingFoldersSection.style.display = 'block';
            addToFolderBtn.disabled = true; // Disable until a folder is selected
        } else {
            existingFoldersSection.style.display = 'none';
        }

        // Reset other fields
        folderModalElement.querySelector('#newFolderName').value = '';
        folderModalElement.querySelector('#createFolderAndSaveBtn').disabled = true;
        folderModalElement.querySelector('#folderModalErrorMsg').style.display = 'none';
        folderModalElement.querySelector('#folderModalErrorMsg').textContent = '';

        // Show the modal
        folderModal.show();
    }

    // --- Folder Modal Event Listeners ---
    if (folderModalElement) {
        const selectFolder = folderModalElement.querySelector('#selectFolder');
        const addToFolderBtn = folderModalElement.querySelector('#addToFolderBtn');
        const newFolderNameInput = folderModalElement.querySelector('#newFolderName');
        const createFolderBtn = folderModalElement.querySelector('#createFolderAndSaveBtn');
        const saveNoFolderBtn = folderModalElement.querySelector('#saveNoFolderBtn');
        const errorMsgElement = folderModalElement.querySelector('#folderModalErrorMsg');

        // Enable/disable "Add" button based on selection
        selectFolder.addEventListener('change', () => {
            addToFolderBtn.disabled = !selectFolder.value;
        });

        // Enable/disable "Create & Save" button based on input
        newFolderNameInput.addEventListener('input', () => {
            createFolderBtn.disabled = !newFolderNameInput.value.trim();
        });

        // Action: Save without folder
        saveNoFolderBtn.addEventListener('click', async () => {
            updateHeartIcon(likeButton, true);
            folderModal.hide();
        });

        // Action: Add to existing folder
        addToFolderBtn.addEventListener('click', async () => {
            updateHeartIcon(likeButton, true);
            folderModal.hide();
        });

        // Action: Create folder and save
        createFolderBtn.addEventListener('click', async () => {
            updateHeartIcon(likeButton, true);
            folderModal.hide();
        });
    }
}

// --- Company Rating Modal Logic ---
function handleCompanyRatingModal() {
    const ratingModalElement = document.getElementById('ratingModal');
    if (!ratingModalElement) {
        console.log("Rating modal not found on this page.");
        return;
    }

    // Initialize Bootstrap modal if needed
    let ratingModal = bootstrap.Modal.getInstance(ratingModalElement);
    if (!ratingModal) {
        try {
            ratingModal = new bootstrap.Modal(ratingModalElement);
        } catch (e) {
            console.error("Failed to initialize Bootstrap Modal for rating:", e);
        }
    }

    const modalStarsContainer = ratingModalElement.querySelector('.modal-stars');
    const submitRatingBtn = ratingModalElement.querySelector('#submitRatingBtn');
    const modalErrorMsg = ratingModalElement.querySelector('#modalErrorMsg');
    let ratingCompanyId = null;
    let selectedRating = 0;

    // Set up modal when it's shown
    ratingModalElement.addEventListener('show.bs.modal', function (event) {
        try {
            const button = event.relatedTarget;
            if (!button) {
                console.warn('Modal opened without a relatedTarget button');
                return;
            }

            ratingCompanyId = button.getAttribute('data-company-id');
            const companyName = button.getAttribute('data-company-name');
            const modalTitle = ratingModalElement.querySelector('#ratingModalLabel');
            const modalCompanyName = ratingModalElement.querySelector('#modalCompanyName');
            const modalCompanyNameInner = ratingModalElement.querySelector('#modalCompanyNameInner');

            console.log('Rating modal opened for company:', companyName, 'ID:', ratingCompanyId);

            if (modalTitle) modalTitle.innerHTML = `Rate <span id="modalCompanyName">${companyName}</span>`;
            if (modalCompanyName) modalCompanyName.textContent = companyName;
            if (modalCompanyNameInner) modalCompanyNameInner.textContent = companyName;

            selectedRating = 0;
            if (submitRatingBtn) submitRatingBtn.disabled = true;
            if (modalErrorMsg) modalErrorMsg.style.display = 'none';
            if (modalStarsContainer) {
                const starButtons = modalStarsContainer.querySelectorAll('.modal-star-btn');
                if (starButtons && starButtons.length > 0) {
                    starButtons.forEach(btn => {
                        btn.classList.remove('active');
                        const starIcon = btn.querySelector('i');
                        if (starIcon) {
                            starIcon.classList.remove('bi-star-fill', 'text-warning');
                            starIcon.classList.add('bi-star', 'text-secondary');
                        }
                    });
                }
            }
        } catch (error) {
            console.error('Error in modal show event:', error);
        }
    });

    // Handle star clicks
    if (modalStarsContainer) {
        modalStarsContainer.addEventListener('click', function (event) {
            try {
                const starButton = event.target.closest('.modal-star-btn');
                if (!starButton) return;

                const ratingValue = starButton.getAttribute('data-rating-value');
                if (!ratingValue) {
                    console.warn('Star button clicked but no rating value found');
                    return;
                }

                selectedRating = parseInt(ratingValue);
                console.log('Selected rating:', selectedRating);

                if (submitRatingBtn) submitRatingBtn.disabled = false;
                if (modalErrorMsg) modalErrorMsg.style.display = 'none';

                // Update star appearance
                const allStarButtons = modalStarsContainer.querySelectorAll('.modal-star-btn');
                if (allStarButtons && allStarButtons.length > 0) {
                    allStarButtons.forEach(btn => {
                        const starIcon = btn.querySelector('i');
                        if (!starIcon) return;

                        const btnValueAttr = btn.getAttribute('data-rating-value');
                        if (!btnValueAttr) return;

                        const btnValue = parseInt(btnValueAttr);

                        if (btnValue <= selectedRating) {
                            starIcon.classList.remove('bi-star', 'text-secondary');
                            starIcon.classList.add('bi-star-fill', 'text-warning');
                            btn.classList.add('active');
                        } else {
                            starIcon.classList.remove('bi-star-fill', 'text-warning');
                            starIcon.classList.add('bi-star', 'text-secondary');
                            btn.classList.remove('active');
                        }
                    });
                }
            } catch (error) {
                console.error('Error handling star click:', error);
            }
        });
    }

    // Handle submit button click
    if (submitRatingBtn) {
        submitRatingBtn.addEventListener('click', function() {
            try {
                if (!selectedRating || !ratingCompanyId) {
                    if (modalErrorMsg) {
                        modalErrorMsg.textContent = 'Please select a rating.';
                        modalErrorMsg.style.display = 'block';
                    }
                    return;
                }

                const csrfToken = getCsrfToken();
                if (!csrfToken) {
                    console.error('CSRF token not found!');
                    if (modalErrorMsg) {
                        modalErrorMsg.textContent = 'CSRF token not found. Please refresh the page.';
                        modalErrorMsg.style.display = 'block';
                    }
                    return;
                }

                // For demo purposes, just update the rating display and close the modal
                const ratingDisplay = document.getElementById(`rating-display-${ratingCompanyId}`);
                if (ratingDisplay) {
                    // Simulate updated stars HTML
                    let starsHtml = '';
                    for (let i = 1; i <= 5; i++) {
                        if (i <= selectedRating) {
                            starsHtml += '<i class="bi bi-star-fill text-warning"></i> ';
                        } else if (i - 0.5 <= selectedRating) {
                            starsHtml += '<i class="bi bi-star-half text-warning"></i> ';
                        } else {
                            starsHtml += '<i class="bi bi-star text-secondary"></i> ';
                        }
                    }
                    starsHtml += `<small class="text-muted ms-1">(${selectedRating}/5)</small>`;
                    ratingDisplay.innerHTML = starsHtml;
                }

                // Close the modal
                if (ratingModal) {
                    ratingModal.hide();
                }

                // Show success message using toast notification
                if (typeof showToast === 'function') {
                    showToast('Rating submitted successfully!', 'success');
                } else {
                    console.log('Rating submitted successfully!');
                }
            } catch (error) {
                console.error('Error in submit button click handler:', error);
                if (modalErrorMsg) {
                    modalErrorMsg.textContent = error.message || 'An unexpected error occurred.';
                    modalErrorMsg.style.display = 'block';
                }
            }
        });
    }
}

// JavaScript for handling the share button
function handleShareButton() {
    const shareButton = document.getElementById('shareButton');
    if (shareButton) {
        shareButton.addEventListener('click', function() {
            if (navigator.share) {
                navigator.share({
                    title: 'Kisay',
                    text: 'Check out this company!',
                    url: window.location.href
                })
                .then(() => console.log('Share successful'))
                .catch((error) => console.log('Error sharing:', error));
            } else {
                // Fallback for browsers that don't support the Web Share API
                const tempInput = document.createElement('input');
                tempInput.value = window.location.href;
                document.body.appendChild(tempInput);
                tempInput.select();
                document.execCommand('copy');
                document.body.removeChild(tempInput);
                alert('URL copied to clipboard!');
            }
        });
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Favorite Button Logic
    handleFavoriteButtons();

    // Initialize Rating Modal Logic
    handleCompanyRatingModal();

    // Initialize Share Button Logic
    handleShareButton();
});
</script>
{% endblock %}
