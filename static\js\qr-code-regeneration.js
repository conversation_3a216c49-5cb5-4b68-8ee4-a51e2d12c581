/**
 * QR Code Regeneration JavaScript
 *
 * This script handles the regeneration of QR codes for companies, assistants, and community assistants.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Company QR Code Regeneration
    const companyRegenerateBtn = document.getElementById('regenerate-qr-code-btn');
    if (companyRegenerateBtn) {
        companyRegenerateBtn.addEventListener('click', function() {
            const companyId = this.getAttribute('data-company-id');
            if (!companyId) {
                console.error('Company ID not found on regenerate button');
                return;
            }

            // Disable button and show loading state
            this.disabled = true;
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="bi bi-arrow-repeat"></i> Generating...';

            // Make AJAX request to regenerate QR code
            fetch(`/accounts/company/${companyId}/generate-qr-code/`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.status === 'success') {
                        // Update QR code image
                        const qrCodeContainer = document.getElementById('qr-code-container');
                        const qrCodeImg = document.getElementById('company-qr-code-img') || document.getElementById('community-qr-code-img');
                        const downloadBtn = document.getElementById('qr-code-download-btn');
                        const isGenerating = originalText.includes('Generate QR Code');

                        // Check if we're on the community settings page
                        const isCommunityPage = window.location.pathname.includes('/community/');
                        const imgId = isCommunityPage ? 'community-qr-code-img' : 'company-qr-code-img';

                        if (qrCodeImg) {
                            // Update existing image
                            qrCodeImg.src = data.qr_code_url;
                            if (downloadBtn) {
                                downloadBtn.href = data.qr_code_url;
                            }
                        } else if (qrCodeContainer) {
                            // Create new image if it doesn't exist
                            // Use different HTML structure based on page type
                            if (isCommunityPage) {
                                qrCodeContainer.innerHTML = `
                                    <img src="${data.qr_code_url}" alt="Community QR Code" class="img-fluid mb-3" style="max-width: 200px;" id="community-qr-code-img">
                                    <p class="text-muted small mb-3">
                                        Share this QR code to let others join your community.
                                    </p>
                                    <div class="d-flex justify-content-center gap-2 mb-2">
                                        <a href="${data.qr_code_url}" download="community-qr-code.png" class="btn btn-outline-primary" id="qr-code-download-btn">
                                            <i class="bi bi-download me-2"></i> Download QR Code
                                        </a>
                                    </div>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" id="regenerate-qr-code-btn" data-company-id="${companyId}">
                                        <i class="bi bi-arrow-clockwise me-1"></i> Regenerate QR Code
                                    </button>
                                `;
                            } else {
                                qrCodeContainer.innerHTML = `
                                    <img src="${data.qr_code_url}" alt="QR Code" class="img-fluid mb-3" style="max-width: 150px;" id="company-qr-code-img">
                                    <div class="d-flex justify-content-center gap-2">
                                        <a href="${data.qr_code_url}" download class="btn btn-outline-secondary btn-sm" id="qr-code-download-btn">
                                            <i class="bi bi-download me-1"></i> Download
                                        </a>
                                        <button type="button" class="btn btn-outline-primary btn-sm" id="regenerate-qr-code-btn" data-company-id="${companyId}">
                                            <i class="bi bi-arrow-clockwise me-1"></i> Regenerate
                                        </button>
                                    </div>
                                `;
                            }

                            // Reattach event listener to new button
                            const newRegenerateBtn = document.getElementById('regenerate-qr-code-btn');
                            if (newRegenerateBtn) {
                                newRegenerateBtn.addEventListener('click', arguments.callee);
                            }
                        }

                        // Show success message
                        const alertDiv = document.createElement('div');
                        alertDiv.className = 'alert alert-success mt-2';
                        alertDiv.innerHTML = isGenerating ? 'QR code generated successfully!' : 'QR code regenerated successfully!';
                        qrCodeContainer.appendChild(alertDiv);

                        // Remove alert after 3 seconds
                        setTimeout(() => {
                            alertDiv.remove();
                        }, 3000);
                    } else {
                        console.error('Error regenerating QR code:', data.message);
                        alert('Error regenerating QR code: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error regenerating QR code:', error);
                    alert('Error regenerating QR code. Please try again.');
                })
                .finally(() => {
                    // Re-enable button and restore original text
                    this.disabled = false;
                    // If this was a "Generate QR Code" button, change it to "Regenerate" after success
                    if (originalText.includes('Generate QR Code')) {
                        this.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i> Regenerate';
                    } else {
                        this.innerHTML = originalText;
                    }
                });
        });
    }

    // Assistant QR Code Regeneration
    const assistantRegenerateBtn = document.getElementById('regenerate-assistant-qr-code-btn');
    if (assistantRegenerateBtn) {
        assistantRegenerateBtn.addEventListener('click', function() {
            const assistantId = this.getAttribute('data-assistant-id');
            if (!assistantId) {
                console.error('Assistant ID not found on regenerate button');
                return;
            }

            // Disable button and show loading state
            this.disabled = true;
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="bi bi-arrow-repeat"></i> Generating...';

            // Make AJAX request to regenerate QR code
            fetch(`/assistant/assistant/${assistantId}/generate-qr-code/`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.status === 'success') {
                        // Update QR code image
                        const qrCodeContainer = document.getElementById('qr-code-container');
                        const qrCodeImg = document.getElementById('assistant-qr-code-img');
                        const downloadBtn = document.getElementById('qr-code-download-btn');

                        if (qrCodeImg) {
                            // Update existing image
                            qrCodeImg.src = data.qr_code_url;
                            if (downloadBtn) {
                                downloadBtn.href = data.qr_code_url;
                            }
                        } else if (qrCodeContainer) {
                            // Create new image if it doesn't exist
                            qrCodeContainer.innerHTML = `
                                <h6 class="card-subtitle mb-2 text-muted">Scan QR Code</h6>
                                <img src="${data.qr_code_url}" alt="QR Code" class="img-fluid" style="max-width: 150px;" id="assistant-qr-code-img">
                                <div class="mt-2">
                                    <a href="${data.qr_code_url}" download class="btn btn-outline-secondary btn-sm me-1" id="qr-code-download-btn">
                                        <i class="bi bi-download"></i> Download
                                    </a>
                                    <button type="button" class="btn btn-outline-primary btn-sm" id="regenerate-assistant-qr-code-btn" data-assistant-id="${assistantId}">
                                        <i class="bi bi-arrow-clockwise"></i> Regenerate
                                    </button>
                                </div>
                            `;
                            // Reattach event listener to new button
                            const newRegenerateBtn = document.getElementById('regenerate-assistant-qr-code-btn');
                            if (newRegenerateBtn) {
                                newRegenerateBtn.addEventListener('click', arguments.callee);
                            }
                        }

                        // Show success message
                        const alertDiv = document.createElement('div');
                        alertDiv.className = 'alert alert-success mt-2';
                        alertDiv.innerHTML = 'QR code regenerated successfully!';
                        qrCodeContainer.appendChild(alertDiv);

                        // Remove alert after 3 seconds
                        setTimeout(() => {
                            alertDiv.remove();
                        }, 3000);
                    } else {
                        console.error('Error regenerating QR code:', data.message);
                        alert('Error regenerating QR code: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error regenerating QR code:', error);
                    alert('Error regenerating QR code. Please try again.');
                })
                .finally(() => {
                    // Re-enable button and restore original text
                    this.disabled = false;
                    this.innerHTML = originalText;
                });
        });
    }
});
