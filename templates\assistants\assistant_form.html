{% extends "assistants/base.html" %}
{% load crispy_forms_tags %}

{% block extra_css %}
{{ block.super }}
<style>
/* Upload indicator styling */
.upload-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 15px 20px;
    border-radius: 5px;
    z-index: 9999;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* TinyMCE image styles */
.mce-content-body img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 10px 0;
    border-radius: 4px;
}

/* Hide HTML elements in TinyMCE */
.tox-tinymce-aux {
    display: none;
}
</style>
{% endblock %}

{% block extra_js %}
{{ block.super }}
{% include "assistants/tinymce_init.html" %}

<script>
// Model Selection JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Handle OpenAI Compatible model selection
    // Use a more flexible selector that works with form prefixes
    const modelSelect = document.querySelector('select[name$="model"]'); // Find select whose name ends with model
    const openaiCompatibleFields = document.getElementById('openai-compatible-fields');

    if (modelSelect && openaiCompatibleFields) {
        console.log('Found model select element:', modelSelect);
        console.log('Found OpenAI Compatible fields container:', openaiCompatibleFields);

        // Function to toggle OpenAI Compatible fields container
        function toggleOpenAICompatibleFields() {
            const isOpenAICompatible = modelSelect.value === 'openai-compatible';
            console.log('Model value:', modelSelect.value, 'Is OpenAI Compatible:', isOpenAICompatible);

            // Show/hide the container based on selection
            openaiCompatibleFields.style.display = isOpenAICompatible ? 'block' : 'none';

            if (isOpenAICompatible) {
                console.log('Showing OpenAI Compatible fields container');
            } else {
                console.log('Hiding OpenAI Compatible fields container');
            }
        }

        // Add change event listener
        modelSelect.addEventListener('change', function() {
            console.log('Model changed to:', this.value);
            toggleOpenAICompatibleFields();
        });

        // Initial check on page load
        console.log('Initial model value:', modelSelect.value);
        toggleOpenAICompatibleFields();
    } else {
        console.error('Required elements not found:', {
            modelSelect: modelSelect ? 'Found' : 'Not found',
            openaiCompatibleFields: openaiCompatibleFields ? 'Found' : 'Not found'
        });
    }
});

// Viewer Management JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Get CSRF token
    function getCsrfToken() {
        return document.querySelector('input[name="csrfmiddlewaretoken"]').value;
    }

    // Add Viewer functionality
    const addViewerBtn = document.getElementById('confirm-add-viewer-btn');
    const viewerSelect = document.getElementById('viewer-select');
    const addViewerModal = document.getElementById('addViewerModal');
    const addViewerError = document.getElementById('add-viewer-error');

    if (addViewerBtn && viewerSelect && addViewerModal) {
        const bsAddViewerModal = new bootstrap.Modal(addViewerModal);

        addViewerBtn.addEventListener('click', function() {
            const viewerId = viewerSelect.value;
            if (!viewerId) {
                addViewerError.textContent = 'Please select a user.';
                addViewerError.style.display = 'block';
                return;
            }

            // Send AJAX request to add viewer
            fetch('{% url "assistants:add_viewer" company.id assistant.id %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': getCsrfToken()
                },
                body: new URLSearchParams({
                    'viewer_id': viewerId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Reload the page to show updated viewer list
                    window.location.reload();
                } else {
                    addViewerError.textContent = data.message || 'An error occurred.';
                    addViewerError.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error adding viewer:', error);
                addViewerError.textContent = 'An error occurred. Please try again.';
                addViewerError.style.display = 'block';
            });
        });
    }

    // Remove Viewer functionality
    const removeViewerBtns = document.querySelectorAll('.remove-viewer-btn');
    const confirmRemoveViewerBtn = document.getElementById('confirm-remove-viewer-btn');
    const removeViewerModal = document.getElementById('removeViewerModal');
    const viewerNameToRemove = document.getElementById('viewer-name-to-remove');
    const viewerIdToRemove = document.getElementById('viewer-id-to-remove');
    const removeViewerError = document.getElementById('remove-viewer-error');

    if (removeViewerBtns.length && confirmRemoveViewerBtn && removeViewerModal) {
        const bsRemoveViewerModal = new bootstrap.Modal(removeViewerModal);

        removeViewerBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const viewerId = this.dataset.viewerId;
                const viewerName = this.dataset.viewerName;

                viewerIdToRemove.value = viewerId;
                viewerNameToRemove.textContent = viewerName;
                removeViewerError.style.display = 'none';

                bsRemoveViewerModal.show();
            });
        });

        confirmRemoveViewerBtn.addEventListener('click', function() {
            const viewerId = viewerIdToRemove.value;

            // Send AJAX request to remove viewer
            fetch('{% url "assistants:remove_viewer" company.id assistant.id %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': getCsrfToken()
                },
                body: new URLSearchParams({
                    'viewer_id': viewerId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Reload the page to show updated viewer list
                    window.location.reload();
                } else {
                    removeViewerError.textContent = data.message || 'An error occurred.';
                    removeViewerError.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error removing viewer:', error);
                removeViewerError.textContent = 'An error occurred. Please try again.';
                removeViewerError.style.display = 'block';
            });
        });
    }
});
</script>
{% endblock %}

{% block page_header %}
    <h1 class="h2">{{ action }} Assistant</h1>
{% endblock page_header %}

{% block main_content %}
<div class="card">
    <div class="card-body">
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            {# Global Update Button #}
            <div class="sticky-top pt-2 pb-2 bg-white border-bottom mb-3">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-arrow-clockwise me-1"></i> Update Assistant
                </button>
            </div>

            {# --- Conditional Rendering based on Assistant Type --- #}
            {% if assistant.assistant_type == 'support' or assistant.assistant_type == 'community' %}
                {# --- Advanced Assistant Settings (Support & Community) --- #}
                <ul class="nav nav-tabs mb-3" id="advancedAssistantFormTabs" role="tablist">
                    {# General Settings Tab Link - First Position #}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="general-settings-tab-support" data-bs-toggle="tab" data-bs-target="#general-settings-pane-support" type="button" role="tab" aria-controls="general-settings-pane-support" aria-selected="true">General Settings</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="nav-settings-tab" data-bs-toggle="tab" data-bs-target="#nav-settings-pane" type="button" role="tab" aria-controls="nav-settings-pane" aria-selected="false">Sidebar Navigation</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="edit-data-tab" data-bs-toggle="tab" data-bs-target="#edit-data-pane" type="button" role="tab" aria-controls="edit-data-pane" aria-selected="false">Edit Data</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="context-tab" data-bs-toggle="tab" data-bs-target="#context-pane" type="button" role="tab" aria-controls="context-pane" aria-selected="false">Context</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="analyze-tab" data-bs-toggle="tab" data-bs-target="#analyze-pane" type="button" role="tab" aria-controls="analyze-pane" aria-selected="false">Analyze & Suggest</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="display-tab-support" data-bs-toggle="tab" data-bs-target="#display-pane-support" type="button" role="tab" aria-controls="display-pane-support" aria-selected="false">Display</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="permissions-tab" data-bs-toggle="tab" data-bs-target="#permissions-pane" type="button" role="tab" aria-controls="permissions-pane" aria-selected="false">Permissions</button>
                    </li>
                </ul>

                <div class="tab-content" id="advancedAssistantFormTabContent">
                    {# Navigation Settings Tab Pane #}
                     {# Remove show active classes #}
                    <div class="tab-pane fade" id="nav-settings-pane" role="tabpanel" aria-labelledby="nav-settings-tab" tabindex="0">
                        {# Add hidden fields for required fields not shown in this view #}
                        {{ form.assistant_type.as_hidden }}
                        {{ form.model.as_hidden }}
                        {# Removed is_public hidden field to avoid conflicts with the visible toggle #}
                        {{ form.is_active.as_hidden }} {# Added hidden field #}

                        <h5>Customize Sidebar Navigation</h5>
                        <p class="text-muted small">Define the sections available in the chat sidebar. These items will appear in the sidebar of the assistant chat interface. Changes require saving the form.</p>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                {{ form.show_sidebar }}
                                <label class="form-check-label" for="{{ form.show_sidebar.id_for_label }}">{{ form.show_sidebar.label }}</label>
                            </div>
                            {% if form.show_sidebar.help_text %}<div class="form-text">{{ form.show_sidebar.help_text }}</div>{% endif %}
                        </div>
                        <div class="mb-3 ms-4"> <!-- Indented to show relationship -->
                            <div class="form-check form-switch">
                                {{ form.show_sidebar_public }}
                                <label class="form-check-label" for="{{ form.show_sidebar_public.id_for_label }}">{{ form.show_sidebar_public.label }}</label>
                            </div>
                            {% if form.show_sidebar_public.help_text %}<div class="form-text">{{ form.show_sidebar_public.help_text }}</div>{% endif %}
                        </div>
                        <hr>

                        {# Render the Formset #}
                        {% if nav_formset %}
                            {{ nav_formset.management_form }}
                            <div id="nav-items-container" class="mb-3">
                                {# Manual rendering in horizontal layout #}
                                {% for form in nav_formset %}
                                    <div class="nav-item-row border rounded p-2 mb-2 d-flex align-items-center" id="{{ form.prefix }}-row"> {# Added d-flex align-items-center #}
                                        {# Use Django's as_hidden for reliability #}
                                        {{ form.id.as_hidden }}
                                        {% if nav_formset.can_order %}{{ form.ORDER.as_hidden }}{% endif %}
                                        {% if form.order %}{{ form.order.as_hidden }}{% endif %} {# Hide model order field if present #}

                                        {# Reorder Buttons Column #}
                                        <div class="me-2"> {# Use margin for spacing #}
                                            <button type="button" class="btn btn-sm btn-outline-secondary move-up-btn" title="Move Up">
                                                <i class="bi bi-arrow-up"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary move-down-btn" title="Move Down">
                                                <i class="bi bi-arrow-down"></i>
                                            </button>
                                        </div>

                                        {# Main Content Column - Wrap existing row in a flex-grow div #}
                                        <div class="flex-grow-1">
                                            <div class="row align-items-center gx-2">
                                                {# Label #}
                                                <div class="col-md-3 mb-2 mb-md-0">
                                                <label for="{{ form.label.id_for_label }}" class="form-label visually-hidden">{{ form.label.label }}</label>
                                                {{ form.label }} {# Render widget directly #}
                                            </div>
                                            {# Visible #}
                                            <div class="col-md-1 mb-2 mb-md-0 form-check form-switch d-flex justify-content-center align-items-center pt-md-2">
                                                {{ form.visible }} {# Render widget directly #}
                                                <label class="form-check-label visually-hidden" for="{{ form.visible.id_for_label }}">Visible</label>
                                            </div>
                                            {# Section Type #}
                                            <div class="col-md-2 mb-2 mb-md-0">
                                                 <label for="{{ form.section_type.id_for_label }}" class="form-label visually-hidden">{{ form.section_type.label }}</label>
                                                {{ form.section_type }} {# Render widget directly #}
                                            </div>
                                            {# Entry Count - REMOVED #}
                                            {# Delete #}
                                            <div class="col-md-4 text-end"> {# Increased width from col-md-2 #}
                                                {% if nav_formset.can_delete %}
                                                <div class="form-check form-check-inline pt-md-2">
                                                    {{ form.DELETE }} {# Render widget directly #}
                                                    <label class="form-check-label small" for="{{ form.DELETE.id_for_label }}">Remove?</label>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% if form.errors %}
                                            <div class="alert alert-danger mt-2 p-1 small">
                                                {{ form.errors }} {# Display any form errors #}
                                            </div>
                                            {% endif %}
                                        </div> {# End flex-grow-1 wrapper #}
                                    </div>
                                {% endfor %}
                            </div>

                            {# Template for adding new forms via JS - MUST be wrapped in a single root element inside <template> #}
                            <template id="empty-nav-form">
                                <div class="nav-item-row border rounded p-2 mb-2 d-flex align-items-center" id="{{ nav_formset.empty_form.prefix }}-row"> {# Added d-flex align-items-center #}
                                    {# Use Django's as_hidden for reliability in template #}
                                    {{ nav_formset.empty_form.id.as_hidden }}
                                    {% if nav_formset.can_order %}{{ nav_formset.empty_form.ORDER.as_hidden }}{% endif %}
                                    {% if nav_formset.empty_form.order %}{{ nav_formset.empty_form.order.as_hidden }}{% endif %}

                                    {# Reorder Buttons Column #}
                                    <div class="me-2"> {# Use margin for spacing #}
                                        <button type="button" class="btn btn-sm btn-outline-secondary move-up-btn" title="Move Up">
                                            <i class="bi bi-arrow-up"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary move-down-btn" title="Move Down">
                                            <i class="bi bi-arrow-down"></i>
                                        </button>
                                    </div>

                                    {# Main Content Column - Wrap existing row in a flex-grow div #}
                                    <div class="flex-grow-1">
                                        <div class="row align-items-center gx-2">
                                            <div class="col-md-3 mb-2 mb-md-0">
                                                <label for="{{ nav_formset.empty_form.label.id_for_label }}" class="form-label visually-hidden">{{ nav_formset.empty_form.label.label }}</label>
                                            {{ nav_formset.empty_form.label }}
                                        </div>
                                        <div class="col-md-1 mb-2 mb-md-0 form-check form-switch d-flex justify-content-center align-items-center pt-md-2">
                                            {{ nav_formset.empty_form.visible }}
                                            <label class="form-check-label visually-hidden" for="{{ nav_formset.empty_form.visible.id_for_label }}">Visible</label>
                                        </div>
                                        <div class="col-md-2 mb-2 mb-md-0">
                                             <label for="{{ nav_formset.empty_form.section_type.id_for_label }}" class="form-label visually-hidden">{{ nav_formset.empty_form.section_type.label }}</label>
                                            {{ nav_formset.empty_form.section_type }}
                                        </div>
                                        {# Entry Count - REMOVED #}
                                        <div class="col-md-4 text-end"> {# Increased width from col-md-2 #}
                                            {% if nav_formset.can_delete %}
                                            <div class="form-check form-check-inline pt-md-2">
                                                {{ nav_formset.empty_form.DELETE }}
                                                <label class="form-check-label small" for="{{ nav_formset.empty_form.DELETE.id_for_label }}">Remove?</label>
                                                </div>
                                                {% endif %}
                                                </div>
                                            </div>
                                    </div> {# End flex-grow-1 wrapper #}
                                </div> {# End of wrapper div #}
                            </template>

                            <button type="button" class="btn btn-sm btn-outline-success" id="add-nav-item-btn">
                                <i class="bi bi-plus-lg me-1"></i> Add Navigation Item
                            </button>
                        {% else %}
                            <p class="text-danger"><em>Navigation formset not available in context. Backend setup might be incomplete.</em></p>
                        {% endif %}
                    </div>

                    {# Edit Data Tab Pane #}
                    <div class="tab-pane fade" id="edit-data-pane" role="tabpanel" aria-labelledby="edit-data-tab" tabindex="0">
                        <h5>Edit Website Data</h5>
                        <p class="text-muted small">Update the content used by the assistant. Content structure depends on the Navigation Settings.</p>
                        {# Placeholder for Django Formset(s) for Website Data Items #}
                        {# Assumes context variable like 'data_formset' or specific formsets per type #}
                        {# This section needs significant backend logic to provide the correct forms based on nav_formset #}
                        <fieldset class="mb-4 p-3 border rounded">
                             <legend class="fs-6 fw-bold mb-3">Company Info</legend>
                             {# Assuming these are part of the main 'form' #}
                             <div class="row">
                                <div class="col-md-6 mb-3">{{ form.name|as_crispy_field }}</div> {# Reusing standard name? Or a specific support name? #}
                                <div class="col-md-6 mb-3">{{ form.persona_name|as_crispy_field }}</div>
                             </div>
                             {# Add fields for mission, founded if they are part of the main Assistant model/form #}
                             {# Or display read-only from company_info context variable #}
                             <p><strong>Mission:</strong> {{ company_info.mission|default:"N/A" }}</p>
                             <p><strong>Founded:</strong> {{ company_info.founded|default:"N/A" }}</p>
                             <a href="{% url 'accounts:company_settings' company_id=assistant.company.id %}" class="btn btn-sm btn-outline-secondary mt-2">Edit Full Company Info</a>
                        </fieldset>

                        <div id="website-data-formsets-container">
                            <p class="text-muted"><em>Data editing forms will appear here based on Navigation Settings. Requires backend implementation (Phase 2/3).</em></p>
                            {# Example for a dynamic section (e.g. product, service, team, etc.) #}
                            {% for sec in nav_formset %}
                                <div class="dynamic-section-edit mb-4 p-3 border rounded">
                                    <h6 class="fw-bold mb-2">{{ sec.label.value|default:sec.label }}</h6>
                                    <div class="mb-2">
                                        <label class="form-label">Content</label>
                                        {{ sec.content }}
                                    </div>
                                    <div class="mb-2">
                                        <label class="form-label">Main Image</label>
                                        <input type="file" name="{{ sec.prefix }}_main_image" accept="image/*" class="form-control form-control-sm">
                                        {% if sec.main_image %}
                                            <div class="mt-2">
                                                <span class="text-muted">Current Image:</span><br>
                                                <img src="{{ sec.main_image.url|default:sec.main_image }}" style="max-width:100px; max-height:100px; border-radius:4px;">
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="mb-2">
                                        <label class="form-label">Gallery Images (Upload up to 5)</label>
                                        <input type="file" name="{{ sec.prefix }}_gallery" accept="image/*" multiple class="form-control form-control-sm">
                                        {% if sec.gallery %}
                                            <div class="mt-2">
                                                <span class="text-muted">Current Gallery:</span>
                                                <div style="display: flex; flex-wrap: wrap; gap: 5px;">
                                                    {% for img in sec.gallery %}
                                                        <img src="{{ img.url|default:img }}" style="max-width:80px; max-height:80px; border-radius:4px;">
                                                    {% endfor %}
                                                </div>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>

                    {# Context Tab Pane #}
                    <div class="tab-pane fade" id="context-pane" role="tabpanel" aria-labelledby="context-tab" tabindex="0">
                        <h5>LLM Configuration & Context</h5>
                        <p class="text-muted small">Adjust LLM behavior and provide additional context for the assistant to use when answering questions.</p>
                        <hr>
                        {# --- Additional Context Field (Enhanced) --- #}
                        <h6>Additional Context (Optional)</h6>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            <strong>Important:</strong> Content added here will be provided to the LLM as high-priority context for answering questions.
                            The LLM will be explicitly instructed to use this information when responding to related queries.
                            <hr>
                            <p class="mb-0"><strong>Tips for effective context:</strong></p>
                            <ul class="mb-0">
                                <li>Be specific and concise - focus on factual information</li>
                                <li>Use plain text format - HTML tags will be automatically cleaned</li>
                                <li>Include names of specific entities (companies, products, people) that the LLM should recognize</li>
                                <li>This context has the highest priority and will override other information</li>
                            </ul>
                        </div>
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <strong>Example:</strong> If you add "We have trained sales people at Stanbic Bank and Centenary Bank" here,
                            the LLM will acknowledge this fact when asked about these banks, rather than saying it has no information.
                        </div>
                        {{ form.extra_context|as_crispy_field }} {# Render the field from AssistantForm #}
                    </div>

                    {# Analyze & Suggest Tab Pane #}
                    <div class="tab-pane fade" id="analyze-pane" role="tabpanel" aria-labelledby="analyze-tab" tabindex="0">
                        <h5>Analyze & Suggest</h5>
                        <p class="text-muted small">Tools for analyzing content and generating suggestions (Placeholder).</p>
                        <button type="button" class="btn btn-outline-primary" id="generate-suggestions-btn">Generate Suggested Questions</button>
                        <div id="suggestions-output" class="mt-3">
                            {# Output area for suggestions #}
                        </div>
                    </div>

                    {# Permissions Tab Pane #}
                    <div class="tab-pane fade" id="permissions-pane" role="tabpanel" aria-labelledby="permissions-tab" tabindex="0">
                        <h5>Manage Permissions</h5>
                        <p class="text-muted small">Control who can access and manage this assistant.</p>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">Public Access</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check form-switch mb-3">
                                            {{ form.is_public }}
                                            <label class="form-check-label" for="{{ form.is_public.id_for_label }}">
                                                Make this assistant public
                                            </label>
                                            <div class="form-text small">
                                                Public assistants are accessible to all authenticated users.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">Status</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check form-switch mb-3">
                                            {{ form.is_active }}
                                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                                Active
                                            </label>
                                            <div class="form-text small">
                                                Inactive assistants cannot be accessed by any users.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">Viewer Access</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="add-viewer-btn" data-bs-toggle="modal" data-bs-target="#addViewerModal">
                                    <i class="bi bi-plus-lg"></i> Add Viewer
                                </button>
                            </div>
                            <div class="card-body">
                                <p class="text-muted small">
                                    Viewers can access this assistant even if it's private, but cannot edit it.
                                </p>

                                <div id="viewers-list">
                                    {% if assistant_viewers %}
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>User</th>
                                                        <th>Email</th>
                                                        <th>Added</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for viewer in assistant_viewers %}
                                                    <tr>
                                                        <td>{{ viewer.get_full_name|default:viewer.username }}</td>
                                                        <td>{{ viewer.email }}</td>
                                                        <td>{{ viewer.date_added|date:"M d, Y" }}</td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-outline-danger remove-viewer-btn"
                                                                    data-viewer-id="{{ viewer.id }}"
                                                                    data-viewer-name="{{ viewer.get_full_name|default:viewer.username }}">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    {% else %}
                                        <div class="alert alert-info">
                                            <i class="bi bi-info-circle me-2"></i>
                                            No viewers have been added to this assistant.
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    {# Display Tab Pane (for Support Assistant) - MADE IDENTICAL TO STANDARD #}
                    <div class="tab-pane fade" id="display-pane-support" role="tabpanel" aria-labelledby="display-tab-support">
                        <p class="text-muted">Customize the visual appearance of the assistant pages.</p>
                        <ul class="nav nav-pills mb-3" id="displaySubTabsSupport" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="simple-display-tab-support" data-bs-toggle="tab" data-bs-target="#simple-display-pane-support" type="button" role="tab" aria-controls="simple-display-pane-support" aria-selected="true">Simple</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="advanced-display-tab-support" data-bs-toggle="tab" data-bs-target="#advanced-display-pane-support" type="button" role="tab" aria-controls="advanced-display-pane-support" aria-selected="false">Advanced (CSS)</button>
                            </li>
                        </ul>
                        <div class="tab-content" id="displaySubTabContentSupport">
                            <div class="tab-pane fade show active" id="simple-display-pane-support" role="tabpanel" aria-labelledby="simple-display-tab-support" tabindex="0">
                                <p class="small text-muted">Use these controls to generate basic CSS styles for the public chat interface. Changes here will overwrite the Advanced CSS field.</p>
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6>General & Chat Area</h6>
                                        <div class="mb-2">
                                            <label for="simple-page-bg-color-support" class="form-label small">Page Background:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-page-bg-color-support" value="#ffffff" title="Chat page background color">
                                        </div>
                                         <div class="mb-2">
                                            <label for="simple-chat-bg-color-support" class="form-label small">Chat Box Background:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-chat-bg-color-support" value="#f8f9fa" title="Chat box background color">
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-body-text-color-support" class="form-label small">Body Text Color:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-body-text-color-support" value="#212529" title="General text color">
                                        </div>
                                         <div class="mb-2">
                                            <label for="simple-font-family-support" class="form-label small">Font Family:</label>
                                            <select class="form-select form-select-sm" id="simple-font-family-support">
                                                <option value="">System Default</option>
                                                <option value="Arial, sans-serif">Arial</option>
                                                <option value="'Times New Roman', Times, serif">Times New Roman</option>
                                                <option value="'Courier New', Courier, monospace">Courier New</option>
                                                <option value="Verdana, sans-serif">Verdana</option>
                                                <option value="Georgia, serif">Georgia</option>
                                            </select>
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-base-font-size-support" class="form-label small">Base Font Size (px):</label>
                                            <input type="number" class="form-control form-control-sm" id="simple-base-font-size-support" value="16" min="8" max="32">
                                        </div>
                                         <div class="mb-2">
                                            <label for="simple-title-color-support" class="form-label small">Title Text Color:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-title-color-support" value="#000000" title="Title text color (e.g., Company Name)">
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-title-font-size-support" class="form-label small">Title Font Size (px):</label>
                                            <input type="number" class="form-control form-control-sm" id="simple-title-font-size-support" value="24" min="12" max="48">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>Messages</h6>
                                        <div class="mb-2">
                                            <label for="simple-user-msg-bg-support" class="form-label small">User Msg Background:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-user-msg-bg-support" value="#e1f5fe" title="User message background color">
                                        </div>
                                         <div class="mb-2">
                                            <label for="simple-user-msg-text-support" class="form-label small">User Msg Text:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-user-msg-text-support" value="#000000" title="User message text color">
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-assistant-msg-bg-support" class="form-label small">Assistant Msg Background:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-assistant-msg-bg-support" value="#f1f8e9" title="Assistant message background color">
                                        </div>
                                         <div class="mb-2">
                                            <label for="simple-assistant-msg-text-support" class="form-label small">Assistant Msg Text:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-assistant-msg-text-support" value="#000000" title="Assistant message text color">
                                        </div>
                                         <div class="mb-2">
                                            <label for="simple-input-bg-color-support" class="form-label small">Input Field Background:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-input-bg-color-support" value="#ffffff" title="Chat input field background">
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-input-text-color-support" class="form-label small">Input Field Text:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-input-text-color-support" value="#000000" title="Chat input field text color">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                         <h6>Buttons</h6>
                                         <div class="mb-2">
                                            <label for="simple-send-btn-bg-support" class="form-label small">Send Button Background:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-send-btn-bg-support" value="#0d6efd" title="Send button background color">
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-send-btn-text-support" class="form-label small">Send Button Text:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-send-btn-text-support" value="#ffffff" title="Send button text color">
                                        </div>
                                         <div class="mb-2">
                                            <label for="simple-reset-btn-bg-support" class="form-label small">Reset Button Background:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-reset-btn-bg-support" value="#6c757d" title="Reset button background color">
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-reset-btn-text-support" class="form-label small">Reset Button Text:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-reset-btn-text-support" value="#ffffff" title="Reset button text color">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>Navigation & Questions</h6>
                                        <div class="mb-2">
                                            <label for="simple-nav-bg-color-support" class="form-label small">Nav Background:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-nav-bg-color-support" value="#eeeeee" title="Navigation sidebar background color">
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-nav-text-color-support" class="form-label small">Nav Text:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-nav-text-color-support" value="#333333" title="Navigation sidebar text color">
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-nav-active-bg-color-support" class="form-label small">Nav Active BG:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-nav-active-bg-color-support" value="#0d6efd" title="Active navigation item background color">
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-nav-active-text-color-support" class="form-label small">Nav Active Text:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-nav-active-text-color-support" value="#ffffff" title="Active navigation item text color">
                                        </div>
                                        <hr class="my-2">
                                        <div class="mb-2">
                                            <label for="simple-question-bg-color-support" class="form-label small">Question BG:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-question-bg-color-support" value="#f0f0f0" title="Suggested question background color">
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-question-text-color-support" class="form-label small">Question Text:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-question-text-color-support" value="#0056b3" title="Suggested question text color">
                                        </div>
                                    </div>
                                </div>
                                <hr>
                                <button type="button" class="btn btn-outline-secondary btn-sm" id="reset-simple-display-support">Reset Simple Settings</button>
                            </div>
                            <div class="tab-pane fade" id="advanced-display-pane-support" role="tabpanel" aria-labelledby="advanced-display-tab-support" tabindex="0">
                                 {{ form.custom_css|as_crispy_field }}
                                 <button type="button" class="btn btn-outline-secondary btn-sm mt-2" id="reset-advanced-display-support">Reset CSS</button>
                            </div>
                        </div>
                    </div>

                    {# --- NEW General Settings Tab Pane for Support Assistants --- #}
                    {# Add show active classes #}
                    <div class="tab-pane fade show active" id="general-settings-pane-support" role="tabpanel" aria-labelledby="general-settings-tab-support" tabindex="0">
                        <h5>General Settings</h5>
                        {# --- Name and Persona Name at the top --- #}
                        <div class="row">
                            <div class="col-md-6 mb-3">{{ form.name|as_crispy_field }}</div>
                            <div class="col-md-6 mb-3">{{ form.persona_name|as_crispy_field }}</div>
                        </div>
                        <div class="mb-3">
                            {{ form.description|as_crispy_field }}
                            <div id="description-word-count-support" class="form-text text-muted small mt-1">0/500 words</div>
                        </div>
                        {# --- LLM settings --- #}
                        <div class="row">
                             <div class="col-md-6 mb-3">{{ form.model|as_crispy_field }}</div>
                        </div>

                        {# OpenAI Compatible fields - hidden by default #}
                        <div id="openai-compatible-fields" style="display: none;">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                <strong>OpenAI Compatible Settings</strong>: Enter your API key, base URL, and model name for the OpenAI-compatible API.
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">{{ form.api_key|as_crispy_field }}</div>
                                <div class="col-md-6 mb-3">{{ form.custom_model_name|as_crispy_field }}</div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">{{ form.base_url|as_crispy_field }}</div>
                            </div>
                        </div>

                         <div class="row">
                            <div class="col-md-6 mb-3">{{ form.temperature|as_crispy_field }}</div>
                            <div class="col-md-6 mb-3">{{ form.max_tokens|as_crispy_field }}</div>
                        </div>
                        <div class="mb-3">{{ form.system_prompt|as_crispy_field }}</div>
                        <div class="mb-3">{{ form.greeting_message|as_crispy_field }}</div> {# Moved greeting_message here #}
                        <hr>
                        {# Other General Fields #}
                        <div class="row">
                            <div class="col-md-6 mb-3">{{ form.categories|as_crispy_field }}</div>
                            <div class="col-md-6 mb-3">{{ form.tags|as_crispy_field }}</div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">{{ form.knowledge_base|as_crispy_field }}</div>
                            <div class="col-md-6 mb-3">
                                <div class="mb-2">
                                    <label class="form-label">Logo Display:</label><br>
                                    {% with logo_url=assistant.get_logo_url %}
                                        {% if assistant.logo %}
                                            <div class="d-flex align-items-center">
                                                {% if assistant.logo.url %}
                                                    <img src="{{ assistant.logo.url }}" alt="{{ assistant.name }} Logo" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                                                    <span class="ms-2 badge bg-primary">Assistant Logo</span>
                                                {% else %}
                                                    <span class="text-muted">Logo file exists but URL is not available</span>
                                                {% endif %}
                                            </div>
                                        {% elif logo_url %}
                                            <div class="d-flex align-items-center">
                                                <img src="{{ logo_url }}" alt="Company Logo" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                                                <span class="ms-2 badge bg-secondary">Company Logo (Fallback)</span>
                                            </div>
                                        {% else %}
                                            <div class="d-flex align-items-center">
                                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 100px; height: 100px;">
                                                    <i class="bi bi-robot text-muted fs-1"></i>
                                                </div>
                                                <span class="ms-2 badge bg-secondary">Default Icon</span>
                                            </div>
                                        {% endif %}
                                    {% endwith %}
                                </div>
                                <div class="mb-3">
                                    <label for="{{ form.logo.id_for_label }}" class="form-label">Assistant Logo</label>
                                    <input type="file" name="logo" id="{{ form.logo.id_for_label }}" class="form-control" accept="image/*">
                                    {% if form.logo.help_text %}
                                        <div class="form-text">{{ form.logo.help_text }}</div>
                                    {% endif %}
                                    {% if form.logo.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.logo.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}

                                    <div id="logo-upload-result" class="mt-2"></div>

                                    <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        const logoInput = document.getElementById('{{ form.logo.id_for_label }}');
                                        const resultDiv = document.getElementById('logo-upload-result');

                                        logoInput.addEventListener('change', function() {
                                            if (this.files && this.files[0]) {
                                                console.log('Logo file selected:', this.files[0].name);

                                                // Show preview
                                                resultDiv.innerHTML = `
                                                    <div class="alert alert-info">
                                                        Selected file: ${this.files[0].name}
                                                    </div>
                                                    <div class="mt-2">
                                                        <img src="${URL.createObjectURL(this.files[0])}" alt="Logo preview" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                                                    </div>
                                                `;

                                                // Check if the clear checkbox exists and uncheck it
                                                const clearCheckbox = this.parentElement.querySelector('input[type="checkbox"]');
                                                if (clearCheckbox) {
                                                    clearCheckbox.checked = false;
                                                    console.log('Unchecked clear checkbox');
                                                }
                                            }
                                        });
                                    });
                                    </script>
                                </div>

                                <div class="mb-3">
                                    <div class="mb-2">
                                        <label class="form-label">Avatar Display:</label><br>
                                        {% if assistant.avatar %}
                                            <div class="d-flex align-items-center">
                                                {% if assistant.avatar.url %}
                                                    <img src="{{ assistant.avatar.url }}" alt="{{ assistant.name }} Avatar" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                                                    <span class="ms-2 badge bg-primary">Assistant Avatar</span>
                                                {% else %}
                                                    <span class="text-muted">Avatar file exists but URL is not available</span>
                                                {% endif %}
                                            </div>
                                        {% else %}
                                            <div class="d-flex align-items-center">
                                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 100px; height: 100px;">
                                                    <i class="bi bi-person-circle text-muted fs-1"></i>
                                                </div>
                                                <span class="ms-2 badge bg-secondary">Default Avatar (will use logo)</span>
                                            </div>
                                        {% endif %}
                                    </div>
                                    <label for="{{ form.avatar.id_for_label }}" class="form-label">Assistant Avatar</label>
                                    <input type="file" name="avatar" id="{{ form.avatar.id_for_label }}" class="form-control" accept="image/*">
                                    {% if form.avatar.help_text %}
                                        <div class="form-text">{{ form.avatar.help_text }}</div>
                                    {% endif %}
                                    {% if form.avatar.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.avatar.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}

                                    <div id="avatar-upload-result-support" class="mt-2"></div>

                                    <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        const avatarInput = document.getElementById('{{ form.avatar.id_for_label }}');
                                        const resultDiv = document.getElementById('avatar-upload-result-support');

                                        avatarInput.addEventListener('change', function() {
                                            if (this.files && this.files[0]) {
                                                console.log('Avatar file selected:', this.files[0].name);

                                                // Show preview
                                                resultDiv.innerHTML = `
                                                    <div class="alert alert-info">
                                                        Selected file: ${this.files[0].name}
                                                    </div>
                                                    <div class="mt-2">
                                                        <img src="${URL.createObjectURL(this.files[0])}" alt="Avatar preview" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                                                    </div>
                                                `;

                                                // Check if the clear checkbox exists and uncheck it
                                                const clearCheckbox = this.parentElement.querySelector('input[type="checkbox"]');
                                                if (clearCheckbox) {
                                                    clearCheckbox.checked = false;
                                                    console.log('Unchecked clear checkbox');
                                                }
                                            }
                                        });
                                    });
                                    </script>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">{{ form.folder|as_crispy_field }}</div>

                        {# --- Tier & Featured Status/Request Section (Copied) --- #}
                        <fieldset class="mb-4 p-3 border rounded">
                            <legend class="fs-6 fw-bold mb-3">Tier Status & Requests</legend>
                            {% if assistant.tier_change_pending %}
                                <div class="alert alert-info d-flex align-items-center" role="alert">
                                    <i class="bi bi-info-circle-fill me-2"></i>
                                    <div>
                                        A request to change the tier to <strong>{{ assistant.get_requested_tier_display }}</strong>
                                        {% if assistant.requested_tier_duration %}
                                            ({{ assistant.get_requested_tier_duration_display }})
                                        {% endif %}
                                        is pending superadmin approval.
                                    </div>
                                </div>
                            {% endif %}
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Current Tier:</label>
                                    <p class="form-control-plaintext">
                                        {{ assistant.get_tier_display }}
                                        {% if assistant.tier_expiry_date %}(Expires: {{ assistant.tier_expiry_date|date:"Y-m-d" }}){% endif %}
                                    </p>
                                </div>
                                <div class="col-md-6 mb-3">{{ form.request_new_tier|as_crispy_field }}</div>
                                <div class="col-md-6 mb-3" id="tier-duration-wrapper" style="display: none;">
                                    {{ form.requested_tier_duration|as_crispy_field }}
                                </div>
                            </div>
                        </fieldset>
                        <fieldset class="mb-3 p-3 border rounded">
                            <legend class="fs-6 fw-bold mb-3">Featured Status & Requests</legend>
                            {% if assistant.featured_request_pending %}
                                <div class="alert alert-info d-flex align-items-center" role="alert">
                                    <i class="bi bi-info-circle-fill me-2"></i>
                                    <div>
                                        A request to feature this assistant
                                        {% if assistant.requested_featured_duration %}
                                            ({{ assistant.get_requested_featured_duration_display }})
                                        {% endif %}
                                        is pending superadmin approval.
                                    </div>
                                </div>
                            {% endif %}
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Current Featured Status:</label>
                                    <p class="form-control-plaintext">
                                        {% if assistant.is_featured %}
                                            <span class="badge bg-primary">Yes</span>
                                            {% if assistant.featured_expiry_date %}(Expires: {{ assistant.featured_expiry_date|date:"Y-m-d" }}){% endif %}
                                        {% else %}
                                            <span class="badge bg-secondary">No</span>
                                        {% endif %}
                                    </p>
                                </div>
                                <div class="col-md-6 mb-3">{{ form.request_featured_status|as_crispy_field }}</div>
                                <div class="col-md-6 mb-3" id="featured-duration-wrapper" style="display: none;">
                                    {{ form.requested_featured_duration|as_crispy_field }}
                                </div>
                            </div>
                        </fieldset>
                        {# --- End Tier & Featured Section (Copied) --- #}

                        {# Removed duplicate is_active and is_public toggles - these are now only in the Permissions tab #}

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mt-4 text-center">
                                    <label class="form-label d-block">Assistant QR Code</label>
                                    <div id="qr-code-container">
                                        {% if assistant and assistant.qr_code %}
                                            <img src="{{ assistant.qr_code.url }}" alt="QR Code for {{ assistant.name }}" class="img-fluid mb-3" style="max-width: 150px;" id="assistant-qr-code-img">
                                            <div class="d-flex justify-content-center gap-2">
                                                <a href="{{ assistant.qr_code.url }}" download class="btn btn-outline-secondary btn-sm" id="qr-code-download-btn">
                                                    <i class="bi bi-download me-1"></i> Download
                                                </a>
                                                <button type="button" class="btn btn-outline-primary btn-sm" id="regenerate-assistant-qr-code-btn" data-assistant-id="{{ assistant.id }}">
                                                    <i class="bi bi-arrow-clockwise me-1"></i> Regenerate
                                                </button>
                                            </div>
                                        {% else %}
                                            <div class="alert alert-info mb-3">
                                                <p class="mb-0">QR code not available.</p>
                                            </div>
                                            <button type="button" class="btn btn-primary btn-sm" id="regenerate-assistant-qr-code-btn" data-assistant-id="{{ assistant.id }}">
                                                <i class="bi bi-qr-code me-1"></i> Generate QR Code
                                            </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {# --- End NEW General Settings Tab Pane --- #}
                </div>

            {% else %}
                {# --- Standard Assistant Settings (Block B) --- #}
                <ul class="nav nav-tabs mb-3" id="assistantFormTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings-pane" type="button" role="tab" aria-controls="settings-pane" aria-selected="true">Settings</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="display-tab" data-bs-toggle="tab" data-bs-target="#display-pane" type="button" role="tab" aria-controls="display-pane" aria-selected="false">Display</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="permissions-tab-standard" data-bs-toggle="tab" data-bs-target="#permissions-pane-standard" type="button" role="tab" aria-controls="permissions-pane-standard" aria-selected="false">Permissions</button>
                    </li>
                </ul>

                <div class="tab-content" id="assistantFormTabContent">
                    {# Settings Pane (Standard Assistant) #}
                    <div class="tab-pane fade show active" id="settings-pane" role="tabpanel" aria-labelledby="settings-tab">
                        {# --- Standard Assistant Fields --- #}
                        <div class="row">
                            <div class="col-md-6 mb-3">{{ form.name|as_crispy_field }}</div>
                            <div class="col-md-6 mb-3">{{ form.persona_name|as_crispy_field }}</div>
                        </div>
                        <div class="mb-3">
                            {{ form.description|as_crispy_field }}
                            <div id="description-word-count" class="form-text text-muted small mt-1">0/500 words</div>
                        </div>
                        {# assistant_type and model moved to hidden fields or context tab for support type #}
                        <div class="row">
                            <div class="col-md-6 mb-3">{{ form.assistant_type|as_crispy_field }}</div>
                            {# <div class="col-md-6 mb-3">{{ form.model|as_crispy_field }}</div> #} {# Removed model from here #}
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">{{ form.categories|as_crispy_field }}</div>
                            <div class="col-md-6 mb-3">{{ form.tags|as_crispy_field }}</div>
                        </div>
                        {# temperature, max_tokens, system_prompt moved to context tab for support type #}
                        {# <div class="row">
                            <div class="col-md-6 mb-3">{{ form.temperature|as_crispy_field }}</div>
                            <div class="col-md-6 mb-3">{{ form.max_tokens|as_crispy_field }}</div>
                        </div>
                        <div class="mb-3">{{ form.system_prompt|as_crispy_field }}</div> #}
                        <div class="mb-3">{{ form.greeting_message|as_crispy_field }}</div>
                        <div class="row">
                            <div class="col-md-6 mb-3">{{ form.knowledge_base|as_crispy_field }}</div>
                            <div class="col-md-6 mb-3">
                                <div class="mb-2">
                                    <label class="form-label">Logo Display:</label><br>
                                    {% with logo_url=assistant.get_logo_url %}
                                        {% if assistant.logo %}
                                            <div class="d-flex align-items-center">
                                                {% if assistant.logo.url %}
                                                    <img src="{{ assistant.logo.url }}" alt="{{ assistant.name }} Logo" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                                                    <span class="ms-2 badge bg-primary">Assistant Logo</span>
                                                    <div class="ms-2">
                                                        <small class="text-muted d-block">Logo URL: {{ assistant.logo.url }}</small>
                                                        <small class="text-muted d-block">Logo name: {{ assistant.logo.name }}</small>
                                                    </div>
                                                {% else %}
                                                    <span class="text-muted">Logo file exists but URL is not available</span>
                                                    <div class="ms-2">
                                                        <small class="text-muted d-block">Logo name: {{ assistant.logo.name }}</small>
                                                    </div>
                                                {% endif %}
                                            </div>
                                        {% elif logo_url %}
                                            <div class="d-flex align-items-center">
                                                <img src="{{ logo_url }}" alt="Company Logo" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                                                <span class="ms-2 badge bg-secondary">Company Logo (Fallback)</span>
                                                <div class="ms-2">
                                                    <small class="text-muted d-block">Logo URL: {{ logo_url }}</small>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="d-flex align-items-center">
                                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 100px; height: 100px;">
                                                    <i class="bi bi-robot text-muted fs-1"></i>
                                                </div>
                                                <span class="ms-2 badge bg-secondary">Default Icon</span>
                                            </div>
                                        {% endif %}
                                    {% endwith %}
                                </div>
                                <div class="mb-3">
                                    <label for="{{ form.logo.id_for_label }}" class="form-label">Assistant Logo</label>
                                    <input type="file" name="logo" id="{{ form.logo.id_for_label }}" class="form-control" accept="image/*">
                                    {% if form.logo.help_text %}
                                        <div class="form-text">{{ form.logo.help_text }}</div>
                                    {% endif %}
                                    {% if form.logo.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.logo.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}

                                    <div id="logo-upload-result-2" class="mt-2"></div>

                                    <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        const logoInput = document.getElementById('{{ form.logo.id_for_label }}');
                                        const resultDiv = document.getElementById('logo-upload-result-2');

                                        logoInput.addEventListener('change', function() {
                                            if (this.files && this.files[0]) {
                                                console.log('Logo file selected:', this.files[0].name);

                                                // Show preview
                                                resultDiv.innerHTML = `
                                                    <div class="alert alert-info">
                                                        Selected file: ${this.files[0].name}
                                                    </div>
                                                    <div class="mt-2">
                                                        <img src="${URL.createObjectURL(this.files[0])}" alt="Logo preview" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                                                    </div>
                                                `;

                                                // Check if the clear checkbox exists and uncheck it
                                                const clearCheckbox = this.parentElement.querySelector('input[type="checkbox"]');
                                                if (clearCheckbox) {
                                                    clearCheckbox.checked = false;
                                                    console.log('Unchecked clear checkbox');
                                                }

                                                // Add a note about form submission
                                                resultDiv.innerHTML += `
                                                    <div class="alert alert-warning mt-2">
                                                        <strong>Important:</strong> Click "Update Assistant" to save the logo.
                                                    </div>
                                                `;
                                            }
                                        });
                                    });
                                    </script>
                                </div>

                                <div class="mb-3">
                                    <div class="mb-2">
                                        <label class="form-label">Avatar Display:</label><br>
                                        {% if assistant.avatar %}
                                            <div class="d-flex align-items-center">
                                                {% if assistant.avatar.url %}
                                                    <img src="{{ assistant.avatar.url }}" alt="{{ assistant.name }} Avatar" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                                                    <span class="ms-2 badge bg-primary">Assistant Avatar</span>
                                                    <div class="ms-2">
                                                        <small class="text-muted d-block">Avatar URL: {{ assistant.avatar.url }}</small>
                                                        <small class="text-muted d-block">Avatar name: {{ assistant.avatar.name }}</small>
                                                    </div>
                                                {% else %}
                                                    <span class="text-muted">Avatar file exists but URL is not available</span>
                                                    <div class="ms-2">
                                                        <small class="text-muted d-block">Avatar name: {{ assistant.avatar.name }}</small>
                                                    </div>
                                                {% endif %}
                                            </div>
                                        {% else %}
                                            <div class="d-flex align-items-center">
                                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 100px; height: 100px;">
                                                    <i class="bi bi-person-circle text-muted fs-1"></i>
                                                </div>
                                                <span class="ms-2 badge bg-secondary">Default Avatar (will use logo)</span>
                                            </div>
                                        {% endif %}
                                    </div>
                                    <label for="{{ form.avatar.id_for_label }}" class="form-label">Assistant Avatar</label>
                                    <input type="file" name="avatar" id="{{ form.avatar.id_for_label }}" class="form-control" accept="image/*">
                                    {% if form.avatar.help_text %}
                                        <div class="form-text">{{ form.avatar.help_text }}</div>
                                    {% endif %}
                                    {% if form.avatar.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.avatar.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}

                                    <div id="avatar-upload-result" class="mt-2"></div>

                                    <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        const avatarInput = document.getElementById('{{ form.avatar.id_for_label }}');
                                        const resultDiv = document.getElementById('avatar-upload-result');

                                        avatarInput.addEventListener('change', function() {
                                            if (this.files && this.files[0]) {
                                                console.log('Avatar file selected:', this.files[0].name);

                                                // Show preview
                                                resultDiv.innerHTML = `
                                                    <div class="alert alert-info">
                                                        Selected file: ${this.files[0].name}
                                                    </div>
                                                    <div class="mt-2">
                                                        <img src="${URL.createObjectURL(this.files[0])}" alt="Avatar preview" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                                                    </div>
                                                `;

                                                // Check if the clear checkbox exists and uncheck it
                                                const clearCheckbox = this.parentElement.querySelector('input[type="checkbox"]');
                                                if (clearCheckbox) {
                                                    clearCheckbox.checked = false;
                                                    console.log('Unchecked clear checkbox');
                                                }

                                                // Add a note about form submission
                                                resultDiv.innerHTML += `
                                                    <div class="alert alert-warning mt-2">
                                                        <strong>Important:</strong> Click "Update Assistant" to save the avatar.
                                                    </div>
                                                `;
                                            }
                                        });
                                    });
                                    </script>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">{{ form.folder|as_crispy_field }}</div>

                        {# --- Tier & Featured Status/Request Section (Standard) --- #}
                        <fieldset class="mb-4 p-3 border rounded">
                            <legend class="fs-6 fw-bold mb-3">Tier Status & Requests</legend>
                            {% if assistant.tier_change_pending %}
                                <div class="alert alert-info d-flex align-items-center" role="alert">
                                    <i class="bi bi-info-circle-fill me-2"></i>
                                    <div>
                                        A request to change the tier to <strong>{{ assistant.get_requested_tier_display }}</strong>
                                        {% if assistant.requested_tier_duration %}
                                            ({{ assistant.get_requested_tier_duration_display }})
                                        {% endif %}
                                        is pending superadmin approval.
                                    </div>
                                </div>
                            {% endif %}
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Current Tier:</label>
                                    <p class="form-control-plaintext">
                                        {{ assistant.get_tier_display }}
                                        {% if assistant.tier_expiry_date %}(Expires: {{ assistant.tier_expiry_date|date:"Y-m-d" }}){% endif %}
                                    </p>
                                </div>
                                <div class="col-md-6 mb-3">{{ form.request_new_tier|as_crispy_field }}</div>
                                <div class="col-md-6 mb-3" id="tier-duration-wrapper" style="display: none;">
                                    {{ form.requested_tier_duration|as_crispy_field }}
                                </div>
                            </div>
                        </fieldset>
                        <fieldset class="mb-3 p-3 border rounded">
                            <legend class="fs-6 fw-bold mb-3">Featured Status & Requests</legend>
                            {% if assistant.featured_request_pending %}
                                <div class="alert alert-info d-flex align-items-center" role="alert">
                                    <i class="bi bi-info-circle-fill me-2"></i>
                                    <div>
                                        A request to feature this assistant
                                        {% if assistant.requested_featured_duration %}
                                            ({{ assistant.get_requested_featured_duration_display }})
                                        {% endif %}
                                        is pending superadmin approval.
                                    </div>
                                </div>
                            {% endif %}
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Current Featured Status:</label>
                                    <p class="form-control-plaintext">
                                        {% if assistant.is_featured %}
                                            <span class="badge bg-primary">Yes</span>
                                            {% if assistant.featured_expiry_date %}(Expires: {{ assistant.featured_expiry_date|date:"Y-m-d" }}){% endif %}
                                        {% else %}
                                            <span class="badge bg-secondary">No</span>
                                        {% endif %}
                                    </p>
                                </div>
                                <div class="col-md-6 mb-3">{{ form.request_featured_status|as_crispy_field }}</div>
                                <div class="col-md-6 mb-3" id="featured-duration-wrapper" style="display: none;">
                                    {{ form.requested_featured_duration|as_crispy_field }}
                                </div>
                            </div>
                        </fieldset>
                        {# --- End Tier & Featured Section (Standard) --- #}

                        {# Removed duplicate is_active and is_public toggles - these are now only in the Permissions tab #}

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mt-4 text-center">
                                    <label class="form-label d-block">Assistant QR Code</label>
                                    <div id="qr-code-container">
                                        {% if assistant and assistant.qr_code %}
                                            <img src="{{ assistant.qr_code.url }}" alt="QR Code for {{ assistant.name }}" class="img-fluid mb-3" style="max-width: 150px;" id="assistant-qr-code-img">
                                            <div class="d-flex justify-content-center gap-2">
                                                <a href="{{ assistant.qr_code.url }}" download class="btn btn-outline-secondary btn-sm" id="qr-code-download-btn">
                                                    <i class="bi bi-download me-1"></i> Download
                                                </a>
                                                <button type="button" class="btn btn-outline-primary btn-sm" id="regenerate-assistant-qr-code-btn" data-assistant-id="{{ assistant.id }}">
                                                    <i class="bi bi-arrow-clockwise me-1"></i> Regenerate
                                                </button>
                                            </div>
                                        {% else %}
                                            <div class="alert alert-info mb-3">
                                                <p class="mb-0">QR code not available.</p>
                                            </div>
                                            <button type="button" class="btn btn-primary btn-sm" id="regenerate-assistant-qr-code-btn" data-assistant-id="{{ assistant.id }}">
                                                <i class="bi bi-qr-code me-1"></i> Generate QR Code
                                            </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {# Display Pane (Standard Assistant) #}
                    <div class="tab-pane fade" id="display-pane" role="tabpanel" aria-labelledby="display-tab">
                        <p class="text-muted">Customize the visual appearance of the assistant pages.</p>
                        <ul class="nav nav-pills mb-3" id="displaySubTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="simple-display-tab" data-bs-toggle="tab" data-bs-target="#simple-display-pane" type="button" role="tab" aria-controls="simple-display-pane" aria-selected="true">Simple</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="advanced-display-tab" data-bs-toggle="tab" data-bs-target="#advanced-display-pane" type="button" role="tab" aria-controls="advanced-display-pane" aria-selected="false">Advanced (CSS)</button>
                            </li>
                        </ul>

                        <div class="tab-content" id="displaySubTabContent">
                            <div class="tab-pane fade show active" id="simple-display-pane" role="tabpanel" aria-labelledby="simple-display-tab" tabindex="0">
                                <p class="small text-muted">Use these controls to generate basic CSS styles for the public chat interface. Changes here will overwrite the Advanced CSS field.</p>
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6>General & Chat Area</h6>
                                        <div class="mb-2">
                                            <label for="simple-page-bg-color" class="form-label small">Page Background:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-page-bg-color" value="#ffffff" title="Chat page background color">
                                        </div>
                                         <div class="mb-2">
                                            <label for="simple-chat-bg-color" class="form-label small">Chat Box Background:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-chat-bg-color" value="#f8f9fa" title="Chat box background color">
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-body-text-color" class="form-label small">Body Text Color:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-body-text-color" value="#212529" title="General text color">
                                        </div>
                                         <div class="mb-2">
                                            <label for="simple-font-family" class="form-label small">Font Family:</label>
                                            <select class="form-select form-select-sm" id="simple-font-family">
                                                <option value="">System Default</option>
                                                <option value="Arial, sans-serif">Arial</option>
                                                <option value="'Times New Roman', Times, serif">Times New Roman</option>
                                                <option value="'Courier New', Courier, monospace">Courier New</option>
                                                <option value="Verdana, sans-serif">Verdana</option>
                                                <option value="Georgia, serif">Georgia</option>
                                            </select>
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-base-font-size" class="form-label small">Base Font Size (px):</label>
                                            <input type="number" class="form-control form-control-sm" id="simple-base-font-size" value="16" min="8" max="32">
                                        </div>
                                         <div class="mb-2">
                                            <label for="simple-title-color" class="form-label small">Title Text Color:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-title-color" value="#000000" title="Title text color (e.g., Company Name)">
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-title-font-size" class="form-label small">Title Font Size (px):</label>
                                            <input type="number" class="form-control form-control-sm" id="simple-title-font-size" value="24" min="12" max="48">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>Messages</h6>
                                        <div class="mb-2">
                                            <label for="simple-user-msg-bg" class="form-label small">User Msg Background:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-user-msg-bg" value="#e1f5fe" title="User message background color">
                                        </div>
                                         <div class="mb-2">
                                            <label for="simple-user-msg-text" class="form-label small">User Msg Text:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-user-msg-text" value="#000000" title="User message text color">
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-assistant-msg-bg" class="form-label small">Assistant Msg Background:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-assistant-msg-bg" value="#f1f8e9" title="Assistant message background color">
                                        </div>
                                         <div class="mb-2">
                                            <label for="simple-assistant-msg-text" class="form-label small">Assistant Msg Text:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-assistant-msg-text" value="#000000" title="Assistant message text color">
                                        </div>
                                         <div class="mb-2">
                                            <label for="simple-input-bg-color" class="form-label small">Input Field Background:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-input-bg-color" value="#ffffff" title="Chat input field background">
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-input-text-color" class="form-label small">Input Field Text:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-input-text-color" value="#000000" title="Chat input field text color">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                         <h6>Buttons</h6>
                                         <div class="mb-2">
                                            <label for="simple-send-btn-bg" class="form-label small">Send Button Background:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-send-btn-bg" value="#0d6efd" title="Send button background color">
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-send-btn-text" class="form-label small">Send Button Text:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-send-btn-text" value="#ffffff" title="Send button text color">
                                        </div>
                                         <div class="mb-2">
                                            <label for="simple-reset-btn-bg" class="form-label small">Reset Button Background:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-reset-btn-bg" value="#6c757d" title="Reset button background color">
                                        </div>
                                        <div class="mb-2">
                                            <label for="simple-reset-btn-text" class="form-label small">Reset Button Text:</label>
                                            <input type="color" class="form-control form-control-color form-control-sm" id="simple-reset-btn-text" value="#ffffff" title="Reset button text color">
                                        </div>
                                    </div>
                                </div>
                                <hr>
                                <button type="button" class="btn btn-outline-secondary btn-sm" id="reset-simple-display">Reset Simple Settings</button>
                            </div>
                            <div class="tab-pane fade" id="advanced-display-pane" role="tabpanel" aria-labelledby="advanced-display-tab" tabindex="0">
                                {{ form.custom_css|as_crispy_field }}
                                <button type="button" class="btn btn-outline-secondary btn-sm mt-2" id="reset-advanced-display">Reset CSS</button>
                            </div>
                        </div>
                    </div>

                    {# Permissions Tab Pane for Standard Assistants #}
                    <div class="tab-pane fade" id="permissions-pane-standard" role="tabpanel" aria-labelledby="permissions-tab-standard" tabindex="0">
                        <h5>Manage Permissions</h5>
                        <p class="text-muted small">Control who can access and manage this assistant.</p>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">Public Access</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check form-switch mb-3">
                                            {{ form.is_public }}
                                            <label class="form-check-label" for="{{ form.is_public.id_for_label }}">
                                                Make this assistant public
                                            </label>
                                            <div class="form-text small">
                                                Public assistants are accessible to all authenticated users.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">Status</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check form-switch mb-3">
                                            {{ form.is_active }}
                                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                                Active
                                            </label>
                                            <div class="form-text small">
                                                Inactive assistants cannot be accessed by any users.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">Viewer Access</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="add-viewer-btn-standard" data-bs-toggle="modal" data-bs-target="#addViewerModal">
                                    <i class="bi bi-plus-lg"></i> Add Viewer
                                </button>
                            </div>
                            <div class="card-body">
                                <p class="text-muted small">
                                    Viewers can access this assistant even if it's private, but cannot edit it.
                                </p>

                                <div id="viewers-list-standard">
                                    {% if assistant_viewers %}
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>User</th>
                                                        <th>Email</th>
                                                        <th>Added</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for viewer in assistant_viewers %}
                                                    <tr>
                                                        <td>{{ viewer.get_full_name|default:viewer.username }}</td>
                                                        <td>{{ viewer.email }}</td>
                                                        <td>{{ viewer.date_added|date:"M d, Y" }}</td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-outline-danger remove-viewer-btn"
                                                                    data-viewer-id="{{ viewer.id }}"
                                                                    data-viewer-name="{{ viewer.get_full_name|default:viewer.username }}">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    {% else %}
                                        <div class="alert alert-info">
                                            <i class="bi bi-info-circle me-2"></i>
                                            No viewers have been added to this assistant.
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            {# Submit/Cancel #}
            <div class="mt-4 pt-3 border-top">
                <button type="submit" class="btn btn-primary" id="submit-button">{{ action }} Assistant</button>
                <a href="{% url 'assistants:list' company.id %}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>

        <script>
            // Add form submission debugging and handling
            document.addEventListener('DOMContentLoaded', function() {
                const form = document.querySelector('form');

                if (form) {
                    // Add event listener to the form submit event
                    form.addEventListener('submit', function(e) {
                        // Don't prevent default - let the form submit normally

                        // Log form data for debugging
                        console.log('Form is being submitted');

                        // Check if FormData API is available
                        if (window.FormData) {
                            const formData = new FormData(form);
                            console.log('Form contains logo file:', formData.has('logo'));
                            console.log('Form contains avatar file:', formData.has('avatar'));

                            // Log all form data entries for debugging
                            for (let [key, value] of formData.entries()) {
                                if ((key === 'logo' || key === 'avatar') && value instanceof File && value.name) {
                                    console.log(`${key} file in form submission:`, key, value.name, value.size, value.type);
                                }
                            }
                        }
                    });
                }
            });
        </script>
    </div>
</div>

{# REMOVED TinyMCE CDN Script #}

{# All your JS goes here #}
<script>
// console.log("Assistant Form JS: Script start."); // Removed log

document.addEventListener('DOMContentLoaded', function() {
// console.log("Assistant Form JS: DOMContentLoaded event fired."); // Removed log

    // --- REMOVED TinyMCE Initialization for static fields ---

    // --- Standard Assistant JS (Word Count, Conditional Duration Fields) ---
    // Get elements for both standard and support tabs
    const descriptionTextarea = document.getElementById('id_description');
    const wordCountDisplay = document.getElementById('description-word-count');
    // For support tab, we need to query by name since the ID might be the same
    const descriptionTextareaSupport = document.querySelector('#general-settings-pane-support textarea[name="description"]');
    const wordCountDisplaySupport = document.getElementById('description-word-count-support');
    const wordLimit = 500;

    function updateWordCount(textarea, display) {
        if (!textarea || !display) return;
        const text = textarea.value.trim();
        const words = text === '' ? 0 : text.split(/\s+/).filter(Boolean).length;
        display.textContent = `${words}/${wordLimit} words`;
        if (words > wordLimit) {
            display.classList.add('text-danger');
            display.classList.remove('text-muted');
        } else {
            display.classList.remove('text-danger');
            display.classList.add('text-muted');
        }
    }

    // Setup for standard assistant
    if (descriptionTextarea && wordCountDisplay) {
        descriptionTextarea.addEventListener('input', function() {
            updateWordCount(descriptionTextarea, wordCountDisplay);
        });
        updateWordCount(descriptionTextarea, wordCountDisplay); // Initial count
    }

    // Setup for support/community assistant
    if (descriptionTextareaSupport && wordCountDisplaySupport) {
        descriptionTextareaSupport.addEventListener('input', function() {
            updateWordCount(descriptionTextareaSupport, wordCountDisplaySupport);
        });
        updateWordCount(descriptionTextareaSupport, wordCountDisplaySupport); // Initial count
    }

    const requestTierSelect = document.getElementById('id_request_new_tier');
    const requestFeaturedCheckbox = document.getElementById('id_request_featured_status');
    const tierDurationWrapper = document.getElementById('tier-duration-wrapper');
    const featuredDurationWrapper = document.getElementById('featured-duration-wrapper');
    const standardTierValue = "{{ Assistant.TIER_STANDARD }}";
    const currentTierValue = "{{ assistant.tier }}";
    const currentFeaturedStatusRaw = "{{ assistant.is_featured|yesno:'true,false' }}";
    const currentFeaturedStatus = currentFeaturedStatusRaw === 'true';

    console.log("DEBUG: Assistant Duration Fields - Elements found:", {
        requestTierSelect: !!requestTierSelect,
        requestFeaturedCheckbox: !!requestFeaturedCheckbox,
        tierDurationWrapper: !!tierDurationWrapper,
        featuredDurationWrapper: !!featuredDurationWrapper
    });
    console.log("DEBUG: Assistant Duration Fields - Values:", {
        standardTierValue,
        currentTierValue,
        currentFeaturedStatus
    });

    function toggleDurationFields() {
        let showTierDuration = false;
        let showFeaturedDuration = false;

        // Show duration if a tier is selected (regardless of current tier)
        if (requestTierSelect && requestTierSelect.value && requestTierSelect.value !== standardTierValue) {
            showTierDuration = true;
        }

        // Show duration if checkbox is checked (regardless of current status)
        if (requestFeaturedCheckbox && requestFeaturedCheckbox.checked) {
            showFeaturedDuration = true;
        }

        console.log("DEBUG: toggleDurationFields - Conditions:", {
            showTierDuration,
            showFeaturedDuration,
            requestTierValue: requestTierSelect ? requestTierSelect.value : 'N/A',
            requestFeaturedChecked: requestFeaturedCheckbox ? requestFeaturedCheckbox.checked : 'N/A'
        });

        if (tierDurationWrapper) {
            tierDurationWrapper.style.display = showTierDuration ? 'block' : 'none';
            console.log(`Assistant Tier Duration Visible: ${showTierDuration} (Requested Tier: ${requestTierSelect ? requestTierSelect.value : 'N/A'})`);
        } else {
            console.error("Assistant tier duration wrapper not found");
        }

        if (featuredDurationWrapper) {
            featuredDurationWrapper.style.display = showFeaturedDuration ? 'block' : 'none';
            console.log(`Assistant Featured Duration Visible: ${showFeaturedDuration} (Requested Featured Checked: ${requestFeaturedCheckbox ? requestFeaturedCheckbox.checked : 'N/A'})`);
        } else {
            console.error("Assistant featured duration wrapper not found");
        }
    }

    if (requestTierSelect) {
        requestTierSelect.addEventListener('change', toggleDurationFields);
        console.log("Added change listener to assistant request tier select.");
    } else {
        console.error("Assistant request tier select element not found.");
    }

    if (requestFeaturedCheckbox) {
        requestFeaturedCheckbox.addEventListener('change', toggleDurationFields);
        console.log("Added change listener to assistant request featured checkbox.");
    } else {
        console.error("Assistant request featured checkbox element not found.");
    }

    console.log("Running initial toggleDurationFields on page load.");
    toggleDurationFields(); // Initial check

    // --- Display Controls JS ---
    function setupDisplayControls(prefix, isSupportType = false) {
        // console.log(`Setting up display controls with prefix: ${prefix}, isSupport: ${isSupportType}`); // Removed log
        const customCssTextarea = document.getElementById('id_custom_css');
        if (!customCssTextarea) {
            console.error("Display Controls JS Error: Custom CSS textarea (#id_custom_css) not found."); // Keep error log
            return; // Cannot proceed without the target textarea
        }

        // --- Get references to all simple controls ---
        const controls = {
            pageBg: document.getElementById(`${prefix}-page-bg-color`),
            chatBg: document.getElementById(`${prefix}-chat-bg-color`),
            bodyText: document.getElementById(`${prefix}-body-text-color`),
            fontFamily: document.getElementById(`${prefix}-font-family`),
            baseFontSize: document.getElementById(`${prefix}-base-font-size`),
            titleColor: document.getElementById(`${prefix}-title-color`),
            titleFontSize: document.getElementById(`${prefix}-title-font-size`),
            userMsgBg: document.getElementById(`${prefix}-user-msg-bg`),
            userMsgText: document.getElementById(`${prefix}-user-msg-text`),
            assistantMsgBg: document.getElementById(`${prefix}-assistant-msg-bg`),
            assistantMsgText: document.getElementById(`${prefix}-assistant-msg-text`),
            inputBg: document.getElementById(`${prefix}-input-bg-color`),
            inputText: document.getElementById(`${prefix}-input-text-color`),
            sendBtnBg: document.getElementById(`${prefix}-send-btn-bg`),
            sendBtnText: document.getElementById(`${prefix}-send-btn-text`),
            resetBtnBg: document.getElementById(`${prefix}-reset-btn-bg`),
            resetBtnText: document.getElementById(`${prefix}-reset-btn-text`),
            // Support-specific controls (will be null if not isSupportType)
            navBg: isSupportType ? document.getElementById(`${prefix}-nav-bg-color`) : null,
            navText: isSupportType ? document.getElementById(`${prefix}-nav-text-color`) : null,
            navActiveBg: isSupportType ? document.getElementById(`${prefix}-nav-active-bg-color`) : null,
            navActiveText: isSupportType ? document.getElementById(`${prefix}-nav-active-text-color`) : null,
            questionBg: isSupportType ? document.getElementById(`${prefix}-question-bg-color`) : null,
            questionText: isSupportType ? document.getElementById(`${prefix}-question-text-color`) : null,
        };

        // --- Define default values ---
        const defaults = {
            pageBg: "#ffffff", chatBg: "#f8f9fa", bodyText: "#212529", fontFamily: "", baseFontSize: "16",
            titleColor: "#000000", titleFontSize: "24", userMsgBg: "#e1f5fe", userMsgText: "#000000",
            assistantMsgBg: "#f1f8e9", assistantMsgText: "#000000", inputBg: "#ffffff", inputText: "#000000",
            sendBtnBg: "#0d6efd", sendBtnText: "#ffffff", resetBtnBg: "#6c757d", resetBtnText: "#ffffff",
            // Support defaults
            navBg: "#eeeeee", navText: "#333333", navActiveBg: "#0d6efd", navActiveText: "#ffffff",
            questionBg: "#f0f0f0", questionText: "#0056b3"
        };

        // --- Function to generate CSS ---
        function generateCssFromSimple() {
            let css = `/* Generated from Simple Settings - Editing here directly might be overwritten */\n\n`;
            css += `/* General Page Styles */\n`;
            css += `.chat-view-container, body {\n`; // Apply to body as well for full page background
            if (controls.pageBg?.value && controls.pageBg.value !== defaults.pageBg) css += `  background-color: ${controls.pageBg.value} !important;\n`;
            if (controls.bodyText?.value && controls.bodyText.value !== defaults.bodyText) css += `  color: ${controls.bodyText.value};\n`;
            if (controls.fontFamily?.value) css += `  font-family: ${controls.fontFamily.value};\n`;
            if (controls.baseFontSize?.value && controls.baseFontSize.value !== defaults.baseFontSize) css += `  font-size: ${controls.baseFontSize.value}px;\n`;
            css += `}\n\n`;

            css += `/* Chat Box Area */\n`;
            css += `#chat-box {\n`; // Assuming chat box has id="chat-box"
            if (controls.chatBg?.value && controls.chatBg.value !== defaults.chatBg) css += `  background-color: ${controls.chatBg.value};\n`;
            css += `}\n\n`;

            css += `/* Titles (e.g., Assistant Name) */\n`;
            css += `.assistant-title {\n`; // Assuming a class for the title
            if (controls.titleColor?.value && controls.titleColor.value !== defaults.titleColor) css += `  color: ${controls.titleColor.value};\n`;
            if (controls.titleFontSize?.value && controls.titleFontSize.value !== defaults.titleFontSize) css += `  font-size: ${controls.titleFontSize.value}px;\n`;
            css += `}\n\n`;

            css += `/* Messages */\n`;
            css += `.message.user-message {\n`;
            if (controls.userMsgBg?.value && controls.userMsgBg.value !== defaults.userMsgBg) css += `  background-color: ${controls.userMsgBg.value};\n`;
            if (controls.userMsgText?.value && controls.userMsgText.value !== defaults.userMsgText) css += `  color: ${controls.userMsgText.value};\n`;
            css += `}\n`;
            css += `.message.assistant-message {\n`;
            if (controls.assistantMsgBg?.value && controls.assistantMsgBg.value !== defaults.assistantMsgBg) css += `  background-color: ${controls.assistantMsgBg.value};\n`;
            if (controls.assistantMsgText?.value && controls.assistantMsgText.value !== defaults.assistantMsgText) css += `  color: ${controls.assistantMsgText.value};\n`;
            css += `}\n\n`;

            css += `/* Input Area */\n`;
            css += `#message-input {\n`; // Assuming input has id="message-input"
            if (controls.inputBg?.value && controls.inputBg.value !== defaults.inputBg) css += `  background-color: ${controls.inputBg.value};\n`;
            if (controls.inputText?.value && controls.inputText.value !== defaults.inputText) css += `  color: ${controls.inputText.value};\n`;
            css += `}\n\n`;

            css += `/* Buttons */\n`;
            css += `#send-button {\n`; // Assuming send button has id="send-button"
            if (controls.sendBtnBg?.value && controls.sendBtnBg.value !== defaults.sendBtnBg) css += `  background-color: ${controls.sendBtnBg.value};\n`;
            if (controls.sendBtnText?.value && controls.sendBtnText.value !== defaults.sendBtnText) css += `  color: ${controls.sendBtnText.value};\n`;
            css += `}\n`;
            css += `#reset-button {\n`; // Assuming reset button has id="reset-button"
            if (controls.resetBtnBg?.value && controls.resetBtnBg.value !== defaults.resetBtnBg) css += `  background-color: ${controls.resetBtnBg.value};\n`;
            if (controls.resetBtnText?.value && controls.resetBtnText.value !== defaults.resetBtnText) css += `  color: ${controls.resetBtnText.value};\n`;
            css += `}\n\n`;

            // --- Add Support-Specific CSS ---
            if (isSupportType) {
                css += `/* Navigation Sidebar */\n`;
                css += `.chat-sidebar {\n`; // Assuming sidebar has class="chat-sidebar"
                if (controls.navBg?.value && controls.navBg.value !== defaults.navBg) css += `  background-color: ${controls.navBg.value};\n`;
                css += `}\n`;
                css += `.chat-sidebar .nav-link {\n`;
                if (controls.navText?.value && controls.navText.value !== defaults.navText) css += `  color: ${controls.navText.value};\n`;
                css += `}\n`;
                 css += `.chat-sidebar .nav-link.active {\n`;
                if (controls.navActiveBg?.value && controls.navActiveBg.value !== defaults.navActiveBg) css += `  background-color: ${controls.navActiveBg.value};\n`;
                if (controls.navActiveText?.value && controls.navActiveText.value !== defaults.navActiveText) css += `  color: ${controls.navActiveText.value};\n`;
                css += `}\n\n`;

                css += `/* Suggested Questions */\n`;
                css += `.suggested-question {\n`; // Assuming questions have class="suggested-question"
                if (controls.questionBg?.value && controls.questionBg.value !== defaults.questionBg) css += `  background-color: ${controls.questionBg.value};\n`;
                if (controls.questionText?.value && controls.questionText.value !== defaults.questionText) css += `  color: ${controls.questionText.value};\n`;
                css += `}\n`;
            }

            customCssTextarea.value = css;
            // console.log("Generated CSS:", css); // Removed log
        }

        // --- Function to reset simple controls ---
        function resetSimpleControls() {
            // console.log("Resetting simple controls"); // Removed log
            for (const key in controls) {
                if (controls[key]) { // Check if element exists
                    // Use the correct default value based on the key
                    controls[key].value = defaults[key];
                }
            }
            generateCssFromSimple(); // Regenerate CSS after resetting
        }


        // --- Add event listeners ---
        // Corrected the ID selector to match the actual HTML element ID
        const simpleControlsPane = document.getElementById(prefix); // <--- Corrected ID selector
        if (simpleControlsPane) {
             // Use event delegation on the pane for efficiency
             simpleControlsPane.addEventListener('input', function(event) {
                 // Check if the event target is one of the simple controls
                 if (event.target.matches('input[type="color"], input[type="number"], select')) {
                     generateCssFromSimple();
                 }
             });
             // 'change' might still be needed for select if 'input' doesn't fire reliably across browsers
             simpleControlsPane.addEventListener('change', function(event) {
                 if (event.target.matches('select')) {
                     generateCssFromSimple();
                 }
             });
        } else {
             console.error(`Display Controls JS Error: Could not find simple controls pane #${prefix}-pane`); // Keep error log
        }

        // Use the correct ID for the reset button based on the prefix structure
        const resetSimpleBtn = document.getElementById(`reset-${prefix}`);
        if (resetSimpleBtn) {
            resetSimpleBtn.addEventListener('click', resetSimpleControls);
        } else {
             console.error(`Display Controls JS Error: Could not find reset button #reset-${prefix}`); // Keep error log
        }

        // --- Initial CSS generation based on current values ---
        // Check if the custom CSS area is empty or only contains the comment
        const initialCss = customCssTextarea.value.trim();
        if (!initialCss || initialCss.startsWith('/* Generated from Simple Settings')) {
             // console.log("Initial CSS is empty or generated, running generateCssFromSimple()"); // Removed log
             generateCssFromSimple();
        } else {
             // console.log("Initial CSS has custom edits, not overwriting."); // Removed log
        }
    }

    // --- Initialize controls based on assistant type ---
    const assistantType = "{{ assistant.assistant_type|escapejs }}".trim();
    const isSupportType = assistantType === 'support';
    const isCommunityType = assistantType === 'community';
    const isAdvancedType = isSupportType || isCommunityType;

    if (isAdvancedType) {
        // Setup for Advanced Assistant Display Tab (Support or Community)
        if (document.getElementById('display-pane-support')) {
            // Use the correct prefix for the support simple pane ID
            setupDisplayControls('simple-display-support', true);
        } else {
            console.error("Could not find support display pane: #display-pane-support"); // Keep error log
        }
    } else {
        // Setup for Standard Assistant Display Tab
        if (document.getElementById('display-pane')) {
             // Use the correct prefix for the standard simple pane ID
            setupDisplayControls('simple-display', false);
        } else {
             console.error("Could not find standard display pane: #display-pane"); // Keep error log
        }
    }

    // --- Advanced Assistant Specific JS (Support & Community) ---
    // const assistantType = "{{ assistant.assistant_type|escapejs }}".trim(); // Already defined above
    // const isAdvancedType = isSupportType || isCommunityType; // Already defined above

    // --- Debugging Type Comparison ---
    // console.log(`Assistant Form JS: Checking assistant type. assistantType = "${assistantType}" (Length: ${assistantType.length})`); // Removed log
    // Log character codes for debugging potential hidden characters
    // let charCodes = [];
    // for (let i = 0; i < assistantType.length; i++) {
    //     charCodes.push(assistantType.charCodeAt(i));
    // }
    // console.log(`Assistant Form JS: Character codes for assistantType: [${charCodes.join(', ')}]`); // Removed log

    // Try includes() comparison as a less strict check
    // const isSupportType = assistantType.includes("support"); // Already defined above
    // console.log(`Assistant Form JS: Comparison result (assistantType.includes("support"))):`, isSupportType); // Removed log
    // --- End Debugging ---

    if (isAdvancedType) { // Use the result for both support and community assistants
        try { // Wrap the entire advanced assistant-specific logic in try...catch
            // console.log("Initializing Advanced Assistant specific JS..."); // Removed log

            // --- Navigation Settings Tab Logic (Formset based) ---
            // Define these variables *inside* the isSupportType block
            const navFormsetPrefix = '{{ nav_formset.prefix|default:"navitems" }}'; // Use prefix from context or default
        const navItemsContainer = document.getElementById('nav-items-container');
        const addNavItemBtn = document.getElementById('add-nav-item-btn');
        const emptyNavFormTemplate = document.getElementById('empty-nav-form'); // Get the template element
        const totalFormsInput = document.getElementById(`id_${navFormsetPrefix}-TOTAL_FORMS`);

        // --- Debugging Checks ---
        if (!addNavItemBtn) console.error("Add Nav Item JS Error: Button #add-nav-item-btn not found."); // Keep error log
        if (!emptyNavFormTemplate) console.error("Add Nav Item JS Error: Template #empty-nav-form not found."); // Keep error log
        if (!totalFormsInput) console.error(`Add Nav Item JS Error: Input #id_${navFormsetPrefix}-TOTAL_FORMS not found.`); // Keep error log
        if (!navItemsContainer) console.error("Add Nav Item JS Error: Container #nav-items-container not found."); // Keep error log
        // --- End Debugging Checks ---


        if (addNavItemBtn && emptyNavFormTemplate && totalFormsInput && navItemsContainer) {
            // console.log("Add Nav Item JS: Initializing event listener. Prefix:", navFormsetPrefix); // Removed log
            addNavItemBtn.addEventListener('click', function() {
                try { // Add try...catch for better error handling
                    // console.log("Add Nav Item JS: Add button clicked."); // Removed log
                    const formRegex = new RegExp(`${navFormsetPrefix}-__prefix__-`, 'g'); // Regex to replace prefix
                    let formCount = parseInt(totalFormsInput.value);
                    // console.log("Add Nav Item JS: Current form count:", formCount); // Removed log

                    // --- Robust Cloning ---
                    if (!emptyNavFormTemplate.content) {
                        console.error("Add Nav Item JS Error: Template element does not support .content"); // Keep error log
                        return;
                    }
                    const templateContent = emptyNavFormTemplate.content.cloneNode(true);
                    const newFormRow = templateContent.querySelector('.nav-item-row'); // Find the actual row div within the cloned content

                    if (!newFormRow) {
                         console.error("Add Nav Item JS Error: Could not find '.nav-item-row' inside the template content."); // Keep error log
                         return;
                    }
                    // console.log("Add Nav Item JS: Successfully cloned node:", newFormRow); // Removed log
                    // --- End Robust Cloning ---


                    // Update IDs and names in the cloned row
                    newFormRow.querySelectorAll('input, select, textarea, label').forEach(el => { // Include labels for 'for' attribute
                        ['id', 'name', 'for'].forEach(attr => {
                            let value = el.getAttribute(attr);
                            if (value) {
                                el.setAttribute(attr, value.replace(formRegex, `${navFormsetPrefix}-${formCount}-`));
                            }
                        });
                    });
                    // Update the outer div's ID as well
                    newFormRow.id = `${navFormsetPrefix}-${formCount}-row`;

                    // Append the new form row to the container
                    navItemsContainer.appendChild(newFormRow);
                     // console.log("Add Nav Item JS: Appended new row to container."); // Removed log

                    // Find the hidden ORDER input in the new row and set its value
                    const orderInput = newFormRow.querySelector(`input[name$="-ORDER"]`); // Find the ORDER field specifically
                    if (orderInput) {
                        // Set order based on the *new* total number of forms (which is formCount + 1)
                        // Django formset ordering often relies on this being the index relative to all forms.
                        orderInput.value = formCount; // Keep as 0-based index for now, Django handles reordering on save
                        // console.log(`Add Nav Item JS: Set ORDER for new form ${formCount} to ${orderInput.value}`); // Removed log
                    } else {
                        console.error(`Add Nav Item JS Error: Could not find ORDER input for new form ${formCount}`); // Keep error log
                    }

                    // Update the TOTAL_FORMS count
                    totalFormsInput.value = formCount + 1;
                    // console.log("Add Nav Item JS: Incremented TOTAL_FORMS to", totalFormsInput.value); // Removed log

                    // Trigger Edit Data UI regeneration after appending
                    // console.log("Add Nav Item JS: Triggering Edit Data UI regeneration after add."); // Removed log
                    triggerEditDataUIRegeneration(); // Call the regeneration function

                } catch (e) {
                    console.error("Add Nav Item JS Error during click handling:", e); // Keep error log
                }
            });
        } else {
            // Removed logging error if not support type, as it's expected not to find elements
            // if (assistantType === supportAssistantType) { ... }
        }

        // --- Edit Data Tab Logic ---
        const websiteDataContainer = document.getElementById('website-data-formsets-container');
        const initialWebsiteData = JSON.parse('{{ website_data_json|escapejs|default:"{}" }}');
        console.log('Initial website data:', initialWebsiteData);
        // Use navigation_items_data passed from view context for reliable IDs on initial load
        let initialNavItemsData = [];
        try {
            // Get navigation items from either the formset data or the passed context
            const navFormsContainer = document.getElementById('nav-items-container');
            if (navFormsContainer) {
                // Extract data from existing form fields
                initialNavItemsData = Array.from(navFormsContainer.querySelectorAll('.nav-item-row')).map(row => {
                    return {
                        id: row.querySelector('input[name$="-id"]')?.value,
                        label: row.querySelector('input[name$="-label"]')?.value,
                        section_type: row.querySelector('select[name$="-section_type"]')?.value,
                        // entry_count: row.querySelector('input[name$="-entry_count"]')?.value, // Removed
                        visible: row.querySelector('input[name$="-visible"]')?.checked,
                        order: row.querySelector('input[name$="-order"]')?.value
                    };
                });
            } else {
                // Fall back to passed context data if formset not rendered
                initialNavItemsData = JSON.parse('{{ navigation_items_data|escapejs|default:"[]" }}');
            }
            // console.log("Edit Data UI: Using navigation items data:", initialNavItemsData); // Removed log
        } catch (e) {
            console.error("Edit Data UI: Error initializing navigation items data:", e); // Keep error log
        }


        // Define fields for each section type
        const sectionFields = {
            "product": ["id", "name", "description", "benefits", "image", "price", "details"],
            "service": ["id", "name", "description", "details", "image"],
            "team": ["name", "role", "bio", "image"],
            "location": ["city", "address", "phone", "image"],
            "faq": ["question", "answer"]
        };

        // Helper function to create an input element
        function createInputElement(type, name, value, placeholder, labelText) {
            const wrapper = document.createElement('div');
            wrapper.className = 'mb-2'; // Add some margin

            const label = document.createElement('label');
            label.className = 'form-label small';
            label.textContent = labelText;
            label.htmlFor = `id_${name}`; // Associate label with input

            let input;
            if (type === 'textarea') {
                input = document.createElement('textarea');
                input.rows = 5; // Increased rows for better editing experience
                input.classList.add('apply-tinymce'); // Add class for TinyMCE initialization

                // Add data attributes for TinyMCE configuration
                input.dataset.enableImages = "true"; // Enable image uploads
                input.dataset.contentType = "html"; // Set content type to HTML
                input.dataset.allowImageUpload = "true"; // Explicitly enable image uploads
            } else {
                input = document.createElement('input');
                input.type = type;
            }
            input.name = name;
            // Use predictable ID based on name
            input.id = `id_${name}`;
            input.className = 'form-control form-control-sm';
            input.value = value || '';
            if (placeholder) {
                input.placeholder = placeholder;
            }

            wrapper.appendChild(label);
            wrapper.appendChild(input);

            return wrapper;
        }

        // --- REMOVED initializeEditDataTinyMCE function ---


         // Function to generate the data editing UI
         function generateDataEditingUI() {
             // console.log("--- generateDataEditingUI START ---"); // Removed log
             let tinyMceEditorIds = []; // RESTORED Array to store IDs for initialization

             // --- RESTORED TinyMCE editor removal logic ---
             if (typeof tinymce !== 'undefined' && websiteDataContainer) {
                 const editorsToRemove = websiteDataContainer.querySelectorAll('textarea.apply-tinymce'); // Find textareas with the target class
                 // console.log(`generateDataEditingUI: Found ${editorsToRemove.length} potential previous editors to remove.`); // Removed log
                 editorsToRemove.forEach(ta => {
                     const editorInstance = tinymce.get(ta.id);
                     if (ta.id && editorInstance) {
                         // console.log(`generateDataEditingUI: Removing existing editor instance for #${ta.id}`); // Removed log
                         editorInstance.remove(); // Use remove() which cleans up better than destroy() before re-init
                     } else if (ta.id) {
                         // console.log(`generateDataEditingUI: No active editor instance found for #${ta.id} during cleanup.`); // Optional log
                     }
                 });
             }
             // --- End editor removal ---

            if (!websiteDataContainer) {
                console.error("Edit Data UI Error: websiteDataContainer not found."); // Keep error log
                return;
            }
             if (!navItemsContainer) {
                console.error("Edit Data UI Error: navItemsContainer not found."); // Keep error log
                return;
            }
            websiteDataContainer.innerHTML = ''; // Clear previous content

            // Let's re-query excluding the template's content directly
            const actualNavRows = Array.from(navItemsContainer.children).filter(el => el.id !== 'empty-nav-form' && el.classList.contains('nav-item-row'));

            // console.log(`Edit Data UI: Found ${actualNavRows.length} actual navigation rows.`); // Removed log

            actualNavRows.forEach((row, index) => {
                // console.log(`Edit Data UI: Processing row ${index}`, row); // Removed log
                // --- Simplified Selectors - Target elements within specific columns ---
                const labelCol = row.querySelector('.col-md-3'); // Column for label
                const visibleCol = row.querySelector('.col-md-1'); // Column for visible checkbox
                const typeCol = row.querySelector('.col-md-2'); // Column for section type
                const deleteCol = row.querySelector('.col-md-4'); // Column for delete checkbox

                const labelInput = labelCol ? labelCol.querySelector('input') : null;
                const typeSelect = typeCol ? typeCol.querySelector('select') : null;
                const visibleCheckbox = visibleCol ? visibleCol.querySelector('input[type="checkbox"]') : null;
                const deleteCheckbox = deleteCol ? deleteCol.querySelector('input[type="checkbox"]') : null; // Check if marked for deletion
                // --- End Simplified Selectors ---

                // Log found elements for debugging
                // console.log(`Edit Data UI: Row ${index} - Found labelInput:`, labelInput); // Removed log
                // console.log(`Edit Data UI: Row ${index} - Found typeSelect:`, typeSelect); // Removed log
                // console.log(`Edit Data UI: Row ${index} - Found visibleCheckbox:`, visibleCheckbox); // Removed log

                // Skip if row is marked for deletion or not visible
                // REMOVED check for !countInput
                // Add specific checks and logs for debugging which element is missing
                if (!labelInput) {
                    console.warn(`Edit Data UI: Skipping row ${index} - Could not find label input.`); // Keep warn log
                    return;
                }
                 if (!typeSelect) {
                    console.warn(`Edit Data UI: Skipping row ${index} - Could not find section_type select.`); // Keep warn log
                    return;
                }
                 if (!visibleCheckbox) {
                    console.warn(`Edit Data UI: Skipping row ${index} - Could not find visible checkbox.`); // Keep warn log
                    return;
                }
                // Original check (now redundant due to specific checks above, but keep for safety)
                // if (!labelInput || !typeSelect || !visibleCheckbox) {
                //      console.warn(`Edit Data UI: Skipping row ${index} due to missing core elements (label, type, or visible).`);
                //      return;
                // }
                 if (deleteCheckbox && deleteCheckbox.checked) {
                     // console.log(`Edit Data UI: Skipping row ${index} because it is marked for deletion.`); // Removed log
                     return;
                 }
                 if (!visibleCheckbox.checked) {
                     // console.log(`Edit Data UI: Skipping row ${index} because it is not visible.`); // Removed log
                     return;
                 }


                const navLabel = labelInput.value.trim();
                const sectionType = typeSelect.value;
                // let entryCount = 1; // Removed - No longer needed

                 // --- Get stable Item ID ---
                 const idInput = row.querySelector('input[name$="-id"]');
                 const itemId = idInput ? idInput.value : null;
                 if (!itemId) {
                     console.warn(`Edit Data UI: Skipping row ${index} - Could not find item ID.`); // Keep warn log
                     return; // Cannot proceed without item ID
                 }
                 const stableIdPart = `item_${itemId}`; // Use "item_ID" as the stable identifier part
                 // --- End get stable Item ID ---


                const sectionDiv = document.createElement('div');
                sectionDiv.className = 'mb-4 p-3 border rounded';
                 sectionDiv.innerHTML = `<h6 class="fw-bold border-bottom pb-2 mb-3">${navLabel}</h6>`;

                 // Use uniqueIdPart for data lookup if needed, or adjust backend data structure
                 // For now, assume backend might use label or a dedicated field. We'll use uniqueIdPart for the *element ID*.
                 // REMOVED incorrect declaration: const sectionData = initialWebsiteData[navLabel] || (sectionType === 'text' ? '' : []); // Example: using navLabel for data lookup
                  // console.log(`Edit Data UI: Processing section "${navLabel}" (stableIdPart: ${stableIdPart}) with type "${sectionType}".`); // Removed log

                  // --- CORRECTED DATA LOOKUP: Use stableIdPart ---
                  const sectionData = initialWebsiteData[stableIdPart] || (sectionType === 'text' ? '' : []);
                  console.log(`Edit Data UI: Looked up data using key "${stableIdPart}". Found:`, sectionData); // Keep this log for debugging

                  if (sectionType === 'text') {
                      // console.log(`generateDataEditingUI: Section type is 'text' for "${navLabel}" (stableIdPart: ${stableIdPart}). Creating textarea.`); // Removed log
                     // Construct name and ID using the stableIdPart (item_ID)
                     const name = `data_${stableIdPart}_content`; // Name uses stableIdPart (e.g., data_item_17_content)
                     const id = `id_${name}`; // Construct ID based on name
                     // Extract the content from sectionData correctly
                     let value = '';
                     if (sectionData) {
                         // If sectionData is a string, use it directly
                         if (typeof sectionData === 'string') {
                             value = sectionData;
                         }
                         // If sectionData is an object with a content property, use that
                         else if (typeof sectionData === 'object' && sectionData.content) {
                             value = sectionData.content;
                         }
                     }
                     console.log(`Content for ${navLabel} (${stableIdPart}):`, value);
                     // console.log(`generateDataEditingUI: Textarea details - ID: ${id}, Name: ${name}`); // Removed log
                     const textareaWrapper = createInputElement('textarea', name, value, `Content for ${navLabel}...`, `${navLabel} Content`);
                     sectionDiv.appendChild(textareaWrapper);

                     // --- RESTORED adding ID to tinyMceEditorIds array ---
                     if (id) {
                         tinyMceEditorIds.push(`#${id}`); // Add the selector to the array
                         // console.log(`generateDataEditingUI: Added #${id} to initialization list.`); // Removed log
                     }
                     // --- End adding ID ---

                 } else if (sectionFields[sectionType]) {
                    const fields = sectionFields[sectionType];
                    // Use data looked up by uniqueIdPart
                    const existingData = Array.isArray(sectionData) ? sectionData : [];
                    const numEntriesToAdd = Math.max(1, existingData.length);
                    // console.log(`Edit Data UI: Adding ${numEntriesToAdd} entries for ${sectionType} section "${navLabel}" (uniqueIdPart: ${uniqueIdPart})`); // Removed log
                    for (let i = 0; i < numEntriesToAdd; i++) {
                        const entryDiv = document.createElement('div');
                        entryDiv.className = 'website-data-entry border-bottom pb-2 mb-2';
                        entryDiv.innerHTML = `<p class="small text-muted mb-1">Entry ${i + 1}</p>`;
                        const entryData = existingData[i] || {};

                        fields.forEach(field => {
                            // Construct name and ID using stableIdPart and entry index
                            const name = `data_${stableIdPart}_${i}_${field}`; // Name uses stableIdPart (e.g., data_item_18_0_name)
                            const id = `id_${name}`;
                            const value = entryData[field] || '';
                            const placeholder = field.charAt(0).toUpperCase() + field.slice(1);
                            entryDiv.appendChild(
                                createInputElement('text', name, value, placeholder, placeholder) // Pass correct name
                            );
                        });
                        sectionDiv.appendChild(entryDiv);
                    }
                } else {
                     console.error(`Edit Data UI: Unsupported section type "${sectionType}" for section "${navLabel}"`); // Keep error log
                     sectionDiv.innerHTML += `<p class="text-danger small">Unsupported section type: ${sectionType}</p>`;
                 }

             websiteDataContainer.appendChild(sectionDiv);

             }); // End actualNavRows.forEach

              if (websiteDataContainer.innerHTML === '') {
                 websiteDataContainer.innerHTML = '<p class="text-muted"><em>No visible navigation items defined. Add items in the "Navigation Settings" tab to edit their data here.</em></p>';
             }

             // --- Enhanced TinyMCE initialization logic ---
             if (tinyMceEditorIds.length > 0 && typeof tinymce !== 'undefined') {
                 const selectorString = tinyMceEditorIds.join(',');
                 console.log(`generateDataEditingUI: Initializing TinyMCE for selectors: ${selectorString}`); // Keep this log for confirmation
                 tinymce.init({
                     selector: selectorString, // Target all collected IDs
                     height: 360,
                     width: 'auto',
                     menubar: true,
                     plugins: 'advlist autolink lists link image charmap print preview anchor ' +
                              'searchreplace visualblocks code fullscreen ' +
                              'insertdatetime media table paste code help wordcount imagetools ' +
                              'emoticons hr pagebreak nonbreaking toc textpattern codesample ' +
                              'quickbars directionality visualchars template save importcss',
                     toolbar1: 'formatselect fontselect fontsizeselect styleselect | ' +
                              'bold italic underline strikethrough subscript superscript | forecolor backcolor | ' +
                              'alignleft aligncenter alignright alignjustify | ' +
                              'bullist numlist outdent indent | ltr rtl',
                     toolbar2: 'undo redo | cut copy paste pastetext | searchreplace | ' +
                              'link unlink image media table emoticons charmap | ' +
                              'hr pagebreak nonbreaking template | removeformat code | fullscreen preview | help',
                     toolbar3: 'insertdatetime | visualchars visualblocks | codesample blockquote cite | save print',
                     // Hide HTML elements in the edit interface
                     hidden_input: false,
                     element_format: 'html',
                     entity_encoding: 'raw',
                     convert_fonts_to_spans: false,
                     verify_html: false,
                     visual: false,
                     content_css: false,

                     // Enhanced Image upload settings
                     images_upload_url: '{% url "assistants:tinymce-upload" %}',
                     file_picker_types: 'image',
                     automatic_uploads: true,
                     images_reuse_filename: false,
                     paste_data_images: true,
                     image_advtab: true,
                     image_title: true,
                     image_caption: true,
                     image_dimensions: true,
                     image_class_list: [
                         {title: 'None', value: ''},
                         {title: 'Responsive', value: 'img-fluid'},
                         {title: 'Left Aligned', value: 'float-left mr-3'},
                         {title: 'Right Aligned', value: 'float-right ml-3'},
                         {title: 'Centered', value: 'mx-auto d-block'}
                     ],
                     // Enable drag and resize of images
                     resize_img_proportional: true,
                     object_resizing: 'img,table',
                     resize: true,

                     // File picker callback for image uploads
                     file_picker_callback: function (callback, value, meta) {
                         if (meta.filetype === 'image') {
                             var input = document.createElement('input');
                             input.setAttribute('type', 'file');
                             input.setAttribute('accept', 'image/*');
                             input.onchange = function () {
                                 var file = this.files[0];
                                 var formData = new FormData();
                                 formData.append('file', file);

                                 // Get CSRF token from cookie
                                 function getCookie(name) {
                                     let cookieValue = null;
                                     if (document.cookie && document.cookie !== '') {
                                         const cookies = document.cookie.split(';');
                                         for (let i = 0; i < cookies.length; i++) {
                                             const cookie = cookies[i].trim();
                                             if (cookie.substring(0, name.length + 1) === (name + '=')) {
                                                 cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                                                 break;
                                             }
                                         }
                                     }
                                     return cookieValue;
                                 }
                                 const csrftoken = getCookie('csrftoken');

                                 // Show upload indicator
                                 const uploadIndicator = document.createElement('div');
                                 uploadIndicator.className = 'upload-indicator';
                                 uploadIndicator.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Uploading image...';
                                 document.body.appendChild(uploadIndicator);

                                 fetch('{% url "assistants:tinymce-upload" %}', {
                                     method: 'POST',
                                     body: formData,
                                     credentials: 'same-origin',
                                     headers: {
                                         'X-CSRFToken': csrftoken
                                     }
                                 })
                                 .then(response => {
                                     if (!response.ok) {
                                         throw new Error(`HTTP error! Status: ${response.status}`);
                                     }
                                     return response.json();
                                 })
                                 .then(data => {
                                     if (data.location) {
                                         console.log('Image uploaded successfully:', data.location);
                                         callback(data.location, { alt: file.name });
                                     } else {
                                         console.error('Image upload failed:', data.error || 'Unknown error');
                                         alert('Image upload failed: ' + (data.error || 'Unknown error'));
                                     }
                                 })
                                 .catch(error => {
                                     console.error('Image upload error:', error);
                                     alert('Image upload failed: ' + error.message);
                                 })
                                 .finally(() => {
                                     // Remove upload indicator
                                     if (uploadIndicator && uploadIndicator.parentNode) {
                                         uploadIndicator.parentNode.removeChild(uploadIndicator);
                                     }
                                 });
                             };
                             input.click();
                         }
                     },

                     // Additional settings
                     content_style: `
                         body { font-family:Helvetica,Arial,sans-serif; font-size:14px; }
                         img { max-width: 100%; height: auto; }
                         img.img-fluid { max-width: 100%; height: auto; }
                         img.float-left { float: left; margin-right: 1rem; margin-bottom: 0.5rem; }
                         img.float-right { float: right; margin-left: 1rem; margin-bottom: 0.5rem; }
                         img.mx-auto.d-block { display: block; margin-left: auto; margin-right: auto; }
                         figure.image { display: inline-block; margin: 0; }
                         figure.image figcaption { font-size: 0.8em; color: #555; text-align: center; }
                         table { border-collapse: collapse; width: 100%; }
                         table td, table th { border: 1px solid #ddd; padding: 8px; }
                         blockquote { border-left: 3px solid #ccc; margin-left: 1.5em; padding-left: 1em; }
                         pre { background-color: #f5f5f5; padding: 1em; border-radius: 3px; }
                         .mce-content-body [data-mce-selected="inline-boundary"] { background-color: #b4d7ff; }
                     `,
                     statusbar: true,

                     // Editor setup function
                     setup: function (editor) {
                         // Create word count warning element
                         const wordCountLimit = 500;
                         let wordCountWarning = null;

                         // Function to check word count and display warning
                         function checkWordCount() {
                             const content = editor.getContent({format: 'text'});
                             const wordCount = content.split(/\s+/).filter(Boolean).length;

                             if (!wordCountWarning) {
                                 // Create warning element if it doesn't exist
                                 wordCountWarning = document.createElement('p');
                                 wordCountWarning.className = 'word-count-warning form-text small mt-1';
                                 const editorContainer = editor.getContainer();
                                 if (editorContainer && editorContainer.parentNode) {
                                     editorContainer.parentNode.insertBefore(wordCountWarning, editorContainer.nextSibling);
                                 }
                             }

                             // Update warning text
                             wordCountWarning.textContent = `${wordCount}/${wordCountLimit} words`;

                             // Add warning styling if over limit
                             if (wordCount > wordCountLimit) {
                                 wordCountWarning.classList.add('text-danger');
                                 wordCountWarning.classList.remove('text-muted');
                             } else {
                                 wordCountWarning.classList.remove('text-danger');
                                 wordCountWarning.classList.add('text-muted');
                             }
                         }

                         // Add event handlers
                         editor.on('change keyup paste', function () {
                             editor.save();
                             // Also update the textarea directly
                             const textarea = document.getElementById(editor.id);
                             if (textarea) {
                                 textarea.value = editor.getContent();
                             }

                             // Check word count
                             checkWordCount();
                         });

                         editor.on('init', function(evt) {
                             console.log(`TinyMCE Dynamic Init Event: Editor #${evt.target.id} initialized successfully.`);
                             // Force the editor to show its content
                             const textarea = document.getElementById(editor.id);
                             if (textarea && textarea.value) {
                                 editor.setContent(textarea.value);
                                 console.log(`Set content for editor #${editor.id} from textarea value`);
                             }

                             // Initial word count check
                             checkWordCount();
                         });

                         editor.on('error', function(e) {
                             console.error(`TinyMCE Dynamic Error Event for #${editor.id}:`, e); // Keep error log
                         });
                     }
                 })
                 .then(editors => {
                     if (editors && editors.length > 0) {
                         console.log(`generateDataEditingUI: tinymce.init SUCCESS for ${editors.length} editors.`); // Keep success log
                     } else {
                         console.warn(`generateDataEditingUI: tinymce.init completed for selector "${selectorString}", but no editor instances returned.`); // Keep warn log
                     }
                 }).catch(error => {
                     console.error(`generateDataEditingUI: tinymce.init FAILED for selector "${selectorString}". Error:`, error); // Keep error log
                 });
             } else if (tinyMceEditorIds.length > 0 && typeof tinymce === 'undefined') {
                 console.error(`generateDataEditingUI: TinyMCE object 'tinymce' is undefined when trying to init collected IDs. Check script loading.`); // Keep error log
             }
             // --- End TinyMCE initialization ---

             // console.log("--- generateDataEditingUI END ---"); // Removed log
         }


        // Initial generation on page load
        if (isAdvancedType) {
            // console.log("Initial call to generateDataEditingUI for advanced assistant."); // Removed log
            generateDataEditingUI(); // This will now handle init within createInputElement
        } else {
            // Handle static editors if needed for non-advanced types
        }


        // --- Simplified Event Listeners for Regenerating Edit Data UI ---
        let editDataTimeout; // Debounce timer

        function triggerEditDataUIRegeneration(delay = 150) { // Increased delay slightly
             clearTimeout(editDataTimeout);
             editDataTimeout = setTimeout(() => {
                 // console.log("Regenerating Edit Data UI..."); // Removed log
                 // Cleanup is now handled at the start of generateDataEditingUI
                 generateDataEditingUI(); // Regenerate UI (which includes initialization at the end)
             }, delay);
        }

        // 1. After adding a new form row via the button (handled within button click listener)
        // No separate listener needed here, it's triggered inside the add button's click handler


        // 2. When relevant fields change within the nav items container
        if (navItemsContainer) { // Check if container exists
            navItemsContainer.addEventListener('change', function(event) {
                // Check if the changed element is relevant for regenerating the data fields
                if (event.target.matches('input[name$="-DELETE"]') ||
                    event.target.matches('input[name$="-visible"]') ||
                    event.target.matches('select[name$="-section_type"]')) {
                     // Removed check for entry_count input
                     // console.log("Regenerating Edit Data UI due to change event on:", event.target); // Removed log
                     triggerEditDataUIRegeneration();
                }
            });

            // 3. When the label changes (needed for uniqueId derivation for *new* rows)
            // Use 'input' event for immediate feedback as user types, but debounce it
            navItemsContainer.addEventListener('input', function(event) {
                 if (event.target.matches('input[name$="-label"]')) {
                     // console.log("Regenerating Edit Data UI due to label input."); // Removed log
                     triggerEditDataUIRegeneration(300); // Debounce label changes
                 }
            });
        }


        // --- Context Tab Logic ---
        // Usually handled directly by the form field, no extra JS needed unless for validation/features.

        // --- Analyze & Suggest Tab Logic ---
        const generateSuggestionsBtn = document.getElementById('generate-suggestions-btn');
        const suggestionsOutput = document.getElementById('suggestions-output');
        const generateSuggestionsUrl = "{% url 'assistants:generate_suggestions' company_id=assistant.company.id assistant_id=assistant.id %}"; // Get URL from Django

        if (generateSuggestionsBtn && suggestionsOutput) {
            generateSuggestionsBtn.addEventListener('click', function() {
                suggestionsOutput.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div> <em>Generating suggestions...</em>';
                generateSuggestionsBtn.disabled = true; // Disable button while loading

                fetch(generateSuggestionsUrl)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.suggestions && data.suggestions.length > 0) {
                            let html = '<ul class="list-group list-group-flush small">';
                            data.suggestions.forEach(q => {
                                html += `<li class="list-group-item">${q}</li>`;
                            });
                            html += '</ul>';
                            suggestionsOutput.innerHTML = html;
                        } else if (data.error) {
                             suggestionsOutput.innerHTML = `<p class="text-danger small"><em>Error: ${data.error}</em></p>`;
                        }
                         else {
                            suggestionsOutput.innerHTML = '<p class="text-muted small"><em>No suggestions generated. Ensure navigation items are defined.</em></p>';
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching suggestions:', error); // Keep error log
                        suggestionsOutput.innerHTML = `<p class="text-danger small"><em>Error fetching suggestions. See console for details.</em></p>`;
                    })
                    .finally(() => {
                         generateSuggestionsBtn.disabled = false; // Re-enable button
                    });
            });
        }

            // --- Reordering Logic for Navigation Items ---
            if (navItemsContainer) {
                // Function to update the hidden ORDER inputs after reordering
                function updateNavOrderInputs() {
                    const rows = navItemsContainer.querySelectorAll('.nav-item-row');
                    // console.log(`Reordering JS: Updating ORDER for ${rows.length} rows.`); // Removed log
                    rows.forEach((row, index) => {
                        const orderInput = row.querySelector(`input[name$="-ORDER"]`);
                        if (orderInput) {
                            orderInput.value = index;
                            // console.log(`Reordering JS: Set ${row.id} ORDER to ${index}`); // Optional: verbose logging
                        } else {
                             console.error(`Reordering JS Error: Could not find ORDER input in row:`, row); // Keep error log
                        }
                    });
                }

                // Use event delegation for move buttons
                navItemsContainer.addEventListener('click', function(event) {
                    const target = event.target;
                    const moveUpBtn = target.closest('.move-up-btn');
                    const moveDownBtn = target.closest('.move-down-btn');

                    if (!moveUpBtn && !moveDownBtn) {
                        return; // Click wasn't on a move button or its icon
                    }

                    const rowToMove = target.closest('.nav-item-row');
                    if (!rowToMove) {
                         console.error("Reordering JS Error: Could not find parent .nav-item-row"); // Keep error log
                         return;
                    }

                    if (moveUpBtn) {
                        const previousRow = rowToMove.previousElementSibling;
                        // Check if previousRow exists and is also a nav-item-row (don't move past the template)
                        if (previousRow && previousRow.classList.contains('nav-item-row')) {
                            navItemsContainer.insertBefore(rowToMove, previousRow);
                            // console.log(`Reordering JS: Moved ${rowToMove.id} up.`); // Removed log
                            // --- Explicitly update moved row and its new sibling immediately ---
                            // This might be redundant with the full updateNavOrderInputs call below,
                            // but can help ensure the critical rows are updated synchronously.
                            // const movedOrderInput = rowToMove.querySelector(`input[name$="-ORDER"]`);
                            // const siblingOrderInput = previousRow.querySelector(`input[name$="-ORDER"]`);
                            // if (movedOrderInput && siblingOrderInput) {
                            //     console.log(`Reordering JS DEBUG: Swapped order values for ${rowToMove.id} and ${previousRow.id}`);
                            // }
                            // --- Explicitly update ORDER values after DOM move ---
                            const movedOrderInput = rowToMove.querySelector(`input[name$="-ORDER"]`);
                            const siblingOrderInput = previousRow.querySelector(`input[name$="-ORDER"]`);
                            if (movedOrderInput && siblingOrderInput) {
                                const oldMovedValue = movedOrderInput.value;
                                movedOrderInput.value = siblingOrderInput.value;
                                siblingOrderInput.value = oldMovedValue;
                                console.log(`Reordering JS DEBUG: Swapped ORDER values for ${rowToMove.id} (${movedOrderInput.value}) and ${previousRow.id} (${siblingOrderInput.value})`);
                                triggerEditDataUIRegeneration(); // <<< Regenerate Edit Data UI
                            } else {
                                console.error("Reordering JS Error: Could not find ORDER inputs for swap (move up).");
                                updateNavOrderInputs(); // Fallback to full update if direct swap fails
                                triggerEditDataUIRegeneration(); // <<< Regenerate Edit Data UI on fallback
                            }
                        } else {
                             // console.log(`Reordering JS: Cannot move ${rowToMove.id} further up.`); // Removed log
                        }
                    } else if (moveDownBtn) {
                        const nextRow = rowToMove.nextElementSibling;
                         // Check if nextRow exists and is also a nav-item-row
                        if (nextRow && nextRow.classList.contains('nav-item-row')) {
                            navItemsContainer.insertBefore(nextRow, rowToMove); // Insert the *next* element before the current one
                            // console.log(`Reordering JS: Moved ${rowToMove.id} down.`); // Removed log
                            // --- Explicitly update moved row and its new sibling immediately ---
                            // const movedOrderInput = rowToMove.querySelector(`input[name$="-ORDER"]`);
                            // const siblingOrderInput = nextRow.querySelector(`input[name$="-ORDER"]`);
                            // if (movedOrderInput && siblingOrderInput) {
                            //     console.log(`Reordering JS DEBUG: Swapped order values for ${rowToMove.id} and ${nextRow.id}`);
                            // }
                             // --- Explicitly update ORDER values after DOM move ---
                            const movedOrderInput = rowToMove.querySelector(`input[name$="-ORDER"]`);
                            const siblingOrderInput = nextRow.querySelector(`input[name$="-ORDER"]`);
                            if (movedOrderInput && siblingOrderInput) {
                                const oldMovedValue = movedOrderInput.value;
                                movedOrderInput.value = siblingOrderInput.value;
                                siblingOrderInput.value = oldMovedValue;
                                console.log(`Reordering JS DEBUG: Swapped ORDER values for ${rowToMove.id} (${movedOrderInput.value}) and ${nextRow.id} (${siblingOrderInput.value})`);
                                triggerEditDataUIRegeneration(); // <<< Regenerate Edit Data UI
                            } else {
                                console.error("Reordering JS Error: Could not find ORDER inputs for swap (move down).");
                                updateNavOrderInputs(); // Fallback to full update if direct swap fails
                                triggerEditDataUIRegeneration(); // <<< Regenerate Edit Data UI on fallback
                            }
                        } else {
                             // console.log(`Reordering JS: Cannot move ${rowToMove.id} further down.`); // Removed log
                        }
                    }
                });

                 // Initial order update on load (in case Django renders them out of order initially)
                 updateNavOrderInputs();
            }
            // --- End Reordering Logic ---

            // --- Pre-submission cleanup & TinyMCE save ---
            const mainForm = document.querySelector('form'); // Get form element here
            if (mainForm && navItemsContainer) { // Check navItemsContainer exists
                mainForm.addEventListener('submit', function(event) {
                    console.log("Form submit event triggered. Saving TinyMCE and checking empty rows...");
                    console.log("Assistant type:", assistantType);
                    console.log("isAdvancedType:", isAdvancedType);

                    // 1. Trigger TinyMCE save *before* checking rows
                    if (typeof tinymce !== 'undefined') {
                        console.log("Triggering tinymce.triggerSave()");
                        tinymce.triggerSave();

                        // Explicitly save each editor instance to ensure content is saved
                        const editors = tinymce.editors;
                        console.log(`Found ${editors.length} TinyMCE editors to save`);

                        // Check word count limits
                        const wordCountLimit = 500;
                        let overLimitEditors = [];

                        editors.forEach(editor => {
                            try {
                                console.log(`Saving editor: ${editor.id}`);
                                editor.save();

                                // Get the textarea and ensure its value is set
                                const textarea = document.getElementById(editor.id);
                                if (textarea) {
                                    textarea.value = editor.getContent();
                                    console.log(`Updated textarea ${editor.id} with editor content`);

                                    // Check word count
                                    const content = editor.getContent({format: 'text'});
                                    const wordCount = content.split(/\s+/).filter(Boolean).length;
                                    if (wordCount > wordCountLimit) {
                                        overLimitEditors.push({
                                            id: editor.id,
                                            wordCount: wordCount,
                                            label: editor.getContainer().closest('.dynamic-section-edit')?.querySelector('h6')?.textContent || 'Unknown section'
                                        });
                                    }
                                }
                            } catch (e) {
                                console.error(`Error saving editor ${editor.id}:`, e);
                            }
                        });

                        // If any editors are over the word limit, show a warning
                        if (overLimitEditors.length > 0) {
                            const warningMessage = `The following sections exceed the ${wordCountLimit} word limit:\n\n` +
                                overLimitEditors.map(e => `- ${e.label}: ${e.wordCount} words`).join('\n') +
                                '\n\nPlease reduce the content length before submitting.';

                            if (!confirm(warningMessage + '\n\nDo you want to submit anyway?')) {
                                event.preventDefault();
                                // Scroll to the first editor that's over the limit
                                if (overLimitEditors.length > 0) {
                                    const firstEditor = document.getElementById(overLimitEditors[0].id);
                                    if (firstEditor) {
                                        firstEditor.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                    }
                                }
                                return;
                            }
                        }
                    }

                    // 2. Check for empty new nav rows
                    const newRows = navItemsContainer.querySelectorAll('.nav-item-row');
                    newRows.forEach(row => {
                        const idInput = row.querySelector('input[name$="-id"]');
                        const labelInput = row.querySelector('input[name$="-label"]');
                        const deleteCheckbox = row.querySelector('input[name$="-DELETE"]');

                        // Check if it's a NEW row (no ID) and the label is empty and delete checkbox exists
                        if (idInput && !idInput.value && labelInput && !labelInput.value.trim() && deleteCheckbox) {
                            // console.log(`Found empty new row (${row.id}), marking for deletion.`); // Removed log
                            deleteCheckbox.checked = true; // Mark for deletion
                        }
                    });
                });
            }
            // --- End pre-submission cleanup ---


        } catch (e) { // Catch any error during advanced assistant initialization
            console.error("Error during Advanced Assistant JS initialization:", e); // Keep error log
        }
     } // End if (isAdvancedType)

    // --- REMOVED listener for Edit Data Tab shown event ---
    // Initialization is now handled at the end of generateDataEditingUI

    // The show_sidebar field is now placed directly in the navigation settings tab in the HTML

    // Handle logo file input
    const logoInputs = document.querySelectorAll('input[type="file"][name="logo"]');
    logoInputs.forEach(input => {
        input.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                console.log('Logo file selected:', this.files[0].name);

                // Check if the clear checkbox exists and uncheck it
                const clearCheckbox = this.parentElement.querySelector('input[type="checkbox"]');
                if (clearCheckbox) {
                    clearCheckbox.checked = false;
                    console.log('Unchecked clear checkbox');
                }
            }
        });
    });

    // Add form submission debugging
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        // Don't prevent default - let the form submit normally

        // Log form data for debugging
        console.log('Form is being submitted');

        // Check if FormData API is available
        if (window.FormData) {
            const formData = new FormData(this);
            console.log('Form contains logo file:', formData.has('logo'));

            // Log all form data entries for debugging
            for (let [key, value] of formData.entries()) {
                if (key === 'logo' && value instanceof File) {
                    console.log('Logo file in form submission:', key, value.name, value.size, value.type);
                }
            }
        }
    });
});
</script>
<style>
/* Make TinyMCE editor look like Bootstrap form-control */
.tox-tinymce {
    border-radius: 0.2rem !important;
    border: 1px solid #ced4da !important;
    min-height: 38px !important;
    box-shadow: none !important;
    width: 100% !important;
    max-width: none !important;
    min-height: 400px !important; /* Increased height for multiple toolbars */
}
.tox-tinymce:focus-within {
    border-color: #86b7fe !important;
    box-shadow: 0 0 0 0.2rem rgba(13,110,253,.25) !important;
}
.tox .tox-edit-area__iframe {
    background-color: #fff !important;
    font-family: inherit !important;
    padding: 0.375rem 0.75rem !important;
}
/* Fix for TinyMCE visibility issue */
.tox-tinymce {
    visibility: visible !important;
    display: block !important;
    opacity: 1 !important;
}
.tox-tinymce[style*="visibility: hidden"] {
    visibility: visible !important;
}
/* Additional TinyMCE styling */
.tox-editor-container {
    background-color: white !important;
    visibility: visible !important;
    width: 100% !important;
}

/* Word count warning styling */
.word-count-warning {
    margin-top: 5px !important;
    font-size: 0.875rem !important;
    display: block !important;
    visibility: visible !important;
}
.word-count-warning.text-danger {
    font-weight: bold !important;
}

/* Hide HTML content below the editor */
.dynamic-section-edit .tox-tinymce + *:not(.tox-tinymce):not(label):not(input):not(select):not(button):not(.form-control):not(.form-label):not(.form-text),
.dynamic-section-edit .tox-tinymce ~ *:not(.tox-tinymce):not(label):not(input):not(select):not(button):not(.form-control):not(.form-label):not(.form-text),
.dynamic-section-edit p:not(.form-text):not(.word-count-warning),
.dynamic-section-edit .mb-2 > p:not(.form-text):not(.word-count-warning) {
    display: none !important;
}

/* Also hide specific elements we know might appear */
.dynamic-section-edit .tox-tinymce + h1,
.dynamic-section-edit .tox-tinymce + h2,
.dynamic-section-edit .tox-tinymce + h3,
.dynamic-section-edit .tox-tinymce + h4,
.dynamic-section-edit .tox-tinymce + h5,
.dynamic-section-edit .tox-tinymce + h6,
.dynamic-section-edit .tox-tinymce + p,
.dynamic-section-edit .tox-tinymce + ul,
.dynamic-section-edit .tox-tinymce + ol,
.dynamic-section-edit .tox-tinymce + div,
.dynamic-section-edit .tox-tinymce + strong,
.dynamic-section-edit .tox-tinymce ~ h1,
.dynamic-section-edit .tox-tinymce ~ h2,
.dynamic-section-edit .tox-tinymce ~ h3,
.dynamic-section-edit .tox-tinymce ~ h4,
.dynamic-section-edit .tox-tinymce ~ h5,
.dynamic-section-edit .tox-tinymce ~ h6,
.dynamic-section-edit .tox-tinymce ~ p,
.dynamic-section-edit .tox-tinymce ~ ul,
.dynamic-section-edit .tox-tinymce ~ ol,
.dynamic-section-edit .tox-tinymce ~ div,
.dynamic-section-edit .tox-tinymce ~ strong {
    display: none !important;
}
</style>

    {# Add Viewer Modal #}
    <div class="modal fade" id="addViewerModal" tabindex="-1" aria-labelledby="addViewerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addViewerModalLabel">Add Viewer</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted small">
                        Select a company member to add as a viewer for this assistant. Viewers can access this assistant even if it's private, but cannot edit it.
                    </p>
                    <div class="mb-3">
                        <label for="viewer-select" class="form-label">Select User</label>
                        <select class="form-select" id="viewer-select">
                            <option value="" selected disabled>Choose a user...</option>
                            {% for member in company_members %}
                                {% if member.user != request.user and member.user not in assistant_viewers %}
                                    <option value="{{ member.user.id }}">{{ member.user.get_full_name|default:member.user.username }} ({{ member.user.email }})</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    <div id="add-viewer-error" class="alert alert-danger" style="display: none;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="confirm-add-viewer-btn">Add Viewer</button>
                </div>
            </div>
        </div>
    </div>

    {# Remove Viewer Confirmation Modal #}
    <div class="modal fade" id="removeViewerModal" tabindex="-1" aria-labelledby="removeViewerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="removeViewerModalLabel">Remove Viewer</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to remove <strong id="viewer-name-to-remove"></strong> as a viewer?</p>
                    <input type="hidden" id="viewer-id-to-remove">
                    <div id="remove-viewer-error" class="alert alert-danger" style="display: none;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirm-remove-viewer-btn">Remove</button>
                </div>
            </div>
        </div>
    </div>
{% endblock main_content %}

