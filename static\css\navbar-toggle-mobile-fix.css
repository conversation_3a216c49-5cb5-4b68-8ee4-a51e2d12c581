/**
 * Navbar Toggle Mobile Fix CSS
 * Additional fixes for the navbar toggle button on mobile devices
 */

/* Ensure proper sizing and positioning on mobile */
@media (max-width: 767.98px) {
  [data-theme="dark"] .navbar-toggler {
    /* Slightly larger touch target on mobile */
    min-width: 42px !important;
    min-height: 38px !important;
    
    /* Ensure proper padding */
    padding: 0.45rem !important;
    
    /* Improve visibility with stronger shadow */
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.4),
                inset 0 1px 1px rgba(255, 255, 255, 0.05) !important;
  }
  
  /* Ensure icon is properly centered */
  [data-theme="dark"] .navbar-toggler-icon {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 1.3em !important;
    height: 1.3em !important;
  }
  
  /* Ensure proper spacing from edge */
  [data-theme="dark"] .navbar > .container .navbar-toggler {
    margin-right: -0.25rem !important;
  }
}

/* Extra small devices */
@media (max-width: 575.98px) {
  [data-theme="dark"] .navbar-toggler {
    /* Slightly smaller on very small screens */
    min-width: 38px !important;
    min-height: 36px !important;
    padding: 0.4rem !important;
  }
  
  /* Ensure icon is properly sized */
  [data-theme="dark"] .navbar-toggler-icon {
    width: 1.2em !important;
    height: 1.2em !important;
  }
}

/* Fix for devices with notches */
@supports (padding-top: env(safe-area-inset-top)) {
  @media (max-width: 767.98px) {
    [data-theme="dark"] .navbar-toggler {
      margin-right: calc(env(safe-area-inset-right) - 0.25rem) !important;
    }
  }
}

/* Fix for impersonation mode on mobile */
@media (max-width: 767.98px) {
  body.is-impersonating [data-theme="dark"] .navbar-toggler {
    margin-top: 0 !important;
  }
}
