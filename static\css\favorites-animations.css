/**
 * Favorites Animations CSS
 * Provides smooth animations for the favorites functionality
 */

/* Smooth transition for favorite items when removing */
.favorite-item {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Empty state messages */
.no-favorites-message, 
.empty-folder-message {
    opacity: 0;
    animation: fadeIn 0.5s ease forwards;
    animation-delay: 0.3s;
}

/* Fade in animation */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Pulse animation for heart icons */
.pulse-heart {
    animation: pulse 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1); }
}

/* Ripple effect for buttons */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple:after {
    content: "";
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
    background-repeat: no-repeat;
    background-position: 50%;
    transform: scale(10, 10);
    opacity: 0;
    transition: transform 0.5s, opacity 0.5s;
}

.ripple:active:after {
    transform: scale(0, 0);
    opacity: 0.3;
    transition: 0s;
}
