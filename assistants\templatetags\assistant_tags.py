from django import template

register = template.Library()

@register.filter(name='filter')
def filter_objects(objects, filter_string):
    """
    Filter a list of objects based on a filter string.

    Usage: {{ objects|filter:"attribute:value" }}
    Example: {{ assistants|filter:"tier:gold" }}
    Example: {{ assistants|filter:"is_featured" }}

    If only an attribute is provided (no colon), it filters objects where that attribute is True.
    The filter is case-insensitive when comparing string values.
    """
    if not objects:
        return []

    if ':' in filter_string:
        # Filter by attribute:value
        attr, value = filter_string.split(':', 1)
        # Debug output
        print(f"DEBUG FILTER: Filtering by {attr}:{value}")
        for obj in objects:
            obj_value = getattr(obj, attr, None)
            print(f"DEBUG FILTER: Object {obj} has {attr}={obj_value} (type={type(obj_value).__name__})")

        # Case-insensitive comparison for string values
        result = [obj for obj in objects if str(getattr(obj, attr, None)).lower() == value.lower()]
        print(f"DEBUG FILTER: Found {len(result)} objects matching {attr}:{value}")
        return result
    else:
        # Filter by boolean attribute
        attr = filter_string
        return [obj for obj in objects if getattr(obj, attr, False)]

@register.simple_tag
def query_string(querydict, **kwargs):
    """
    Updates a QueryDict with new values and returns the encoded query string.
    Preserves existing parameters.
    Removes the key if the value is None or empty string.
    """
    updated = querydict.copy()
    for key, value in kwargs.items():
        if value is None or value == '':
            # Remove the key if the value is None or empty
            if key in updated:
                del updated[key]
        else:
            # Set/update the key with the new value (ensure it's a string)
            updated[key] = str(value)
    # Return the encoded query string (with '?') only if there are parameters
    return '?' + updated.urlencode() if updated else ''

@register.filter(name='get_nav_data')
def get_nav_data(website_data_dict, unique_id):
    """
    Retrieves the value from the website_data dictionary using unique_id as the key or 'item_ID' format.
    Usage: {{ website_data|get_nav_data:item.unique_id }}
    Returns None if the key doesn't exist or the dictionary is not valid.

    The function checks for both direct key lookup and 'item_ID' format keys.
    """
    if not isinstance(website_data_dict, dict) or unique_id is None:
        return None

    # First try direct lookup
    if unique_id in website_data_dict:
        return website_data_dict.get(unique_id)

    # If unique_id is 'item_X', use it directly
    if unique_id.startswith('item_') and unique_id in website_data_dict:
        value = website_data_dict.get(unique_id)
        # If the value is a dict with content, return the content
        if isinstance(value, dict) and 'content' in value:
            return value['content']
        # If the value is a string, return it directly
        elif isinstance(value, str):
            return value
        return value

    # If nothing found, return None
    return None

@register.filter(name='exclude_community')
def exclude_community(objects):
    """
    Filter out community assistants from a list of assistants.

    Usage: {{ assistants|exclude_community }}
    """
    if not objects:
        return []

    # Import the Assistant model to get the TYPE_COMMUNITY constant
    from assistants.models import Assistant

    return [obj for obj in objects if getattr(obj, 'assistant_type', '') != Assistant.TYPE_COMMUNITY]
