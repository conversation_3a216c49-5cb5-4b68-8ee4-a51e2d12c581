/**
 * Floating Buttons Fix CSS
 * Adjusts the position of floating buttons to prevent them from interfering with the menu
 */

/* Theme toggle button - move it away from the menu */
.theme-toggle-btn {
    position: fixed !important;
    bottom: 100px !important; /* Move it higher up */
    right: 20px !important;
    z-index: 990 !important; /* Lower z-index so it doesn't interfere with menu */
}

/* Impersonation float button - move it away from the menu */
.impersonation-float-button,
#impersonation-float-button {
    position: fixed !important;
    bottom: 170px !important; /* Move it higher up, above theme toggle */
    right: 20px !important;
    z-index: 990 !important; /* Lower z-index so it doesn't interfere with menu */
}

/* Fix button - move it away from the menu */
button[id^="fix-"],
button[class*="fix-button"] {
    position: fixed !important;
    bottom: 240px !important; /* Move it higher up, above impersonation button */
    right: 20px !important;
    z-index: 990 !important; /* Lower z-index so it doesn't interfere with menu */
}

/* Ensure navbar has higher z-index than floating buttons */
.navbar,
.navbar-collapse,
.navbar-toggler,
.navbar .nav-item,
.navbar .nav-link,
.navbar .dropdown-item,
.navbar .dropdown-menu {
    z-index: 1100 !important; /* Higher than floating buttons */
    position: relative !important;
    pointer-events: auto !important;
}

/* Ensure menu items are clickable */
.navbar .nav-link,
.navbar .dropdown-item,
.navbar-nav .nav-item a {
    position: relative !important;
    z-index: 1110 !important; /* Even higher to ensure they're clickable */
    pointer-events: auto !important;
}

/* Ensure navbar toggler is always clickable */
.navbar-toggler {
    position: relative !important;
    z-index: 1120 !important; /* Highest z-index to ensure it's always clickable */
    pointer-events: auto !important;
}

/* Ensure dropdown menus appear above other elements */
.dropdown-menu {
    z-index: 1115 !important;
    pointer-events: auto !important;
}

/* Mobile-specific adjustments */
@media (max-width: 768px) {
    /* Stack buttons vertically with more space on mobile */
    .theme-toggle-btn {
        bottom: 80px !important;
        right: 15px !important;
        width: 50px !important;
        height: 50px !important;
    }
    
    .impersonation-float-button,
    #impersonation-float-button {
        bottom: 140px !important;
        right: 15px !important;
        width: 50px !important;
        height: 50px !important;
    }
    
    button[id^="fix-"],
    button[class*="fix-button"] {
        bottom: 200px !important;
        right: 15px !important;
        width: 50px !important;
        height: 50px !important;
    }
    
    /* Ensure navbar elements have even higher z-index on mobile */
    .navbar,
    .navbar-collapse,
    .navbar-toggler {
        z-index: 1200 !important;
    }
    
    .navbar .nav-link,
    .navbar .dropdown-item,
    .navbar-nav .nav-item a {
        z-index: 1210 !important;
    }
    
    .navbar-toggler {
        z-index: 1220 !important;
    }
}
