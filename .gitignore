# Python
*.py[cod]
__pycache__/
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/

# Virtual Environment
venv/
ENV/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.pytest_cache/

# Translations
*.mo
*.pot

# Documentation
docs/_build/
docs/env/

# Dependencies
node_modules/
bower_components/

# Project specific
temp/
logs/
uploads/
local_settings.py
*.env
!.env.example
.env.local
.env.development
.env.test
.env.production

# Docker
.docker/
docker-compose.override.yml

# Deployment
*.pem
*.key
*.cert

# Local development SSL certificates
*.crt
*.key
*.pem

# Backup files
*.bak
*.swp
*.swo
*~
.#*
\#*#

# Content files
company_assistant/media/
company_assistant/static/

# Database dumps
*.dump
*.sql

# Environment specific
.python-version
.ruby-version
.node-version

# Generated files
*.pyc
*.pyo
*.pyd
.Python
*.py[cod]
*$py.class
*.so

# QR codes
static/qrcodes/

# Temporary files
.tmp/
tmp/
