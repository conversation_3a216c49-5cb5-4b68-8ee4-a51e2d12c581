/**
 * Prevent White Flash JavaScript
 * This script runs immediately to ensure dark background is applied before page load
 */

(function() {
    // Set dark background color immediately
    document.documentElement.style.backgroundColor = '#121212';
    document.documentElement.style.color = '#ffffff';
    
    // Create and inject a style element for immediate dark mode
    const style = document.createElement('style');
    style.textContent = `
        html, body, main {
            background-color: #121212 !important;
            color: #ffffff !important;
            transition: none !important;
        }
        
        .chat-container, .general-chat-container {
            background-color: #1e1e1e !important;
            border-color: #333333 !important;
        }
        
        .chat-box, .general-chat-box, #chat-box {
            background-color: #252525 !important;
            border-color: #333333 !important;
        }
        
        [style*="background-color: white"],
        [style*="background-color: #fff"],
        [style*="background-color: rgb(255, 255, 255)"],
        [style*="background: white"],
        [style*="background: #fff"],
        [style*="background: rgb(255, 255, 255)"] {
            background-color: #121212 !important;
            background: #121212 !important;
            color: #ffffff !important;
        }
    `;
    document.head.appendChild(style);
    
    // Set data-theme attribute
    document.documentElement.setAttribute('data-theme', 'dark');
})();

// When DOM is loaded, ensure dark mode is applied
document.addEventListener('DOMContentLoaded', function() {
    // Apply dark mode to all elements
    document.documentElement.setAttribute('data-theme', 'dark');
    document.body.setAttribute('data-theme', 'dark');
    document.documentElement.classList.add('dark-mode');
    document.body.classList.add('dark-mode');
    
    // Apply to main content
    const mainContent = document.querySelector('main');
    if (mainContent) {
        mainContent.classList.add('dark-mode');
        mainContent.setAttribute('data-theme', 'dark');
        mainContent.style.backgroundColor = '#121212';
        mainContent.style.color = '#ffffff';
    }
    
    // Apply to chat container
    const chatContainer = document.querySelector('.chat-container, .general-chat-container');
    if (chatContainer) {
        chatContainer.style.backgroundColor = '#1e1e1e';
        chatContainer.style.borderColor = '#333333';
    }
    
    // Apply to chat box
    const chatBox = document.querySelector('.chat-box, .general-chat-box, #chat-box');
    if (chatBox) {
        chatBox.style.backgroundColor = '#252525';
        chatBox.style.borderColor = '#333333';
    }
});
