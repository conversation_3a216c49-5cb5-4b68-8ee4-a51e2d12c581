from django.core.management.base import BaseCommand
from django.urls import reverse
from assistants.models import Assistant
from utils.qr_generator import generate_model_qr_code
import time

class Command(BaseCommand):
    help = 'Generate QR codes for all assistants that do not have one, with priority for community assistants'

    def add_arguments(self, parser):
        parser.add_argument(
            '--community-only',
            action='store_true',
            help='Only generate QR codes for community assistants',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force regeneration of QR codes even if they already exist',
        )

    def handle(self, *args, **options):
        community_only = options.get('community_only', False)
        force = options.get('force', False)

        # Build the query
        query = {}
        if community_only:
            query['assistant_type'] = 'community'
        if not force:
            query['qr_code'] = ''  # Only get assistants without QR codes

        # Get the assistants
        assistants = Assistant.objects.filter(**query)
        total = assistants.count()

        self.stdout.write(f"Found {total} assistants that need QR codes")

        # Process each assistant
        success_count = 0
        fail_count = 0

        for i, assistant in enumerate(assistants, 1):
            self.stdout.write(f"Processing {i}/{total}: {assistant.name} (ID: {assistant.id})")

            # Prioritize community assistants with more attempts
            max_attempts = 3 if assistant.assistant_type == 'community' else 1
            success = False

            for attempt in range(max_attempts):
                try:
                    # Use the assistant's public chat URL for the QR code
                    url_path = reverse('assistants:assistant_chat', kwargs={'slug': assistant.slug})
                    success = generate_model_qr_code(assistant, url_path, field_name='qr_code')

                    if success:
                        # Save the assistant with the new QR code
                        assistant.save(update_fields=['qr_code'])
                        self.stdout.write(self.style.SUCCESS(
                            f"Successfully generated QR code for {assistant.name} (attempt {attempt+1})"
                        ))
                        success_count += 1
                        break
                    else:
                        self.stdout.write(self.style.WARNING(
                            f"Failed to generate QR code for {assistant.name} (attempt {attempt+1})"
                        ))
                        # Small delay before retry
                        time.sleep(0.5)
                except Exception as e:
                    self.stdout.write(self.style.ERROR(
                        f"Error generating QR code for {assistant.name}: {str(e)}"
                    ))
                    # Continue to next attempt

            if not success:
                fail_count += 1

        # Summary
        self.stdout.write("\nSummary:")
        self.stdout.write(self.style.SUCCESS(f"Successfully generated QR codes for {success_count} assistants"))
        if fail_count > 0:
            self.stdout.write(self.style.ERROR(f"Failed to generate QR codes for {fail_count} assistants"))
        else:
            self.stdout.write(self.style.SUCCESS("All QR codes were generated successfully"))
