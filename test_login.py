"""
Test script for login functionality.
"""

import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import RequestFactory, TestCase
from django.urls import reverse
from django.contrib.messages.storage.fallback import FallbackStorage
from accounts.auth_views import CustomLoginView
from accounts.auth_utils import should_require_approval, store_signin_approval, verify_signin_token

User = get_user_model()

class TestLoginView(TestCase):
    def setUp(self):
        self.factory = RequestFactory()
        # Get or create a test user
        try:
            self.user = User.objects.get(username='testuser')
        except User.DoesNotExist:
            self.user = User.objects.create_user(
                username='testuser',
                email='<EMAIL>',
                password='testpassword123'
            )

    def test_login_view(self):
        """Test the login view."""
        print("Testing login view...")

        # Create a POST request
        url = reverse('accounts:login')
        data = {
            'username': 'testuser',
            'password': 'testpassword123',
        }
        request = self.factory.post(url, data)

        # Add session and messages middleware
        setattr(request, 'session', {})
        messages = FallbackStorage(request)
        setattr(request, '_messages', messages)

        # Set remote address
        request.META['REMOTE_ADDR'] = '127.0.0.1'

        # Create a login view instance
        view = CustomLoginView.as_view()

        # Use a mock to make should_require_approval always return True for testing
        from unittest.mock import patch
        with patch('accounts.auth_utils.should_require_approval', return_value=True):
            # Call the view
            response = view(request)

            # Check if the response is a redirect
            if response.status_code == 302:
                print(f"Login view redirected to: {response.url}")
                if response.url == '/':  # Redirected to home
                    print("Login view redirected to home page as expected.")
                else:
                    print(f"Unexpected redirect URL: {response.url}")
            else:
                print(f"Unexpected response status code: {response.status_code}")

        # Test token verification
        print("\nTesting token verification...")
        token = store_signin_approval(self.user)
        verified_user = verify_signin_token(token)
        if verified_user and verified_user.username == self.user.username:
            print("Token verification successful.")
        else:
            print("Token verification failed.")

if __name__ == '__main__':
    print("Testing login functionality...")
    test_case = TestLoginView()
    test_case.setUp()
    test_case.test_login_view()
