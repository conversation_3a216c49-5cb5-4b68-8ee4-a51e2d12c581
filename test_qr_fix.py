#!/usr/bin/env python
"""
Simple test script to verify the QR code fix works.
This script generates a test QR code and saves it for inspection.
"""

import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import QR code generation functions
from utils.qr_generator import generate_qr_with_a

def main():
    """Generate a test QR code to verify the fix."""
    print("=== QR Code Fix Test ===")
    print("Generating a test QR code to verify the letter A is properly sized...")
    
    try:
        # Generate a test QR code
        test_url = "https://24seven.site/test"
        print(f"Generating QR code for: {test_url}")
        
        qr_img = generate_qr_with_a(test_url, letter="A")
        
        # Save the QR code
        output_path = "qr_test_result.png"
        qr_img.save(output_path)
        
        print(f"✅ QR code generated successfully!")
        print(f"📁 Saved as: {output_path}")
        print(f"📏 Size: {qr_img.size[0]}x{qr_img.size[1]} pixels")
        print()
        print("🔍 Please check the generated QR code:")
        print("   - Open the file 'qr_test_result.png'")
        print("   - Look for a large, bold letter 'A' in the center")
        print("   - The letter should be black on a white background")
        print("   - If the letter is tiny, the fix didn't work")
        print("   - If the letter is large and clear, the fix worked!")
        
    except Exception as e:
        print(f"❌ Error generating QR code: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
