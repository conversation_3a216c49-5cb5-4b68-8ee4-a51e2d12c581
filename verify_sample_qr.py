#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to verify the vertical positioning of the letter "A" in the sample QR code.
"""

import os
import sys
import numpy as np
from PIL import Image

def find_letter_boundaries(image_path):
    """
    Find the boundaries of the letter "A" in the QR code.
    
    Args:
        image_path (str): Path to the QR code image
    
    Returns:
        tuple: (top, bottom, left, right) boundaries of the letter "A"
    """
    try:
        # Open the image
        img = Image.open(image_path).convert('RGB')
        
        # Convert to numpy array
        img_array = np.array(img)
        
        # Get image dimensions
        height, width, _ = img_array.shape
        
        # Create a mask for black pixels (letter "A")
        # The letter "A" is black (0, 0, 0) on a white background
        black_mask = np.all(img_array == [0, 0, 0], axis=2)
        
        # Find the boundaries of the letter "A"
        # We'll focus on the center region of the QR code
        center_region_size = min(width, height) // 2
        center_x, center_y = width // 2, height // 2
        
        # Define the center region
        top = max(0, center_y - center_region_size // 2)
        bottom = min(height, center_y + center_region_size // 2)
        left = max(0, center_x - center_region_size // 2)
        right = min(width, center_x + center_region_size // 2)
        
        # Extract the center region
        center_region = black_mask[top:bottom, left:right]
        
        # Find the boundaries of the letter "A" in the center region
        rows_with_black = np.any(center_region, axis=1)
        cols_with_black = np.any(center_region, axis=0)
        
        if not np.any(rows_with_black) or not np.any(cols_with_black):
            print(f"Could not find letter 'A' in the center region of {image_path}")
            return None
        
        # Find the first and last rows and columns with black pixels
        first_row = np.argmax(rows_with_black)
        last_row = len(rows_with_black) - 1 - np.argmax(rows_with_black[::-1])
        first_col = np.argmax(cols_with_black)
        last_col = len(cols_with_black) - 1 - np.argmax(cols_with_black[::-1])
        
        # Adjust the boundaries to the original image coordinates
        letter_top = top + first_row
        letter_bottom = top + last_row
        letter_left = left + first_col
        letter_right = left + last_col
        
        return (letter_top, letter_bottom, letter_left, letter_right)
    
    except Exception as e:
        print(f"Error finding letter boundaries in {image_path}: {e}")
        return None

def verify_vertical_positioning(image_path):
    """
    Verify that the letter "A" is perfectly centered vertically in the QR code.
    
    Args:
        image_path (str): Path to the QR code image
    
    Returns:
        bool: True if the letter "A" is perfectly centered vertically, False otherwise
    """
    try:
        # Find the boundaries of the letter "A"
        boundaries = find_letter_boundaries(image_path)
        if not boundaries:
            return False
        
        letter_top, letter_bottom, letter_left, letter_right = boundaries
        
        # Open the image to get its dimensions
        img = Image.open(image_path)
        img_width, img_height = img.size
        
        # Calculate the center of the image
        center_y = img_height / 2
        
        # Calculate the center of the letter "A"
        letter_center_y = (letter_top + letter_bottom) / 2
        
        # Calculate the distance from the top of the image to the top of the letter
        top_distance = letter_top
        
        # Calculate the distance from the bottom of the letter to the bottom of the image
        bottom_distance = img_height - letter_bottom
        
        # Calculate the difference between the top and bottom distances
        distance_diff = abs(top_distance - bottom_distance)
        
        # Calculate the difference between the letter center and the image center
        center_diff = abs(letter_center_y - center_y)
        
        # Calculate the tolerance as a percentage of the image height
        tolerance = img_height * 0.02  # 2% tolerance
        
        # Check if the letter "A" is centered vertically
        is_centered = center_diff <= tolerance
        
        # Print the results
        print(f"Image dimensions: {img_width}x{img_height}")
        print(f"Letter 'A' boundaries: top={letter_top}, bottom={letter_bottom}, left={letter_left}, right={letter_right}")
        print(f"Image center: {center_y}")
        print(f"Letter 'A' center: {letter_center_y}")
        print(f"Distance from top: {top_distance}")
        print(f"Distance from bottom: {bottom_distance}")
        print(f"Distance difference: {distance_diff}")
        print(f"Center difference: {center_diff}")
        print(f"Tolerance: {tolerance}")
        
        if is_centered:
            print(f"✅ Letter 'A' is centered vertically (within tolerance)")
        else:
            print(f"❌ Letter 'A' is NOT centered vertically")
            
            # Calculate the adjustment needed
            adjustment = center_y - letter_center_y
            print(f"Adjustment needed: {adjustment} pixels")
        
        return is_centered
    
    except Exception as e:
        print(f"Error verifying vertical positioning in {image_path}: {e}")
        return False

def main():
    """
    Verify the vertical positioning of the letter "A" in the sample QR code.
    
    Returns:
        bool: True if the letter "A" is centered vertically, False otherwise
    """
    try:
        # Check if the sample QR code exists
        sample_qr_path = "sample_qr_code.png"
        if not os.path.exists(sample_qr_path):
            print(f"Error: Sample QR code not found at {sample_qr_path}")
            return False
        
        print(f"Verifying vertical positioning of the letter 'A' in the sample QR code...")
        print(f"QR code: {sample_qr_path}")
        
        # Verify the vertical positioning
        is_centered = verify_vertical_positioning(sample_qr_path)
        
        return is_centered
    
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
