# Generated by Django 5.2.1 on 2025-05-20 05:56

import django.core.validators
import django.db.models.deletion
import tinymce.models
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("accounts", "0001_initial"),
        ("contenttypes", "0002_remove_content_type_name"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Assistant",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Internal name for identification.", max_length=100
                    ),
                ),
                (
                    "persona_name",
                    models.CharField(
                        blank=True,
                        default="Emilly",
                        help_text="Optional: The name the assistant uses in conversation (defaults to internal name).",
                        max_length=100,
                    ),
                ),
                ("slug", models.SlugField(max_length=100, unique=True)),
                ("description", tinymce.models.HTM<PERSON>ield(blank=True)),
                (
                    "assistant_type",
                    models.CharField(
                        choices=[
                            ("general", "General Purpose"),
                            ("support", "Customer Support"),
                            ("community", "Community Assistant"),
                        ],
                        default="general",
                        max_length=20,
                    ),
                ),
                (
                    "model",
                    models.CharField(
                        choices=[
                            ("gpt-3.5-turbo", "GPT-3.5 Turbo"),
                            ("gpt-4", "GPT-4"),
                            ("gpt-4-turbo", "GPT-4 Turbo"),
                            ("claude-2", "Claude 2"),
                            ("claude-instant", "Claude Instant"),
                            ("gemini-2.0-flash", "Gemini 2.0 Flash"),
                        ],
                        default="gpt-3.5-turbo",
                        max_length=50,
                    ),
                ),
                (
                    "tier",
                    models.CharField(
                        choices=[
                            ("Gold", "Gold"),
                            ("Silver", "Silver"),
                            ("Bronze", "Bronze"),
                            ("Standard", "Standard"),
                        ],
                        db_index=True,
                        default="Standard",
                        help_text="Directory display tier for this assistant.",
                        max_length=10,
                    ),
                ),
                (
                    "is_featured",
                    models.BooleanField(
                        db_index=True,
                        default=False,
                        help_text="Mark this assistant to appear in the featured carousel on the directory.",
                    ),
                ),
                (
                    "requested_tier",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Gold", "Gold"),
                            ("Silver", "Silver"),
                            ("Bronze", "Bronze"),
                            ("Standard", "Standard"),
                        ],
                        help_text="Tier requested for the assistant, pending superadmin approval.",
                        max_length=10,
                        null=True,
                    ),
                ),
                (
                    "tier_change_pending",
                    models.BooleanField(
                        db_index=True,
                        default=False,
                        help_text="Indicates if a tier change request is pending approval.",
                    ),
                ),
                (
                    "tier_expiry_date",
                    models.DateTimeField(
                        blank=True,
                        help_text="Date and time when the current tier expires (if applicable).",
                        null=True,
                    ),
                ),
                (
                    "requested_tier_duration",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("monthly", "Monthly"),
                            ("quarterly", "Quarterly"),
                            ("annually", "Annually"),
                        ],
                        help_text="Duration requested for the tier upgrade.",
                        max_length=10,
                        null=True,
                    ),
                ),
                (
                    "featured_request_pending",
                    models.BooleanField(
                        db_index=True,
                        default=False,
                        help_text="Indicates if a request to feature this assistant is pending approval.",
                    ),
                ),
                (
                    "featured_expiry_date",
                    models.DateTimeField(
                        blank=True,
                        help_text="Date and time when the featured status expires (if applicable).",
                        null=True,
                    ),
                ),
                (
                    "requested_featured_duration",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("monthly", "Monthly"),
                            ("quarterly", "Quarterly"),
                            ("annually", "Annually"),
                        ],
                        help_text="Duration requested for the featured status.",
                        max_length=10,
                        null=True,
                    ),
                ),
                (
                    "logo",
                    models.ImageField(
                        blank=True,
                        help_text="Optional logo specifically for this assistant. Falls back to company logo if not set.",
                        null=True,
                        upload_to="assistant_logos/",
                    ),
                ),
                (
                    "avatar",
                    models.ImageField(
                        blank=True,
                        help_text="Optional avatar for the chat interface. Falls back to assistant logo, then company logo if not set.",
                        null=True,
                        upload_to="assistant_avatars/",
                    ),
                ),
                (
                    "website_data",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Structured website data for customer care assistants (content keyed by NavigationItem unique_id)",
                    ),
                ),
                (
                    "extra_context",
                    tinymce.models.HTMLField(
                        blank=True, help_text="Additional context for the LLM"
                    ),
                ),
                (
                    "temperature",
                    models.FloatField(
                        default=0.7,
                        help_text="Controls randomness in responses (0.0-2.0)",
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(2.0),
                        ],
                    ),
                ),
                (
                    "max_tokens",
                    models.IntegerField(
                        default=2048,
                        help_text="Maximum length of generated responses",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(4096),
                        ],
                    ),
                ),
                (
                    "system_prompt",
                    tinymce.models.HTMLField(
                        blank=True,
                        help_text="Instructions that define the assistant's behavior",
                    ),
                ),
                (
                    "greeting_message",
                    tinymce.models.HTMLField(
                        blank=True,
                        help_text="Optional: Custom initial greeting message displayed in the chat interface.",
                    ),
                ),
                (
                    "knowledge_base",
                    models.FileField(
                        blank=True,
                        help_text="Optional files for domain-specific knowledge",
                        null=True,
                        upload_to="assistants/knowledge_base/",
                    ),
                ),
                (
                    "custom_css",
                    models.TextField(
                        blank=True,
                        help_text="Custom CSS rules to style the assistant's appearance (e.g., chat interface).",
                    ),
                ),
                (
                    "show_sidebar",
                    models.BooleanField(
                        default=True,
                        help_text="Whether to show the sidebar in the chat interface. Only applies to non-general assistants.",
                    ),
                ),
                (
                    "show_sidebar_public",
                    models.BooleanField(
                        default=True,
                        help_text="Whether to show the sidebar in the public chat interface. Only applies when show_sidebar is True.",
                    ),
                ),
                (
                    "saved_suggestions",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of suggested questions saved from the Analyze & Suggest tab.",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        db_index=True,
                        default=False,
                        help_text="Whether this assistant is currently active and usable (approved).",
                    ),
                ),
                (
                    "is_public",
                    models.BooleanField(
                        default=True,
                        help_text="Controls visibility in the public directory and company pages",
                    ),
                ),
                (
                    "qr_code",
                    models.ImageField(
                        blank=True, null=True, upload_to="assistant_qrcodes/"
                    ),
                ),
                ("total_interactions", models.IntegerField(default=0)),
                (
                    "average_rating",
                    models.FloatField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1.0),
                            django.core.validators.MaxValueValidator(5.0),
                        ],
                    ),
                ),
                (
                    "last_trained",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the assistant was last trained",
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assistants",
                        to="accounts.company",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_assistants",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "linked_company",
                    models.ForeignKey(
                        blank=True,
                        help_text="Optional company to link this community assistant to",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="linked_community_assistants",
                        to="accounts.company",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "permissions": [
                    ("view_assistant_usage", "Can view assistant usage data"),
                    ("view_assistant_analytics", "Can view assistant analytics"),
                    (
                        "create_assistant_token",
                        "Can create access tokens for assistants",
                    ),
                    (
                        "access_all_private",
                        "Can access all private assistants in the company",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="AssistantAccessToken",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "token",
                    models.UUIDField(default=uuid.uuid4, editable=False, unique=True),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Optional date/time when this token expires.",
                        null=True,
                    ),
                ),
                (
                    "max_uses",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Optional limit on the number of times this token can be used.",
                        null=True,
                    ),
                ),
                ("uses_count", models.PositiveIntegerField(default=0, editable=False)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "notes",
                    models.CharField(
                        blank=True,
                        help_text="Optional notes for the creator about this token's purpose.",
                        max_length=255,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "assistant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="access_tokens",
                        to="assistants.assistant",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_assistant_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Assistant Access Token",
                "verbose_name_plural": "Assistant Access Tokens",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="AssistantFolder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "order",
                    models.IntegerField(
                        db_index=True,
                        default=0,
                        help_text="Order in which folders appear (lower numbers first).",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assistant_folders",
                        to="accounts.company",
                    ),
                ),
            ],
            options={
                "ordering": ["order", "name"],
                "unique_together": {("company", "name")},
            },
        ),
        migrations.AddField(
            model_name="assistant",
            name="folder",
            field=models.ForeignKey(
                blank=True,
                help_text="Optional folder to organize this assistant.",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="assistants",
                to="assistants.assistantfolder",
            ),
        ),
        migrations.CreateModel(
            name="Comment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("object_id", models.PositiveIntegerField()),
                ("text", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Comment",
                "verbose_name_plural": "Comments",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="CommunityContext",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        blank=True,
                        help_text="Title for this knowledge context",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "text_content",
                    models.TextField(
                        help_text="The knowledge content to be used by the assistant"
                    ),
                ),
                (
                    "keywords",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of keywords to help categorize this context",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this context is available for use",
                    ),
                ),
                (
                    "times_used",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Number of times this context was used in responses",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "assistant",
                    models.ForeignKey(
                        limit_choices_to={"assistant_type": "community"},
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="community_contexts",
                        to="assistants.assistant",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="contributed_contexts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Community Context",
                "verbose_name_plural": "Community Contexts",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ContextImage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("image", models.ImageField(upload_to="community_context_images/")),
                ("caption", models.CharField(blank=True, max_length=255)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "context",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="images",
                        to="assistants.communitycontext",
                    ),
                ),
            ],
            options={
                "verbose_name": "Context Image",
                "verbose_name_plural": "Context Images",
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="ContextUpvote",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "context",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="upvotes",
                        to="assistants.communitycontext",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="context_upvotes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Context Upvote",
                "verbose_name_plural": "Context Upvotes",
            },
        ),
        migrations.CreateModel(
            name="FlaggedQuestion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "tracking_id",
                    models.CharField(
                        help_text="Unique ID for tracking this flagged question, especially for anonymous users",
                        max_length=50,
                        unique=True,
                    ),
                ),
                (
                    "question",
                    models.TextField(help_text="The question that was flagged"),
                ),
                (
                    "question_text",
                    models.TextField(
                        blank=True, help_text="The question that was flagged", null=True
                    ),
                ),
                (
                    "original_answer",
                    models.TextField(help_text="The answer that was provided"),
                ),
                (
                    "reason",
                    models.TextField(blank=True, help_text="Reason for flagging"),
                ),
                (
                    "is_anonymous",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this question was flagged by an anonymous user",
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True,
                        help_text="Optional email for anonymous users to receive notifications",
                        max_length=254,
                        null=True,
                    ),
                ),
                (
                    "is_resolved",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this flagged question has been resolved",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                (
                    "assistant",
                    models.ForeignKey(
                        limit_choices_to={"assistant_type": "community"},
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="flagged_questions",
                        to="assistants.assistant",
                    ),
                ),
                (
                    "flagged_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="flagged_questions_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resolved_questions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="flagged_questions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Flagged Question",
                "verbose_name_plural": "Flagged Questions",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ContextNotification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_read", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "context",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to="assistants.communitycontext",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="context_notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "flagged_question",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to="assistants.flaggedquestion",
                    ),
                ),
            ],
            options={
                "verbose_name": "Context Notification",
                "verbose_name_plural": "Context Notifications",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Interaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("prompt", models.TextField()),
                ("response", models.TextField()),
                ("context", models.TextField(blank=True)),
                (
                    "rating",
                    models.IntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                (
                    "duration",
                    models.FloatField(
                        help_text="Time taken to generate response in seconds"
                    ),
                ),
                (
                    "token_count",
                    models.IntegerField(
                        help_text="Total tokens used in the interaction"
                    ),
                ),
                (
                    "use_community_context",
                    models.BooleanField(
                        default=False,
                        help_text="Whether to use community context for this interaction",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "assistant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="interactions",
                        to="assistants.assistant",
                    ),
                ),
                (
                    "used_contexts",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Contexts used in generating this response",
                        related_name="interactions_used_in",
                        to="assistants.communitycontext",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assistant_interactions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="AnswerUpvote",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="answer_upvotes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "interaction",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="upvotes",
                        to="assistants.interaction",
                    ),
                ),
            ],
            options={
                "verbose_name": "Answer Upvote",
                "verbose_name_plural": "Answer Upvotes",
            },
        ),
        migrations.CreateModel(
            name="NavigationItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "label",
                    models.CharField(
                        help_text="Display name for the navigation item.",
                        max_length=100,
                    ),
                ),
                (
                    "unique_id",
                    models.SlugField(
                        editable=False,
                        help_text="Unique identifier for this section (auto-generated from label).",
                        max_length=100,
                    ),
                ),
                (
                    "order",
                    models.PositiveIntegerField(
                        db_index=True,
                        default=0,
                        help_text="Order in sidebar (lower numbers first).",
                    ),
                ),
                (
                    "visible",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this item appears in the navigation.",
                    ),
                ),
                (
                    "section_type",
                    models.CharField(
                        choices=[
                            ("text", "Text"),
                            ("product", "Product"),
                            ("service", "Service"),
                            ("team", "Team"),
                            ("location", "Location"),
                            ("faq", "FAQ"),
                        ],
                        default="text",
                        help_text="The type of content this navigation item represents.",
                        max_length=20,
                    ),
                ),
                (
                    "gallery",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="Gallery images for this navigation item.",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "assistant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="navigation_items",
                        to="assistants.assistant",
                    ),
                ),
            ],
            options={
                "verbose_name": "Navigation Item",
                "verbose_name_plural": "Navigation Items",
                "ordering": ["order", "label"],
            },
        ),
        migrations.CreateModel(
            name="ReportedContent",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("object_id", models.PositiveIntegerField()),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("spam", "Spam"),
                            ("inappropriate", "Inappropriate Content"),
                            ("offensive", "Offensive Language"),
                            ("misinformation", "Misinformation"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                ("reason", models.TextField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Review"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("removed", "Content Removed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "assistant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reported_content",
                        to="assistants.assistant",
                    ),
                ),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "reported_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="reported_content",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Reported Content",
                "verbose_name_plural": "Reported Content",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ModeratorAction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("approve", "Approve Content"),
                            ("reject", "Reject Content"),
                            ("remove", "Remove Content"),
                            ("warn", "Warn User"),
                            ("ban", "Ban User"),
                            ("unban", "Unban User"),
                            ("adjust_reputation", "Adjust Reputation"),
                        ],
                        max_length=20,
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                ("reputation_adjustment", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "moderator",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="moderator_actions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "target_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="moderation_received",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "reported_content",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="moderator_actions",
                        to="assistants.reportedcontent",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="UserBan",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("reason", models.TextField()),
                ("is_permanent", models.BooleanField(default=False)),
                ("start_date", models.DateTimeField(auto_now_add=True)),
                ("end_date", models.DateTimeField(blank=True, null=True)),
                (
                    "assistant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="banned_users",
                        to="assistants.assistant",
                    ),
                ),
                (
                    "banned_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="bans_issued",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bans",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-start_date"],
            },
        ),
        migrations.CreateModel(
            name="UserReputation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("score", models.IntegerField(default=0)),
                ("level", models.CharField(default="New Member", max_length=50)),
                ("contributions_count", models.PositiveIntegerField(default=0)),
                ("upvotes_received", models.PositiveIntegerField(default=0)),
                ("reports_against", models.PositiveIntegerField(default=0)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reputation",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.AddIndex(
            model_name="assistant",
            index=models.Index(
                fields=["company", "assistant_type"],
                name="assistants__company_d6ce21_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="assistant",
            index=models.Index(
                fields=["company", "is_active"], name="assistants__company_ce9d32_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="comment",
            index=models.Index(
                fields=["content_type", "object_id"],
                name="assistants__content_12dca1_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="comment",
            index=models.Index(
                fields=["user", "created_at"], name="assistants__user_id_2b4438_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="communitycontext",
            index=models.Index(
                fields=["assistant", "created_at"],
                name="assistants__assista_6f597f_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="communitycontext",
            index=models.Index(
                fields=["is_active"], name="assistants__is_acti_c3cc4d_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="contextupvote",
            unique_together={("user", "context")},
        ),
        migrations.AddIndex(
            model_name="flaggedquestion",
            index=models.Index(
                fields=["assistant", "created_at"],
                name="assistants__assista_4daf20_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="flaggedquestion",
            index=models.Index(
                fields=["is_resolved"], name="assistants__is_reso_c0292c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="contextnotification",
            index=models.Index(
                fields=["user", "is_read"], name="assistants__user_id_c2cf10_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="interaction",
            index=models.Index(
                fields=["assistant", "created_at"],
                name="assistants__assista_c8f189_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="interaction",
            index=models.Index(
                fields=["user", "created_at"], name="assistants__user_id_121c89_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="answerupvote",
            unique_together={("user", "interaction")},
        ),
        migrations.AlterUniqueTogether(
            name="navigationitem",
            unique_together={("assistant", "label")},
        ),
    ]
