import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import models
from assistants.models import Assistant

# Get all community assistants
community_assistants = Assistant.objects.filter(
    assistant_type=Assistant.TYPE_COMMUNITY,
    is_active=True
)

# Print count
print(f"Total community assistants: {community_assistants.count()}")

# Group by tier
tiers = {}
for assistant in community_assistants:
    tier = assistant.tier
    if tier not in tiers:
        tiers[tier] = []
    tiers[tier].append(assistant)

# Print tier counts
print("\nCommunity assistants by tier:")
for tier, assistants in tiers.items():
    print(f"{tier}: {len(assistants)}")
    # Print details of each assistant
    for assistant in assistants:
        print(f"  - {assistant.id}: {assistant.name} (Company: {assistant.company.name})")
        print(f"    - is_featured: {assistant.is_featured}")
        print(f"    - is_public: {assistant.is_public}")
        print(f"    - is_active: {assistant.is_active}")
