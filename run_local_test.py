#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to run Django in local test mode with cPanel-like settings.
This script sets up the environment variables and runs the Django development server.
"""

import os
import sys
import subprocess
import stat

def setup_environment():
    """Set up the environment for local testing."""
    # Set environment variables
    os.environ['DJANGO_SETTINGS_MODULE'] = 'company_assistant.settings'
    os.environ['CPANEL_ENV'] = 'True'
    os.environ['DEBUG'] = 'True'
    
    # Create necessary directories
    base_dir = os.path.dirname(os.path.abspath(__file__))
    for directory in ['logs', 'cache', 'token_storage', 'media', 'session']:
        dir_path = os.path.join(base_dir, directory)
        os.makedirs(dir_path, exist_ok=True)
        try:
            # Set permissions (0777) for all directories
            os.chmod(dir_path, stat.S_IRWXU | stat.S_IRWXG | stat.S_IRWXO)
        except Exception as e:
            print(f"Warning: Could not set permissions for {directory} directory: {e}")
    
    # Print confirmation
    print("=== Local Test Environment Setup ===")
    print(f"Base directory: {base_dir}")
    print(f"Session directory: {os.path.join(base_dir, 'session')}")
    print(f"Cache directory: {os.path.join(base_dir, 'cache')}")
    print(f"Token storage directory: {os.path.join(base_dir, 'token_storage')}")
    print(f"Log directory: {os.path.join(base_dir, 'logs')}")
    print(f"Media directory: {os.path.join(base_dir, 'media')}")
    print("Environment variables:")
    print(f"  DJANGO_SETTINGS_MODULE: {os.environ.get('DJANGO_SETTINGS_MODULE')}")
    print(f"  CPANEL_ENV: {os.environ.get('CPANEL_ENV')}")
    print(f"  DEBUG: {os.environ.get('DEBUG')}")
    print("===============================")

def run_server():
    """Run the Django development server."""
    print("\nStarting Django development server with cPanel-like settings...")
    try:
        # Run the Django development server
        subprocess.run(
            [sys.executable, "manage.py", "runserver"],
            check=True
        )
    except subprocess.CalledProcessError as e:
        print(f"Error running server: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nServer stopped by user.")
        sys.exit(0)

def main():
    """Main function."""
    setup_environment()
    run_server()

if __name__ == "__main__":
    main()
