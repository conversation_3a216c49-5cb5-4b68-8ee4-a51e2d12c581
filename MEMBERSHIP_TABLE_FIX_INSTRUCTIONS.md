# Fixing the Missing Table Issue

You're encountering an error because a migration is trying to remove a table that doesn't exist. The error message is:

```
django.db.utils.ProgrammingError: table "accounts_membership_accessible_folders" does not exist
```

This is happening because migration `0010_remove_registrationlink_role_remove_membership_role_and_more.py` is trying to remove the `accessible_folders` field from the `Membership` model, but the corresponding table doesn't exist in the database.

## Option 1: Using the Python Script

We've created a Python script to fix this issue. Run:

```bash
python fix_membership_table.py
```

Then run the migrations:

```bash
python manage.py migrate
```

## Option 2: Using SQL

If the Python script doesn't work, you can directly modify the database:

1. Open a PostgreSQL command prompt:
   ```bash
   psql -U postgres -d postgres2
   ```
   (Enter your password when prompted)

2. Run these SQL commands:
   ```sql
   -- Mark the migration as applied
   INSERT INTO django_migrations (app, name, applied)
   SELECT 'accounts', '0010_remove_registrationlink_role_remove_membership_role_and_more', NOW()
   WHERE NOT EXISTS (
       SELECT 1 FROM django_migrations 
       WHERE app = 'accounts' AND name = '0010_remove_registrationlink_role_remove_membership_role_and_more'
   );

   -- Verify the migration is marked as applied
   SELECT app, name, applied 
   FROM django_migrations 
   WHERE app = 'accounts' 
   ORDER BY applied;
   ```

3. Exit the PostgreSQL prompt:
   ```sql
   \q
   ```

4. Run the remaining migrations:
   ```bash
   python manage.py migrate
   ```

## Option 3: Using the Batch File

We've created a batch file to run the SQL script. Simply run:

```bash
fix_membership_table.bat
```

Then run the migrations:

```bash
python manage.py migrate
```

## Understanding the Issue

The issue is that your database schema is out of sync with the migration history. The `accessible_folders` field was added in migration `0006_combined_rbac_setup.py`, but the corresponding table might not have been created properly.

When migration `0010_remove_registrationlink_role_remove_membership_role_and_more.py` tries to remove this field, it can't find the table to remove.

By marking the migration as applied without actually running it, we're telling Django that the field has already been removed, so it can continue with the remaining migrations.

## Verifying the Fix

After applying any of these fixes, verify that the migrations are properly applied:

```bash
python manage.py showmigrations accounts
```

You should see `0010_remove_registrationlink_role_remove_membership_role_and_more` marked as applied (with an [X]).

## Troubleshooting

If you continue to have issues:

1. You might need to manually create the table before removing it:
   ```sql
   CREATE TABLE accounts_membership_accessible_folders (
       id SERIAL PRIMARY KEY,
       membership_id INTEGER NOT NULL,
       assistantfolder_id INTEGER NOT NULL,
       UNIQUE (membership_id, assistantfolder_id)
   );
   ```

2. If all else fails, you might need to:
   - Backup your data
   - Drop the database
   - Create a new database
   - Run migrations from scratch
   - Restore your data
