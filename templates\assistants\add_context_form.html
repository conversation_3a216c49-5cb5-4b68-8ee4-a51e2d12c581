{% extends 'base/layout.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Add Context - {{ assistant.name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row align-items-center mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Add Your Knowledge</h1>
            <p class="text-muted mb-0">
                Help improve {{ assistant.name }} by contributing your expertise to this question
            </p>
        </div>
        <div class="col-auto">
            <a href="{% url 'assistants:flagged_question_detail' company.id assistant.id flagged.id %}" class="btn btn-light">
                <i class="bi bi-arrow-left me-2"></i>
                Back to Question
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Original Question & Answer</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Question:</label>
                        <div class="p-3 bg-light rounded">{{ flagged.question }}</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">Current Answer:</label>
                        <div class="p-3 bg-light rounded">{{ flagged.original_answer }}</div>
                    </div>
                    <div>
                        <label class="form-label fw-bold">Reason for flagging:</label>
                        <div class="p-3 bg-light rounded">{{ flagged.reason }}</div>
                    </div>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="card-title mb-0">Share Your Knowledge</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}

                        <div class="mb-3">
                            <label for="id_title" class="form-label fw-bold">Title:</label>
                            <input type="text" class="form-control" id="id_title" name="title" placeholder="Give your contribution a descriptive title" value="Context for: {{ flagged.question|truncatechars:50 }}">
                        </div>

                        <div class="mb-4">
                            <label for="id_text_content" class="form-label fw-bold">Your Knowledge (max 500 words):</label>
                            <textarea class="form-control" id="id_text_content" name="text_content" rows="8" placeholder="Please provide additional information, facts, or context that would improve this answer. Your contribution will help the community."></textarea>
                            <div class="word-counter mt-1 text-end">
                                <small class="text-muted" id="word-count">0</small>
                                <small class="text-muted"> / 500 words</small>
                            </div>
                            <div class="form-text text-info mt-2">
                                <i class="bi bi-lightbulb me-1"></i>
                                Your knowledge will be added to the assistant's context and help improve future responses.
                            </div>
                        </div>



                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>
                                Submit Contribution
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="card-title mb-0">What Happens Next?</h5>
                </div>
                <div class="card-body">
                    <ol class="ps-3">
                        <li class="mb-2">Your contribution will be added to the community knowledge base</li>
                        <li class="mb-2">Other community members can view and build upon your knowledge</li>
                        <li class="mb-2">The person who flagged this question will be notified</li>
                        <li class="mb-2">The assistant will use this knowledge to provide better answers to everyone</li>
                    </ol>
                    <div class="alert alert-info mt-3">
                        <i class="bi bi-people-fill me-2"></i>
                        Your contributions help build a more knowledgeable community assistant!
                    </div>
                </div>
            </div>

            <div class="card shadow-sm mt-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Tips for Great Contributions</h5>
                </div>
                <div class="card-body">
                    <ul class="ps-3">
                        <li class="mb-2">Be specific and factual</li>
                        <li class="mb-2">Include relevant details that were missing</li>
                        <li class="mb-2"><strong>Keep content under 500 words</strong></li>
                        <li class="mb-2">Cite sources when possible</li>
                        <li class="mb-2">Focus on information, not opinions</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Load TinyMCE JavaScript using Django's form media -->
{{ form.media.js }}

<!-- Load TinyMCE word limit script -->
<script src="{% static 'js/tinymce-word-limit.js' %}"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize TinyMCE
        tinymce.init({
            selector: '#id_text_content',
            height: 400,
            width: '100%',
            menubar: 'file edit view insert format tools table help',
            plugins: 'lists link image table wordcount',
            toolbar: 'bold italic | alignleft aligncenter alignright | bullist numlist | link image table | hr',
            toolbar_mode: 'sliding',
            statusbar: true,
            branding: false,
            promotion: false,
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px; min-height: 300px; }',
            setup: function(editor) {
                editor.on('change', function() {
                    editor.save();
                });
                editor.on('init', function() {
                    // Initialize word count limit
                    if (typeof initWordCountLimit === 'function') {
                        initWordCountLimit('id_text_content', 'word-count');
                    }
                });
            }
        });

        // Add form submit handler to prevent submission if over limit
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                if (typeof countTinyMCEWords === 'function') {
                    const wordCount = countTinyMCEWords('id_text_content');
                    if (wordCount > 500) {
                        e.preventDefault();
                        alert('Your content exceeds the 500 word limit. Please shorten your text to submit.');
                        return false;
                    }
                }
            });
        }
    });
</script>
{% endblock %}
