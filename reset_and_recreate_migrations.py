"""
Master script to reset and recreate all migrations.
This script will:
1. Delete all migration files
2. Reset the migration history in the database
3. Recreate and apply migrations for all apps
"""
import os
import sys
import django
import importlib

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import our scripts
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
delete_migrations_module = importlib.import_module('delete_migrations')
reset_migrations_module = importlib.import_module('reset_migrations')
recreate_migrations_module = importlib.import_module('recreate_migrations')

def main():
    """Run all migration reset and recreation steps."""
    print("=== MIGRATION RESET AND RECREATION PROCESS ===")
    print("\nThis process will:")
    print("1. Delete all migration files")
    print("2. Reset the migration history in the database")
    print("3. Recreate and apply migrations for all apps")
    print("\nWARNING: This will delete all migration files and reset the database schema.")
    print("Make sure you have a backup of your database if you need to preserve data.")
    
    # Confirm before proceeding
    confirm = input("\nDo you want to proceed? (y/n): ")
    if confirm.lower() != 'y':
        print("Operation cancelled.")
        return
    
    # Step 1: Delete all migration files
    print("\n=== STEP 1: DELETING MIGRATION FILES ===")
    delete_migrations_module.delete_migrations()
    
    # Step 2: Reset the migration history
    print("\n=== STEP 2: RESETTING MIGRATION HISTORY ===")
    if hasattr(reset_migrations_module, 'reset_migrations'):
        reset_migrations_module.reset_migrations()
    else:
        reset_migrations_module.reset_migration_history()
    
    # Step 3: Recreate and apply migrations
    print("\n=== STEP 3: RECREATING AND APPLYING MIGRATIONS ===")
    recreate_migrations_module.recreate_migrations()
    
    print("\n=== MIGRATION RESET AND RECREATION COMPLETED ===")
    print("Your database schema should now be properly set up with clean migrations.")

if __name__ == "__main__":
    main()
