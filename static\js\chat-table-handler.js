/**
 * Chat Table Handler
 * Improves table rendering in chat messages
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize table handling
    initTableHandler();

    // Set up mutation observer to handle dynamically added tables
    observeChatBox();
});

/**
 * Initialize table handling for existing tables
 */
function initTableHandler() {
    // Process all tables in message content
    processAllTables();

    // Add window resize listener to check table scrollability
    window.addEventListener('resize', function() {
        checkTableScrollability();
    });
}

/**
 * Process all tables in message content
 */
function processAllTables() {
    // Get all tables in message content
    const tables = document.querySelectorAll('.message-content table');

    // Process each table
    tables.forEach(function(table) {
        processTable(table);
    });

    // Check table scrollability after processing
    setTimeout(checkTableScrollability, 100);
}

/**
 * Process a single table
 * @param {HTMLElement} table - The table element to process
 */
function processTable(table) {
    // Skip if table is already processed
    if (table.closest('.table-wrapper') || table.hasAttribute('data-processed')) {
        return;
    }

    // Detect contact information tables
    const isContactTable = detectContactInfoTable(table);

    // Add special class for contact tables
    if (isContactTable) {
        table.classList.add('contact-info');
    }

    // Fix missing structure
    fixTableStructure(table);

    // Create table wrapper
    const wrapper = document.createElement('div');
    wrapper.className = 'table-wrapper';

    // Replace table with wrapper containing table
    table.parentNode.insertBefore(wrapper, table);
    wrapper.appendChild(table);

    // Ensure table has proper styling
    table.style.width = '100%';
    table.style.margin = '0';

    // Add data-processed attribute to mark as processed
    table.setAttribute('data-processed', 'true');
}

/**
 * Detect if a table is a contact information table
 * @param {HTMLElement} table - The table element to check
 * @returns {boolean} - Whether the table is a contact information table
 */
function detectContactInfoTable(table) {
    // Get all text content from the table
    const tableText = table.textContent.toUpperCase();

    // Check for common contact information keywords
    const contactKeywords = [
        'CHAT', 'EMAIL', 'PHONE', 'CONTACT', 'OFFICE', 'HOURS',
        'LOCATION', 'ADDRESS', 'CALL', 'MEDIA', 'PRESS'
    ];

    // Check if any of the keywords are present
    return contactKeywords.some(keyword => tableText.includes(keyword));
}

/**
 * Fix missing table structure
 * @param {HTMLElement} table - The table element to fix
 */
function fixTableStructure(table) {
    // Check if table has tbody
    if (!table.querySelector('tbody')) {
        // Create tbody
        const tbody = document.createElement('tbody');

        // Move all rows to tbody
        while (table.firstChild) {
            tbody.appendChild(table.firstChild);
        }

        // Add tbody to table
        table.appendChild(tbody);
    }

    // Check if table has any rows
    if (table.querySelectorAll('tr').length === 0) {
        // Create a row
        const tr = document.createElement('tr');

        // Create a cell
        const td = document.createElement('td');

        // Move all content to cell
        while (table.firstChild && table.firstChild.nodeName !== 'TBODY') {
            td.appendChild(table.firstChild);
        }

        // Add cell to row
        tr.appendChild(td);

        // Add row to tbody or table
        const tbody = table.querySelector('tbody') || table;
        tbody.appendChild(tr);
    }
}

/**
 * Check if tables are scrollable and add appropriate class
 */
function checkTableScrollability() {
    const wrappers = document.querySelectorAll('.table-wrapper');

    wrappers.forEach(function(wrapper) {
        // Check if table is wider than wrapper
        const isScrollable = wrapper.scrollWidth > wrapper.clientWidth;

        // Add or remove scrollable class
        if (isScrollable) {
            wrapper.classList.add('scrollable');
        } else {
            wrapper.classList.remove('scrollable');
        }
    });
}

/**
 * Set up mutation observer to handle dynamically added tables
 */
function observeChatBox() {
    const chatBox = document.getElementById('chat-box');

    if (!chatBox) {
        return;
    }

    // Create mutation observer
    const observer = new MutationObserver(function(mutations) {
        let tableAdded = false;

        // Check if any tables were added
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // Check each added node
                mutation.addedNodes.forEach(function(node) {
                    // If node is an element, check for tables
                    if (node.nodeType === 1) {
                        const tables = node.querySelectorAll('.message-content table');
                        if (tables.length > 0) {
                            tableAdded = true;
                        }
                    }
                });
            }
        });

        // If tables were added, process them
        if (tableAdded) {
            processAllTables();
        }
    });

    // Start observing chat box
    observer.observe(chatBox, {
        childList: true,
        subtree: true
    });
}

/**
 * Process HTML content to wrap tables before adding to DOM
 * @param {string} html - The HTML content to process
 * @returns {string} - The processed HTML content
 */
function processHtmlTables(html) {
    // Create a temporary div to parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Find all tables
    const tables = tempDiv.querySelectorAll('table');

    // Process each table
    tables.forEach(function(table) {
        // Skip if table is already processed
        if (table.closest('.table-wrapper') || table.hasAttribute('data-processed')) {
            return;
        }

        // Detect contact information tables
        const isContactTable = detectContactInfoTable(table);

        // Add special class for contact tables
        if (isContactTable) {
            table.classList.add('contact-info');
        }

        // Fix missing structure
        fixTableStructure(table);

        // Create table wrapper
        const wrapper = document.createElement('div');
        wrapper.className = 'table-wrapper';

        // Replace table with wrapper containing table
        table.parentNode.insertBefore(wrapper, table);
        wrapper.appendChild(table);

        // Ensure table has proper styling
        table.style.width = '100%';
        table.style.margin = '0';

        // Add data-processed attribute to mark as processed
        table.setAttribute('data-processed', 'true');
    });

    // Return processed HTML
    return tempDiv.innerHTML;
}

/**
 * Fix tables that are not properly structured in HTML content
 * @param {string} html - The HTML content to process
 * @returns {string} - The processed HTML content
 */
function fixTableHtml(html) {
    // Check if the content contains a table
    if (!html.includes('<table') || !html.includes('</table>')) {
        return html;
    }

    // Create a temporary div to parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Find all tables
    const tables = tempDiv.querySelectorAll('table');

    // Process each table
    tables.forEach(function(table) {
        // Check if this is a contact information table
        const tableText = table.textContent.toUpperCase();
        const isContactTable = ['CHAT', 'EMAIL', 'PHONE', 'CONTACT', 'OFFICE', 'HOURS',
                               'LOCATION', 'ADDRESS', 'CALL', 'MEDIA', 'PRESS']
                               .some(keyword => tableText.includes(keyword));

        // Add special class for contact tables
        if (isContactTable) {
            table.classList.add('contact-info');
        }

        // Check if table has proper structure
        if (!table.querySelector('tbody') && table.querySelectorAll('tr').length > 0) {
            // Create tbody
            const tbody = document.createElement('tbody');

            // Move all rows to tbody
            Array.from(table.querySelectorAll('tr')).forEach(row => {
                tbody.appendChild(row);
            });

            // Add tbody to table
            table.appendChild(tbody);
        }
    });

    // Return processed HTML
    return tempDiv.innerHTML;
}

// Make functions available globally
window.processHtmlTables = processHtmlTables;
window.fixTableHtml = fixTableHtml;
