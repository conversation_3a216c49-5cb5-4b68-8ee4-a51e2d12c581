"""
Script to fake a specific migration that's causing issues.
"""
import os
import sys
import django
from django.db import connection

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

def fake_migration_record():
    """Manually insert a record in the django_migrations table."""
    print("Faking migration record in django_migrations table...")
    try:
        with connection.cursor() as cursor:
            # Check if the migration is already recorded
            cursor.execute("""
            SELECT EXISTS (
                SELECT 1 FROM django_migrations
                WHERE app = 'assistants' AND name = '0039_alter_assistant_is_active'
            );
            """)
            migration_exists = cursor.fetchone()[0]

            if migration_exists:
                print("Migration assistants.0039_alter_assistant_is_active already recorded.")
                return True

            # Insert the migration record
            cursor.execute("""
            INSERT INTO django_migrations (app, name, applied)
            VALUES ('assistants', '0039_alter_assistant_is_active', datetime('now'));
            """)

            print("Migration record for assistants.0039_alter_assistant_is_active added successfully.")
            return True
    except Exception as e:
        print(f"Error faking migration record: {e}")
        return False

def main():
    """Main function to fake a specific migration."""
    print("Starting migration fix...")

    # Fake the migration record
    if not fake_migration_record():
        print("Failed to fake migration record. Aborting.")
        return

    print("\nNext steps:")
    print("1. Run 'python manage.py runserver' to start the server")
    print("2. If you still encounter issues, try 'python manage.py migrate --fake-initial'")

if __name__ == "__main__":
    main()
