import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django's database connection
from django.db import connection

print("Starting CompanyInvitation table creation script...")

# SQL to create the accounts_companyinvitation table
companyinvitation_sql = """
CREATE TABLE IF NOT EXISTS "accounts_companyinvitation" (
    "id" serial NOT NULL PRIMARY KEY,
    "email" varchar(254) NOT NULL,
    "token" varchar(100) NOT NULL UNIQUE,
    "status" varchar(20) NOT NULL,
    "invited_at" timestamp with time zone NOT NULL,
    "expires_at" timestamp with time zone NOT NULL,
    "accepted_at" timestamp with time zone NULL,
    "company_id" integer NOT NULL REFERENCES "accounts_company" ("id") DEFERRABLE INITIALLY DEFERRED,
    "invited_by_id" integer NULL REFERENCES "auth_user" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# Execute the SQL
try:
    with connection.cursor() as cursor:
        print("Creating accounts_companyinvitation table...")
        cursor.execute(companyinvitation_sql)
        print("CompanyInvitation table created successfully!")
    
    print("Table created successfully!")
except Exception as e:
    print(f"Error creating table: {e}")
