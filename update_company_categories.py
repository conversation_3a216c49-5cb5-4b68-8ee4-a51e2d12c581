import os
import django
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django models
from accounts.models import CompanyInformation
from directory.models import CompanyListing, CompanyCategory

def load_categories_json():
    """Load the categories from the JSON file."""
    try:
        with open('static/json/category.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: category.json file not found in the static/json directory.")
        return {}
    except json.JSONDecodeError:
        print("Error: category.json file is not valid JSON.")
        return {}

def update_company_categories():
    """Update company categories to use the new format."""
    categories_data = load_categories_json()
    if not categories_data:
        print("No categories data found. Exiting.")
        return

    # Get all company information records
    company_infos = CompanyInformation.objects.all()
    print(f"Found {len(company_infos)} company information records.")

    for info in company_infos:
        # Get the current industry
        current_industry = info.industry.strip()
        
        # Skip if no industry is set
        if not current_industry:
            continue
        
        # Find matching industry in the categories data
        matching_industry = None
        for industry in categories_data.keys():
            if industry.lower() == current_industry.lower():
                matching_industry = industry
                break
        
        if matching_industry:
            # Update the industry to match the exact case from the JSON
            info.industry = matching_industry
            
            # Get the company listing
            try:
                listing = CompanyListing.objects.get(company=info.company)
                
                # Get the categories from the listing
                category_names = [cat.name for cat in listing.categories.all()]
                
                # Convert to comma-separated string and save to CompanyInformation
                if category_names:
                    info.categories = ', '.join(category_names)
                
                # Save the updated information
                info.save()
                print(f"Updated {info.company.name}: Industry={info.industry}, Categories={info.categories}")
            except CompanyListing.DoesNotExist:
                # No listing exists, just save the industry
                info.save()
                print(f"Updated {info.company.name}: Industry={info.industry} (no listing found)")
        else:
            print(f"No matching industry found for '{current_industry}' in {info.company.name}")

def clear_company_categories():
    """Clear all CompanyCategory records."""
    count = CompanyCategory.objects.count()
    CompanyCategory.objects.all().delete()
    print(f"Deleted {count} CompanyCategory records.")

if __name__ == "__main__":
    print("Starting company categories update...")
    update_company_categories()
    clear_company_categories()
    print("Company categories update complete.")
