from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.urls import reverse
from guardian.shortcuts import assign_perm
from directory.qr_utils import generate_styled_qr_with_letter_a_in_triangle # Updated QR utility
from django.contrib.sites.models import Site
from .models import Assistant
from directory.models import AssistantListing # For directory syncing
from django.utils.html import strip_tags      # For processing HTML description
from django.utils.text import Truncator       # For creating short description

@receiver(post_save, sender=Assistant)
def assign_assistant_permissions(sender, instance, created, **kwargs):
    """
    Assigns object-level permissions for a new assistant to relevant groups and the company owner.
    Styled QR code generation is handled by the 'assistant_post_save_generate_styled_qr' signal.
    """
    if created:
        try:
            # Get relevant groups
            admin_group = Group.objects.get(name='Company Administrators')
            member_group = Group.objects.get(name='Company Members')
            viewer_group = Group.objects.get(name='Company Guests')

            # Define permissions for each group
            admin_member_perms = [
                'view_assistant',
                'change_assistant',
                'delete_assistant',
                'view_assistant_usage',
                'view_assistant_analytics',
                # 'create_assistant_token', # Maybe only admins? Decide based on requirements
                # 'access_all_private', # This is likely a company-level perm, not object-level
            ]

            viewer_perms = [
                'view_assistant',  # Viewers should be able to view assistants
            ]

            # Map groups to their permissions
            group_perms = {
                admin_group: admin_member_perms,
                member_group: admin_member_perms,
                viewer_group: viewer_perms
            }

            # Assign permissions to groups based on their role
            for group, perms in group_perms.items():
                for perm_codename in perms:
                    try:
                        # Construct the full permission name
                        full_perm_name = f"assistants.{perm_codename}"
                        # Assign the permission for the specific assistant instance to the group
                        assign_perm(full_perm_name, group, instance)
                        print(f"Assigned '{full_perm_name}' for Assistant ID {instance.id} to Group '{group.name}'")
                    except Permission.DoesNotExist:
                        print(f"Warning: Permission '{full_perm_name}' not found. Skipping assignment for Group '{group.name}'.")
                    except Exception as e:
                        print(f"Error assigning perm '{full_perm_name}' to group '{group.name}' for assistant {instance.id}: {e}")

            # Assign permissions to the company owner
            owner = instance.company.owner
            if owner:
                print(f"Assigning assistant permissions to owner '{owner.username}' for Assistant ID {instance.id}")
                # Use the admin permissions for the owner
                for perm_codename in admin_member_perms:
                    try:
                        # Construct the full permission name
                        full_perm_name = f"assistants.{perm_codename}"
                        # Assign the permission for the specific assistant instance to the owner
                        assign_perm(full_perm_name, owner, instance)
                        print(f"Assigned '{full_perm_name}' for Assistant ID {instance.id} to Owner '{owner.username}'")
                    except Permission.DoesNotExist:
                        print(f"Warning: Permission '{full_perm_name}' not found. Skipping assignment for Owner '{owner.username}'.")
                    except Exception as e:
                        print(f"Error assigning perm '{full_perm_name}' to owner '{owner.username}' for assistant {instance.id}: {e}")

        except Group.DoesNotExist as e:
            print(f"Warning: Could not find required group during assistant permission assignment: {e}. Permissions may not be fully assigned.")
        except Exception as e:
            # Catch any other unexpected errors during permission assignment
            print(f"Error in assign_assistant_permissions signal for Assistant ID {instance.id}: {e}")


@receiver(post_save, sender=Assistant)
def assistant_post_save_update_listing(sender, instance, created, **kwargs):
    """
    Synchronizes the Assistant's listing status in the directory.
    Creates or updates an AssistantListing based on the assistant's
    is_public and is_active status, and syncs descriptive fields.
    """
    should_be_listed = instance.is_public and instance.is_active

    # Prepare description fields
    plain_description = strip_tags(instance.description) # Remove HTML tags for short description
    short_desc = Truncator(plain_description).chars(250, truncate='...') # Truncate to 250 chars with an ellipsis
    long_desc = instance.description # Keep HTML for long description

    listing, listing_created = AssistantListing.objects.get_or_create(
        assistant=instance,
        defaults={
            'short_description': short_desc,
            'long_description': long_desc,
            'is_listed': should_be_listed
            # Note: 'categories', 'tags', 'capabilities' are not sourced here
            # They would need to be on the Assistant model or handled elsewhere if needed during this signal
        }
    )

    updated_fields = []
    if not listing_created: # Only check for updates if the listing was not just created with defaults
        if listing.is_listed != should_be_listed:
            listing.is_listed = should_be_listed
            updated_fields.append('is_listed')

        if listing.short_description != short_desc:
            listing.short_description = short_desc
            updated_fields.append('short_description')

        if listing.long_description != long_desc:
            listing.long_description = long_desc
            updated_fields.append('long_description')

    if updated_fields:
        listing.save(update_fields=updated_fields)
        print(f"Updated AssistantListing for '{instance.name}'. Fields updated: {updated_fields}.")
    elif listing_created:
        print(f"Created AssistantListing for '{instance.name}'. is_listed: {listing.is_listed}, short_description: '{Truncator(listing.short_description).chars(30, truncate='...')}'")
    # No specific print if not created and no fields changed that we track here.


@receiver(post_save, sender=Assistant)
def assistant_post_save_generate_qr(sender, instance, created, **kwargs):
    """
    Generates a QR code with an 'A' in the center for the assistant if it has a slug and
    either the QR code is missing or the slug has changed.
    The QR code points to the assistant's public chat page.
    """
    from utils.qr_generator import generate_model_qr_code
    import logging
    logger = logging.getLogger(__name__)

    # Always generate a slug if it doesn't exist
    if not instance.slug:
        from django.utils.text import slugify
        try:
            instance.slug = slugify(f"{instance.name}-{instance.id}")
            # Save without triggering this signal again
            Assistant.objects.filter(pk=instance.pk).update(slug=instance.slug)
            logger.info(f"Generated slug '{instance.slug}' for Assistant '{instance.name}' (PK: {instance.pk})")
        except Exception as e:
            logger.error(f"Error generating slug for Assistant '{instance.name}' (PK: {instance.pk}): {str(e)}")
            return

    # Determine if QR code needs update
    regenerate_qr = False
    if created:
        regenerate_qr = True
        logger.info(f"New assistant '{instance.name}', preparing to generate QR code.")
    elif not instance.qr_code:
        regenerate_qr = True
        logger.info(f"Assistant '{instance.name}' is missing QR code, preparing to generate.")
    else:
        # Check if slug changed by checking if the current QR filename contains the current slug
        if instance.qr_code.name and instance.slug not in instance.qr_code.name:
            regenerate_qr = True
            logger.info(f"Assistant '{instance.name}' QR filename '{instance.qr_code.name}' does not match slug '{instance.slug}'. Regenerating QR.")

    if regenerate_qr:
        try:
            # Generate QR code using the assistant's chat URL
            url_path = reverse('assistants:assistant_chat', kwargs={'slug': instance.slug})
            success = generate_model_qr_code(instance, url_path, field_name='qr_code')

            if success:
                # Save the assistant with the new QR code without triggering this signal again
                Assistant.objects.filter(pk=instance.pk).update(qr_code=instance.qr_code, updated_at=instance.updated_at)
                logger.info(f"Successfully generated and saved QR code for Assistant: {instance.name}")
            else:
                logger.error(f"Failed to generate QR code for Assistant: {instance.name}")

        except Exception as e:
            import traceback
            logger.error(f"Error in assistant_post_save_generate_qr for Assistant '{instance.name}': {str(e)}")
            logger.error(traceback.format_exc())
