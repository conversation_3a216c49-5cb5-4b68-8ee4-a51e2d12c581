import json
import markdown # Import the markdown library
import traceback # Import traceback
import logging # Import logging
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, StreamingHttpResponse, HttpResponseForbidden
from django.core.paginator import Paginator
from django.contrib import messages
from django.utils import timezone
from datetime import timedelta # Import timedelta
from django.db.models import Avg, Count, Sum, Prefetch
from django.views.decorators.http import require_POST
# Removed csrf_exempt import
from django.core.exceptions import PermissionDenied
from django import forms
from django.urls import reverse, reverse_lazy
from accounts.utils import generate_qr_code
from django.contrib.auth.mixins import LoginRequiredMixin
from formtools.wizard.views import SessionWizardView
from django.db.models import Q, F # Import Q for complex lookups, F for ordering nulls last
from django.utils.text import slugify # Import slugify
import os # Import os for path joining
from django.conf import settings # Import settings
from django.db import transaction # Import transaction for atomic operations
from django.core.files.storage import FileSystemStorage # Import FileSystemStorage
from django.core.serializers.json import DjangoJSONEncoder # Import DjangoJSONEncoder
import re # Import re for regex parsing
from collections import defaultdict # Import defaultdict
from .llm_suggested_questions import generate_llm_suggested_questions
from .utils import handle_image_upload # Import the image handler

# Set up logger
logger = logging.getLogger(__name__)
logger.debug("Assistants views module loaded")

from itertools import groupby # Import groupby

from accounts.models import Company # Removed Membership import
# Import the helper function for group checks
from accounts.utils import can_access_assistant, _user_in_company_group
from directory.models import SavedItem, AssistantListing # Import directory models (Category/Tag not needed here)
from .models import Assistant, Interaction, AssistantFolder, CommunityContext # Import AssistantFolder and CommunityContext
from .forms import (
    AssistantForm, AssistantInteractionForm, AssistantTrainingForm,
    AssistantBasicInfoForm, AssistantConfigForm, AssistantFolderForm, # Import wizard forms and Folder form
    NavigationItemFormSet # Import the new formset
)
from .llm_utils import (
    generate_assistant_response,
    get_token_count,
    estimate_cost as calculate_cost
)
from .media_utils import ensure_media_directories

# Import test views
from .test_views import test_logo_upload

# --- Assistant Creation Wizard ---

# Define form steps
FORMS = [
    ("basic", AssistantBasicInfoForm),
    ("config", AssistantConfigForm),
]

# Define templates for each step
TEMPLATES = {
    "basic": "assistants/assistant_wizard_form.html",
    "config": "assistants/assistant_wizard_form.html",
}

class AssistantCreateWizard(LoginRequiredMixin, SessionWizardView):
    form_list = FORMS
    template_name = "assistants/assistant_wizard_form.html" # Default template
    file_storage = FileSystemStorage(location=os.path.join(settings.MEDIA_ROOT, 'temp_wizard_files'))

    def get_template_names(self):
        return [TEMPLATES[self.steps.current]]

    def get_context_data(self, form, **kwargs):
        context = super().get_context_data(form=form, **kwargs)
        company_id = self.kwargs['company_id']
        # Fetch company ensuring user has access, prefetch info for sidebar
        company = get_object_or_404(
            Company.objects.prefetch_related('info').filter( # Added prefetch
                Q(pk=company_id, owner=self.request.user) |
                Q(pk=company_id, memberships__user=self.request.user)
            ).distinct()
        )
        # Permission check: Use the new company-level permission for managing assistants
        if not self.request.user.has_perm('accounts.manage_company_assistants', company):
             raise PermissionDenied("You do not have permission to create assistants for this company.")

        # Check if we're in the Community Assistants section by examining the request path
        is_community_section = 'community-assistants' in self.request.path.lower()

        # If this is a community entity or we're in the Community Assistants section, restrict assistant types to only community type
        if company.entity_type == 'community' or is_community_section:
            # If we're on the first step (basic info), modify the form's assistant_type choices
            if self.steps.current == 'basic' and hasattr(form.fields, 'assistant_type'):
                form.fields['assistant_type'].choices = [(Assistant.TYPE_COMMUNITY, 'Community Assistant')]
                form.fields['assistant_type'].initial = Assistant.TYPE_COMMUNITY
                form.fields['assistant_type'].widget.attrs['disabled'] = 'disabled'
                # Add a hidden field to ensure the value is submitted
                form.fields['assistant_type_hidden'] = forms.CharField(
                    widget=forms.HiddenInput(),
                    initial=Assistant.TYPE_COMMUNITY,
                    required=False
                )

        context['company'] = company
        context['step_title'] = f"Step {self.steps.step1} of {self.steps.count}: {self.steps.current.capitalize()}"
        context['is_community_entity'] = company.entity_type == 'community'
        context['is_community_section'] = is_community_section
        return context

    def done(self, form_list, **kwargs):
        form_data = self.get_all_cleaned_data()
        company_id = self.kwargs['company_id']
        # Re-fetch company ensuring user has access
        company = get_object_or_404(
            Company.objects.filter(
                Q(pk=company_id, owner=self.request.user) |
                Q(pk=company_id, memberships__user=self.request.user)
            ).distinct()
        )
        # Permission check again before saving: Use the new company-level permission
        if not self.request.user.has_perm('accounts.manage_company_assistants', company):
             raise PermissionDenied("You do not have permission to create assistants for this company.")

        # Check if we're in the Community Assistants section by examining the request path
        is_community_section = 'community-assistants' in self.request.path.lower()

        # For community entities or if we're in the Community Assistants section, force the assistant type to be community
        if company.entity_type == 'community' or is_community_section:
            form_data['assistant_type'] = Assistant.TYPE_COMMUNITY
        # For regular companies and not in Community Assistants section, prevent creating community assistants
        elif form_data.get('assistant_type') == Assistant.TYPE_COMMUNITY:
            messages.error(self.request, "Community assistants can only be created within community entities or from the Community Assistants section.")
            return redirect(reverse_lazy('assistants:list', kwargs={'company_id': company_id}))

        # Create the assistant instance
        assistant = Assistant(
            company=company,
            created_by=self.request.user,
            name=form_data['name'],
            description=form_data['description'],
            assistant_type=form_data['assistant_type'],
            model=form_data['model'],
            temperature=form_data.get('temperature', 0.7),
            max_tokens=form_data.get('max_tokens', 2048),
            system_prompt=form_data.get('system_prompt', ''),
            greeting_message=form_data.get('greeting_message', ''),
            is_active=False,  # Set to False by default to require approval
            is_public=form_data.get('is_public', True)
        )

        # Handle OpenAI Compatible fields if model is openai-compatible
        if form_data['model'] == 'openai-compatible':
            assistant.api_key = form_data.get('api_key', '')
            assistant.base_url = form_data.get('base_url', 'https://api.openai.com/v1')
            assistant.custom_model_name = form_data.get('custom_model_name', '')

        # Handle logo separately to ensure it's properly processed
        logo = form_data.get('logo', None)
        if logo and hasattr(logo, 'content_type'):
            # Validate logo file type
            allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml']
            if logo.content_type in allowed_types:
                if logo.size <= 5 * 1024 * 1024:  # 5MB limit
                    assistant.logo = logo
                    print(f"Logo set during creation: {logo.name}")
                    # Ensure the logo is saved
                    assistant.save()
                else:
                    messages.warning(self.request, "Logo file size exceeds 5MB limit. Logo was not set.")

        assistant._temp_categories_str = form_data.get('categories', '')
        assistant._temp_tags_str = form_data.get('tags', '')
        assistant.save()

        messages.success(self.request, f'Assistant "{assistant.name}" created successfully! It is now pending approval by an administrator.')
        return redirect(reverse_lazy('assistants:list', kwargs={'company_id': company_id}))

# --- End Wizard ---

@login_required
def generate_assistant_qr_code(request, assistant_id):
    """Generate or regenerate QR code for an assistant."""
    from utils.qr_generator import generate_model_qr_code

    # Get the assistant
    assistant = get_object_or_404(Assistant, id=assistant_id)

    # Check permissions
    if not request.user.has_perm('assistants.change_assistant', assistant) and assistant.company.owner != request.user:
        return JsonResponse({'status': 'error', 'message': 'Permission denied'}, status=403)

    try:
        # Generate the QR code using the assistant's chat URL
        url_path = reverse('assistants:assistant_chat', kwargs={'slug': assistant.slug})
        success = generate_model_qr_code(assistant, url_path, field_name='qr_code')

        if success:
            # Save the assistant with the new QR code
            assistant.save(update_fields=['qr_code'])
            return JsonResponse({
                'status': 'success',
                'message': 'QR code generated successfully',
                'qr_code_url': assistant.qr_code.url
            })
        else:
            return JsonResponse({
                'status': 'error',
                'message': 'Failed to generate QR code'
            }, status=500)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Error generating QR code: {str(e)}'
        }, status=500)

@login_required
def assistant_update(request, company_id, assistant_id):
    """Update an existing AI assistant."""
    # Fetch company ensuring user has access, prefetch related info
    company = get_object_or_404(
        Company.objects.prefetch_related('info').filter( # Added prefetch
            Q(pk=company_id, owner=request.user) |
            Q(pk=company_id, memberships__user=request.user) # Corrected: Use request.user directly
        ).distinct()
    )
    # Fetch assistant and related company info
    assistant = get_object_or_404(
        Assistant.objects.select_related('company', 'company__info', 'folder'), # Added company__info
        id=assistant_id,
        company=company
    )

    # Permission Check: Allow owner OR user with company-level permission OR user in admin group
    # OR if user is in member group AND is the creator of the assistant
    is_owner = request.user.id == company.owner_id
    is_admin = _user_in_company_group(request.user, company, ["company administrators"])
    is_member = _user_in_company_group(request.user, company, ["company members"])
    is_creator = request.user.id == assistant.created_by_id

    # Owners and admins can edit any assistant
    can_manage = is_owner or is_admin or request.user.has_perm('accounts.manage_company_assistants', company)

    # Members can only edit assistants they created
    if is_member and not can_manage:
        can_manage = is_creator

    if not can_manage:
        raise PermissionDenied("You do not have permission to edit this assistant.")

    nav_formset = None # Initialize formset variable

    # Check if we're in the Community Assistants section by examining the request path
    is_community_section = 'community-assistants' in request.path.lower()

    if request.method == 'POST':
        # --- START DEBUGGING ---
        print("--- DEBUG: Raw POST data ---")
        import pprint
        pprint.pprint(request.POST)
        print("--- END DEBUG ---")
        # --- END DEBUGGING ---

        # For community entities or Community Assistants section, enforce community assistant type
        if (company.entity_type == 'community' or is_community_section) and 'assistant_type' in request.POST and request.POST['assistant_type'] != Assistant.TYPE_COMMUNITY:
            messages.error(request, "Only community assistants can be created within community entities or from the Community Assistants section.")
            return redirect('assistants:update', company_id=company.id, assistant_id=assistant.id)

        # For regular companies and not in Community Assistants section, prevent changing to community type
        if company.entity_type != 'community' and not is_community_section and 'assistant_type' in request.POST and request.POST['assistant_type'] == Assistant.TYPE_COMMUNITY:
            messages.error(request, "Community assistants can only be created within community entities or from the Community Assistants section.")
            return redirect('assistants:update', company_id=company.id, assistant_id=assistant.id)

        # Debug the request.FILES content
        print("DEBUG: request.FILES content:", request.FILES)
        if 'logo' in request.FILES:
            logo_file = request.FILES['logo']
            print(f"DEBUG: Logo file in request.FILES: name={logo_file.name}, size={logo_file.size}, content_type={logo_file.content_type}")

            # Check if the file is valid
            try:
                # Read a few bytes to check if the file is accessible
                logo_file.seek(0)
                logo_file.read(10)
                logo_file.seek(0)  # Reset file pointer
                print(f"DEBUG: Logo file is readable")

                # Check if the file is a valid image
                from PIL import Image
                try:
                    img = Image.open(logo_file)
                    img.verify()  # Verify it's a valid image
                    logo_file.seek(0)  # Reset file pointer
                    print(f"DEBUG: Logo file is a valid image: format={img.format}, size={img.size}")
                except Exception as e:
                    print(f"DEBUG: Logo file is not a valid image: {e}")
            except Exception as e:
                print(f"DEBUG: Error reading logo file: {e}")

        form = AssistantForm(request.POST, request.FILES, instance=assistant, company=company)

        # Instantiate formset if it's a support or community assistant and POST request
        if assistant.assistant_type == Assistant.TYPE_SUPPORT or assistant.assistant_type == Assistant.TYPE_COMMUNITY:
            nav_formset = NavigationItemFormSet(request.POST, instance=assistant, prefix='navitems')

        # Validate both form and formset if applicable
        if form.is_valid():
            # Validate formset if it's a support or community assistant
            formset_valid = True
            if assistant.assistant_type == Assistant.TYPE_SUPPORT or assistant.assistant_type == Assistant.TYPE_COMMUNITY:
                if nav_formset:
                    if not nav_formset.is_valid():
                        formset_valid = False
                        messages.error(request, "Please correct the errors in the navigation items.")
                else:
                    # This case shouldn't happen if type is support, but good to handle
                    formset_valid = False # Treat as invalid if formset is missing when expected
                    messages.error(request, "Navigation formset is missing unexpectedly.")

                if not formset_valid:
                    context = { # Define context here for the error case
                        'company': company,
                        'assistant': assistant,
                        'form': form,
                        'nav_formset': nav_formset,
                        'action': 'Update',
                        'assistant_types': Assistant.ASSISTANT_TYPES,
                        'model_choices': Assistant.MODEL_CHOICES,
                        'website_data_json': json.dumps(assistant.website_data or {}),
                        'company_info': assistant.company.info,
                        'navigation_items_data': json.dumps(list(assistant.navigation_items.values()), cls=DjangoJSONEncoder) # Use DjangoJSONEncoder
                    }
                    return render(request, 'assistants/assistant_form.html', context)


            # --- Form and Formset (if applicable) are valid ---
            if formset_valid:
                try:
                    with transaction.atomic():
                        # Store the current logo BEFORE processing the form save
                        current_logo = assistant.logo
                        # Process the form but don't commit yet
                        assistant_instance = form.save(commit=False)

                        # --- START DEBUGGING ---
                        print(f"--- DEBUG: website_data after form.save(commit=False): {assistant_instance.website_data}")
                        print(f"--- DEBUG: is_public value in POST: {request.POST.get('is_public')}")
                        print(f"--- DEBUG: is_public value in form.cleaned_data: {form.cleaned_data.get('is_public')}")
                        print(f"--- DEBUG: is_public value in assistant_instance: {assistant_instance.is_public}")

                        # Explicitly process dynamic rich text fields from POST into website_data JSON
                        updated_website_data = assistant_instance.website_data or {}
                        print(f"--- DEBUG: Initial website_data for processing: {updated_website_data}")
                        for key, value in request.POST.items():
                            if key.startswith('data_') and key.endswith('_content'):
                                data_key = key.split('_', 1)[1].rsplit('_', 1)[0]
                                print(f"--- DEBUG: Processing key '{key}', extracted data_key '{data_key}'")
                                if data_key not in updated_website_data:
                                    updated_website_data[data_key] = {}
                                updated_website_data[data_key]['content'] = value
                                print(f"--- DEBUG: Updated website_data[{data_key}]['content'] = {value[:100]}...") # Log truncated value

                        # Assign the potentially updated dictionary back to the instance field
                        assistant_instance.website_data = updated_website_data
                        # --- END DEBUGGING ---

                        # Handle logo persistence properly with improved logic
                        logo_in_form = form.cleaned_data.get('logo')
                        print(f"DEBUG: logo_in_form value: {logo_in_form}")
                        print(f"DEBUG: logo in request.FILES: {'logo' in request.FILES}")

                        # Enhanced logo handling with better error checking
                        if 'logo' in request.FILES:
                            logo_file = request.FILES['logo']
                            print(f"DEBUG: Using logo directly from request.FILES: {logo_file.name}")

                            # Verify the file is a valid image
                            from PIL import Image
                            import os
                            from django.conf import settings

                            try:
                                # Reset file pointer
                                logo_file.seek(0)

                                # Check if it's a valid image
                                img = Image.open(logo_file)
                                img.verify()  # Verify it's a valid image
                                logo_file.seek(0)  # Reset file pointer
                                print(f"DEBUG: Logo file is a valid image: format={img.format}, size={img.size}")

                                # Ensure all media directories exist
                                ensure_media_directories()
                                print(f"DEBUG: Ensured all media directories exist")

                                # Create a unique filename
                                import uuid
                                from django.utils.text import slugify
                                base_name, ext = os.path.splitext(logo_file.name)
                                safe_name = f"{slugify(base_name)}_{uuid.uuid4().hex[:8]}{ext.lower()}"
                                target_name = f"assistant_logos/{safe_name}"
                                target_path = os.path.join(settings.MEDIA_ROOT, target_name)

                                print(f"DEBUG: Saving logo to {target_path}")

                                # Ensure the assistant_logos directory exists
                                os.makedirs(os.path.dirname(target_path), exist_ok=True)

                                # Save the file directly to the filesystem
                                with open(target_path, 'wb+') as destination:
                                    for chunk in logo_file.chunks():
                                        destination.write(chunk)

                                # Verify the file was saved
                                if os.path.exists(target_path):
                                    print(f"DEBUG: Logo file saved successfully to {target_path}")

                                    # Update the model with the new path using Django's File object
                                    from django.core.files import File
                                    with open(target_path, 'rb') as f:
                                        django_file = File(f)
                                        # Save the file to the model field
                                        assistant_instance.logo.save(target_name, django_file, save=False)
                                    print(f"DEBUG: Set assistant_instance.logo to {assistant_instance.logo.name}")
                                else:
                                    print(f"DEBUG: Failed to save logo file to {target_path}")
                            except Exception as e:
                                print(f"DEBUG: Error processing logo file: {e}")
                                # Continue without setting the logo

                            # Force a save to ensure the logo is properly saved
                            assistant_instance.save()

                            # Reload the instance to ensure the logo is properly loaded
                            assistant_instance = Assistant.objects.get(pk=assistant_instance.pk)

                            # Check if the logo is properly loaded
                            if assistant_instance.logo:
                                print(f"DEBUG: Logo after reload: {assistant_instance.logo.name}")

                                # Verify the file exists
                                logo_path = os.path.join(settings.MEDIA_ROOT, assistant_instance.logo.name)
                                if os.path.exists(logo_path):
                                    print(f"DEBUG: Logo file exists at {logo_path}")
                                    print(f"DEBUG: Logo URL after reload: {assistant_instance.logo_url()}")
                                else:
                                    print(f"DEBUG: Logo file does not exist at {logo_path}")
                            else:
                                print("DEBUG: Logo not found after reload")
                        elif logo_in_form is False:
                            # User checked "Clear" - explicitly set to None
                            print("DEBUG: Logo cleared by user")
                            assistant_instance.logo = None
                        elif current_logo and not 'logo-clear' in request.POST:
                            # User didn't upload a new logo and didn't check "clear" - keep existing
                            print(f"DEBUG: Keeping existing logo: {current_logo.name}")
                            assistant_instance.logo = current_logo

                        # Handle tier and featured status requests
                        if form.cleaned_data.get('request_new_tier'):
                            assistant_instance.requested_tier = form.cleaned_data['request_new_tier']
                            assistant_instance.requested_tier_duration = form.cleaned_data.get('requested_tier_duration') or Assistant.DURATION_MONTHLY
                            assistant_instance.tier_change_pending = True
                            print(f"DEBUG: Setting requested_tier_duration to {assistant_instance.requested_tier_duration}")
                        if form.cleaned_data.get('request_featured_status'):
                            assistant_instance.requested_featured_duration = form.cleaned_data.get('requested_featured_duration') or Assistant.DURATION_MONTHLY
                            assistant_instance.featured_request_pending = True
                            print(f"DEBUG: Setting requested_featured_duration to {assistant_instance.requested_featured_duration}")

                        # --- START DEBUGGING ---
                        print(f"--- DEBUG: Final website_data before save: {assistant_instance.website_data}")
                        # --- END DEBUGGING ---

                        # Explicitly handle is_public field to ensure it's properly saved
                        is_public_value = form.cleaned_data.get('is_public')
                        print(f"--- DEBUG: Final is_public value before save: {is_public_value}")
                        assistant_instance.is_public = is_public_value

                        # Save the assistant
                        assistant_instance.save() # Save changes including potentially updated website_data

                        # Double-check that the logo was saved properly
                        if assistant_instance.logo:
                            print(f"Logo saved successfully: {assistant_instance.logo.name}")

                            # Force a refresh from the database to ensure the logo is properly loaded
                            assistant_instance.refresh_from_db()
                            print(f"After refresh_from_db: logo = {assistant_instance.logo.name if assistant_instance.logo else 'None'}")

                            # Check the logo URL
                            logo_url = assistant_instance.logo_url()
                            print(f"Logo URL: {logo_url}")

                            # Check the get_logo_url method
                            get_logo_url = assistant_instance.get_logo_url()
                            print(f"get_logo_url: {get_logo_url}")
                        else:
                            print("No logo saved")

                        # Save navigation items if it's a support or community assistant
                        if (assistant.assistant_type == Assistant.TYPE_SUPPORT or assistant.assistant_type == Assistant.TYPE_COMMUNITY) and nav_formset:
                            # Save the formset first (handles labels, visibility, deletions, new items)
                            nav_formset.save()
                            assistant_instance.refresh_from_db() # Get latest state including new/deleted items

                            # --- Manually Update Order ---
                            order_data = {} # {item_id: order_index}
                            prefix = nav_formset.prefix
                            num_forms = int(request.POST.get(f'{prefix}-TOTAL_FORMS', 0))
                            print(f"DEBUG: Processing {num_forms} navigation items for {assistant.assistant_type} assistant")
                            for i in range(num_forms):
                                item_id_key = f'{prefix}-{i}-id'
                                order_key = f'{prefix}-{i}-ORDER'
                                item_id_str = request.POST.get(item_id_key)
                                order_str = request.POST.get(order_key)
                                print(f"DEBUG: Form {i}: ID={item_id_str}, ORDER={order_str}")

                                if item_id_str and order_str is not None: # Check if order_str exists
                                    try:
                                        item_id = int(item_id_str)
                                        order_index = int(order_str)
                                        order_data[item_id] = order_index
                                        print(f"DEBUG: Added order_data[{item_id}] = {order_index}")
                                    except (ValueError, TypeError) as e:
                                        # Log parse errors
                                        print(f"DEBUG: Error parsing ID or ORDER: {e}")
                                        pass
                                else:
                                     # Log missing data
                                     print(f"DEBUG: Missing ID or ORDER for form {i}")

                            # Apply the extracted order to the database objects
                            updated_count = 0
                            items_to_update = assistant_instance.navigation_items.all() # Get all current items
                            for item in items_to_update:
                                if item.id in order_data:
                                    new_order = order_data[item.id]
                                    if item.order != new_order: # Only update if changed
                                        item.order = new_order
                                        item.save(update_fields=['order'])
                                        updated_count += 1
                                else:
                                     # Item might have been deleted, ignore
                                     pass

                            # --- End Manual Order Update ---


                            # --- Process and Save Dynamic Website Data (Including Images) ---
                            assistant_instance.refresh_from_db() # Ensure we have the latest nav items
                            nav_items_map = {item.id: item for item in assistant_instance.navigation_items.all()}
                            old_website_data = assistant_instance.website_data or {}
                            final_website_data = {}

                            # Store the definitive list of navigation items
                            final_website_data['navigation_items'] = list(
                                assistant_instance.navigation_items.filter(visible=True).order_by('order').values(
                                    'id', 'unique_id', 'label', 'section_type', 'visible', 'order'
                                )
                            )

                            # Use defaultdict for easier handling of nested structures for text data
                            parsed_text_data = defaultdict(lambda: defaultdict(dict))
                            data_pattern = re.compile(r'^data_item_(\d+)_(?:(\d+)_)?([a-zA-Z0-9_]+)$')

                            # 1. Parse Text Data from POST
                            for key, value in request.POST.items():
                                match = data_pattern.match(key)
                                if match:
                                    item_id_str, index_str, field_name = match.groups()
                                    value = value.strip()
                                    try:
                                        item_id = int(item_id_str)
                                        if item_id not in nav_items_map: continue # Skip data for deleted nav items

                                        if index_str is not None: # Multi-entry
                                            index = int(index_str)
                                            parsed_text_data[item_id][index][field_name] = value
                                        else: # Single entry (text)
                                            parsed_text_data[item_id]['content'] = value
                                    except ValueError:
                                        pass # Ignore parse errors

                            # 2. Process Each Navigation Item (Text and Images)
                            for item_id, nav_item in nav_items_map.items():
                                data_key = f"item_{item_id}"
                                old_item_data = old_website_data.get(data_key, {})
                                item_unique_id = nav_item.unique_id # Use unique_id for filenames

                                if nav_item.section_type == 'text':
                                    item_data = {'content': parsed_text_data.get(item_id, {}).get('content', '')}

                                    # Handle Main Image
                                    main_image_file = request.FILES.get(f'navitems-{item_id}-main_image')
                                    if main_image_file:
                                        saved_path = handle_image_upload(main_image_file, item_unique_id, 'main_image')
                                        if saved_path: item_data['main_image'] = saved_path
                                    elif isinstance(old_item_data, dict) and 'main_image' in old_item_data:
                                        item_data['main_image'] = old_item_data['main_image'] # Preserve old

                                    # Handle Gallery Images
                                    gallery_files = request.FILES.getlist(f'navitems-{item_id}-gallery')
                                    if gallery_files:
                                        gallery_paths = []
                                        for idx, file in enumerate(gallery_files):
                                            saved_path = handle_image_upload(file, item_unique_id, 'gallery', gallery_index=idx)
                                            if saved_path: gallery_paths.append(saved_path)
                                        if gallery_paths: item_data['gallery'] = gallery_paths
                                    elif isinstance(old_item_data, dict) and 'gallery' in old_item_data:
                                        item_data['gallery'] = old_item_data['gallery'] # Preserve old

                                    final_website_data[data_key] = item_data

                                else: # Multi-entry types (product, service, etc.)
                                    item_entries_data = parsed_text_data.get(item_id, {})
                                    entry_list = []
                                    old_entry_list = old_item_data if isinstance(old_item_data, list) else []

                                    # Determine max index from parsed text data for this item
                                    max_index = -1
                                    if item_entries_data:
                                        try:
                                            max_index = max(k for k in item_entries_data.keys() if isinstance(k, int))
                                        except ValueError:
                                            max_index = -1 # No integer keys found

                                    # Iterate up to the max index found in POST data
                                    for index in range(max_index + 1):
                                        entry_data = item_entries_data.get(index, {}) # Get text data for this index
                                        old_entry_data = old_entry_list[index] if index < len(old_entry_list) and isinstance(old_entry_list[index], dict) else {}

                                        # Handle Main Image for entry
                                        main_image_file = request.FILES.get(f'navitems-{item_id}-{index}-main_image')
                                        if main_image_file:
                                            saved_path = handle_image_upload(main_image_file, item_unique_id, 'main_image', index=index)
                                            if saved_path: entry_data['main_image'] = saved_path
                                        elif 'main_image' in old_entry_data:
                                            entry_data['main_image'] = old_entry_data['main_image'] # Preserve old

                                        # Handle Gallery Images for entry
                                        gallery_files = request.FILES.getlist(f'navitems-{item_id}-{index}-gallery')
                                        if gallery_files:
                                            gallery_paths = []
                                            for g_idx, file in enumerate(gallery_files):
                                                saved_path = handle_image_upload(file, item_unique_id, 'gallery', index=index, gallery_index=g_idx)
                                                if saved_path: gallery_paths.append(saved_path)
                                            if gallery_paths: entry_data['gallery'] = gallery_paths
                                        elif 'gallery' in old_entry_data:
                                            entry_data['gallery'] = old_entry_data['gallery'] # Preserve old

                                        # Only add entry if it has some data (text or images)
                                        if entry_data:
                                            entry_list.append(entry_data)

                                    final_website_data[data_key] = entry_list

                            # Save the combined data
                            assistant_instance.website_data = final_website_data
                            assistant_instance.save(update_fields=['website_data'])
                            # --- End Website Data Processing ---

                        messages.success(request, 'Assistant updated successfully.')
                        # Redirect back to the update page instead of the detail page
                        return redirect('assistants:update', company_id=company.id, assistant_id=assistant_instance.id) # Use assistant_instance.id

                except Exception as e:
                    messages.error(request, f'Error saving assistant: {str(e)}')
                    print(f'Error saving assistant: {str(e)}')
                    traceback.print_exc() # Print traceback for detailed error
                    # Re-render form with error
                    context = {
                        'company': company,
                        'assistant': assistant,
                        'form': form,
                        'nav_formset': nav_formset,
                        'action': 'Update',
                        'assistant_types': Assistant.ASSISTANT_TYPES,
                        'model_choices': Assistant.MODEL_CHOICES,
                        'website_data_json': json.dumps(assistant.website_data or {}),
                        'company_info': assistant.company.info,
                        'navigation_items_data': json.dumps(list(assistant.navigation_items.values()), cls=DjangoJSONEncoder) # Use DjangoJSONEncoder
                    }
                    return render(request, 'assistants/assistant_form.html', context)

    else:  # GET request
        form = AssistantForm(instance=assistant, company=company)
        if assistant.assistant_type == Assistant.TYPE_SUPPORT or assistant.assistant_type == Assistant.TYPE_COMMUNITY:
            nav_formset = NavigationItemFormSet(instance=assistant, prefix='navitems')

        # For community entities or Community Assistants section, restrict assistant type choices
        if company.entity_type == 'community' or is_community_section:
            form.fields['assistant_type'].choices = [(Assistant.TYPE_COMMUNITY, 'Community Assistant')]
            form.fields['assistant_type'].widget.attrs['disabled'] = 'disabled'

    # Get viewers with view_assistant permission for this assistant
    from guardian.shortcuts import get_users_with_perms
    from accounts.models import Membership

    # Get all users with view_assistant permission
    users_with_perms = get_users_with_perms(
        assistant,
        attach_perms=True,
        with_group_users=False
    )

    # Filter to only include users with view_assistant permission
    assistant_viewers = [
        user for user, perms in users_with_perms.items()
        if 'view_assistant' in perms and user != company.owner
    ]

    # Get all company members for the viewer selection dropdown
    # Get all members of the company (excluding the owner)
    company_members = Membership.objects.filter(
        company=company
    ).select_related('user').exclude(user=company.owner)

    # Prepare context for rendering the form
    context = {
        'company': company,
        'assistant': assistant,
        'form': form,
        'nav_formset': nav_formset,
        'action': 'Update',
        'assistant_types': Assistant.ASSISTANT_TYPES,
        'model_choices': Assistant.MODEL_CHOICES,
        'website_data_json': json.dumps(assistant.website_data or {}),
        'company_info': getattr(assistant.company, 'info', None),
        'navigation_items_data': json.dumps(list(assistant.navigation_items.values('id', 'unique_id', 'label', 'section_type', 'visible', 'order')), cls=DjangoJSONEncoder), # Removed 'entry_count'
        'is_community_entity': company.entity_type == 'community',
        'is_community_section': is_community_section,
        'assistant_viewers': assistant_viewers,
        'company_members': company_members
    }
    return render(request, 'assistants/assistant_form.html', context)

@login_required
@require_POST # Ensure this view only accepts POST requests
def assistant_delete(request, company_id, assistant_id):
    """Delete an AI assistant."""
    # Fetch company ensuring user has access, prefetch info
    company = get_object_or_404(
        Company.objects.prefetch_related('info').filter( # Added prefetch
            Q(pk=company_id, owner=request.user) |
            Q(pk=company_id, memberships__user=request.user)
        ).distinct()
    )
    assistant = get_object_or_404(Assistant, id=assistant_id, company=company)

    # Allow company owner OR user with explicit permission
    if assistant.company.owner != request.user and not request.user.has_perm('assistants.delete_assistant', assistant):
        raise PermissionDenied("You do not have permission to delete this assistant.")

    # Since we use @require_POST, we don't need to check request.method == 'POST' anymore
    name = assistant.name
    assistant.delete()
    messages.success(request, f'Assistant "{name}" was deleted successfully.')
    return redirect('assistants:list', company_id=company.id)
    # Removed the GET request handling and rendering of the confirmation template

@login_required
def assistant_list(request, company_id):
    """Display list of AI assistants for a company."""
    # Fetch company ensuring user has access, prefetch info
    company = get_object_or_404(
        Company.objects.prefetch_related('info').filter( # Added prefetch
            Q(pk=company_id, owner=request.user) |
            Q(pk=company_id, memberships__user=request.user)
        ).distinct()
    )

    # Permission Check: Use the same permission as the dashboard for consistency
    perm_to_check = 'accounts.view_company_activity'
    if not request.user.has_perm(perm_to_check, company):
        raise PermissionDenied(f"You do not have permission ({perm_to_check}) to manage assistants for this company.")

    # --- Fetch Assistants User Can View ---
    # Check user roles
    is_owner = request.user.id == company.owner_id
    is_admin = _user_in_company_group(request.user, company, ["company administrators"])
    is_member = _user_in_company_group(request.user, company, ["company members"])
    is_viewer = _user_in_company_group(request.user, company, ["company guests"])

    # Check if user can manage company (owner or admin)
    can_manage_company = is_owner or is_admin or request.user.has_perm('accounts.manage_company_assistants', company)

    if can_manage_company:
        # Owners/Admins see ALL assistants for the company
        viewable_assistants_qs = Assistant.objects.filter(company=company).select_related('folder')
    elif is_member:
        # Members can only see assistants they created and public assistants
        member_assistants_qs = Assistant.objects.filter(company=company, created_by=request.user).select_related('folder')
        public_assistants_qs = Assistant.objects.filter(company=company, is_public=True).select_related('folder')
        viewable_assistants_qs = (member_assistants_qs | public_assistants_qs).distinct()
    else:
        # Viewers and other users: Use guardian + public assistants
        from guardian.shortcuts import get_objects_for_user
        all_viewable_assistants_qs = get_objects_for_user(
            request.user,
            'assistants.view_assistant',
            Assistant.objects.filter(company=company),
            accept_global_perms=False # Check object-level perms specifically
        )
        public_assistants_qs = Assistant.objects.filter(company=company, is_public=True)
        viewable_assistants_qs = (all_viewable_assistants_qs | public_assistants_qs).distinct().select_related('folder')

    # --- Apply Filters ---
    search_query = request.GET.get('q')
    filter_type = request.GET.get('type')
    folder_id_filter = request.GET.get('folder_id') # Renamed to avoid clash

    filtered_assistants_qs = viewable_assistants_qs # Start with all viewable
    if search_query:
        filtered_assistants_qs = filtered_assistants_qs.filter(name__icontains=search_query)
    if filter_type:
        filtered_assistants_qs = filtered_assistants_qs.filter(assistant_type=filter_type)

    # --- Fetch and Sort Folders ---
    # Fetch folders and order them by the 'order' field, then by 'name'
    folders_qs = AssistantFolder.objects.filter(company=company).order_by('order', 'name')

    # --- Apply Folder Filter (if any) AFTER getting sorted folders ---
    target_folder_obj = None
    if folder_id_filter:
        if folder_id_filter == 'unassigned':
            filtered_assistants_qs = filtered_assistants_qs.filter(folder__isnull=True)
        else:
            try:
                # Ensure the requested folder belongs to the current company
                target_folder_obj = get_object_or_404(AssistantFolder, pk=int(folder_id_filter), company=company)
                filtered_assistants_qs = filtered_assistants_qs.filter(folder=target_folder_obj)
            except (ValueError, TypeError, AssistantFolder.DoesNotExist):
                # Handle invalid/non-existent folder_id - show all accessible? Or raise error?
                # For now, ignore invalid folder_id and show default list
                folder_id_filter = None # Reset filter state if invalid
                pass # Keep filtered_assistants_qs as is (before folder filter)

    # --- Group Assistants Manually Based on Sorted Folders ---
    # Order assistants primarily by name for consistency within groups
    final_assistants_list = list(filtered_assistants_qs.order_by('name'))

    grouped_assistants_list = []

    # 1. Get Assistants for each folder (in the ASCENDING sorted order)
    # Only add folders if no specific folder is selected OR if the current folder matches the selected one
    if not folder_id_filter or folder_id_filter == 'unassigned': # Show all folders if no filter or unassigned
        for folder in folders_qs:
            folder_assistants = [a for a in final_assistants_list if a.folder_id == folder.id]
            if folder_assistants: # Only add folder group if it has assistants after filtering
                grouped_assistants_list.append((folder, folder_assistants))
    elif target_folder_obj: # Only show the selected folder
         folder_assistants = [a for a in final_assistants_list if a.folder_id == target_folder_obj.id]
         if folder_assistants:
             grouped_assistants_list.append((target_folder_obj, folder_assistants))

    # 2. Get Unassigned Assistants and add them LAST (only if no specific folder is selected or 'unassigned' is selected)
    if not target_folder_obj or folder_id_filter == 'unassigned':
        unassigned_assistants = [a for a in final_assistants_list if a.folder is None]
        if unassigned_assistants:
            grouped_assistants_list.append((None, unassigned_assistants)) # None represents 'Unassigned'


    # Get items per page from request, default to 10
    items_per_page = request.GET.get('items_per_page')
    try:
        items_per_page = int(items_per_page) if items_per_page else 10
        # Limit to valid options: 10, 25, 50, 100
        if items_per_page not in [10, 25, 50, 100]:
            items_per_page = 10
    except ValueError:
        items_per_page = 10

    # Create a paginator object for the filtered assistants
    # This is just to create a page_obj for the pagination template
    # We'll still use the grouped_assistants_list for display
    paginator = Paginator(filtered_assistants_qs, items_per_page)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'company': company,
        'grouped_assistants_list': grouped_assistants_list, # Pass manually ordered grouped list
        'folders': folders_qs, # Pass sorted folders for filter links/dropdowns
        'selected_folder_id': folder_id_filter, # Pass selected folder ID for filter state
        'search_query': search_query,
        'filter_type': filter_type,
        'assistant_types': Assistant.ASSISTANT_TYPES,
        'model_choices': Assistant.MODEL_CHOICES,
        'page_obj': page_obj  # Add page_obj for pagination
    }

    if request.headers.get('HX-Request'):
        return render(request, 'assistants/partials/assistant_list.html', context)
    return render(request, 'assistants/assistant_list.html', context)

@login_required
def assistant_detail(request, company_id, assistant_id):
    """Display details and chat interface for an AI assistant."""
    # Fetch company ensuring user has access, prefetch info
    company = get_object_or_404(
        Company.objects.prefetch_related('info').filter( # Added prefetch
            Q(pk=company_id, owner=request.user) |
            Q(pk=company_id, memberships__user=request.user)
        ).distinct()
    )
    assistant = get_object_or_404(Assistant.objects.select_related('folder'), id=assistant_id, company=company)

    if not can_access_assistant(request.user, assistant):
         raise PermissionDenied("You do not have permission to access this assistant.")

    # For community assistants, redirect to the community dashboard
    if assistant.assistant_type == Assistant.TYPE_COMMUNITY:
        return redirect('assistants:community_dashboard', company_id=company.id, assistant_id=assistant.id)

    interactions = Interaction.objects.filter(
        assistant=assistant,
        user=request.user
    ).order_by('-created_at')[:10]

    is_favorited = False
    if request.user.is_authenticated:
        is_favorited = SavedItem.objects.filter(
            user=request.user,
            assistant=assistant,
            item_type='assistant'
        ).exists()

    form = AssistantInteractionForm()

    context = {
        'company': company,
        'assistant': assistant,
        'interactions': interactions,
        'form': form,
        'is_favorited': is_favorited
    }
    return render(request, 'assistants/assistant_detail.html', context)

@login_required
def community_dashboard(request, company_id, assistant_id):
    """Display the social media-style community assistant dashboard."""
    # Import Q for complex lookups
    from django.db.models import Q

    # Fetch company ensuring user has access, prefetch info
    company = get_object_or_404(
        Company.objects.prefetch_related('info').filter(
            Q(pk=company_id, owner=request.user) |
            Q(pk=company_id, memberships__user=request.user)
        ).distinct()
    )
    assistant = get_object_or_404(Assistant.objects.select_related('folder'), id=assistant_id, company=company)

    # Verify this is a community assistant
    if assistant.assistant_type != Assistant.TYPE_COMMUNITY:
        return redirect('assistants:detail', company_id=company.id, assistant_id=assistant.id)

    if not can_access_assistant(request.user, assistant):
         raise PermissionDenied("You do not have permission to access this assistant.")

    # Get active tab from query parameters
    active_tab = request.GET.get('tab', 'feed')

    # Get time period for statistics
    period = request.GET.get('period', 'month')

    # Get filter parameters for contributions tab
    filter_by = request.GET.get('filter', 'all')
    sort_by = request.GET.get('sort', 'newest')
    search_query = request.GET.get('search', '')

    # Get filter parameters for reports tab
    status = request.GET.get('status', 'pending')
    report_type = request.GET.get('report_type', 'all')

    # Get community contexts with upvote counts
    from .models import CommunityContext, ContextUpvote, ReportedContent, UserReputation
    from django.db.models import Count, F, Q

    # Get recent activities for feed tab
    recent_activities = []

    # Get contexts with upvote counts
    contexts = CommunityContext.objects.filter(
        assistant=assistant,
        is_active=True
    ).select_related('created_by').annotate(
        upvote_count=Count('upvotes')
    )

    # Convert contexts to activity items
    for context in contexts.order_by('-created_at')[:20]:
        recent_activities.append({
            'type': 'context',
            'user': context.created_by,
            'title': context.title,
            'content': context.text_content,
            'created_at': context.created_at,
            'upvotes': context.upvote_count,
            'object_id': context.id,
            'content_type': 'assistants.communitycontext'
        })

    # Get recent interactions (questions)
    interactions = Interaction.objects.filter(
        assistant=assistant
    ).select_related('user').order_by('-created_at')[:20]

    # Convert interactions to activity items
    for interaction in interactions:
        recent_activities.append({
            'type': 'question',
            'user': interaction.user,
            'content': interaction.prompt,
            'answer': interaction.response,
            'created_at': interaction.created_at,
            'object_id': interaction.id,
            'content_type': 'assistants.interaction'
        })

    # Sort activities by created_at
    recent_activities.sort(key=lambda x: x['created_at'], reverse=True)

    # Get contexts for contributions tab with filtering
    user_contexts = contexts

    if filter_by == 'mine':
        user_contexts = user_contexts.filter(created_by=request.user)
    elif filter_by == 'upvoted':
        upvoted_ids = ContextUpvote.objects.filter(user=request.user).values_list('context_id', flat=True)
        user_contexts = user_contexts.filter(id__in=upvoted_ids)

    if search_query:
        user_contexts = user_contexts.filter(
            Q(title__icontains=search_query) |
            Q(text_content__icontains=search_query)
        )

    if sort_by == 'newest':
        user_contexts = user_contexts.order_by('-created_at')
    elif sort_by == 'oldest':
        user_contexts = user_contexts.order_by('created_at')
    elif sort_by == 'upvotes':
        user_contexts = user_contexts.order_by('-upvote_count', '-created_at')

    # Paginate user contexts
    from django.core.paginator import Paginator
    paginator = Paginator(user_contexts, 10)  # 10 contexts per page
    page = request.GET.get('page', 1)
    user_contexts = paginator.get_page(page)

    # Get reports for reports tab (staff/superuser only)
    reports = None
    pending_reports_count = 0
    if request.user.is_staff or request.user.is_superuser:
        reports_qs = ReportedContent.objects.filter(assistant=assistant)

        if status != 'all':
            reports_qs = reports_qs.filter(status=status)

        if report_type != 'all':
            reports_qs = reports_qs.filter(report_type=report_type)

        if search_query:
            reports_qs = reports_qs.filter(
                Q(reason__icontains=search_query) |
                Q(reported_by__username__icontains=search_query)
            )

        reports_qs = reports_qs.order_by('-created_at')

        # Paginate reports
        reports_paginator = Paginator(reports_qs, 10)  # 10 reports per page
        reports_page = request.GET.get('page', 1)
        reports = reports_paginator.get_page(reports_page)

        # Count pending reports for badge
        pending_reports_count = ReportedContent.objects.filter(
            assistant=assistant,
            status='pending'
        ).count()

    # Get flagged content for moderation tab (available to all users)
    flagged_content = ReportedContent.objects.filter(
        assistant=assistant,
        status='pending'
    ).select_related('content_type', 'reported_by').order_by('-created_at')[:10]

    # Get users with most flags
    from django.db.models import Count
    from django.contrib.auth import get_user_model
    from django.contrib.contenttypes.models import ContentType
    User = get_user_model()

    # Get all pending reported content
    reported_content = ReportedContent.objects.filter(
        assistant=assistant,
        status='pending'
    ).select_related('content_type')

    # Get content types for CommunityContext and Comment
    context_type = ContentType.objects.get_for_model(CommunityContext)

    # Process the reported content to find flagged users
    user_report_counts = {}

    for report in reported_content:
        user_id = None
        content_obj = report.content_object

        if content_obj:
            # Check if it's a CommunityContext
            if report.content_type_id == context_type.id:
                if hasattr(content_obj, 'created_by_id') and content_obj.created_by_id:
                    user_id = content_obj.created_by_id
            # Check if it's a Comment
            elif hasattr(content_obj, 'user_id') and content_obj.user_id:
                user_id = content_obj.user_id

        if user_id:
            if user_id in user_report_counts:
                user_report_counts[user_id] += 1
            else:
                user_report_counts[user_id] = 1

    # Get user details for the flagged users
    flagged_users = []
    for user_id, report_count in sorted(user_report_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
        try:
            user = User.objects.get(id=user_id)

            # Get contribution count
            contribution_count = CommunityContext.objects.filter(
                assistant=assistant,
                created_by=user
            ).count()

            # Get last activity
            last_activity = CommunityContext.objects.filter(
                assistant=assistant,
                created_by=user
            ).order_by('-created_at').first()

            last_activity_date = last_activity.created_at if last_activity else None

            # If no context activity, check for comment activity
            if not last_activity_date:
                from .models import Comment
                last_comment = Comment.objects.filter(
                    user=user
                ).order_by('-created_at').first()

                last_activity_date = last_comment.created_at if last_comment else None

            flagged_users.append({
                'user': user,
                'report_count': report_count,
                'contribution_count': contribution_count,
                'last_activity': last_activity_date
            })
        except User.DoesNotExist:
            pass

    # Count flagged users for badge
    flagged_users_count = len(flagged_users)

    # Get statistics for stats tab
    if period == 'week':
        start_date = timezone.now() - timezone.timedelta(days=7)
    elif period == 'month':
        start_date = timezone.now() - timezone.timedelta(days=30)
    elif period == 'year':
        start_date = timezone.now() - timezone.timedelta(days=365)
    else:  # all time
        start_date = None

    # Calculate statistics based on time period
    if start_date:
        new_users = UserReputation.objects.filter(
            user__date_joined__gte=start_date
        ).count()

        new_contexts = CommunityContext.objects.filter(
            assistant=assistant,
            created_at__gte=start_date
        ).count()

        new_interactions = Interaction.objects.filter(
            assistant=assistant,
            created_at__gte=start_date
        ).count()

        new_reports = ReportedContent.objects.filter(
            assistant=assistant,
            created_at__gte=start_date
        ).count()
    else:
        new_users = UserReputation.objects.count()
        new_contexts = CommunityContext.objects.filter(assistant=assistant).count()
        new_interactions = Interaction.objects.filter(assistant=assistant).count()
        new_reports = ReportedContent.objects.filter(assistant=assistant).count()

    # Get top contributors
    top_contributors = UserReputation.objects.all().order_by('-score')[:5]

    # Get popular keywords/tags
    # This is a placeholder - in a real implementation, you would extract keywords from contexts
    popular_keywords = [
        {'name': 'AI', 'count': 15},
        {'name': 'Machine Learning', 'count': 12},
        {'name': 'Python', 'count': 10},
        {'name': 'Data Science', 'count': 8},
        {'name': 'Neural Networks', 'count': 5}
    ]

    # Get upvoted contexts for the current user
    upvoted_contexts = []
    if request.user.is_authenticated:
        upvoted_contexts = ContextUpvote.objects.filter(
            user=request.user
        ).values_list('context_id', flat=True)

    # Get total counts for stats
    total_members = UserReputation.objects.count()
    total_contexts = CommunityContext.objects.filter(assistant=assistant).count()
    total_questions = Interaction.objects.filter(assistant=assistant).count()
    total_interactions = total_questions  # This could be expanded to include other types of interactions

    # Get context form for adding new knowledge
    from .forms import SimpleCommunityContextForm
    context_form = SimpleCommunityContextForm()

    # Check if assistant is favorited by the user
    is_favorited = False
    if request.user.is_authenticated:
        is_favorited = SavedItem.objects.filter(
            user=request.user,
            assistant=assistant,
            item_type='assistant'
        ).exists()

    # Prepare context for the template
    context = {
        'company': company,
        'assistant': assistant,
        'recent_activities': recent_activities,
        'user_contexts': user_contexts,
        'reports': reports,
        'pending_reports_count': pending_reports_count,
        'context_form': context_form,
        'upvoted_contexts': upvoted_contexts,
        'is_favorited': is_favorited,

        # Moderation data
        'flagged_content': flagged_content,
        'flagged_users': flagged_users,
        'flagged_users_count': flagged_users_count,

        # Filter and sort parameters
        'filter': filter_by,
        'sort': sort_by,
        'search': search_query,
        'status': status,
        'report_type': report_type,
        'period': period,

        # Statistics
        'new_users': new_users,
        'new_contexts': new_contexts,
        'new_interactions': new_interactions,
        'new_reports': new_reports,
        'total_members': total_members,
        'total_contexts': total_contexts,
        'total_questions': total_questions,
        'total_interactions': total_interactions,
        'top_contributors': top_contributors,
        'popular_keywords': popular_keywords,
    }

    # Render the new social dashboard template
    return render(request, 'assistants/social_dashboard.html', context)

@login_required
def community_chat(request, company_id, assistant_id):
    """Display the community assistant chat interface."""
    # Fetch company ensuring user has access, prefetch info
    company = get_object_or_404(
        Company.objects.prefetch_related('info').filter(
            Q(pk=company_id, owner=request.user) |
            Q(pk=company_id, memberships__user=request.user)
        ).distinct()
    )
    assistant = get_object_or_404(Assistant.objects.select_related('folder'), id=assistant_id, company=company)

    # Verify this is a community assistant
    if assistant.assistant_type != Assistant.TYPE_COMMUNITY:
        return redirect('assistants:detail', company_id=company.id, assistant_id=assistant.id)

    if not can_access_assistant(request.user, assistant):
         raise PermissionDenied("You do not have permission to access this assistant.")

    # Get chat interactions for this user
    interactions = Interaction.objects.filter(
        assistant=assistant,
        user=request.user
    ).order_by('-created_at')[:10]

    # Get community contexts
    from .models import CommunityContext
    contexts = CommunityContext.objects.filter(
        assistant=assistant,
        is_active=True
    ).select_related('created_by').order_by('-created_at')[:10]

    # Get recent community questions (interactions from all users)
    recent_questions = Interaction.objects.filter(
        assistant=assistant,
        use_community_context=True
    ).select_related('user').order_by('-created_at')[:5]

    # Get popular keywords
    popular_keywords = []
    # This is a placeholder - in a real implementation, you would aggregate keywords from contexts
    # For example: popular_keywords = CommunityContext.objects.filter(assistant=assistant).values('keywords').annotate(count=Count('id')).order_by('-count')[:10]

    is_favorited = False
    if request.user.is_authenticated:
        is_favorited = SavedItem.objects.filter(
            user=request.user,
            assistant=assistant,
            item_type='assistant'
        ).exists()

    form = AssistantInteractionForm()

    context = {
        'company': company,
        'assistant': assistant,
        'interactions': interactions,
        'contexts': contexts,
        'recent_questions': recent_questions,
        'popular_keywords': popular_keywords,
        'form': form,
        'is_favorited': is_favorited
    }
    return render(request, 'assistants/community_dashboard.html', context)

# Removed @login_required to allow guest access for public assistants
def assistant_interact(request, company_id, assistant_id):
    """Handle chat interactions with an AI assistant."""
    print("DEBUG: Starting assistant_interact view")
    print(f"DEBUG: company_id={company_id}, assistant_id={assistant_id}")

    # Fetch assistant first to check public status
    try:
        assistant = get_object_or_404(Assistant, id=assistant_id, company_id=company_id)
        print(f"DEBUG: Found assistant: {assistant.name}, is_public={assistant.is_public}")
    except Exception as e:
        print(f"DEBUG: Error fetching assistant: {e}")
        raise

    # Check permissions: Allow if public OR if logged-in user has access
    is_allowed = False
    if assistant.is_public:
        is_allowed = True
        print("DEBUG: Access allowed - assistant is public")
    elif request.user.is_authenticated and can_access_assistant(request.user, assistant):
        is_allowed = True
        print(f"DEBUG: Access allowed - authenticated user {request.user.username} has access")

    if not is_allowed:
        # Check if it's an HTMX request for appropriate error response
        if request.headers.get('HX-Request') or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'error': 'Permission denied. Please log in or ensure you have access.'}, status=403)
        # For non-AJAX, redirect or raise standard PermissionDenied
        # Redirecting to login might be better UX than a raw 403 page
        # return redirect(f"{reverse('login')}?next={request.path}") # Option: Redirect to login
        raise PermissionDenied("You do not have permission to interact with this assistant.")

    if request.method == 'POST':
        logger.debug("Received POST request")
        try:
            # Determine user object (None for guests)
            user = request.user if request.user.is_authenticated else None
            logger.debug(f"User: {user}")

            try:
                logger.debug(f"Request body: {request.body.decode('utf-8')[:500]}...")
                data = json.loads(request.body)
                logger.debug(f"Parsed JSON data: {data}")

                message = data.get('message', '').strip()
                history = data.get('history', [])
                is_nav_click = data.get('navigation_click', False) # Check for nav click flag
                section_id = data.get('section_id') # Get section_id if present

                logger.debug(f"message={message[:100]}...")
                logger.debug(f"history length={len(history)}")
                logger.debug(f"is_nav_click={is_nav_click}")
                logger.debug(f"section_id={section_id}")

                if not isinstance(history, list):
                    logger.debug("History is not a list, setting to empty list")
                    history = []
            except json.JSONDecodeError as e:
                 logger.error(f"JSON decode error: {e}")
                 return JsonResponse({'error': 'Invalid JSON format in request body.'}, status=400)

            # --- Handle Navigation Click Directly ---
            if is_nav_click and section_id:
                try:
                    # Get website data and navigation items
                    website_data = assistant.website_data or {}

                    # Log website data keys for debugging
                    print(f"DEBUG - Navigation Click - Website data keys: {list(website_data.keys())}")

                    # Try to find the navigation item in multiple ways
                    nav_item = None
                    item_data = None
                    db_nav_item = None

                    # Method 1: Try to find in JSON navigation items
                    navigation_items_json = website_data.get('navigation_items', [])
                    nav_item = next((item for item in navigation_items_json if item.get('unique_id') == section_id), None)

                    # Method 2: Try to find in database
                    if not nav_item:
                        from .models import NavigationItem
                        try:
                            db_nav_item = NavigationItem.objects.get(assistant=assistant, unique_id=section_id)
                            # Create a dict representation of the database item
                            nav_item = {
                                'id': db_nav_item.id,
                                'unique_id': db_nav_item.unique_id,
                                'label': db_nav_item.label,
                                'section_type': db_nav_item.section_type
                            }
                            print(f"DEBUG - Navigation Click - Found item in database: {db_nav_item.label} (ID: {db_nav_item.id})")
                        except NavigationItem.DoesNotExist:
                            print(f"DEBUG - Navigation Click - Item not found in database: {section_id}")

                    if not nav_item:
                        raise ValueError(f"Navigation item with ID '{section_id}' not found.")

                    # Get label and section type
                    label = nav_item.get('label', 'This Section')
                    section_type = nav_item.get('section_type', 'text')

                    # Try multiple ways to get content
                    # Method 1: Try direct lookup by section_id
                    item_data = website_data.get(section_id)
                    if item_data:
                        print(f"DEBUG - Navigation Click - Found content using key: {section_id}")

                    # Method 2: Try using item_{id} format if we have an ID
                    if not item_data and 'id' in nav_item:
                        item_id = nav_item['id']
                        data_key = f"item_{item_id}"
                        item_data = website_data.get(data_key)
                        if item_data:
                            print(f"DEBUG - Navigation Click - Found content using key: {data_key}")

                    # Method 3: If we have a database item, try using its ID
                    if not item_data and db_nav_item:
                        data_key = f"item_{db_nav_item.id}"
                        item_data = website_data.get(data_key)
                        if item_data:
                            print(f"DEBUG - Navigation Click - Found content using database ID key: {data_key}")

                    # Method 4: Check if there's content in the extra_context field
                    if not item_data and assistant.extra_context:
                        import re
                        section_pattern = re.compile(rf"## Content for Section: '[^']*' \(ID: {re.escape(section_id)}\)(.*?)(?=## Content for Section:|$)", re.DOTALL)
                        match = section_pattern.search(assistant.extra_context)
                        if match:
                            item_data = match.group(1).strip()
                            print(f"DEBUG - Navigation Click - Found content in extra_context")

                    # Start building the formatted content with a header
                    formatted_content = f"<h4 class='nav-content-title'>{label}</h4><hr class='nav-content-divider'>"

                    # Process the content based on what we found
                    if item_data:
                        if isinstance(item_data, dict) and 'content' in item_data:
                            formatted_content += f"<div class='nav-content-body'>{item_data['content']}</div>"
                        elif isinstance(item_data, str):
                            formatted_content += f"<div class='nav-content-body'>{item_data}</div>"
                        elif isinstance(item_data, list):
                            # Handle list of entries (for multi-entry sections)
                            formatted_content += "<div class='nav-content-body'>"
                            for i, entry in enumerate(item_data):
                                if isinstance(entry, dict) and 'content' in entry:
                                    formatted_content += f"<div class='nav-entry'><h5>Entry {i+1}</h5>{entry['content']}</div>"
                                elif isinstance(entry, str):
                                    formatted_content += f"<div class='nav-entry'><h5>Entry {i+1}</h5>{entry}</div>"
                            formatted_content += "</div>"
                        else:
                            # Try to convert to string
                            try:
                                formatted_content += f"<div class='nav-content-body'>{json.dumps(item_data, indent=2)}</div>"
                            except:
                                formatted_content += f"<div class='nav-content-body'><p>Content available but in an unprocessable format.</p></div>"
                    else:
                        # Generate placeholder content based on section_type
                        formatted_content += "<div class='nav-content-body'>"
                        if section_type == 'text':
                            formatted_content += f"<p>Information about {label}</p>"
                        elif section_type == 'product':
                            formatted_content += f"<p>Product information for {label}</p>"
                        elif section_type == 'service':
                            formatted_content += f"<p>Service information for {label}</p>"
                        elif section_type == 'team':
                            formatted_content += f"<p>Team information for {label}</p>"
                        elif section_type == 'location':
                            formatted_content += f"<p>Location information for {label}</p>"
                        elif section_type == 'faq':
                            formatted_content += f"<p>Frequently asked questions about {label}</p>"
                        else:
                            formatted_content += f"<p>Information about {label}</p>"

                        formatted_content += '<p class="text-muted">No specific content available for this section.</p>'
                        formatted_content += "</div>"

                    # Add CSS styles for the navigation content
                    formatted_content = f"""
                    <style>
                        .nav-content-title {{
                            color: #0d6efd;
                            margin-bottom: 0.75rem;
                            font-weight: 600;
                        }}
                        .nav-content-divider {{
                            margin-top: 0.5rem;
                            margin-bottom: 1rem;
                            border-color: rgba(13, 110, 253, 0.2);
                        }}
                        .nav-content-body {{
                            line-height: 1.6;
                        }}
                        .nav-entry {{
                            margin-bottom: 1.5rem;
                            padding-bottom: 1rem;
                            border-bottom: 1px solid rgba(0,0,0,0.1);
                        }}
                        .nav-entry:last-child {{
                            border-bottom: none;
                        }}
                    </style>
                    {formatted_content}
                    """

                    # Return direct response, bypassing LLM and history saving
                    return JsonResponse({
                        'status': 'success',
                        'content': formatted_content,
                        'is_navigation_response': True, # Flag for frontend
                        'section_id': section_id,
                        'section_label': label
                    })
                except Exception as e:
                    print(f"Error handling navigation click for section {section_id}: {e}")
                    traceback.print_exc() # Print full traceback for debugging
                    # Return error but still flag as nav response if possible
                    return JsonResponse({'status': 'error', 'error': f"Error loading section: {e}", 'is_navigation_response': True}, status=500)
            # --- End Handle Navigation Click ---

            # --- Handle Standard Chat Message ---
            elif not message: # Check if message is empty *after* checking for nav click
                 return JsonResponse({'error': 'Message is required.'}, status=400)
            else:
                # Check if we should use community context
                use_community_context = False
                if assistant.assistant_type == Assistant.TYPE_COMMUNITY:
                    # Check if the request includes use_community_context parameter
                    try:
                        use_community_context = data.get('use_community_context', False)
                    except:
                        # If we can't parse from JSON data, try form data
                        use_community_context = request.POST.get('use_community_context') == '1'

                # For community assistants, retrieve relevant contexts if needed
                if assistant.assistant_type == Assistant.TYPE_COMMUNITY and use_community_context:
                    from .models import CommunityContext
                    # Get relevant contexts based on the message
                    contexts = CommunityContext.objects.filter(
                        assistant=assistant,
                        is_active=True
                    ).order_by('-created_at')[:5]  # Get the 5 most recent contexts for now

                    # Store context info for the response
                    used_contexts = []
                    for context in contexts:
                        used_contexts.append({
                            'id': context.id,
                            'title': context.title or f"Context #{context.id}",
                            'created_by': context.created_by.username if context.created_by else "Anonymous",
                            'created_at': context.created_at.strftime('%Y-%m-%d')
                        })

                    # Build context string from community contexts
                    context_str = "\n\n".join([c.text_content for c in contexts])

                    # Update contexts usage count
                    for context in contexts:
                        context.times_used += 1
                        context.save(update_fields=['times_used'])

                    # We'll save the used contexts to the interaction after it's created

                    # Get navigation items and website data for comprehensive context
                    website_data = assistant.website_data or {}
                    navigation_items = assistant.navigation_items.filter(visible=True).order_by('order')

                    # Build navigation context string
                    nav_context = ""
                    if navigation_items.exists():
                        nav_context += "\n\nNavigation Structure:\n"
                        for item in navigation_items:
                            nav_context += f"- {item.label} (ID: {item.unique_id}, Type: {item.section_type})\n"

                        # Add content from navigation items
                        nav_context += "\n--- Navigation Content Sections ---\n"
                        for item in navigation_items:
                            item_id = item.id
                            data_key = f"item_{item_id}"
                            item_data = website_data.get(data_key)

                            nav_context += f"\n## Content for Section: '{item.label}' (ID: {item.unique_id})\n"
                            if item_data:
                                # Check if it's a text section and extract content
                                if item.section_type == 'text' and isinstance(item_data, dict):
                                    content = item_data.get('content', '')
                                    nav_context += f"{content}\n"
                                # Handle other simple types directly
                                elif isinstance(item_data, (str, int, float, bool)):
                                    nav_context += f"{item_data}\n"
                                # Dump complex types as JSON
                                else:
                                    try:
                                        nav_context += f"{json.dumps(item_data, indent=2)}\n"
                                    except TypeError:
                                        nav_context += f"(Unserializable data for this section)\n"
                            else:
                                nav_context += "(No specific data provided for this section)\n"
                        nav_context += "--- End Navigation Content Sections ---\n"

                    # Add context to the system prompt
                    community_system_prompt = f"{assistant.system_prompt}\n\nHere is additional context from the community:\n{context_str}"

                    # Add navigation context if available
                    if nav_context:
                        community_system_prompt += f"\n\nHere is additional context from navigation items:\n{nav_context}"

                    # Update assistant's extra_context temporarily with community context
                    original_extra_context = assistant.extra_context
                    assistant.extra_context = f"{original_extra_context}\n\nHere is additional context from the community:\n{context_str}"

                    # If we have navigation context, add it to the extra_context
                    if nav_context:
                        assistant.extra_context += f"\n\nHere is additional context from navigation items:\n{nav_context}"

                    # Proceed with LLM call for community assistant
                    response = generate_assistant_response(
                        assistant=assistant,
                        user_input=message,
                        history=history,
                        user=user  # Pass user object (can be None)
                    )

                    # Restore the original extra_context
                    assistant.extra_context = original_extra_context

                    # Add used contexts to the response
                    response['used_contexts'] = used_contexts
                else:
                    # --- Build Context for Support/General Assistants ---
                    final_system_prompt = assistant.system_prompt
                    nav_context = ""
                    if assistant.assistant_type in [Assistant.TYPE_SUPPORT, Assistant.TYPE_GENERAL]: # Include for Support and General
                        website_data = assistant.website_data or {}
                        navigation_items = assistant.navigation_items.filter(visible=True).order_by('order')

                        if navigation_items.exists():
                            nav_context += "\n\nNavigation Structure:\n"
                            for item in navigation_items:
                                nav_context += f"- {item.label} (ID: {item.unique_id}, Type: {item.section_type})\n"

                            nav_context += "\n--- Navigation Content Sections ---\n"
                            for item in navigation_items:
                                item_id = item.id
                                data_key = f"item_{item_id}"
                                item_data = website_data.get(data_key)

                                nav_context += f"\n## Content for Section: '{item.label}' (ID: {item.unique_id})\n"
                                if item_data:
                                    # Check if it's a text section and extract content
                                    if item.section_type == 'text' and isinstance(item_data, dict):
                                        content = item_data.get('content', '')
                                        nav_context += f"{content}\n"
                                    # Handle other simple types directly
                                    elif isinstance(item_data, (str, int, float, bool)):
                                        nav_context += f"{item_data}\n"
                                    # Dump complex types as JSON
                                    else:
                                        try:
                                            nav_context += f"{json.dumps(item_data, indent=2)}\n"
                                        except TypeError:
                                            nav_context += f"(Unserializable data for this section)\n"
                                else:
                                    nav_context += "(No specific data provided for this section)\n"
                            nav_context += "--- End Navigation Content Sections ---\n"

                        # Append nav context to the system prompt if it exists
                        if nav_context:
                            final_system_prompt += f"\n\nHere is additional context from navigation items:\n{nav_context}"

                    # --- End Build Context ---

                    # Get context_id from request data if available
                    context_id = data.get('context_id', '')

                    # Proceed with LLM call for regular messages
                    print("DEBUG: Calling generate_assistant_response")
                    print(f"DEBUG: assistant={assistant.name}, model={assistant.model}")
                    print(f"DEBUG: user_input={message[:100]}...")
                    print(f"DEBUG: history length={len(history)}")
                    print(f"DEBUG: user={user}")
                    print(f"DEBUG: current_context_id={context_id}")

                    try:
                        response = generate_assistant_response(
                            assistant=assistant,
                            user_input=message,
                            history=history,
                            user=user,  # Pass user object (can be None)
                            current_context_id=context_id  # Pass the current context ID for image lookup
                        )
                        print(f"DEBUG: Response received: {response.keys()}")
                    except Exception as e:
                        print(f"DEBUG: Error in generate_assistant_response: {e}")
                        print(f"DEBUG: Exception type: {type(e).__name__}")
                        print(f"DEBUG: Exception args: {e.args}")
                        traceback.print_exc()
                        raise

                if response['status'] == 'success':
                    content_str = response.get('content')
                    if isinstance(content_str, str):
                        content_str_trimmed = content_str.strip()
                        if content_str_trimmed.startswith('<') and content_str_trimmed.endswith('>'):
                            response['content'] = content_str
                        else:
                            response['content'] = markdown.markdown(
                                content_str,
                                extensions=['fenced_code', 'tables', 'nl2br']
                            )

                    # Save use_community_context flag in the interaction record
                    if 'interaction_id' in response and assistant.assistant_type == Assistant.TYPE_COMMUNITY:
                        try:
                            interaction = Interaction.objects.get(id=response['interaction_id'])
                            interaction.use_community_context = use_community_context

                            # If we used community contexts, save them to the interaction
                            if use_community_context and 'contexts' in locals():
                                interaction.used_contexts.set(contexts)

                            interaction.save(update_fields=['use_community_context'])
                        except Exception as e:
                            print(f"Error updating interaction with use_community_context: {e}")

                    # --- Load Saved Suggestions (only for support assistants) ---
                    if assistant.assistant_type == Assistant.TYPE_SUPPORT:
                        # Retrieve saved suggestions, provide defaults if empty
                        saved_suggestions_from_db = assistant.saved_suggestions or {}

                        # Flatten if dict, else fallback
                        if isinstance(saved_suggestions_from_db, dict):
                            final_suggestions = [q for questions in saved_suggestions_from_db.values() for q in questions]
                        else:
                            final_suggestions = saved_suggestions_from_db if isinstance(saved_suggestions_from_db, list) else []
                        if not final_suggestions:
                            # Provide generic defaults if nothing is saved
                            final_suggestions = [
                                "What can you help me with?",
                                "Tell me about your products",
                                "What services do you offer?",
                                "Where are you located?"
                            ]
                        # Limit to 5 suggestions for the chat interface
                        response['suggestions'] = final_suggestions[:5]
                    # --- End Load Saved Suggestions ---

                    # Always return JSON for standard LLM responses
                    return JsonResponse(response)
                # Return JSON error response
                # Use status=500 for actual server-side errors during generation
                return JsonResponse({'status': 'error', 'error': response.get('content', 'Unknown error during generation.')}, status=500)

        except Exception as e:
            print(f"Error in assistant_interact: {e}")
            traceback.print_exc() # Print full traceback for debugging
            return JsonResponse({'error': 'An internal server error occurred.'}, status=500)

    return JsonResponse({'error': 'Invalid request method.'}, status=405)

@login_required
def assistant_train(request, company_id, assistant_id):
    """Handle training data uploads for an AI assistant."""
    # Fetch company ensuring user has access, prefetch info
    company = get_object_or_404(
        Company.objects.prefetch_related('info').filter( # Added prefetch
            Q(pk=company_id, owner=request.user) |
            Q(pk=company_id, memberships__user=request.user)
        ).distinct()
    )
    assistant = get_object_or_404(Assistant, id=assistant_id, company=company)

    # Permission Check: Allow owner OR user with object-level permission OR user in admin/member group.
    company_groups = ["company administrators", "company members"]
    is_owner = request.user.id == company.owner_id
    can_train = is_owner or \
                request.user.has_perm('assistants.change_assistant', assistant) or \
                _user_in_company_group(request.user, company, company_groups)
    if not can_train:
        raise PermissionDenied("You do not have permission to train this assistant.")

    if request.method == 'POST':
        form = AssistantTrainingForm(request.POST, request.FILES)
        if form.is_valid():
            try:
                assistant.process_training_data(
                    file=form.cleaned_data['training_data']
                )
                messages.success(
                    request,
                    f'Training data processed successfully for {assistant.name}'
                )
                return redirect('assistants:detail',
                              company_id=company.id,
                              assistant_id=assistant.id)
            except Exception as e:
                messages.error(request, f'Training failed: {str(e)}')
    else:
        form = AssistantTrainingForm()

    context = {
        'company': company,
        'assistant': assistant,
        'form': form
    }
    return render(request, 'assistants/assistant_train.html', context)

# API Views (No sidebar, no prefetch needed)
@login_required
def api_chat(request, company_id, assistant_id):
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    company = get_object_or_404(Company, id=company_id) # Simplified check for API
    assistant = get_object_or_404(Assistant, id=assistant_id, company=company)
    if not can_access_assistant(request.user, assistant): # Still need permission check
        return JsonResponse({'error': 'Permission denied.'}, status=403)
    try:
        data = json.loads(request.body)
        message = data.get('message', '').strip()
        context_id = data.get('context_id', '')
        if not message: return JsonResponse({'error': 'Message is required'}, status=400)
        response = generate_assistant_response(assistant=assistant, user_input=message, user=request.user, current_context_id=context_id)
        return JsonResponse(response)
    except json.JSONDecodeError: return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e: return JsonResponse({'error': str(e)}, status=500)

@login_required
def api_stream(request, company_id, assistant_id):
    company = get_object_or_404(Company, id=company_id)
    assistant = get_object_or_404(Assistant, id=assistant_id, company=company)
    if not can_access_assistant(request.user, assistant):
         def error_stream(): yield 'data: {"error": "Permission denied"}\n\n'
         return StreamingHttpResponse(error_stream(), content_type='text/event-stream', status=403)
    def event_stream(): yield 'data: {"error": "Streaming not implemented"}\n\n'
    return StreamingHttpResponse(event_stream(), content_type='text/event-stream')

# Analytics Views
@login_required
def assistant_analytics(request, company_id, assistant_id):
    """Display analytics for an AI assistant."""
    # Fetch company ensuring user has access, prefetch info
    company = get_object_or_404(
        Company.objects.prefetch_related('info').filter( # Added prefetch
            Q(pk=company_id, owner=request.user) |
            Q(pk=company_id, memberships__user=request.user)
        ).distinct()
    )
    assistant = get_object_or_404(Assistant, id=assistant_id, company=company)

    # Permission Check: Allow owner OR user with object-level permission OR user in admin/member group.
    company_groups = ["company administrators", "company members"]
    is_owner = request.user.id == company.owner_id
    can_view_analytics = is_owner or \
                         request.user.has_perm('assistants.view_assistant_analytics', assistant) or \
                         _user_in_company_group(request.user, company, company_groups)
    if not can_view_analytics:
        raise PermissionDenied("You do not have permission to view analytics for this assistant.")

    period = request.GET.get('period', 'all')
    now = timezone.now()
    start_date = None
    if period == 'day': start_date = now - timedelta(days=1)
    elif period == 'week': start_date = now - timedelta(weeks=1)
    elif period == 'month': start_date = now - timedelta(days=30)

    interactions_qs = Interaction.objects.filter(assistant=assistant)
    if start_date:
        interactions_qs = interactions_qs.filter(created_at__gte=start_date)

    analytics = interactions_qs.aggregate(
        total_interactions=Count('id'),
        average_rating=Avg('rating'),
        total_tokens=Sum('token_count'),
        average_response_time=Avg('duration')
    )
    analytics['total_interactions'] = analytics.get('total_interactions') or 0
    analytics['total_tokens'] = analytics.get('total_tokens') or 0

    recent_interactions = interactions_qs.select_related('user').order_by('-created_at')[:10]

    context = {
        'company': company,
        'assistant': assistant,
        'analytics': analytics,
        'recent_interactions': recent_interactions,
        'selected_period': period
    }
    return render(request, 'assistants/assistant_analytics.html', context)

@login_required
def assistant_usage(request, company_id, assistant_id):
    """Display usage statistics for an AI assistant."""
    # Fetch company ensuring user has access, prefetch info
    company = get_object_or_404(
        Company.objects.prefetch_related('info').filter( # Added prefetch
            Q(pk=company_id, owner=request.user) |
            Q(pk=company_id, memberships__user=request.user)
        ).distinct()
    )
    assistant = get_object_or_404(Assistant, id=assistant_id, company=company)

    # Permission Check: Allow owner OR user with object-level permission OR user in admin/member group.
    company_groups = ["company administrators", "company members"]
    is_owner = request.user.id == company.owner_id
    can_view_usage = is_owner or \
                     request.user.has_perm('assistants.view_assistant_usage', assistant) or \
                     _user_in_company_group(request.user, company, company_groups)
    if not can_view_usage:
        raise PermissionDenied("You do not have permission to view usage for this assistant.")

    month_start = timezone.now().replace(day=1, hour=0, minute=0, second=0)
    monthly_interactions = Interaction.objects.filter(
        assistant=assistant,
        created_at__gte=month_start
    )

    usage = {
        'monthly_interactions': monthly_interactions.count(),
        'monthly_tokens': monthly_interactions.aggregate(Sum('token_count'))['token_count__sum'] or 0,
        'estimated_cost': calculate_cost(
            monthly_interactions.aggregate(Sum('token_count'))['token_count__sum'] or 0,
            assistant.model
        )
    }

    context = {
        'company': company,
        'assistant': assistant,
        'usage': usage
    }
    return render(request, 'assistants/assistant_usage.html', context)

@login_required
def assistant_history(request, company_id, assistant_id):
    """Display the chat history for an AI assistant."""
    # Fetch company ensuring user has access, prefetch info
    company = get_object_or_404(
        Company.objects.prefetch_related('info').filter( # Added prefetch
            Q(pk=company_id, owner=request.user) |
            Q(pk=company_id, memberships__user=request.user)
        ).distinct()
    )
    assistant = get_object_or_404(Assistant, id=assistant_id, company=company)

    # Permission Check: Allow owner OR user with object-level permission OR user in admin/member group.
    company_groups = ["company administrators", "company members"]
    is_owner = request.user.id == company.owner_id
    can_view_history = is_owner or \
                       request.user.has_perm('assistants.view_assistant_usage', assistant) or \
                       _user_in_company_group(request.user, company, company_groups)
    if not can_view_history:
        raise PermissionDenied("You do not have permission to view history for this assistant.")

    # Check if user is owner OR has global access permission on the company to see all interactions
    # OR if they are in the admin/member group (who should also see all interactions)
    can_see_all = is_owner or \
                  request.user.has_perm('assistants.access_all_private', company) or \
                  _user_in_company_group(request.user, company, company_groups)
    if can_see_all:
         interactions_qs = Interaction.objects.filter(assistant=assistant)
    else:
         # Otherwise, only show interactions for the current user
         interactions_qs = Interaction.objects.filter(assistant=assistant, user=request.user)

    interactions = interactions_qs.order_by('created_at')

    paginator = Paginator(interactions, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'company': company,
        'assistant': assistant,
        'page_obj': page_obj,
    }
    return render(request, 'assistants/assistant_history.html', context)

# Community Assistants List View
def community_assistants_list(request):
    """Display a list of all community assistants the user has access to."""

    # Get all community assistants
    if request.user.is_authenticated:
        # For superusers, show all community assistants
        if request.user.is_superuser:
            assistants = Assistant.objects.filter(
                assistant_type=Assistant.TYPE_COMMUNITY,
                is_active=True
            ).select_related('company').prefetch_related(
                Prefetch('company__info')
            )
        else:
            # For regular users, show public community assistants and those from companies they belong to
            assistants = Assistant.objects.filter(
                Q(assistant_type=Assistant.TYPE_COMMUNITY) &
                (
                    Q(is_public=True) |  # Public assistants
                    Q(company__owner=request.user) |  # User owns the company
                    Q(company__memberships__user=request.user)  # User is a member of the company
                )
            ).filter(
                is_active=True
            ).select_related('company').prefetch_related(
                Prefetch('company__info')
            ).distinct()
    else:
        # For anonymous users, show only public community assistants
        assistants = Assistant.objects.filter(
            assistant_type=Assistant.TYPE_COMMUNITY,
            is_active=True,
            is_public=True
        ).select_related('company').prefetch_related(
            Prefetch('company__info')
        )

    # Check if we should hide standard tier assistants
    from directory.models import DirectorySettings
    settings_obj = DirectorySettings.load()

    # Get featured assistants (needed for the carousel)
    # For community assistants page, we want to include community assistants in the featured list
    featured_assistants = assistants.filter(is_featured=True)
    print(f"DEBUG: Found {featured_assistants.count()} featured community assistants")
    for assistant in featured_assistants:
        print(f"DEBUG: Featured assistant: {assistant.id} - {assistant.name} (is_featured={assistant.is_featured}, tier={assistant.tier})")

    if settings_obj.hide_standard_tier_assistants:
        # Get non-standard tier assistants
        non_standard_assistants = assistants.filter(~Q(tier=Assistant.TIER_STANDARD))

        # Combine featured and non-standard (avoiding duplicates)
        assistants = (featured_assistants | non_standard_assistants).distinct()
    # If hide_standard_tier_assistants is False, show all assistants regardless of tier

    # Apply search filter if provided
    search_query = request.GET.get('q', '').strip()
    if search_query:
        assistants = assistants.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(company__name__icontains=search_query)
        )

    # Apply sorting
    assistants = assistants.order_by('name')

    # Debug output
    print(f"DEBUG: Final assistants count: {assistants.count()}")
    for assistant in assistants:
        print(f"DEBUG: Assistant: {assistant.id} - {assistant.name} (tier={assistant.tier}, type={assistant.assistant_type}, company={assistant.company.name})")

    # Get saved assistant IDs for the current user (for like/favorite buttons)
    saved_assistant_ids = []
    if request.user.is_authenticated:
        from directory.models import SavedItem
        saved_assistant_ids = list(SavedItem.objects.filter(
            user=request.user,
            item_type='assistant',
            assistant__in=assistants
        ).values_list('assistant_id', flat=True))

    # Get items per page from request, default to 10
    items_per_page = request.GET.get('items_per_page')
    try:
        items_per_page = int(items_per_page) if items_per_page else 10
        # Limit to valid options: 10, 25, 50, 100
        if items_per_page not in [10, 25, 50, 100]:
            items_per_page = 10
    except ValueError:
        items_per_page = 10

    # Paginate the results
    paginator = Paginator(assistants, items_per_page)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get directory settings for featured carousel
    from directory.models import DirectorySettings
    directory_settings = DirectorySettings.load()

    # The active_company is already available in the template context
    # through the company_context processor in accounts/context_processors.py

    context = {
        'page_obj': page_obj,
        'assistants': page_obj.object_list,
        'featured_assistants': featured_assistants,
        'title': 'Community Assistants',
        'description': 'Browse and chat with community assistants',
        'search_query': search_query,
        'saved_assistant_ids': saved_assistant_ids,
        'hide_standard_tier_community_assistants': directory_settings.hide_standard_tier_community_assistants,
        'directory_settings_dict': {
            'featured_visible_count': directory_settings.featured_visible_count,
            'featured_autoplay_delay': directory_settings.featured_autoplay_delay,
        },
    }

    return render(request, 'assistants/community_assistants_list.html', context)

# Utility Views
@login_required
@require_POST
def rate_interaction(request, company_id, assistant_id):
    try:
        interaction_id = request.POST.get('interaction_id')
        rating = int(request.POST.get('rating'))
        if not (1 <= rating <= 5): raise ValueError("Rating must be between 1 and 5")
        interaction = get_object_or_404(Interaction, id=interaction_id, assistant_id=assistant_id, user=request.user)
        interaction.rating = rating
        interaction.save()
        return JsonResponse({'status': 'success', 'message': 'Rating submitted successfully'})
    except Exception as e: return JsonResponse({'status': 'error', 'message': str(e)}, status=400)

@login_required
def suggest_prompt(request, company_id):
    company = get_object_or_404(Company, id=company_id) # Simplified check
    context = request.GET.get('context', '').strip()
    if not context: return JsonResponse({'error': 'Context is required'}, status=400)
    suggestions = ["Example prompt 1", "Example prompt 2", "Example prompt 3"]
    return JsonResponse({'suggestions': suggestions})

@login_required
def estimate_cost(request, company_id):
    company = get_object_or_404(Company, id=company_id) # Simplified check
    token_count = request.GET.get('tokens', 0)
    model = request.GET.get('model', 'gpt-3.5-turbo')
    try:
        token_count = int(token_count)
        estimated_cost = calculate_cost(token_count, model)
        return JsonResponse({'token_count': token_count, 'estimated_cost': estimated_cost})
    except ValueError: return JsonResponse({'error': 'Invalid token count'}, status=400)


@login_required
@require_POST
def upvote_answer(request, interaction_id):
    """Upvote an answer and attribute to contexts used."""
    from .models import Interaction, AnswerUpvote, ContextUpvote

    interaction = get_object_or_404(Interaction, pk=interaction_id)

    # Check if user has already upvoted this answer
    upvote, created = AnswerUpvote.objects.get_or_create(
        user=request.user,
        interaction=interaction
    )

    if created:
        # If this is a new upvote, also upvote all contexts used
        for context in interaction.used_contexts.all():
            ContextUpvote.objects.get_or_create(
                user=request.user,
                context=context
            )

        message = "Thank you for your feedback! We've also upvoted the knowledge sources used."
        status = "added"
    else:
        # If upvote already exists, remove it (toggle behavior)
        upvote.delete()
        message = "Upvote removed."
        status = "removed"

    # Return JSON response for AJAX handling
    return JsonResponse({
        'status': 'success',
        'message': message,
        'upvote_status': status
    })

# HTMX Partial Views (No sidebar, no prefetch needed)
@login_required
def assistant_list_partial(request, company_id):
    return assistant_list(request, company_id)

# Context API View
def get_context_content(request, company_id, assistant_id, context_id):
    """Get the content of a community context."""
    # Fetch assistant first to check public status
    assistant = get_object_or_404(Assistant, id=assistant_id, company_id=company_id)

    # Check permissions: Allow if public OR if logged-in user has access
    is_allowed = False
    if assistant.is_public:
        is_allowed = True
    elif request.user.is_authenticated and can_access_assistant(request.user, assistant):
        is_allowed = True

    if not is_allowed:
        return JsonResponse({'error': 'Permission denied'}, status=403)

    # Get the context
    try:
        from django.db.models import Count
        context = get_object_or_404(CommunityContext, id=context_id, assistant=assistant)

        # Get direct upvote count
        direct_upvote_count = context.upvotes.count()

        # Get answer upvote count (upvotes from answers that used this context)
        from .models import AnswerUpvote
        answer_upvote_count = AnswerUpvote.objects.filter(
            interaction__used_contexts=context
        ).count()

        # Check if the current user has upvoted this context
        is_upvoted = False
        if request.user.is_authenticated:
            is_upvoted = context.upvotes.filter(user=request.user).exists()

        # Get upvote URL for the context
        from django.urls import reverse
        upvote_url = reverse('assistants:upvote_context', kwargs={
            'company_id': company_id,
            'assistant_id': assistant_id,
            'context_id': context_id
        })

        return JsonResponse({
            'id': context.id,
            'title': context.title or f"Context #{context.id}",
            'content': context.text_content,
            'created_by': context.created_by.username if context.created_by else "Anonymous",
            'created_at': context.created_at.strftime('%Y-%m-%d'),
            'times_used': context.times_used,
            'direct_upvote_count': direct_upvote_count,
            'answer_upvote_count': answer_upvote_count,
            'total_upvote_count': direct_upvote_count + answer_upvote_count,
            'is_upvoted': is_upvoted,
            'upvote_url': upvote_url
        })
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def chat_responses_partial(request, company_id, assistant_id):
    company = get_object_or_404(Company, id=company_id)
    assistant = get_object_or_404(Assistant, id=assistant_id, company=company)
    if not can_access_assistant(request.user, assistant): return HttpResponseForbidden("Permission Denied")
    interactions = Interaction.objects.filter(assistant=assistant, user=request.user).order_by('-created_at')[:10]
    return render(request, 'assistants/partials/chat_responses.html', {'interactions': interactions, 'assistant': assistant})

@login_required
def assistant_status_partial(request, company_id, assistant_id):
    company = get_object_or_404(Company, id=company_id)
    assistant = get_object_or_404(Assistant, id=assistant_id, company=company)
    if not can_access_assistant(request.user, assistant): return HttpResponseForbidden("Permission Denied")
    return render(request, 'assistants/partials/assistant_status.html', {'assistant': assistant})

@login_required
def training_status(request, company_id, assistant_id):
    company = get_object_or_404(Company, id=company_id)
    assistant = get_object_or_404(Assistant, id=assistant_id, company=company)
    # Use object-level permission check
    if not request.user.has_perm('assistants.change_assistant', assistant):
        return JsonResponse({'error': 'Permission denied.'}, status=403)
    # can_access_assistant check is implicitly covered by change_assistant check above
    status = {'is_training': False, 'progress': 0, 'last_trained': assistant.last_trained.isoformat() if assistant.last_trained else None}
    return JsonResponse(status)

from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST

@csrf_exempt
@require_POST
def api_llm_suggested_questions(request):
    """
    API endpoint: Receives POST with 'conversation' (list of dicts or string) and 'nav_context' (string), returns 5 LLM-suggested questions.
    """
    import json
    import logging # Moved import back to top
    from .models import Assistant # Import Assistant model at the top of the function
    logger = logging.getLogger("assistants.llm_suggested_questions") # Moved logger init back to top
    try:
        data = json.loads(request.body.decode('utf-8'))
        conversation_data = data.get('conversation', []) # Default to empty list
        nav_context_from_request = data.get('nav_context', '') # Context from JS nav clicks
        assistant_id = data.get('assistant_id')

        if not assistant_id:
            return JsonResponse({'error': 'Missing assistant_id'}, status=400)

        try:
            # Fetch assistant, prefetch navigation items if it's a support type
            assistant = Assistant.objects.prefetch_related('navigation_items').get(id=assistant_id)
        except Assistant.DoesNotExist:
            return JsonResponse({'error': 'Assistant not found'}, status=404)
        except Exception as ex:
             return JsonResponse({'error': f'Error fetching assistant: {ex}'}, status=500)

        # Determine if it's the initial load (empty conversation history)
        is_initial_load = not conversation_data

        # Prepare conversation string for LLM/checks
        if isinstance(conversation_data, list):
             conversation_str = "\n".join([
                 f"{msg.get('role','user')}: {msg.get('content','')}" for msg in conversation_data
             ])
        else:
             conversation_str = str(conversation_data)

        # Prepare nav_context for LLM
        final_nav_context = nav_context_from_request # Start with context from JS click (usually empty initially)
        if is_initial_load and assistant.assistant_type == Assistant.TYPE_SUPPORT:
            # For initial load of support assistants, build detailed context from nav items
            nav_items = assistant.navigation_items.filter(visible=True).order_by('order')
            if nav_items.exists():
                # Get website data
                website_data = assistant.website_data or {}

                # Prepare context from navigation items with detailed content
                context_data = []
                for item in nav_items:
                    item_id = item.id
                    data_key = f"item_{item_id}"
                    item_data = website_data.get(data_key)

                    # Add section header with more details
                    section_header = f"SECTION: '{item.label}' (Type: {item.section_type}, ID: {item.unique_id})"

                    if item_data:
                        # Format the context data with the section label
                        if isinstance(item_data, (str, int, float, bool)):
                            context_data.append(f"{section_header}\n{item_data}")
                        elif isinstance(item_data, dict) and 'content' in item_data:
                            # For HTML content, try to extract text
                            content = item_data['content']
                            # Remove HTML tags for better context
                            import re
                            content_text = re.sub(r'<[^>]+>', ' ', content)
                            context_data.append(f"{section_header}\n{content_text}")
                        else:
                            # For complex data structures, convert to JSON
                            context_data.append(f"{section_header}\n{json.dumps(item_data, indent=2)}")
                    else:
                        # Even if no data, include the section name for awareness
                        context_data.append(f"{section_header}\n(No content available)")

                # Combine all context data with clear section separators
                final_nav_context = "\n\n---\n\n".join(context_data)
                logger.info("[LLM SUGGESTIONS] Initial load: Using detailed nav items for context")
            else:
                 logger.info("[LLM SUGGESTIONS] Initial load: No visible nav items found.")
        elif not is_initial_load:
             # For subsequent loads, use the context passed from JS (if any)
             final_nav_context = nav_context_from_request
             logger.info("[LLM SUGGESTIONS] Subsequent load: Using nav_context from request: %s", final_nav_context)
        else:
             # Initial load for non-support types, or if nav context from request is empty
             final_nav_context = nav_context_from_request # Keep it as potentially empty
             logger.info("[LLM SUGGESTIONS] Initial load (non-support) or empty request context: nav_context: %s", final_nav_context)


        # Logger is now initialized at the top
        logger.info("[LLM SUGGESTIONS] assistant_id=%s", assistant_id) # Keep logging assistant_id
        logger.info("[LLM SUGGESTIONS] is_initial_load: %s", is_initial_load) # Log initial load status
        logger.info("[LLM SUGGESTIONS] conversation_data (raw): %s", conversation_data) # Log raw conversation data
        logger.info("[LLM SUGGESTIONS] conversation_str (for LLM): %s", conversation_str) # Log formatted conversation string
        logger.info("[LLM SUGGESTIONS] final_nav_context (for LLM): %s", final_nav_context) # Log final nav context being used

        # --- Strict Check for Assistant Inability (Only if NOT initial load) ---
        if not is_initial_load:
            try:
                # Ensure conversation_data is a list and has at least 2 messages
                if isinstance(conversation_data, list) and len(conversation_data) >= 2:
                    last_assistant_msg = conversation_data[-1].get('content', '').lower()

                # Keywords indicating inability in assistant response
                inability_keywords = ["don't know", "do not know", "cannot", "no details", "unable to", "sorry, i don't have access", "lack the ability", "do not have details"]

                # Check if the last assistant message indicates inability
                assistant_cant_answer = any(keyword in last_assistant_msg for keyword in inability_keywords)

                if assistant_cant_answer:
                    logger.warning("[LLM SUGGESTIONS] Assistant indicated inability in last response. Suppressing ALL suggestions for this turn.")
                    # Return empty list immediately, bypassing LLM call
                    return JsonResponse({'suggested_questions': []})

            except Exception as check_ex:
                logger.error("[LLM SUGGESTIONS] Error during inability check: %s. Proceeding with generation.", check_ex)
                # Fall through to generate suggestions if check fails for subsequent loads

        # --- If no inability detected (or initial load), proceed with LLM generation ---
        logger.info("[LLM SUGGESTIONS] Proceeding with LLM generation.")
        # Use final_nav_context which includes nav items on initial load
        suggestions = generate_llm_suggested_questions(conversation_str, final_nav_context)
        logger.info("[LLM SUGGESTIONS] LLM generated suggestions: %s", suggestions)

        # Optional: Could add a less strict filter here as a fallback if needed, but for now, rely on the prompt.

        return JsonResponse({'suggested_questions': suggestions})
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

# Public assistant chat view (No sidebar, no prefetch needed)
def assistant_chat_view(request, slug):
    logger.debug(f"Starting assistant_chat_view for slug: {slug}")

    try:
        # Import SiteConfiguration from the correct module
        from site_settings.models import SiteConfiguration

        assistant = get_object_or_404(Assistant.objects.select_related('company', 'company__info'), slug=slug, is_public=True, is_active=True) # Added company__info prefetch
        logger.debug(f"Found assistant: {assistant.id}, {assistant.name}, model={assistant.model}")

        site_config = SiteConfiguration.load()
        logger.debug(f"Loaded site configuration")

        default_logo_url = site_config.default_assistant_logo.url if site_config.default_assistant_logo else None
        logger.debug(f"Default logo URL: {default_logo_url}")
    except Exception as e:
        logger.error(f"Error in assistant_chat_view: {e}")
        logger.exception("Full traceback:")
        raise

    # Fetch ordered, visible navigation items for the sidebar
    navigation_items = assistant.navigation_items.filter(visible=True).order_by('order')

    # Load initial suggestions from saved field or generate them (only for support assistants) - COMMENTED OUT
    initial_suggestions = []
    """
    if assistant.assistant_type == Assistant.TYPE_SUPPORT:
        # Debug log the assistant and navigation items
        print(f"DEBUG: Assistant: {assistant.name}, ID: {assistant.id}")
        print(f"DEBUG: Assistant type: {assistant.assistant_type}")
        print(f"DEBUG: Website data keys: {list(assistant.website_data.keys() if assistant.website_data else [])}")

        # Debug log the navigation items
        nav_items_debug = assistant.navigation_items.filter(visible=True).order_by('order')
        print(f"DEBUG: Navigation items count: {nav_items_debug.count()}")
        for item in nav_items_debug:
            print(f"DEBUG: Item: {item.label}, Type: {item.section_type}, ID: {item.unique_id}")
            item_id = item.id
            data_key = f"item_{item_id}"
            item_data = assistant.website_data.get(data_key) if assistant.website_data else None
            print(f"DEBUG: Data for {item.label}: {item_data}")

        # Always regenerate suggestions by clearing saved suggestions
        assistant.saved_suggestions = {}
        assistant.save(update_fields=['saved_suggestions'])
        initial_suggestions = {}

        # If no saved suggestions, generate them from the navigation items
        if not initial_suggestions:
            # Get website data and navigation items
            website_data = assistant.website_data or {}
            nav_items = assistant.navigation_items.filter(visible=True).order_by('order')

            # Generate suggestions based on navigation items
            from assistants.llm_suggested_questions import generate_llm_suggested_questions

            # Prepare context from navigation items with detailed content
            context_data = []
            for item in nav_items:
                item_id = item.id
                data_key = f"item_{item_id}"
                item_data = website_data.get(data_key)

                # Add section header with more details
                section_header = f"SECTION: '{item.label}' (Type: {item.section_type}, ID: {item.unique_id})"

                if item_data:
                    # Format the context data with the section label
                    if isinstance(item_data, (str, int, float, bool)):
                        context_data.append(f"{section_header}\n{item_data}")
                    elif isinstance(item_data, dict) and 'content' in item_data:
                        # For HTML content, try to extract text
                        content = item_data['content']
                        # Remove HTML tags for better context
                        import re
                        content_text = re.sub(r'<[^>]+>', ' ', content)
                        context_data.append(f"{section_header}\n{content_text}")
                    else:
                        # For complex data structures, convert to JSON
                        context_data.append(f"{section_header}\n{json.dumps(item_data, indent=2)}")
                else:
                    # Even if no data, include the section name for awareness
                    context_data.append(f"{section_header}\n(No content available)")

            # Combine all context data with clear section separators
            combined_context = "\n\n---\n\n".join(context_data)

            # Debug log the context data
            print(f"DEBUG: Context data for suggestions: {context_data}")
            print(f"DEBUG: Combined context: {combined_context}")

            # Generate suggestions if we have context data
            if combined_context:
                print("DEBUG: Generating suggestions from combined context")
                questions = generate_llm_suggested_questions("", combined_context)
                print(f"DEBUG: Generated questions: {questions}")
                initial_suggestions = questions

                # Force regeneration by clearing saved_suggestions
                assistant.saved_suggestions = {}
                assistant.save(update_fields=['saved_suggestions'])
            else:
                print("DEBUG: No context data, using fallback questions")
                # Fallback to default questions if no context data
                initial_suggestions = [
                    "What can you help me with?",
                    "Tell me about your products",
                    "What services do you offer?",
                    "Where are you located?"
                ]
    """

    # Add the form for TinyMCE if this is a community assistant
    form = None
    if assistant.assistant_type == 'community':
        from .forms import SimpleCommunityContextForm
        form = SimpleCommunityContextForm()

    # Check if assistant is favorited by the user
    is_favorited = False
    if request.user.is_authenticated:
        is_favorited = SavedItem.objects.filter(
            user=request.user,
            assistant=assistant,
            item_type='assistant'
        ).exists()

    context = {
        'assistant': assistant,
        'navigation_items': navigation_items, # Pass the queryset
        'default_logo_url': default_logo_url, # Pass default logo URL
        'form': form,  # Pass the form to the template for TinyMCE media
        'website_data': assistant.website_data or {},  # Pass website_data to the template
        'debug': True,  # Enable debug mode
        'is_favorited': is_favorited  # Pass favorited status
    }

    # Only include initial_suggestions for support assistants - COMMENTED OUT
    """
    if assistant.assistant_type == Assistant.TYPE_SUPPORT:
        # Ensure suggestions are passed as a JSON string for safe JS injection
        if isinstance(initial_suggestions, dict):
            # Flatten the dictionary of questions into a single list
            flattened_suggestions = []
            for questions in initial_suggestions.values():
                if isinstance(questions, list):
                    flattened_suggestions.extend(questions)
            # Take the first 5 questions
            context['initial_suggestions'] = json.dumps(flattened_suggestions[:5])
        else:
            # If it's already a list, just take the first 5
            context['initial_suggestions'] = json.dumps(initial_suggestions[:5])
    """
    return render(request, 'assistants/assistant_chat.html', context)

# Public assistant interact endpoint (for slug-based URLs)
def public_assistant_interact(request, slug):
    """Handle chat interactions with a public AI assistant using slug."""
    # Use print statements for immediate visibility in the console
    print(f"DEBUG: Starting public_assistant_interact view for slug: {slug}")
    print(f"DEBUG: Request method: {request.method}")
    print(f"DEBUG: Request path: {request.path}")

    try:
        # Fetch assistant by slug
        assistant = get_object_or_404(Assistant, slug=slug, is_public=True, is_active=True)
        print(f"DEBUG: Found public assistant: {assistant.id}, {assistant.name}, model={assistant.model}")
        print(f"DEBUG: Assistant company: {assistant.company.id}, {assistant.company.name}")
        print(f"DEBUG: Assistant type: {assistant.assistant_type}")
        print(f"DEBUG: Assistant is_public: {assistant.is_public}")
        print(f"DEBUG: Assistant is_active: {assistant.is_active}")
    except Exception as e:
        print(f"DEBUG: Error fetching assistant by slug: {e}")
        import traceback
        traceback.print_exc()
        return JsonResponse({
            'status': 'error',
            'error': f"Error finding assistant: {str(e)}"
        }, status=404)

    if request.method == 'POST':
        try:
            # Determine user object (None for guests)
            user = request.user if request.user.is_authenticated else None

            try:
                data = json.loads(request.body)
                message = data.get('message', '').strip()
                history = data.get('history', [])
                is_nav_click = data.get('navigation_click', False)
                section_id = data.get('section_id')

                # DEBUG: Log the request data
                print(f"DEBUG - Public Interact - Request data: message='{message}', is_nav_click={is_nav_click}, section_id='{section_id}'")

                if not isinstance(history, list):
                    history = []
            except json.JSONDecodeError:
                return JsonResponse({'error': 'Invalid JSON format in request body.'}, status=400)

            # --- Handle Navigation Click Directly ---
            if is_nav_click and section_id:
                try:
                    website_data = assistant.website_data or {}
                    navigation_items = website_data.get('navigation_items', [])
                    item_data = website_data.get(section_id)
                    nav_item = next((item for item in navigation_items if item.get('unique_id') == section_id), None)

                    if not nav_item:
                        # Try to find it in the database instead
                        from .models import NavigationItem
                        try:
                            db_nav_item = NavigationItem.objects.get(assistant=assistant, unique_id=section_id)
                            # Create a dict representation of the database item
                            nav_item = {
                                'id': db_nav_item.id,
                                'unique_id': db_nav_item.unique_id,
                                'label': db_nav_item.label,
                                'section_type': db_nav_item.section_type
                            }
                            # Try to get item data using the item ID
                            item_data = website_data.get(f"item_{db_nav_item.id}")
                        except NavigationItem.DoesNotExist:
                            raise ValueError(f"Navigation item with ID '{section_id}' not found.")

                    if not nav_item:
                        raise ValueError(f"Navigation item with ID '{section_id}' not found.")

                    label = nav_item.get('label', 'This Section')
                    section_type = nav_item.get('section_type', 'text')
                    formatted_content = f"<h5>{label}</h5><hr>" # Start with header

                    # Format content based on section_type and item_data
                    if isinstance(item_data, dict) and 'content' in item_data:
                        formatted_content += item_data['content']
                    elif isinstance(item_data, str):
                        formatted_content += item_data
                    else:
                        # Generate placeholder content based on section_type
                        if section_type == 'text':
                            formatted_content += f"<p>Information about {label}</p>"
                        elif section_type == 'product':
                            formatted_content += f"<p>Product information for {label}</p>"
                        elif section_type == 'service':
                            formatted_content += f"<p>Service information for {label}</p>"
                        elif section_type == 'team':
                            formatted_content += f"<p>Team information for {label}</p>"
                        elif section_type == 'location':
                            formatted_content += f"<p>Location information for {label}</p>"
                        elif section_type == 'faq':
                            formatted_content += f"<p>Frequently asked questions about {label}</p>"
                        else:
                            formatted_content += f"<p>Information about {label}</p>"

                        formatted_content += '<p class="text-muted">No specific entries available for this section.</p>'

                    # Return direct response, bypassing LLM and history saving
                    return JsonResponse({
                        'status': 'success',
                        'content': formatted_content,
                        'is_navigation_response': True # Flag for frontend
                    })
                except Exception as e:
                    print(f"Error handling navigation click for section {section_id}: {e}")
                    traceback.print_exc() # Print full traceback for debugging
                    # Return error but still flag as nav response if possible
                    return JsonResponse({'status': 'error', 'error': f"Error loading section: {e}", 'is_navigation_response': True}, status=500)

            # --- Handle regular chat message ---
            # Process regular chat messages for public assistants
            print(f"DEBUG: Processing regular chat message for public assistant: {message[:100]}...")

            if not message:
                print("DEBUG: Empty message received")
                return JsonResponse({'error': 'Message is required.'}, status=400)

            try:
                # Build context from navigation items if available
                nav_context = ""
                website_data = assistant.website_data or {}
                print(f"DEBUG: Website data keys: {list(website_data.keys())}")

                navigation_items = assistant.navigation_items.filter(visible=True).order_by('order')
                print(f"DEBUG: Found {navigation_items.count()} navigation items")

                if navigation_items.exists():
                    logger.debug(f"Found {navigation_items.count()} navigation items")
                    nav_context += "\n\nNavigation Structure:\n"
                    for item in navigation_items:
                        nav_context += f"- {item.label} (ID: {item.unique_id}, Type: {item.section_type})\n"

                    # Add content from navigation items
                    nav_context += "\n--- Navigation Content Sections ---\n"
                    for item in navigation_items:
                        item_id = item.id
                        data_key = f"item_{item_id}"
                        item_data = website_data.get(data_key)

                        nav_context += f"\n## Content for Section: '{item.label}' (ID: {item.unique_id})\n"
                        if item_data:
                            # Check if it's a text section and extract content
                            if item.section_type == 'text' and isinstance(item_data, dict):
                                content = item_data.get('content', '')
                                nav_context += f"{content}\n"
                            # Handle other simple types directly
                            elif isinstance(item_data, (str, int, float, bool)):
                                nav_context += f"{item_data}\n"
                            # Dump complex types as JSON
                            else:
                                try:
                                    nav_context += f"{json.dumps(item_data, indent=2)}\n"
                                except TypeError:
                                    nav_context += f"(Unserializable data for this section)\n"
                        else:
                            nav_context += "(No specific data provided for this section)\n"
                    nav_context += "--- End Navigation Content Sections ---\n"

                # Update assistant's extra_context temporarily with navigation context if available
                original_extra_context = assistant.extra_context
                if nav_context:
                    logger.debug("Adding navigation context to assistant's extra_context")
                    assistant.extra_context = f"{original_extra_context}\n\nHere is additional context from navigation items:\n{nav_context}"

                # Call the LLM to generate a response
                print("DEBUG: Calling generate_assistant_response")
                print(f"DEBUG: Assistant: {assistant.name}, Model: {assistant.model}")
                print(f"DEBUG: User input: {message[:100]}...")
                print(f"DEBUG: History length: {len(history)}")

                try:
                    response = generate_assistant_response(
                        assistant=assistant,
                        user_input=message,
                        history=history,
                        user=user  # Pass user object (can be None)
                    )
                    print(f"DEBUG: Response received: {response.keys()}")
                except Exception as llm_error:
                    print(f"DEBUG: Error in generate_assistant_response: {llm_error}")
                    print(f"DEBUG: Exception type: {type(llm_error).__name__}")
                    print(f"DEBUG: Exception args: {llm_error.args}")
                    import traceback
                    traceback.print_exc()
                    return JsonResponse({
                        'status': 'error',
                        'error': f"Error generating response: {str(llm_error)}"
                    }, status=500)

                # Restore the original extra_context
                assistant.extra_context = original_extra_context

                # Process the response
                if response.get('status') == 'error':
                    logger.error(f"Error in response: {response.get('content')}")
                    return JsonResponse({
                        'status': 'error',
                        'error': response.get('content', 'Unknown error during generation.')
                    }, status=500)

                # Format content with markdown if needed
                content_str = response.get('content')
                if isinstance(content_str, str):
                    content_str_trimmed = content_str.strip()
                    if content_str_trimmed.startswith('<') and content_str_trimmed.endswith('>'):
                        response['content'] = content_str
                    else:
                        response['content'] = markdown.markdown(
                            content_str,
                            extensions=['fenced_code', 'tables', 'nl2br']
                        )

                # Return the response
                return JsonResponse(response)

            except Exception as e:
                logger.error(f"Error processing chat message: {e}")
                logger.error(f"Exception type: {type(e).__name__}")
                logger.error(f"Exception args: {e.args}")
                logger.exception("Full traceback:")
                return JsonResponse({
                    'status': 'error',
                    'error': f"Error processing message: {str(e)}"
                }, status=500)

        except Exception as e:
            print(f"DEBUG - Public Interact - Unexpected error: {e}")
            traceback.print_exc()
            return JsonResponse({'error': str(e)}, status=500)
    else:
        return JsonResponse({'error': 'Method not allowed'}, status=405)

# ID-based assistant chat view (for backward compatibility)
def assistant_chat_view_by_id(request, company_id, assistant_id):
    # Get the assistant by ID
    assistant = get_object_or_404(Assistant, id=assistant_id, company_id=company_id)

    # Redirect to the slug-based URL
    return redirect('assistants:assistant_chat', slug=assistant.slug)


# --- Folder Management Views ---

@login_required
@require_POST # Ensure only POST requests
def assistant_folder_create(request, company_id):
    company = get_object_or_404(Company, id=company_id)

    # Explicitly check if the user is the owner OR a superuser OR has the specific permission
    can_create_folder = False
    if request.user.is_superuser:
        can_create_folder = True
    elif request.user.id == company.owner_id: # More robust check using IDs
        can_create_folder = True
    elif request.user.has_perm('accounts.add_assistantfolder', company):
        can_create_folder = True

    if not can_create_folder:
        return JsonResponse({'status': 'error', 'message': 'Permission denied.'}, status=403)

    try:
        form = AssistantFolderForm(request.POST, company=company) # Pass company for validation
        if form.is_valid():
            folder = form.save(commit=False)
            folder.company = company
            folder.save()
            # Return success, maybe include folder details for JS update
            return JsonResponse({
                'status': 'success',
                'message': 'Folder created successfully.',
                'folder': {'id': folder.id, 'name': folder.name}
            })
        else:
            # Return form errors
            return JsonResponse({'status': 'error', 'errors': form.errors}, status=400)
    except Exception as e:
        # Log the exception for debugging on the server
        print(f"ERROR in assistant_folder_create: {type(e).__name__} - {e}")
        # Return a generic JSON error for unexpected issues
        return JsonResponse({'status': 'error', 'message': 'An unexpected server error occurred.'}, status=500)

@login_required
@require_POST # Ensure only POST requests
def assistant_folder_edit(request, folder_id):
    folder = get_object_or_404(AssistantFolder, id=folder_id)
    company = folder.company
    # Permission Check: Allow owner, superuser, or user with explicit change permission on the company
    can_edit_folder = False
    if request.user.is_superuser:
        can_edit_folder = True
    elif request.user.id == company.owner_id: # Check against company owner
        can_edit_folder = True
    elif request.user.has_perm('accounts.change_assistantfolder', company):
        can_edit_folder = True

    if not can_edit_folder:
        return JsonResponse({'status': 'error', 'message': 'Permission denied.'}, status=403)

    form = AssistantFolderForm(request.POST, instance=folder, company=company) # Pass company and instance
    if form.is_valid():
        folder = form.save()
        return JsonResponse({
            'status': 'success',
            'message': 'Folder updated successfully.',
            'folder': {'id': folder.id, 'name': folder.name}
        })
    else:
        return JsonResponse({'status': 'error', 'errors': form.errors}, status=400)

@login_required
@require_POST # Ensure only POST requests
def assistant_folder_delete(request, folder_id):
    folder = get_object_or_404(AssistantFolder, id=folder_id)
    company = folder.company
    # Permission Check: Allow owner, superuser, or user with explicit delete permission on the company
    can_delete_folder = False
    if request.user.is_superuser:
        can_delete_folder = True
    elif request.user.id == company.owner_id: # Check against company owner
        can_delete_folder = True
    elif request.user.has_perm('accounts.delete_assistantfolder', company):
        can_delete_folder = True

    if not can_delete_folder:
        return JsonResponse({'status': 'error', 'message': 'Permission denied.'}, status=403)

    # Set assistants in this folder to have folder=None before deleting
    Assistant.objects.filter(folder=folder).update(folder=None)
    folder_name = folder.name # Get name for message
    folder.delete()

    return JsonResponse({
        'status': 'success',
        'message': f'Folder "{folder_name}" deleted successfully. Assistants moved to Unassigned.'
    })

@login_required
@require_POST
def add_viewer(request, company_id, assistant_id):
    """Add a viewer to a private assistant."""
    # Fetch company ensuring user has access
    company = get_object_or_404(
        Company.objects.filter(
            Q(pk=company_id, owner=request.user) |
            Q(pk=company_id, memberships__user=request.user)
        ).distinct()
    )
    assistant = get_object_or_404(Assistant, id=assistant_id, company=company)

    # Permission check: Only owner or admin can add viewers
    if not (request.user.id == company.owner_id or
            request.user.has_perm('assistants.change_assistant', assistant) or
            _user_in_company_group(request.user, company, ["company administrators"])):
        return JsonResponse({'status': 'error', 'message': 'Permission denied.'}, status=403)

    # Get viewer ID from request
    viewer_id = request.POST.get('viewer_id')
    if not viewer_id:
        return JsonResponse({'status': 'error', 'message': 'Viewer ID is required.'}, status=400)

    # Get viewer user
    from django.contrib.auth import get_user_model
    User = get_user_model()
    try:
        viewer = User.objects.get(id=viewer_id)
    except User.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': 'User not found.'}, status=404)

    # Check if viewer is a member of the company
    from accounts.models import Membership
    if not Membership.objects.filter(user=viewer, company=company).exists():
        return JsonResponse({'status': 'error', 'message': 'User is not a member of this company.'}, status=400)

    # Assign view_assistant permission to the viewer for this assistant
    from guardian.shortcuts import assign_perm
    assign_perm('assistants.view_assistant', viewer, assistant)

    return JsonResponse({'status': 'success', 'message': 'Viewer added successfully.'})

@login_required
@require_POST
def remove_viewer(request, company_id, assistant_id):
    """Remove a viewer from a private assistant."""
    # Fetch company ensuring user has access
    company = get_object_or_404(
        Company.objects.filter(
            Q(pk=company_id, owner=request.user) |
            Q(pk=company_id, memberships__user=request.user)
        ).distinct()
    )
    assistant = get_object_or_404(Assistant, id=assistant_id, company=company)

    # Permission check: Only owner or admin can remove viewers
    if not (request.user.id == company.owner_id or
            request.user.has_perm('assistants.change_assistant', assistant) or
            _user_in_company_group(request.user, company, ["company administrators"])):
        return JsonResponse({'status': 'error', 'message': 'Permission denied.'}, status=403)

    # Get viewer ID from request
    viewer_id = request.POST.get('viewer_id')
    if not viewer_id:
        return JsonResponse({'status': 'error', 'message': 'Viewer ID is required.'}, status=400)

    # Get viewer user
    from django.contrib.auth import get_user_model
    User = get_user_model()
    try:
        viewer = User.objects.get(id=viewer_id)
    except User.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': 'User not found.'}, status=404)

    # Remove view_assistant permission from the viewer for this assistant
    from guardian.shortcuts import remove_perm
    remove_perm('assistants.view_assistant', viewer, assistant)

    return JsonResponse({'status': 'success', 'message': 'Viewer removed successfully.'})

@login_required
@require_POST # Ensure only POST requests
def assign_assistant_folder(request, assistant_id):
    """Assigns an assistant to a specific folder or unassigns it."""
    assistant = get_object_or_404(Assistant, id=assistant_id)
    company = assistant.company
    # Permission Check: Allow owner, superuser, or user with explicit change permission
    if not (request.user.is_superuser or request.user.id == company.owner_id or request.user.has_perm('assistants.change_assistant', assistant)):
        return JsonResponse({'status': 'error', 'message': 'Permission denied.'}, status=403)

    folder_id = request.POST.get('folder_id')
    target_folder = None

    if folder_id and folder_id != 'unassigned':
        try:
            # Ensure the target folder belongs to the same company
            target_folder = get_object_or_404(AssistantFolder, id=int(folder_id), company=company)
        except (ValueError, TypeError):
            return JsonResponse({'status': 'error', 'message': 'Invalid folder ID.'}, status=400)
        except AssistantFolder.DoesNotExist:
             return JsonResponse({'status': 'error', 'message': 'Folder not found or does not belong to this company.'}, status=404)

    # Update the assistant's folder
    assistant.folder = target_folder
    assistant.save(update_fields=['folder'])

    return JsonResponse({
        'status': 'success',
        'message': f'Assistant "{assistant.name}" moved successfully.',
        'folder_name': target_folder.name if target_folder else 'Unassigned'
    })

# --- End Folder Management Views ---


# --- Analyze & Suggest View ---

@login_required
def generate_suggested_questions_view(request, company_id, assistant_id):
    """Generates suggested questions based on the assistant's navigation structure."""
    # Fetch company ensuring user has access
    company = get_object_or_404(
        Company.objects.filter(
            Q(pk=company_id, owner=request.user) |
            Q(pk=company_id, memberships__user=request.user)
        ).distinct()
    )
    assistant = get_object_or_404(Assistant, id=assistant_id, company=company)

    # Permission Check: Allow owner OR user with view/change permission OR user in admin/member group.
    # Using 'change_assistant' perm as a proxy for being able to analyze/suggest for it.
    company_groups = ["company administrators", "company members"]
    is_owner = request.user.id == company.owner_id
    can_analyze = is_owner or \
                  request.user.has_perm('assistants.change_assistant', assistant) or \
                  _user_in_company_group(request.user, company, company_groups)

    if not can_analyze:
        return JsonResponse({'error': 'Permission denied.'}, status=403)

    website_data = assistant.website_data or {}
    navigation_items = website_data.get('navigation_items', [])
    # Use the LLM to generate 5 contextual questions per navigation item based on actual content
    from assistants.llm_suggested_questions import generate_llm_suggested_questions

    suggestions_per_item = {}

    for item in navigation_items:
        if not isinstance(item, dict) or not item.get('visible'):
            continue  # Skip invisible or non-dict items

        label = item.get('label', 'this section')
        unique_id = item.get('unique_id')
        section_type = item.get('section_type', 'text')
        item_data = website_data.get(unique_id)

        # Prepare the context/content for this section with more details
        section_header = f"SECTION: '{label}' (Type: {section_type}, ID: {unique_id})"

        if isinstance(item_data, list):
            # Join dicts/strings into a readable context for the LLM
            context_lines = []
            for entry in item_data:
                if isinstance(entry, dict):
                    context_lines.append("; ".join(f"{k}: {v}" for k, v in entry.items()))
                elif isinstance(entry, str):
                    # For HTML content, try to extract text
                    import re
                    entry_text = re.sub(r'<[^>]+>', ' ', entry)
                    context_lines.append(entry_text)
            nav_context = f"{section_header}\n" + "\n".join(context_lines)
        elif isinstance(item_data, dict):
            if 'content' in item_data:
                # For HTML content, try to extract text
                import re
                content_text = re.sub(r'<[^>]+>', ' ', item_data['content'])
                nav_context = f"{section_header}\n{content_text}"
            else:
                nav_context = f"{section_header}\n" + "; ".join(f"{k}: {v}" for k, v in item_data.items())
        elif isinstance(item_data, str):
            # For HTML content, try to extract text
            import re
            item_text = re.sub(r'<[^>]+>', ' ', item_data)
            nav_context = f"{section_header}\n{item_text}"
        else:
            nav_context = f"{section_header}\n(No additional content)"

        # Call the LLM with the navigation context
        questions = generate_llm_suggested_questions("", nav_context)
        suggestions_per_item[unique_id] = questions

    assistant.saved_suggestions = suggestions_per_item
    try:
        # Save the generated suggestions to the assistant model
        assistant.save(update_fields=['saved_suggestions'])
    except Exception as e:
        print(f"Error saving per-item suggestions for Assistant ID {assistant.id}: {e}")
        traceback.print_exc()  # Print full traceback for save errors

    return JsonResponse({'suggestions': suggestions_per_item})

# --- End Analyze & Suggest View ---

# --- TinyMCE Image Upload View ---
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.views.decorators.csrf import csrf_exempt
import os
import uuid
from django.utils.text import slugify

@csrf_exempt  # Add csrf_exempt to allow uploads from TinyMCE
def tinymce_image_upload(request):
    """
    Handle image uploads from TinyMCE editor.
    This view is specifically designed to work with TinyMCE's image upload functionality.
    """
    import sys
    import traceback
    import inspect
    import django
    from django.http import HttpResponse, HttpResponseRedirect

    # Set up super detailed debugging
    debug_info = []
    def debug(msg):
        print(f"DEBUG TINYMCE: {msg}")
        debug_info.append(msg)

    # If this is a GET request, return a simple response
    if request.method == 'GET':
        debug(f"Received GET request instead of POST")
        return JsonResponse({
            'error': 'This endpoint only accepts POST requests for image uploads',
            'debug_info': debug_info
        }, status=400)


    debug(f"Request received: {request.method}")
    debug(f"Python version: {sys.version}")
    debug(f"Django version: {django.get_version()}")

    # Check CSRF token
    csrf_token = request.META.get('HTTP_X_CSRFTOKEN', None)
    debug(f"CSRF Token in request: {csrf_token}")

    # Check request headers
    debug("Request Headers:")
    for key, value in request.headers.items():
        debug(f"  {key}: {value}")

    # Check media settings
    debug(f"MEDIA_ROOT: {settings.MEDIA_ROOT}")
    debug(f"MEDIA_URL: {settings.MEDIA_URL}")

    # Check if media directory exists and is writable
    if not os.path.exists(settings.MEDIA_ROOT):
        debug(f"MEDIA_ROOT does not exist: {settings.MEDIA_ROOT}")
        try:
            os.makedirs(settings.MEDIA_ROOT, exist_ok=True)
            debug(f"Created MEDIA_ROOT directory")
        except Exception as e:
            debug(f"Failed to create MEDIA_ROOT: {str(e)}")
    else:
        debug(f"MEDIA_ROOT exists")
        # Check if it's writable
        try:
            test_file = os.path.join(settings.MEDIA_ROOT, 'test_write.txt')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            debug(f"MEDIA_ROOT is writable")
        except Exception as e:
            debug(f"MEDIA_ROOT is not writable: {str(e)}")

    if request.method == 'POST':
        debug("Processing POST request")
        debug(f"Content-Type: {request.content_type}")
        debug(f"Content-Length: {request.META.get('CONTENT_LENGTH', 'unknown')}")

        # Detailed POST data inspection
        debug(f"POST data keys: {list(request.POST.keys())}")
        for key in request.POST:
            debug(f"POST[{key}] = {request.POST[key][:100]}...")  # Show first 100 chars

        # Detailed FILES inspection
        debug(f"FILES data keys: {list(request.FILES.keys())}")
        for key in request.FILES:
            file_obj = request.FILES[key]
            debug(f"FILES[{key}] = {file_obj.name}, size: {file_obj.size}, type: {file_obj.content_type}")

        # Check for file in both 'file' and standard Django file upload
        image = None
        if 'file' in request.FILES:
            image = request.FILES['file']
            debug(f"Found file in 'file' key")
        else:
            # Try other common file field names
            for key in request.FILES:
                image = request.FILES[key]
                debug(f"Found file in key: {key}")
                break

        if not image:
            debug("No file found in request.FILES")
            # Return detailed debug info in the response
            debug_response = {
                'error': 'No file uploaded',
                'debug_info': debug_info
            }
            return JsonResponse(debug_response, status=400)

        debug(f"Found file {image.name}, size: {image.size}, type: {image.content_type}")

        # Ensure the file is an image
        if not image.content_type.startswith('image/'):
            debug(f"File is not an image. Content type: {image.content_type}")
            debug_response = {
                'error': 'File is not an image',
                'content_type': image.content_type,
                'debug_info': debug_info
            }
            return JsonResponse(debug_response, status=400)

        try:
            # Create a unique filename to prevent overwriting
            filename = image.name
            base_name, ext = os.path.splitext(filename)
            safe_name = f"{slugify(base_name)}_{uuid.uuid4().hex[:8]}{ext.lower()}"
            debug(f"Generated safe filename: {safe_name}")

            # Ensure the upload directory exists
            upload_dir = os.path.join(settings.MEDIA_ROOT, 'tinymce_uploads')
            debug(f"Upload directory: {upload_dir}")

            if not os.path.exists(upload_dir):
                try:
                    os.makedirs(upload_dir, exist_ok=True)
                    debug(f"Created directory: {upload_dir}")
                except Exception as e:
                    debug(f"Failed to create upload directory: {str(e)}")
                    debug(f"Exception type: {type(e).__name__}")
                    debug(f"Exception traceback: {traceback.format_exc()}")

            # Check if upload directory is writable
            try:
                test_file = os.path.join(upload_dir, 'test_write.txt')
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                debug(f"Upload directory is writable")
            except Exception as e:
                debug(f"Upload directory is not writable: {str(e)}")
                debug(f"Exception traceback: {traceback.format_exc()}")

            # Create the full file path
            file_path = os.path.join(upload_dir, safe_name)
            debug(f"Full file path: {file_path}")

            # Try multiple methods to save the file
            success = False
            error_messages = []

            # Method 1: Save using Django's default_storage
            try:
                debug("Trying Method 1: Django's default_storage")
                from django.core.files.storage import default_storage
                from django.core.files.base import ContentFile

                # Reset file pointer
                image.seek(0)

                # Get file content
                file_content = image.read()
                debug(f"Read {len(file_content)} bytes from file")

                # Save file
                path = default_storage.save(f'tinymce_uploads/{safe_name}', ContentFile(file_content))
                debug(f"File saved to path: {path}")

                # Get URL
                image_url = default_storage.url(path)
                debug(f"Generated URL: {image_url}")

                success = True
                debug("Method 1 succeeded")
            except Exception as e1:
                error_message = f"Method 1 failed: {str(e1)}"
                debug(error_message)
                debug(f"Exception type: {type(e1).__name__}")
                debug(f"Exception traceback: {traceback.format_exc()}")
                error_messages.append(error_message)

                # Method 2: Save directly to filesystem
                try:
                    debug("Trying Method 2: Direct filesystem write")
                    # Reset file pointer
                    image.seek(0)

                    # Write file
                    with open(file_path, 'wb+') as destination:
                        bytes_written = 0
                        for chunk in image.chunks():
                            destination.write(chunk)
                            bytes_written += len(chunk)
                        debug(f"Wrote {bytes_written} bytes to file")

                    # Generate URL
                    image_url = f"{settings.MEDIA_URL}tinymce_uploads/{safe_name}"
                    debug(f"Generated URL: {image_url}")

                    success = True
                    debug("Method 2 succeeded")
                except Exception as e2:
                    error_message = f"Method 2 failed: {str(e2)}"
                    debug(error_message)
                    debug(f"Exception type: {type(e2).__name__}")
                    debug(f"Exception traceback: {traceback.format_exc()}")
                    error_messages.append(error_message)

                    # Method 3: Last resort - try to use FileSystemStorage
                    try:
                        debug("Trying Method 3: FileSystemStorage")
                        from django.core.files.storage import FileSystemStorage

                        # Reset file pointer
                        image.seek(0)

                        # Create storage
                        fs = FileSystemStorage(location=upload_dir)
                        debug(f"Created FileSystemStorage with location: {upload_dir}")

                        # Save file
                        filename = fs.save(safe_name, image)
                        debug(f"File saved with name: {filename}")

                        # Generate URL
                        image_url = fs.url(filename)
                        debug(f"Generated URL: {image_url}")

                        success = True
                        debug("Method 3 succeeded")
                    except Exception as e3:
                        error_message = f"Method 3 failed: {str(e3)}"
                        debug(error_message)
                        debug(f"Exception type: {type(e3).__name__}")
                        debug(f"Exception traceback: {traceback.format_exc()}")
                        error_messages.append(error_message)

            # Verify the file was saved by checking if it exists
            if success:
                full_path = os.path.join(settings.MEDIA_ROOT, 'tinymce_uploads', safe_name)
                if os.path.exists(full_path):
                    debug(f"File verified at: {full_path}")
                    file_size = os.path.getsize(full_path)
                    debug(f"File size on disk: {file_size} bytes")
                else:
                    debug(f"Warning: File not found at expected path: {full_path}")
                    # Try to find where the file might have been saved
                    debug("Searching for file in MEDIA_ROOT...")
                    for root, dirs, files in os.walk(settings.MEDIA_ROOT):
                        if safe_name in files:
                            debug(f"Found file at: {os.path.join(root, safe_name)}")

                # Return the URL in the format TinyMCE expects
                debug(f"Returning success response with URL: {image_url}")
                return JsonResponse({
                    'location': image_url,
                    'debug_info': debug_info
                })
            else:
                # All methods failed
                debug("All file saving methods failed")
                error_response = {
                    'error': 'Failed to save file using any method',
                    'details': error_messages,
                    'debug_info': debug_info
                }
                return JsonResponse(error_response, status=500)

        except Exception as e:
            debug(f"Unexpected error: {str(e)}")
            debug(f"Exception type: {type(e).__name__}")
            debug(f"Exception traceback: {traceback.format_exc()}")

            error_response = {
                'error': f'Error uploading image: {str(e)}',
                'exception_type': type(e).__name__,
                'debug_info': debug_info
            }
            return JsonResponse(error_response, status=500)

    debug(f"Invalid request method: {request.method}")
    return JsonResponse({
        'error': 'Invalid request method, only POST is supported',
        'method': request.method,
        'debug_info': debug_info
    }, status=400)

def get_content_by_section_id(request, company_id, assistant_id, section_id):
    """Get content for a specific section ID from an assistant's website_data."""
    # Get the assistant
    assistant = get_object_or_404(Assistant, id=assistant_id)

    # Check if the assistant is public or if the user has access
    if not assistant.is_public and not can_access_assistant(request.user, assistant):
        return JsonResponse({'error': 'Access denied'}, status=403)

    # Get the website data
    website_data = assistant.website_data or {}

    # Find the content for the section ID
    content = None

    # Check if there are navigation items in the website data
    nav_items = website_data.get('navigation_items', [])

    # Find the navigation item with the matching unique_id
    nav_item = None
    for item in nav_items:
        if item.get('unique_id') == section_id:
            nav_item = item
            break

    # If we found the navigation item, get its content
    if nav_item:
        item_id = nav_item.get('id')
        if item_id:
            content_key = f'item_{item_id}'
            content = website_data.get(content_key, '')

    # If we didn't find content, check if there's a direct match in website_data
    if not content:
        content = website_data.get(section_id, '')

    # Return the content
    return JsonResponse({
        'content': content,
        'section_id': section_id
    })