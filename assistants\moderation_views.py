from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.utils import timezone
from django.db.models import Count, Avg, Sum, Q
from django.http import JsonResponse
from django.urls import reverse
from django.views.decorators.http import require_POST
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import get_user_model

from accounts.models import Company, ActivityLog
from .models import (
    Assistant, CommunityContext, FlaggedQuestion,
    UserReputation, ReportedContent, ModeratorAction, UserBan
)

User = get_user_model()

def is_moderator(user):
    """Check if user has moderation permissions."""
    # This is a placeholder - implement proper permission checks
    return user.is_staff or user.is_superuser

@login_required
@user_passes_test(is_moderator)
def moderation_dashboard(request, company_id, assistant_id):
    """Main moderation dashboard view."""
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )

    # Get statistics for the dashboard
    total_users = UserReputation.objects.filter(
        user__assistant_interactions__assistant=assistant
    ).distinct().count()

    total_contexts = CommunityContext.objects.filter(
        assistant=assistant
    ).count()

    pending_reports = ReportedContent.objects.filter(
        assistant=assistant,
        status='pending'
    ).count()

    recent_actions = ModeratorAction.objects.filter(
        reported_content__assistant=assistant
    ).order_by('-created_at')[:10]

    active_bans = UserBan.objects.filter(
        assistant=assistant
    ).filter(
        Q(is_permanent=True) | Q(end_date__gt=timezone.now())
    ).count()

    # Get top contributors
    top_contributors = UserReputation.objects.filter(
        user__contributed_contexts__assistant=assistant
    ).order_by('-score')[:10]

    # Get recent activity
    recent_activity = ActivityLog.objects.filter(
        company=company
    ).order_by('-created_at')[:20]

    context = {
        'company': company,
        'assistant': assistant,
        'total_users': total_users,
        'total_contexts': total_contexts,
        'pending_reports': pending_reports,
        'recent_actions': recent_actions,
        'active_bans': active_bans,
        'top_contributors': top_contributors,
        'recent_activity': recent_activity,
    }

    return render(request, 'assistants/moderation/dashboard.html', context)

@login_required
@user_passes_test(is_moderator)
def user_management(request, company_id, assistant_id):
    """User management view."""
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )

    # Get all users with reputation
    users = UserReputation.objects.all().order_by('-score')

    # Filter by search query if provided
    search_query = request.GET.get('q')
    if search_query:
        users = users.filter(
            Q(user__username__icontains=search_query) |
            Q(user__email__icontains=search_query) |
            Q(user__first_name__icontains=search_query) |
            Q(user__last_name__icontains=search_query)
        )

    # Filter by level if provided
    level_filter = request.GET.get('level')
    if level_filter:
        users = users.filter(level=level_filter)

    context = {
        'company': company,
        'assistant': assistant,
        'users': users,
        'search_query': search_query,
        'level_filter': level_filter,
    }

    return render(request, 'assistants/moderation/users.html', context)

@login_required
@user_passes_test(is_moderator)
def content_moderation(request, company_id, assistant_id):
    """Content moderation view."""
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )

    # Get all reported content
    reports = ReportedContent.objects.filter(
        assistant=assistant
    ).order_by('-created_at')

    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        reports = reports.filter(status=status_filter)

    # Filter by type if provided
    type_filter = request.GET.get('type')
    if type_filter:
        reports = reports.filter(report_type=type_filter)

    context = {
        'company': company,
        'assistant': assistant,
        'reports': reports,
        'status_filter': status_filter,
        'type_filter': type_filter,
    }

    return render(request, 'assistants/moderation/content.html', context)

@login_required
@user_passes_test(is_moderator)
def statistics(request, company_id, assistant_id):
    """Statistics and analytics view."""
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )

    # Get time period for statistics
    period = request.GET.get('period', 'month')
    if period == 'week':
        start_date = timezone.now() - timezone.timedelta(days=7)
    elif period == 'month':
        start_date = timezone.now() - timezone.timedelta(days=30)
    elif period == 'year':
        start_date = timezone.now() - timezone.timedelta(days=365)
    else:
        start_date = None

    # Get statistics based on time period
    if start_date:
        new_contexts = CommunityContext.objects.filter(
            assistant=assistant,
            created_at__gte=start_date
        ).count()

        new_users = UserReputation.objects.filter(
            user__assistant_interactions__assistant=assistant,
            user__date_joined__gte=start_date
        ).distinct().count()

        new_reports = ReportedContent.objects.filter(
            assistant=assistant,
            created_at__gte=start_date
        ).count()

        moderation_actions = ModeratorAction.objects.filter(
            reported_content__assistant=assistant,
            created_at__gte=start_date
        ).count()
    else:
        new_contexts = CommunityContext.objects.filter(assistant=assistant).count()
        new_users = UserReputation.objects.filter(
            user__assistant_interactions__assistant=assistant
        ).distinct().count()
        new_reports = ReportedContent.objects.filter(assistant=assistant).count()
        moderation_actions = ModeratorAction.objects.filter(
            reported_content__assistant=assistant
        ).count()

    context = {
        'company': company,
        'assistant': assistant,
        'period': period,
        'new_contexts': new_contexts,
        'new_users': new_users,
        'new_reports': new_reports,
        'moderation_actions': moderation_actions,
    }

    return render(request, 'assistants/moderation/statistics.html', context)

@login_required
@user_passes_test(is_moderator)
@require_POST
def take_action(request, company_id, assistant_id):
    """Handle moderation actions."""
    company = get_object_or_404(Company, pk=company_id)
    assistant = get_object_or_404(
        Assistant,
        pk=assistant_id,
        company=company,
        assistant_type=Assistant.TYPE_COMMUNITY
    )

    action_type = request.POST.get('action_type')
    target_user_id = request.POST.get('target_user_id')
    report_id = request.POST.get('report_id')
    notes = request.POST.get('notes', '')
    reputation_adjustment = int(request.POST.get('reputation_adjustment', 0))

    # Get target user
    target_user = get_object_or_404(User, pk=target_user_id) if target_user_id else None

    # Get report if provided
    report = get_object_or_404(ReportedContent, pk=report_id) if report_id else None

    # Create moderator action
    action = ModeratorAction.objects.create(
        moderator=request.user,
        action_type=action_type,
        target_user=target_user,
        reported_content=report,
        notes=notes,
        reputation_adjustment=reputation_adjustment
    )

    # Handle different action types
    if action_type == 'approve':
        if report:
            report.status = 'approved'
            report.save()

    elif action_type == 'reject':
        if report:
            report.status = 'rejected'
            report.save()

    elif action_type == 'remove':
        if report:
            report.status = 'removed'
            report.save()

            # Remove the content
            content_object = report.content_object
            if content_object:
                if isinstance(content_object, CommunityContext):
                    content_object.is_active = False
                    content_object.save()

    elif action_type == 'warn':
        if target_user:
            # Send warning notification to user
            # This would be implemented based on your notification system
            pass

    elif action_type == 'ban':
        if target_user:
            ban_duration = int(request.POST.get('ban_duration', 0))
            is_permanent = ban_duration == 0

            end_date = None
            if not is_permanent:
                end_date = timezone.now() + timezone.timedelta(days=ban_duration)

            UserBan.objects.create(
                user=target_user,
                assistant=assistant,
                reason=notes,
                is_permanent=is_permanent,
                end_date=end_date,
                banned_by=request.user
            )

            # Check if we should remove all content from this user
            remove_content = request.POST.get('remove_content') == '1'
            if remove_content:
                # Remove all contexts created by this user
                CommunityContext.objects.filter(
                    assistant=assistant,
                    created_by=target_user
                ).update(is_active=False)

                # Remove all comments by this user
                from django.contrib.contenttypes.models import ContentType
                from .models import Comment
                comment_type = ContentType.objects.get_for_model(Comment)

                # Get all comments by this user
                user_comments = Comment.objects.filter(user=target_user)

                # Mark all reports about this user's comments as 'removed'
                comment_reports = []
                for comment in user_comments:
                    # Find reports for this comment
                    reports = ReportedContent.objects.filter(
                        assistant=assistant,
                        content_type=comment_type,
                        object_id=comment.id
                    )
                    comment_reports.extend(reports)

                for report in comment_reports:
                    report.status = 'removed'
                    report.save()

                    # Get the comment and mark it as removed
                    comment = report.content_object
                    if comment:
                        # You might want to add a 'is_active' field to your Comment model
                        # For now, we'll just set the text to indicate it was removed
                        comment.text = "[This comment was removed by a moderator]"
                        comment.save()

    elif action_type == 'unban':
        if target_user:
            # Find active bans and mark them as ended
            active_bans = UserBan.objects.filter(
                user=target_user,
                assistant=assistant
            ).filter(
                Q(is_permanent=True) | Q(end_date__gt=timezone.now())
            )

            for ban in active_bans:
                ban.end_date = timezone.now()
                ban.is_permanent = False
                ban.save()

    elif action_type == 'adjust_reputation':
        if target_user and reputation_adjustment:
            # Get or create user reputation
            reputation, created = UserReputation.objects.get_or_create(user=target_user)

            # Adjust reputation score
            reputation.score += reputation_adjustment
            reputation.save()

            # Update level based on new score
            reputation.update_level()

    messages.success(request, f"Action '{action.get_action_type_display()}' taken successfully.")

    # Redirect back to the appropriate page
    if 'next' in request.POST:
        return redirect(request.POST.get('next'))
    return redirect('assistants:moderation_dashboard', company_id=company_id, assistant_id=assistant_id)
