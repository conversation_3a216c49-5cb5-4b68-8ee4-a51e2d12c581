{% extends "assistants/base.html" %}
{% load crispy_forms_tags %}

{% block title %}Train Assistant{% endblock %}

{% block page_header %}
    <h1 class="h2">Train {{ assistant.name }}</h1>
{% endblock %}

{% block main_content %}
<div class="card">
    <div class="card-body">
        <p class="card-text">Upload a file (e.g., PDF, TXT, DOCX) containing knowledge or examples to train the assistant.</p>
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            {{ form|crispy }}
            <div class="mt-3">
                <button type="submit" class="btn btn-primary">Upload and Train</button>
                <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
        {% if assistant.last_trained %}
            <p class="mt-3 text-muted small">Last trained: {{ assistant.last_trained|timesince }} ago</p>
        {% endif %}
    </div>
</div>
{% endblock %}
