"""
Dark Mode test script to test dark mode functionality.
"""

import os
import django
import uuid
import json
import time

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.test import Client
from django.urls import reverse
from django.contrib.auth.models import User

def test_dark_mode_default():
    """Test that dark mode is enabled by default."""
    print("Testing dark mode default...")

    # Create client
    client = Client()

    # Test homepage
    response = client.get('/')
    assert response.status_code == 200, "Homepage should load"

    # Check if dark mode is enabled by default
    content = response.content.decode('utf-8')
    assert 'data-theme="dark"' in content, "Dark mode should be enabled by default"

    print("Dark mode default test passed!")
    return True

def test_dark_mode_css_classes():
    """Test that dark mode CSS classes are applied correctly."""
    print("Testing dark mode CSS classes...")

    # Create client
    client = Client()

    # Test homepage
    response = client.get('/')
    assert response.status_code == 200, "Homepage should load"

    # Check for dark mode specific CSS classes
    content = response.content.decode('utf-8')

    # Check for dark mode theme attribute
    assert 'data-theme="dark"' in content, "Dark mode theme attribute should be applied"

    # Check for dark mode text
    assert 'text-light' in content or 'text-white' in content or 'text-gray-300' in content, "Dark mode text class should be applied"

    print("Dark mode CSS classes test passed!")
    return True

def test_dark_mode_consistency():
    """Test that dark mode is consistent across different pages."""
    print("Testing dark mode consistency...")

    # Create client
    client = Client()

    # Test various pages
    pages = [
        '/',  # Homepage
        '/accounts/login/',  # Login page
        '/directory/assistants/',  # Assistant directory
        '/directory/companies/',  # Company directory
    ]

    for page in pages:
        response = client.get(page)
        assert response.status_code == 200, f"{page} should load"

        # Check if dark mode is enabled
        content = response.content.decode('utf-8')
        assert 'data-theme="dark"' in content, f"Dark mode should be enabled on {page}"

    print("Dark mode consistency test passed!")
    return True

def test_dark_mode_chat_bubbles():
    """Test that chat bubbles have correct dark mode styling."""
    print("Testing dark mode chat bubbles...")

    # Create a test user
    username = f"bubble_test_user_{uuid.uuid4().hex[:8]}"
    password = "BubbleTest123!"
    user = User.objects.create_user(username=username, password=password)

    # Create client and login
    client = Client()
    client.login(username=username, password=password)

    # Find an existing assistant to test with
    from assistants.models import Assistant
    assistant = Assistant.objects.filter(is_public=True, is_active=True).first()

    if assistant:
        # Test assistant chat page
        response = client.get(reverse('assistants:assistant_chat', kwargs={'slug': assistant.slug}))
        assert response.status_code == 200, "Assistant chat page should load"

        # Check for dark mode chat bubble styling
        content = response.content.decode('utf-8')

        # Check for chat container with dark mode styling
        assert 'chat-container' in content, "Chat container should be present"

        # Check for user message bubble with dark mode styling
        assert 'user-message' in content or 'message-user' in content, "User message bubble should be present"

        # Check for assistant message bubble with dark mode styling
        assert 'assistant-message' in content or 'message-assistant' in content, "Assistant message bubble should be present"
    else:
        print("No active public assistant found for testing chat bubbles")

    print("Dark mode chat bubbles test passed!")
    return True

def test_dark_mode_buttons():
    """Test that buttons have correct dark mode styling."""
    print("Testing dark mode buttons...")

    # Create client
    client = Client()

    # Test homepage
    response = client.get('/')
    assert response.status_code == 200, "Homepage should load"

    # Check for dark mode button styling
    content = response.content.decode('utf-8')

    # Check for primary buttons
    assert 'btn-primary' in content, "Primary buttons should be present"

    # Check for secondary buttons
    assert 'btn-secondary' in content or 'btn-outline-secondary' in content, "Secondary buttons should be present"

    # Check for 3D glass-look styling
    assert 'glass-btn' in content or 'glass-button' in content or 'glass-effect' in content or 'glass-bg' in content, "3D glass-look styling should be present"

    print("Dark mode buttons test passed!")
    return True

def test_dark_mode_forms():
    """Test that forms have correct dark mode styling."""
    print("Testing dark mode forms...")

    # Create client
    client = Client()

    # Test login page which has a form
    response = client.get('/accounts/login/')
    assert response.status_code == 200, "Login page should load"

    # Check for dark mode form styling
    content = response.content.decode('utf-8')

    # Check for form elements
    assert 'form-control' in content, "Form controls should be present"

    # Check for dark mode theme attribute
    assert 'data-theme="dark"' in content, "Dark mode theme attribute should be present"

    print("Dark mode forms test passed!")
    return True

def test_dark_mode_cards():
    """Test that cards have correct dark mode styling."""
    print("Testing dark mode cards...")

    # Create client
    client = Client()

    # Test assistant directory which has cards
    response = client.get('/directory/assistants/')
    assert response.status_code == 200, "Assistant directory should load"

    # Check for dark mode card styling
    content = response.content.decode('utf-8')

    # Check for card elements
    assert 'card' in content, "Cards should be present"

    # Check for dark mode theme attribute
    assert 'data-theme="dark"' in content, "Dark mode theme attribute should be present"

    print("Dark mode cards test passed!")
    return True

def run_all_dark_mode_tests():
    """Run all dark mode tests."""
    print("Running all dark mode tests...")

    results = []
    results.append(test_dark_mode_default())
    results.append(test_dark_mode_css_classes())
    results.append(test_dark_mode_consistency())
    results.append(test_dark_mode_chat_bubbles())
    results.append(test_dark_mode_buttons())
    results.append(test_dark_mode_forms())
    results.append(test_dark_mode_cards())

    # Return True only if all tests passed
    return all(results)

if __name__ == "__main__":
    run_all_dark_mode_tests()
