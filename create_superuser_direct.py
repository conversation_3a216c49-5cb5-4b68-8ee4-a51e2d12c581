import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from django.contrib.auth.hashers import make_password
from datetime import datetime, timezone

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

print("Starting superuser creation script...")
print(f"Django version: {django.get_version()}")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

# Superuser details
username = 'admin'
email = '<EMAIL>'
password = 'admin123'  # You should change this to a secure password
hashed_password = make_password(password)
now = datetime.now(timezone.utc)

# SQL to insert a superuser
superuser_sql = """
INSERT INTO auth_user (
    username,
    first_name,
    last_name,
    email,
    password,
    is_superuser,
    is_staff,
    is_active,
    date_joined,
    last_login
)
VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
ON CONFLICT (username) DO NOTHING
RETURNING id;
"""

# Connect to the database
try:
    print("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(**db_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()

    # Create the superuser
    print(f"Creating superuser '{username}'...")
    cursor.execute(
        superuser_sql,
        (
            username,  # username
            '',  # first_name
            '',  # last_name
            email,  # email
            hashed_password,  # password
            True,  # is_superuser
            True,  # is_staff
            True,  # is_active
            now,  # date_joined
            now,  # last_login
        )
    )

    result = cursor.fetchone()
    if result:
        print(f"Superuser created with ID: {result[0]}")
    else:
        print("Superuser already exists or could not be created.")

    # Close the connection
    cursor.close()
    conn.close()
    print("Database connection closed.")

    print("\nSuperuser created successfully!")
    print(f"Username: {username}")
    print(f"Password: {password}")
    print(f"Email: {email}")
    print("\nYou can now log in to the admin interface at http://127.0.0.1:8000/admin/")

except Exception as e:
    print(f"Error: {e}")
