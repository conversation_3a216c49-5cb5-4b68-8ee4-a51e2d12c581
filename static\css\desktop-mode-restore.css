/**
 * Desktop Mode Restore CSS
 * Restores desktop mode to original styling while keeping mobile mode changes
 */

/* Desktop-only styles (screens larger than 768px) */
@media (min-width: 769px) {
  /* Reset message content to original styling for desktop */
  .message-content,
  .message-content.tinymce-content,
  .tinymce-content.message-content,
  span.message-content,
  div.message-content,
  span.message-content.tinymce-content,
  div.message-content.tinymce-content,
  span.tinymce-content.message-content,
  div.tinymce-content.message-content {
    max-width: fit-content !important;
    width: auto !important;
    min-width: auto !important;
    border-radius: 1.25rem !important;
    padding: 0.8rem 1.2rem !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
    line-height: 1.5 !important;
    font-size: 1rem !important;
    display: block !important;
    margin: 0 !important;
  }

  /* Reset message container to original styling for desktop */
  .message {
    display: flex !important;
    width: 100% !important;
    margin: 0.75rem 0 !important;
    padding: 0 !important;
    transition: all 0.2s ease !important;
  }

  /* Reset user message alignment for desktop */
  .user-message {
    justify-content: flex-end !important;
    width: auto !important;
    max-width: 100% !important;
  }

  /* Reset assistant message alignment for desktop */
  .assistant-message {
    justify-content: flex-start !important;
    width: auto !important;
    max-width: 100% !important;
  }

  /* Reset user message content for desktop */
  .user-message .message-content {
    background: #3b7dd8 !important;
    color: #ffffff !important;
    text-align: left !important;
    border-radius: 1.25rem !important;
    border-bottom-right-radius: 0.3rem !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
    font-weight: 450 !important;
    border: none !important;
    transform: translateZ(0) !important;
    transition: all 0.2s ease !important;
    padding: 1rem 1.25rem !important;
    line-height: 1.6 !important;
    font-size: 0.95rem !important;
    width: auto !important;
    max-width: fit-content !important;
    min-width: auto !important;
  }

  /* Reset assistant message content for desktop */
  .assistant-message .message-content {
    background: #ffffff !important;
    color: #333333 !important;
    text-align: left !important;
    border-radius: 1.25rem !important;
    border-bottom-left-radius: 0.3rem !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
    font-weight: 450 !important;
    border: none !important;
    transform: translateZ(0) !important;
    transition: all 0.2s ease !important;
    padding: 1rem 1.25rem !important;
    line-height: 1.6 !important;
    font-size: 0.95rem !important;
    width: auto !important;
    max-width: fit-content !important;
    min-width: auto !important;
  }

  /* Ensure TinyMCE content is properly displayed in desktop mode */
  .message-content.tinymce-content,
  .tinymce-content.message-content {
    width: auto !important;
    max-width: fit-content !important;
    min-width: auto !important;
  }

  /* Allow paragraphs and divs to have their original styling in desktop mode */
  .message-content p,
  .message-content div,
  .message-content span:not(.message-content),
  .tinymce-content p,
  .tinymce-content div,
  .tinymce-content span:not(.tinymce-content) {
    width: auto !important;
    max-width: 100% !important;
    min-width: auto !important;
    margin: initial !important;
    padding: initial !important;
  }

  /* Allow tables to have their original styling in desktop mode */
  .message-content table,
  .tinymce-content table {
    width: 100% !important;
    min-width: auto !important;
    max-width: 100% !important;
    table-layout: auto !important;
    margin: 0.75rem 0 !important;
  }

  /* Allow images to have their original styling in desktop mode */
  .message-content img,
  .tinymce-content img {
    max-width: 100% !important;
    height: auto !important;
    width: auto !important;
    display: block !important;
    margin: 0.5rem auto !important;
  }
}
