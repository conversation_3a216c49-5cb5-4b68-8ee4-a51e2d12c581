import os
import json
import time
import openai
import re
from openai import APIConnectionError
import traceback
from typing import Dict, List, Optional, Any, Union
from django.conf import settings
from django.utils.text import slugify
from django.utils import timezone
from anthropic import Anthropic

# Configure API clients
openai_client = openai.OpenAI(
    api_key=settings.OPENAI_API_KEY,
    base_url="https://api.openai.com/v1"  # Always use the official OpenAI endpoint for OpenAI models
)

# Configure Groq client for Llama models exactly as in the documentation
groq_client = openai.OpenAI(
    base_url="https://api.groq.com/openai/v1",
    api_key=settings.GROQ_API_KEY
)

anthropic_client = Anthropic(api_key=settings.ANTHROPIC_API_KEY)

# Configure Gemini client if API key is available
gemini_client = None
if settings.GEMINI_API_KEY:
    try:
        import google.generativeai as genai
        genai.configure(api_key=settings.GEMINI_API_KEY)
        gemini_client = genai
        print("Gemini API client configured successfully")
    except ImportError:
        print("google-generativeai package not installed. Gemini models will not be available.")
        print("Install with: pip install google-generativeai")
    except Exception as e:
        print(f"Error configuring Gemini API client: {e}")

def get_token_count(text: str, model: str = "gpt-3.5-turbo") -> int:
    """Estimate token count for a given text string."""
    # Simple estimation: 1 token ~= 4 chars for English text
    return len(text) // 4

def get_navigation_item_content(assistant, item_unique_id=None, item_id=None, item_label=None):
    """
    Try to get content for a navigation item from the database.
    This is a fallback method when the content is not found in website_data.
    """
    try:
        from websites.models import NavigationItem

        # Try to find the navigation item in the database
        query = {}
        if item_unique_id:
            query['unique_id'] = item_unique_id
        elif item_id:
            query['id'] = item_id
        elif item_label:
            query['label__iexact'] = item_label

        if not query:
            return None

        # Add website filter if assistant has a website
        if hasattr(assistant, 'website') and assistant.website:
            query['website'] = assistant.website

        # Try to find the navigation item
        nav_item = NavigationItem.objects.filter(**query).first()

        if nav_item:
            # Return content based on section type
            if nav_item.section_type == 'text':
                return {'content': nav_item.content or "", 'main_image': nav_item.main_image, 'gallery': nav_item.gallery}
            elif nav_item.section_type in ['products', 'services', 'team', 'gallery']:
                # For these types, we'd need to get entries from the related model
                # This is a simplified version - in a real app, you'd query the related model
                return {'content': f"This is a {nav_item.section_type} section. Specific entries would be retrieved here."}
            else:
                return {'content': nav_item.content or ""}
        return None
    except Exception:
        traceback.print_exc()
        return None

def generate_assistant_response(assistant, user_input: str, user,
                              history: Optional[list] = None,
                              current_context_id: Optional[str] = None) -> Dict[str, Any]:
    """Generate a response from an AI assistant, considering conversation history."""
    start_time = time.time()
    history = history or [] # Ensure history is a list

    # --- Construct detailed system prompt like Gradio script ---
    website_data = assistant.website_data or {}
    # Get navigation items from website_data, default to empty list
    navigation_items = website_data.get('navigation_items', [])
    extra_context = assistant.extra_context or ""

    # Ensure company info is in website_data
    try:
        # Always try to get fresh company info from the database
        company_info = {}

        # Check if company has info attribute
        if hasattr(assistant.company, 'info') and assistant.company.info:
            # Get company info safely with error handling
            try:
                # Try direct attribute access first
                mission = assistant.company.info.mission
                founded = assistant.company.info.founded
                description = assistant.company.info.description
                industry = assistant.company.info.industry
                size = assistant.company.info.size

                company_info = {
                    "mission": mission or "",
                    "founded": founded or "",
                    "description": description or "",
                    "industry": industry or "",
                    "size": size or ""
                }
                print(f"[LLM DEBUG] Successfully retrieved company info from database")
            except Exception as e:
                print(f"[LLM DEBUG] Error retrieving company info fields: {str(e)}")
                # Try a more basic approach with getattr
                try:
                    company_info = {
                        "mission": getattr(assistant.company.info, 'mission', "") or "",
                        "founded": getattr(assistant.company.info, 'founded', "") or "",
                        "description": getattr(assistant.company.info, 'description', "") or "",
                        "industry": getattr(assistant.company.info, 'industry', "") or "",
                        "size": getattr(assistant.company.info, 'size', "") or ""
                    }
                    print(f"[LLM DEBUG] Retrieved basic company info using getattr")
                except Exception as e2:
                    print(f"[LLM DEBUG] Error retrieving basic company info: {str(e2)}")
        else:
            print(f"[LLM DEBUG] Company does not have info attribute or it is None")

        # Update website_data with company_info
        if company_info:
            if "company_info" in website_data:
                # Update existing company_info with any new values
                for key, value in company_info.items():
                    if value:  # Only update if the value is not empty
                        website_data["company_info"][key] = value
            else:
                # Create new company_info
                website_data["company_info"] = company_info
        elif "company_info" not in website_data:
            # Ensure we have at least an empty dict
            website_data["company_info"] = {}
    except Exception as e:
        print(f"[LLM DEBUG] Error in company info retrieval: {str(e)}")
        # Ensure we have at least an empty dict
        if "company_info" not in website_data:
            website_data["company_info"] = {}

    # --- Construct messages for the LLM ---
    system_prompt = f"""You are {assistant.name}, an AI assistant for {assistant.company.name if hasattr(assistant, 'company') and assistant.company else 'the company'}.
Your role is to provide helpful, accurate information about the company and its offerings.

## GUIDELINES
- Be friendly, professional, and concise in your responses.
- Use the information provided below to answer questions accurately.
- If you don't know the answer to a question, say so clearly rather than making up information.
- Focus on providing factual information rather than opinions.
- Avoid using phrases like "Based on the information provided" or "According to the context".
"""

    # Add website data in a structured format
    system_context = system_prompt

    # Add Navigation Structure Summary
    if navigation_items:
        system_context += "\n\n## AVAILABLE INFORMATION\n"
        system_context += "The following information is available to help answer questions:\n\n"

        # List all navigation items in a simple format
        for item in navigation_items:
            if isinstance(item, dict) and 'label' in item and 'unique_id' in item:
                label = item.get('label', 'Unknown')
                section_type = item.get('section_type', 'general')
                system_context += f"- **{label}** ({section_type})\n"

        system_context += "\n"

        # Add Website Data Content with clear structure
        system_context += "## DETAILED CONTENT\n\n"

        # Iterate through navigation items to add their content
        for item in navigation_items:
            if isinstance(item, dict) and 'unique_id' in item and 'label' in item:
                unique_id = item['unique_id']
                label = item['label']
                item_id = item.get('id')
                section_type = item.get('section_type', 'general')

                # Try multiple ways to get content
                item_content = None

                # Method 1: Try using the unique_id directly
                item_content = website_data.get(unique_id)
                if item_content:
                    print(f"[LLM DEBUG] Found content for '{label}' using key: {unique_id}")

                # Method 2: Try using item_{id} format if we have an ID
                if not item_content and item_id:
                    data_key = f"item_{item_id}"
                    item_content = website_data.get(data_key)
                    if item_content:
                        print(f"[LLM DEBUG] Found content for '{label}' using key: {data_key}")

                # Method 3: Try using the label as a key
                if not item_content:
                    label_key = slugify(label)
                    item_content = website_data.get(label_key)
                    if item_content:
                        print(f"[LLM DEBUG] Found content for '{label}' using label key: {label_key}")

                # Method 4: Try to get content directly from the database
                if not item_content:
                    db_content = get_navigation_item_content(assistant, item_unique_id=unique_id, item_id=item_id, item_label=label)
                    if db_content:
                        item_content = db_content
                        print(f"[LLM DEBUG] Found content for '{label}' from database")
                        # Store this content in website_data for future use
                        website_data[unique_id] = db_content

                # Format and add the content to the system context with clear section headers
                if item_content:
                    # Create a clear section header
                    system_context += f"### {label}\n\n"

                    # Format the content based on its type
                    if isinstance(item_content, dict) and 'content' in item_content:
                        content = item_content['content']
                        system_context += f"{content}\n\n"
                    elif isinstance(item_content, str):
                        system_context += f"{item_content}\n\n"
                    else:
                        system_context += f"{json.dumps(item_content, indent=2)}\n\n"

                    # Add a separator for better visual distinction
                    system_context += "---\n\n"

    # Add any remaining website data that wasn't included with navigation items
    filtered_data = {k: v for k, v in website_data.items() if k != 'navigation_items' and k not in [item.get('unique_id') for item in navigation_items if isinstance(item, dict) and 'unique_id' in item]}
    if filtered_data:
        system_context += "\n\n## ADDITIONAL INFORMATION\n\n"

        # Format each piece of data with clear headers
        for key, value in filtered_data.items():
            # Create a clear section header
            system_context += f"### {key.replace('_', ' ').title()}\n\n"

            # Format the content based on its type
            if isinstance(value, dict):
                # For nested dictionaries, format each key-value pair
                for sub_key, sub_value in value.items():
                    if sub_value:  # Only include non-empty values
                        system_context += f"**{sub_key}**: {sub_value}\n"
                system_context += "\n"
            elif isinstance(value, str):
                system_context += f"{value}\n\n"
            else:
                system_context += f"{json.dumps(value, indent=2)}\n\n"

            # Add a separator for better visual distinction
            system_context += "---\n\n"

    # Add extra context if available
    if extra_context:
        system_context += f"\n\n## ADDITIONAL CONTEXT\n{extra_context}\n"

    # Construct messages array for the API call
    messages = [
        {"role": "system", "content": system_context}
    ]

    # Add conversation history
    for entry in history:
        if isinstance(entry, dict) and 'role' in entry and 'content' in entry:
            # If entry is already in the right format, add it directly
            messages.append(entry)
        elif isinstance(entry, list) and len(entry) == 2:
            # If entry is a list of [user_msg, assistant_msg]
            messages.append({"role": "user", "content": entry[0]})
            messages.append({"role": "assistant", "content": entry[1]})

    # Add the current user message
    messages.append({"role": "user", "content": user_input})

    try:
        # Determine model type and call appropriate API
        model_name = assistant.model

        # Call the appropriate LLM API based on the model
        if model_name.startswith('gpt') or model_name.startswith('llama'):
            # Both OpenAI and Llama models (via Groq) use the OpenAI client
            response = _generate_openai_response(
                messages=messages,
                model=model_name,
                temperature=assistant.temperature,
                max_tokens=assistant.max_tokens
            )
            response_content = response['content']
        elif model_name.startswith('claude'):
            response = _generate_anthropic_response(
                messages=messages,
                model=model_name,
                temperature=assistant.temperature,
                max_tokens=assistant.max_tokens
            )
            response_content = response['content']
        elif model_name.startswith('gemini'):
             if not gemini_client:
                 raise ValueError("Gemini API key not configured, cannot use Gemini models.")
             # Use the dedicated Gemini client
             response = _generate_gemini_response(
                 messages=messages,
                 model=model_name, # Pass the specific gemini model name
                 temperature=assistant.temperature,
                 max_tokens=assistant.max_tokens
             )
             response_content = response['content']
        else:
            # Fallback for unsupported models
            if extra_context:
                response_content = f"Based on the information I have, {extra_context}. I don't have additional specific details beyond this."
                print(f"[LLM DEBUG] Using fallback response for unsupported model: {model_name}")
            else:
                raise ValueError(f"Unsupported model: {model_name}")

        # Calculate metrics
        duration = time.time() - start_time

        # Ensure response_content is a string before token calculation
        response_text = response_content if isinstance(response_content, str) else json.dumps(response_content)

        # Calculate token count
        token_count = get_token_count(user_input + response_text, model_name)

        # Record interaction
        from .models import Interaction # Import locally to avoid circular dependency
        interaction_obj = Interaction.objects.create(
            assistant=assistant,
            user=user,
            prompt=user_input, # The latest user message
            response=response_text, # Save the actual response content
            duration=duration,
            token_count=token_count,
        )

        # --- Prepare Image/Gallery Data ---
        response_images = []
        response_gallery = []
        final_context_id = current_context_id # Use the passed context ID

        # Return the response
        return {
            'content': response_text,
            'images': response_images,
            'gallery': response_gallery,
            'context_id': final_context_id
        }

    except APIConnectionError as e:
        # Handle connection errors specifically (e.g., no internet)
        print(f"Connection Error generating assistant response for {assistant.name}: {e}")
        traceback.print_exc() # Print the full traceback
        return {
            'content': "Sorry, I couldn't connect to the AI service. Please check your internet connection and try again.",
            'status': 'error'
        }
    except Exception as e:
        # Log other errors for debugging, including traceback
        print(f"Error generating assistant response for {assistant.name}: {e}")
        traceback.print_exc() # Print the full traceback
        return {
            'content': "I'm sorry, I encountered an error while processing your request. Please try again later.",
            'status': 'error'
        }


def _generate_openai_response(messages: list, model: str,
                            temperature: float, max_tokens: int) -> Dict[str, str]:
    """Generate a response using OpenAI's API or Groq API for Llama models."""
    # Select the appropriate client based on the model
    if model.startswith('llama'):
        # Use Groq client for Llama models
        print(f"[LLM DEBUG] Using Groq client for model: {model}")
        client = groq_client
    else:
        # Use OpenAI client for GPT models
        print(f"[LLM DEBUG] Using OpenAI client for model: {model}")
        client = openai_client

    # Make the API call with the selected client
    response = client.chat.completions.create(
        model=model,
        messages=messages,
        temperature=temperature,
        max_tokens=max_tokens
    )

    return {
        'content': response.choices[0].message.content.strip()
    }

def _generate_anthropic_response(messages: list, model: str,
                               temperature: float, max_tokens: int) -> Dict[str, str]:
    """Generate a response using Anthropic's API."""
    # Find the system prompt
    system_prompt = ""
    for message in messages:
        if message["role"] == "system":
            system_prompt = message["content"]
            break

    # Convert messages to Anthropic format (excluding system message)
    anthropic_messages = []
    for message in messages:
        if message["role"] != "system":
            anthropic_messages.append({
                "role": message["role"],
                "content": message["content"]
            })

    response = anthropic_client.messages.create(
        model=model,
        system=system_prompt,
        messages=anthropic_messages,
        temperature=temperature,
        max_tokens=max_tokens
    )

    return {
        'content': response.content[0].text
    }

# Added function for Gemini
def _generate_gemini_response(messages: list, model: str,
                            temperature: float, max_tokens: int) -> Dict[str, str]:
    """Generate a response using the Gemini API via OpenAI client structure."""
    if not gemini_client:
        raise ValueError("Gemini API client not configured")

    # Extract system prompt
    system_prompt = ""
    for message in messages:
        if message["role"] == "system":
            system_prompt = message["content"]
            break

    # Convert messages to Gemini format
    gemini_messages = []
    for message in messages:
        if message["role"] != "system":  # Skip system message as it's handled separately
            gemini_messages.append({
                "role": "user" if message["role"] == "user" else "model",
                "parts": [{"text": message["content"]}]
            })

    # Create a Gemini model instance
    gemini_model = gemini_client.GenerativeModel(model_name=model)

    # Generate response
    response = gemini_model.generate_content(
        gemini_messages,
        generation_config={
            "temperature": temperature,
            "max_output_tokens": max_tokens,
            "system_instruction": system_prompt
        }
    )

    return {
        'content': response.text
    }

def display_images(image_paths: List[str]) -> List[Dict[str, str]]:
    """Convert image paths to a format suitable for display in the chat interface."""
    result = []
    for path in image_paths:
        if path:  # Only process non-empty paths
            result.append({
                'url': path,
                'alt': os.path.basename(path)
            })
    return result
