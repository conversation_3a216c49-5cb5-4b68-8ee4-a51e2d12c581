/**
 * Assistant Folder Functionality Tests
 * Tests for the assistant folder-related JavaScript functionality
 */

// Import test framework
const { describe, test, beforeEach, afterEach, expect } = require('./test-runner');

// Mock DOM elements and functions
const setupMockDOM = () => {
    // Create mock DOM elements
    document.body.innerHTML = `
        <div id="createFolderModal" class="modal">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Create Folder</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="createFolderForm">
                            <div class="mb-3">
                                <label for="folderName" class="form-label">Folder Name</label>
                                <input type="text" class="form-control" id="folderName" required>
                                <div id="createErrorMsg" class="invalid-feedback"></div>
                            </div>
                            <button type="submit" class="btn btn-primary">Create Folder</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div id="editFolderModal" class="modal">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Folder</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="editFolderForm">
                            <input type="hidden" id="editFolderId">
                            <div class="mb-3">
                                <label for="editFolderName" class="form-label">Folder Name</label>
                                <input type="text" class="form-control" id="editFolderName" required>
                                <div id="editErrorMsg" class="invalid-feedback"></div>
                            </div>
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div id="confirmDeleteModal" class="modal">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Confirm Delete</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to delete this folder? Assistants in this folder will be moved to "Unassigned".</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" id="confirmDeleteBtn" class="btn btn-danger">Delete</button>
                    </div>
                </div>
            </div>
        </div>
        <div id="assignFolderModal" class="modal">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Assign to Folder</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="assignFolderForm">
                            <input type="hidden" id="assignAssistantId">
                            <div class="mb-3">
                                <label for="folderSelect" class="form-label">Select Folder</label>
                                <select class="form-select" id="folderSelect">
                                    <option value="">-- Unassigned --</option>
                                    <option value="1">Test Folder 1</option>
                                    <option value="2">Test Folder 2</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">Assign</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <button class="js-trigger-create-folder-modal btn btn-sm btn-outline-secondary">
            <i class="bi bi-plus-circle"></i> Add Folder
        </button>
        <button class="edit-folder-btn" data-folder-id="1" data-folder-name="Test Folder 1">
            <i class="bi bi-pencil-square"></i> Edit
        </button>
        <button class="delete-folder-btn" data-folder-id="1" data-folder-name="Test Folder 1">
            <i class="bi bi-trash"></i> Delete
        </button>
        <button class="assign-folder-btn" data-assistant-id="123" data-assistant-name="Test Assistant">
            <i class="bi bi-folder"></i> Assign to Folder
        </button>
    `;

    // Mock Bootstrap modal
    window.bootstrap = {
        Modal: class {
            constructor(element) {
                this.element = element;
                this._instance = this;
            }
            show() {
                this.element.classList.add('show');
            }
            hide() {
                this.element.classList.remove('show');
            }
            static getInstance(element) {
                return element._instance;
            }
        }
    };

    // Mock fetch
    global.fetch = jest.fn().mockImplementation((url) => {
        if (url.includes('folder_create')) {
            return Promise.resolve({
                ok: true,
                json: () => Promise.resolve({
                    status: 'success',
                    message: 'Folder created successfully.',
                    folder: { id: 3, name: 'New Test Folder' }
                })
            });
        } else if (url.includes('folder_edit')) {
            return Promise.resolve({
                ok: true,
                json: () => Promise.resolve({
                    status: 'success',
                    message: 'Folder updated successfully.',
                    folder: { id: 1, name: 'Updated Folder Name' }
                })
            });
        } else if (url.includes('folder_delete')) {
            return Promise.resolve({
                ok: true,
                json: () => Promise.resolve({
                    status: 'success',
                    message: 'Folder deleted successfully.'
                })
            });
        } else if (url.includes('assign_folder')) {
            return Promise.resolve({
                ok: true,
                json: () => Promise.resolve({
                    status: 'success',
                    message: 'Assistant moved successfully.',
                    folder_name: 'Test Folder 1'
                })
            });
        }
        return Promise.reject(new Error('Unhandled fetch URL'));
    });

    // Mock CSRF token
    window.getCsrfToken = jest.fn().mockReturnValue('mock-csrf-token');
};

// Clean up after tests
const cleanupMockDOM = () => {
    document.body.innerHTML = '';
    delete window.bootstrap;
    global.fetch.mockClear();
    delete window.getCsrfToken;
};

describe('Assistant Folder Functionality', () => {
    beforeEach(() => {
        setupMockDOM();
    });

    afterEach(() => {
        cleanupMockDOM();
    });

    test('Create folder modal shows when trigger button is clicked', () => {
        // Mock the event handler
        const handleCreateFolderModal = () => {
            const modal = document.getElementById('createFolderModal');
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();
        };

        // Add event listener
        const triggerBtn = document.querySelector('.js-trigger-create-folder-modal');
        triggerBtn.addEventListener('click', handleCreateFolderModal);

        // Click the button
        triggerBtn.click();

        // Check modal was shown
        const modal = document.getElementById('createFolderModal');
        expect(modal.classList.contains('show')).toBe(true);
    });

    test('Edit folder modal shows with correct data when edit button is clicked', () => {
        // Mock the event handler
        const handleEditFolderModal = (e) => {
            const folderId = e.currentTarget.getAttribute('data-folder-id');
            const folderName = e.currentTarget.getAttribute('data-folder-name');

            const modal = document.getElementById('editFolderModal');
            document.getElementById('editFolderId').value = folderId;
            document.getElementById('editFolderName').value = folderName;

            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();
        };

        // Add event listener
        const editBtn = document.querySelector('.edit-folder-btn');
        editBtn.addEventListener('click', handleEditFolderModal);

        // Click the button
        editBtn.click();

        // Check modal was shown with correct data
        const modal = document.getElementById('editFolderModal');
        expect(modal.classList.contains('show')).toBe(true);
        expect(document.getElementById('editFolderId').value).toBe('1');
        expect(document.getElementById('editFolderName').value).toBe('Test Folder 1');
    });

    test('Delete folder confirmation modal shows when delete button is clicked', () => {
        // Mock the event handler
        const handleDeleteFolderModal = (e) => {
            const folderId = e.currentTarget.getAttribute('data-folder-id');
            const folderName = e.currentTarget.getAttribute('data-folder-name');

            const modal = document.getElementById('confirmDeleteModal');
            const confirmBtn = document.getElementById('confirmDeleteBtn');
            confirmBtn.setAttribute('data-folder-id', folderId);

            // Update modal text to include folder name
            const modalBody = modal.querySelector('.modal-body p');
            modalBody.textContent = `Are you sure you want to delete the folder "${folderName}"? Assistants in this folder will be moved to "Unassigned".`;

            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();
        };

        // Add event listener
        const deleteBtn = document.querySelector('.delete-folder-btn');
        deleteBtn.addEventListener('click', handleDeleteFolderModal);

        // Click the button
        deleteBtn.click();

        // Check modal was shown with correct data
        const modal = document.getElementById('confirmDeleteModal');
        expect(modal.classList.contains('show')).toBe(true);
        expect(document.getElementById('confirmDeleteBtn').getAttribute('data-folder-id')).toBe('1');
    });

    test('Assign folder modal shows when assign button is clicked', () => {
        // Mock the event handler
        const handleAssignFolderModal = (e) => {
            const assistantId = e.currentTarget.getAttribute('data-assistant-id');
            const assistantName = e.currentTarget.getAttribute('data-assistant-name');

            const modal = document.getElementById('assignFolderModal');
            document.getElementById('assignAssistantId').value = assistantId;

            // Update modal title to include assistant name
            const modalTitle = modal.querySelector('.modal-title');
            modalTitle.textContent = `Assign "${assistantName}" to Folder`;

            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();
        };

        // Add event listener
        const assignBtn = document.querySelector('.assign-folder-btn');
        assignBtn.addEventListener('click', handleAssignFolderModal);

        // Click the button
        assignBtn.click();

        // Check modal was shown with correct data
        const modal = document.getElementById('assignFolderModal');
        expect(modal.classList.contains('show')).toBe(true);
        expect(document.getElementById('assignAssistantId').value).toBe('123');
        expect(modal.querySelector('.modal-title').textContent).toBe('Assign "Test Assistant" to Folder');
    });

    test('Create folder form submits correctly', async () => {
        // Mock the form submission handler
        const handleCreateFolderSubmit = async (e) => {
            e.preventDefault();

            const folderName = document.getElementById('folderName').value;
            const createErrorMsg = document.getElementById('createErrorMsg');

            if (!folderName.trim()) {
                createErrorMsg.textContent = 'Please enter a folder name.';
                createErrorMsg.style.display = 'block';
                return;
            }

            try {
                const response = await fetch('/assistants/company/1/folders/create/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': getCsrfToken(),
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({ 'name': folderName })
                });

                const data = await response.json();

                if (response.ok && data.status === 'success') {
                    // Success - would normally reload or update UI
                    const modal = document.getElementById('createFolderModal');
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    modalInstance.hide();
                } else {
                    createErrorMsg.textContent = data.errors?.name?.[0] || data.message || 'Error creating folder.';
                    createErrorMsg.style.display = 'block';
                }
            } catch (error) {
                createErrorMsg.textContent = 'An unexpected error occurred.';
                createErrorMsg.style.display = 'block';
            }
        };

        // Set up the form
        const form = document.getElementById('createFolderForm');
        form.addEventListener('submit', handleCreateFolderSubmit);

        // Fill in the form
        document.getElementById('folderName').value = 'New Test Folder';

        // Submit the form
        form.dispatchEvent(new Event('submit'));

        // Wait for async operations
        await new Promise(resolve => setTimeout(resolve, 0));

        // Check fetch was called with correct parameters
        expect(global.fetch).toHaveBeenCalledWith('/assistants/company/1/folders/create/', expect.any(Object));

        // Check form data was sent correctly
        const requestBody = new URLSearchParams(global.fetch.mock.calls[0][1].body);
        expect(requestBody.get('name')).toBe('New Test Folder');

        // Check modal was hidden
        const modal = document.getElementById('createFolderModal');
        expect(modal.classList.contains('show')).toBe(false);
    });
});
