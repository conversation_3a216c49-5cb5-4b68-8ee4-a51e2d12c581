{% comment %}
This template tag displays a single consolidated notification for all entities that are pending approval.
Usage: {% include 'accounts/tags/consolidated_simple_notification.html' with pending_companies=pending_companies pending_communities=pending_communities pending_assistants=pending_assistants %}
Parameters:
- pending_companies: List of companies pending approval
- pending_communities: List of communities pending approval
- pending_assistants: List of assistants pending approval
{% endcomment %}

{% if pending_companies or pending_communities or pending_assistants %}
    {% with pending_companies_count=pending_companies|length pending_communities_count=pending_communities|length pending_assistants_count=pending_assistants|length %}
    {% with total_count=pending_companies_count|add:pending_communities_count|add:pending_assistants_count %}
        <!-- Debug info: Companies: {{ pending_companies_count }}, Communities: {{ pending_communities_count }}, Assistants: {{ pending_assistants_count }}, Total: {{ total_count }} -->
        <div class="glass-notification notification-warning d-flex align-items-center simple-pending-alert" role="alert">
            <i class="bi bi-hourglass-split notification-icon"></i>
            <div class="notification-text">
                {% if total_count == 1 %}
                    {% if pending_companies_count == 1 %}
                        Your company <strong>{{ pending_companies.0.name }}</strong> is pending admin approval.
                    {% elif pending_communities_count == 1 %}
                        Your community <strong>{{ pending_communities.0.name }}</strong> is pending admin approval.
                    {% elif pending_assistants_count == 1 %}
                        Your assistant <strong>{{ pending_assistants.0.name }}</strong> is pending admin approval.
                    {% endif %}
                {% else %}
                    You have <strong>{{ total_count }}</strong> items pending admin approval:
                    <span class="pending-items-list">
                        {% if pending_companies_count > 0 %}
                            <span class="pending-item">{{ pending_companies_count }} company{{ pending_companies_count|pluralize }}</span>{% if pending_communities_count > 0 or pending_assistants_count > 0 %}, {% endif %}
                        {% endif %}
                        {% if pending_communities_count > 0 %}
                            <span class="pending-item">{{ pending_communities_count }} community{{ pending_communities_count|pluralize }}</span>{% if pending_assistants_count > 0 %}, {% endif %}
                        {% endif %}
                        {% if pending_assistants_count > 0 %}
                            <span class="pending-item">{{ pending_assistants_count }} assistant{{ pending_assistants_count|pluralize }}</span>
                        {% endif %}
                    </span>
                {% endif %}
                You'll be notified once approved.
            </div>
        </div>
    {% endwith %}
    {% endwith %}

    <script>
        // Make the simple pending alert disappear after 20 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const pendingAlerts = document.querySelectorAll('.simple-pending-alert');
            pendingAlerts.forEach(alert => {
                // Add animation class
                alert.classList.add('new-notification');

                setTimeout(() => {
                    // Fade out effect
                    alert.style.transition = 'all 1s ease';
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateY(-10px)';

                    // Remove from DOM after fade completes
                    setTimeout(() => {
                        alert.remove();
                    }, 1000);
                }, 20000); // 20 seconds
            });
        });
    </script>
{% endif %}
