/**
 * Enhanced Like <PERSON><PERSON> Styling
 * Provides improved styling for like buttons in both light and dark modes
 */

/* Base like button styling - transparent background */
.like-button,
.btn-like,
.btn-favorite,
.favorite-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: none;
  border: none;
  background-color: transparent !important;
}

/* Light mode hover effect */
.like-button:hover,
.btn-like:hover,
.btn-favorite:hover,
.favorite-button:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: none;
  background-color: transparent !important;
}

/* Light mode active effect */
.like-button:active,
.btn-like:active,
.btn-favorite:active,
.favorite-button:active {
  transform: translateY(0) scale(0.95);
  box-shadow: none;
  background-color: transparent !important;
}

/* Light mode heart icon - white for unliked */
.like-button .bi-heart,
.btn-like .bi-heart,
.btn-favorite .bi-heart,
.favorite-button .bi-heart,
.like-button:not(.text-danger) svg,
.btn-like:not(.text-danger) svg,
.btn-favorite:not(.text-danger) svg,
.favorite-button:not(.text-danger) svg {
  color: #ffffff !important;
  fill: #ffffff !important;
  font-size: 1.3rem;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* Light mode filled heart icon - pink for liked */
.like-button .bi-heart-fill,
.btn-like .bi-heart-fill,
.btn-favorite .bi-heart-fill,
.favorite-button .bi-heart-fill,
.like-button.text-danger svg,
.btn-like.text-danger svg,
.btn-favorite.text-danger svg,
.favorite-button.text-danger svg {
  color: #ff3366 !important;
  fill: #ff3366 !important;
  font-size: 1.3rem;
  filter: drop-shadow(0 0 3px rgba(255, 51, 102, 0.4));
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Light mode hover effect for heart icon */
.like-button:hover .bi-heart,
.btn-like:hover .bi-heart,
.btn-favorite:hover .bi-heart,
.favorite-button:hover .bi-heart,
.like-button:not(.text-danger):hover svg,
.btn-like:not(.text-danger):hover svg,
.btn-favorite:not(.text-danger):hover svg,
.favorite-button:not(.text-danger):hover svg {
  color: #ffffff !important;
  fill: #ffffff !important;
  transform: scale(1.2);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4));
}

.like-button:hover .bi-heart-fill,
.btn-like:hover .bi-heart-fill,
.btn-favorite:hover .bi-heart-fill,
.favorite-button:hover .bi-heart-fill,
.like-button.text-danger:hover svg,
.btn-like.text-danger:hover svg,
.btn-favorite.text-danger:hover svg,
.favorite-button.text-danger:hover svg {
  transform: scale(1.2);
  filter: drop-shadow(0 0 5px rgba(255, 51, 102, 0.6));
}

/* Liked state - still transparent background */
.like-button.text-danger,
.btn-like.text-danger,
.btn-favorite.text-danger,
.favorite-button.text-danger,
.like-button.active,
.btn-like.active,
.btn-favorite.active,
.favorite-button.active {
  background-color: transparent !important;
  border: none;
  box-shadow: none;
}

/* Ripple effect container */
.like-button::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 51, 102, 0.3) 0%, rgba(255, 51, 102, 0) 70%);
  border-radius: 50%;
  opacity: 0;
  transform: scale(0);
  transition: transform 0.5s ease-out, opacity 0.5s ease-out;
}

/* Ripple effect animation class (added via JS) */
.like-button.ripple::after {
  opacity: 1;
  transform: scale(2);
}

/* Dark mode styling - transparent background with white/pink hearts */
/* Use !important to override any other dark mode styles */
[data-theme="dark"] .like-button,
html[data-theme="dark"] .like-button,
body[data-theme="dark"] .like-button,
[data-theme="dark"] .general-assistant-header .like-button,
[data-theme="dark"] .btn-like,
[data-theme="dark"] .btn-favorite,
[data-theme="dark"] .favorite-button {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 50% !important;
  width: 36px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

[data-theme="dark"] .like-button:hover,
html[data-theme="dark"] .like-button:hover,
body[data-theme="dark"] .like-button:hover,
[data-theme="dark"] .general-assistant-header .like-button:hover,
[data-theme="dark"] .btn-like:hover,
[data-theme="dark"] .btn-favorite:hover,
[data-theme="dark"] .favorite-button:hover {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  transform: translateY(-2px) scale(1.05) !important;
}

[data-theme="dark"] .like-button:active,
html[data-theme="dark"] .like-button:active,
body[data-theme="dark"] .like-button:active,
[data-theme="dark"] .general-assistant-header .like-button:active,
[data-theme="dark"] .btn-like:active,
[data-theme="dark"] .btn-favorite:active,
[data-theme="dark"] .favorite-button:active {
  transform: translateY(0) scale(0.95) !important;
  box-shadow: none !important;
}

/* White heart for unliked in dark mode */
[data-theme="dark"] .like-button .bi-heart,
html[data-theme="dark"] .like-button .bi-heart,
body[data-theme="dark"] .like-button .bi-heart,
[data-theme="dark"] .general-assistant-header .like-button .bi-heart,
[data-theme="dark"] .btn-like .bi-heart,
[data-theme="dark"] .btn-favorite .bi-heart,
[data-theme="dark"] .favorite-button .bi-heart,
[data-theme="dark"] .like-button:not(.text-danger) svg,
html[data-theme="dark"] .like-button:not(.text-danger) svg,
body[data-theme="dark"] .like-button:not(.text-danger) svg,
[data-theme="dark"] .general-assistant-header .like-button:not(.text-danger) svg,
[data-theme="dark"] .btn-like:not(.text-danger) svg,
[data-theme="dark"] .btn-favorite:not(.text-danger) svg,
[data-theme="dark"] .favorite-button:not(.text-danger) svg {
  color: #ffffff !important;
  fill: #ffffff !important;
  font-size: 1.3rem !important;
  background-color: transparent !important;
  background: none !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5)) !important;
}

/* Pink heart for liked in dark mode */
[data-theme="dark"] .like-button .bi-heart-fill,
html[data-theme="dark"] .like-button .bi-heart-fill,
body[data-theme="dark"] .like-button .bi-heart-fill,
[data-theme="dark"] .general-assistant-header .like-button .bi-heart-fill,
[data-theme="dark"] .btn-like .bi-heart-fill,
[data-theme="dark"] .btn-favorite .bi-heart-fill,
[data-theme="dark"] .favorite-button .bi-heart-fill,
[data-theme="dark"] .like-button.text-danger svg,
html[data-theme="dark"] .like-button.text-danger svg,
body[data-theme="dark"] .like-button.text-danger svg,
[data-theme="dark"] .general-assistant-header .like-button.text-danger svg,
[data-theme="dark"] .btn-like.text-danger svg,
[data-theme="dark"] .btn-favorite.text-danger svg,
[data-theme="dark"] .favorite-button.text-danger svg {
  color: #ff3366 !important;
  fill: #ff3366 !important;
  font-size: 1.3rem !important;
  filter: drop-shadow(0 0 3px rgba(255, 51, 102, 0.5)) !important;
  background-color: transparent !important;
  background: none !important;
}

/* Hover effects for hearts */
[data-theme="dark"] .like-button:hover .bi-heart,
html[data-theme="dark"] .like-button:hover .bi-heart,
body[data-theme="dark"] .like-button:hover .bi-heart,
[data-theme="dark"] .general-assistant-header .like-button:hover .bi-heart,
[data-theme="dark"] .btn-like:hover .bi-heart,
[data-theme="dark"] .btn-favorite:hover .bi-heart,
[data-theme="dark"] .favorite-button:hover .bi-heart,
[data-theme="dark"] .like-button:not(.text-danger):hover svg,
html[data-theme="dark"] .like-button:not(.text-danger):hover svg,
body[data-theme="dark"] .like-button:not(.text-danger):hover svg,
[data-theme="dark"] .general-assistant-header .like-button:not(.text-danger):hover svg,
[data-theme="dark"] .btn-like:not(.text-danger):hover svg,
[data-theme="dark"] .btn-favorite:not(.text-danger):hover svg,
[data-theme="dark"] .favorite-button:not(.text-danger):hover svg {
  color: #ffffff !important;
  fill: #ffffff !important;
  transform: scale(1.2) !important;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.6)) !important;
}

[data-theme="dark"] .like-button:hover .bi-heart-fill,
html[data-theme="dark"] .like-button:hover .bi-heart-fill,
body[data-theme="dark"] .like-button:hover .bi-heart-fill,
[data-theme="dark"] .general-assistant-header .like-button:hover .bi-heart-fill,
[data-theme="dark"] .btn-like:hover .bi-heart-fill,
[data-theme="dark"] .btn-favorite:hover .bi-heart-fill,
[data-theme="dark"] .favorite-button:hover .bi-heart-fill,
[data-theme="dark"] .like-button.text-danger:hover svg,
html[data-theme="dark"] .like-button.text-danger:hover svg,
body[data-theme="dark"] .like-button.text-danger:hover svg,
[data-theme="dark"] .general-assistant-header .like-button.text-danger:hover svg,
[data-theme="dark"] .btn-like.text-danger:hover svg,
[data-theme="dark"] .btn-favorite.text-danger:hover svg,
[data-theme="dark"] .favorite-button.text-danger:hover svg {
  transform: scale(1.2) !important;
  filter: drop-shadow(0 0 5px rgba(255, 51, 102, 0.7)) !important;
}

/* Liked state - still transparent background */
[data-theme="dark"] .like-button.text-danger,
html[data-theme="dark"] .like-button.text-danger,
body[data-theme="dark"] .like-button.text-danger,
[data-theme="dark"] .general-assistant-header .like-button.text-danger,
[data-theme="dark"] .btn-like.text-danger,
[data-theme="dark"] .btn-favorite.text-danger,
[data-theme="dark"] .favorite-button.text-danger,
[data-theme="dark"] .like-button.active,
[data-theme="dark"] .btn-like.active,
[data-theme="dark"] .btn-favorite.active,
[data-theme="dark"] .favorite-button.active {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* Pulse animation for liked state */
@keyframes heart-pulse {
  0% {
    transform: scale(1);
  }
  15% {
    transform: scale(1.3);
  }
  30% {
    transform: scale(1);
  }
  45% {
    transform: scale(1.15);
  }
  60% {
    transform: scale(1);
  }
}

/* Apply pulse animation class (added via JS) */
.like-button .pulse-heart {
  animation: heart-pulse 0.8s ease-in-out;
}
