# Fixing the Duplicate Table Issue

You're encountering an error because a migration is trying to create a table that already exists. The error message is:

```
psycopg2.errors.DuplicateTable: relation "accounts_registrationlink_accessible_folders" already exists
```

This is happening because migration `0011_registrationlink_accessible_folders.py` is trying to create the `accounts_registrationlink_accessible_folders` table, but it already exists in the database.

## Option 1: Using Django's Fake Migration

The simplest way to fix this is to use Django's `--fake` option to mark the migration as applied without actually running it:

```bash
python manage.py migrate accounts 0011_registrationlink_accessible_folders --fake
```

Then run the remaining migrations:

```bash
python manage.py migrate
```

## Option 2: Using SQL

If the Django command doesn't work, you can directly modify the database:

1. Open a PostgreSQL command prompt:
   ```bash
   psql -U postgres -d postgres2
   ```
   (Enter your password when prompted)

2. Run these SQL commands:
   ```sql
   -- Mark the migration as applied
   INSERT INTO django_migrations (app, name, applied)
   SELECT 'accounts', '0011_registrationlink_accessible_folders', NOW()
   WHERE NOT EXISTS (
       SELECT 1 FROM django_migrations 
       WHERE app = 'accounts' AND name = '0011_registrationlink_accessible_folders'
   );

   -- Verify the migration is marked as applied
   SELECT app, name, applied 
   FROM django_migrations 
   WHERE app = 'accounts' AND name = '0011_registrationlink_accessible_folders';
   ```

3. Exit the PostgreSQL prompt:
   ```sql
   \q
   ```

4. Run the remaining migrations:
   ```bash
   python manage.py migrate
   ```

## Option 3: Using the Batch File

We've created a batch file to run the SQL script. Simply run:

```bash
fix_registrationlink_table.bat
```

Then run the migrations:

```bash
python manage.py migrate
```

## Understanding the Issue

The issue is that your database schema is out of sync with the migration history. The table for the `accessible_folders` field on the `RegistrationLink` model already exists, but Django doesn't know that it's already been created.

By marking the migration as applied without actually running it, we're telling Django that the table has already been created, so it can continue with the remaining migrations.

## Verifying the Fix

After applying any of these fixes, verify that the migrations are properly applied:

```bash
python manage.py showmigrations accounts
```

You should see `0011_registrationlink_accessible_folders` marked as applied (with an [X]).
