import os
import django
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

print("Starting AssistantListing table creation script...")

# Database connection parameters
db_params = {
    'dbname': 'virtualo',
    'user': 'postgres',
    'password': 'M@kerere1',
    'host': 'localhost',
    'port': '5432'
}

# SQL to create the directory_assistantlisting table
assistantlisting_sql = """
CREATE TABLE IF NOT EXISTS "directory_assistantlisting" (
    "id" serial NOT NULL PRIMARY KEY,
    "is_listed" boolean NOT NULL,
    "short_description" text NOT NULL,
    "long_description" text NOT NULL,
    "categories" jsonb NOT NULL,
    "tags" jsonb NOT NULL,
    "capabilities" jsonb NOT NULL,
    "avg_rating" numeric(3, 2) NOT NULL,
    "total_ratings" integer NOT NULL,
    "created_at" timestamp with time zone NOT NULL,
    "updated_at" timestamp with time zone NOT NULL,
    "assistant_id" integer NOT NULL REFERENCES "assistants_assistant" ("id") DEFERRABLE INITIALLY DEFERRED
);
"""

# Connect to the database
try:
    print("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(**db_params)
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cursor = conn.cursor()
    
    # Create the directory_assistantlisting table
    print("Creating directory_assistantlisting table...")
    cursor.execute(assistantlisting_sql)
    print("AssistantListing table created successfully!")
    
    # Close the connection
    cursor.close()
    conn.close()
    print("Database connection closed.")
    
except Exception as e:
    print(f"Error: {e}")
