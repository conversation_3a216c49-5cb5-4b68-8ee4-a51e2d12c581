# PostgreSQL Migration Guide for Company Assistant

This guide provides step-by-step instructions for migrating the Company Assistant project from SQLite to PostgreSQL.

## Prerequisites

- PostgreSQL installed on your system
- Python with Django and required packages installed
- Basic knowledge of database management

## Step 1: Install PostgreSQL

### Windows

1. Download and install PostgreSQL from the [official website](https://www.postgresql.org/download/windows/)
2. During installation:
   - Set a password for the postgres user (remember this password!)
   - Keep the default port (5432)
   - Complete the installation

### Linux

```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
```

### macOS

```bash
brew install postgresql
```

## Step 2: Create a Database

After installing PostgreSQL, create a database for the project:

```bash
# Connect to PostgreSQL as the postgres user
psql -U postgres

# Create a database
CREATE DATABASE virtualo;

# Exit PostgreSQL
\q
```

## Step 3: Install Required Python Packages

Ensure you have the PostgreSQL adapter for Python installed:

```bash
pip install psycopg2-binary
```

## Step 4: Configure Django Settings

The `local_settings.py` file has been updated with PostgreSQL configuration. Review and update the password if needed:

```python
# Database settings for PostgreSQL
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'virtualo',  # Database name
        'USER': 'postgres',  # PostgreSQL username
        'PASSWORD': '',  # Empty password or set to your actual PostgreSQL password
        'HOST': 'localhost',
        'PORT': '5432',
        'CONN_MAX_AGE': 600,  # Keep connections alive for 10 minutes
        'OPTIONS': {
            'connect_timeout': 10,
            'client_encoding': 'UTF8',
        },
    }
}
```

## Step 5: Migration Process

### Option 1: Fresh Migration (Recommended for New Projects)

If you're starting with a fresh database or don't need to preserve data:

1. Delete all migration files (except `__init__.py`):

   ```bash
   # Windows
   for /r %i in (*/migrations/*.py) do @if not "%~nxi"=="__init__.py" del "%i"

   # Linux/macOS
   find . -path "*/migrations/*.py" -not -name "__init__.py" -delete
   ```

2. Create new migrations:

   ```bash
   python manage.py makemigrations
   ```

3. Apply migrations:

   ```bash
   python manage.py migrate
   ```

4. Create a superuser:

   ```bash
   python manage.py createsuperuser
   ```

### Option 2: Migrate Existing Data

If you need to preserve your data:

1. Dump data from SQLite:

   ```bash
   python manage.py dumpdata --exclude auth.permission --exclude contenttypes > data_dump.json
   ```

2. Reset migration state in PostgreSQL:

   ```bash
   python reset_migrations.py
   ```

3. Apply migrations to PostgreSQL:

   ```bash
   python manage.py migrate
   ```

4. Load data into PostgreSQL:

   ```bash
   python manage.py loaddata data_dump.json
   ```

5. Fix any database issues:

   ```bash
   python fix_db_issues.py
   ```

## Step 6: Handling Migration Conflicts

If you encounter migration conflicts:

### Using the Fake Initial Flag

```bash
python manage.py migrate --fake-initial
```

### Marking Specific Migrations as Applied

```bash
python reset_migrations.py app_name migration_name
```

### Merging Migrations

```bash
python manage.py makemigrations --merge
```

## Step 7: Troubleshooting

### Connection Issues

- Verify PostgreSQL is running
- Check username and password
- Ensure the database exists
- Confirm PostgreSQL is listening on the specified port

### Migration Errors

Common errors and solutions:

1. **Relation already exists**:
   - Use `--fake` flag for that specific migration
   - Example: `python manage.py migrate app_name 0001_initial --fake`

2. **Column already exists**:
   - Mark the migration as applied using `reset_migrations.py`
   - Example: `python reset_migrations.py app_name migration_name`

3. **Duplicate key value violates unique constraint**:
   - Fix sequence issues with `fix_db_issues.py`

4. **No such table**:
   - Ensure migrations are applied in the correct order
   - Try using `--fake-initial` flag

## Step 8: Post-Migration Tasks

After successful migration:

1. Run the development server to test:

   ```bash
   python manage.py runserver
   ```

2. Check for any runtime errors related to the database

3. Verify all functionality works as expected

4. Update any environment variables or deployment scripts to use PostgreSQL

## PostgreSQL-Specific Considerations

- PostgreSQL is case-sensitive for string comparisons (use `ILIKE` instead of `LIKE` for case-insensitive searches)
- PostgreSQL has better transaction support than SQLite
- PostgreSQL enforces field sizes strictly, unlike SQLite

## Conclusion

You have successfully migrated your Django project from SQLite to PostgreSQL. This provides better performance, concurrency, and reliability for your application.

For any issues not covered in this guide, refer to the Django documentation or PostgreSQL documentation.
