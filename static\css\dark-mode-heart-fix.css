/**
 * Dark Mode Heart Icon Fix
 * This CSS ensures all heart icons are white by default in dark mode
 * and only turn pink/red when liked
 */

/* GLOBAL RESET FOR DARK MODE - Make ALL heart icons white by default */
[data-theme="dark"] .like-button i,
[data-theme="dark"] .btn-like i,
[data-theme="dark"] .btn-favorite i,
[data-theme="dark"] .favorite-button i,
[data-theme="dark"] .like-button svg,
[data-theme="dark"] .btn-like svg,
[data-theme="dark"] .btn-favorite svg,
[data-theme="dark"] .favorite-button svg,
[data-theme="dark"] .like-button .bi-heart,
[data-theme="dark"] .btn-like .bi-heart,
[data-theme="dark"] .btn-favorite .bi-heart,
[data-theme="dark"] .favorite-button .bi-heart,
[data-theme="dark"] .like-button .bi-heart-fill,
[data-theme="dark"] .btn-like .bi-heart-fill,
[data-theme="dark"] .btn-favorite .bi-heart-fill,
[data-theme="dark"] .favorite-button .bi-heart-fill {
  color: #ffffff !important;
  fill: #ffffff !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5)) !important;
}

/* ONLY liked hearts should be pink/red in dark mode */
[data-theme="dark"] .like-button.text-danger i,
[data-theme="dark"] .btn-like.text-danger i,
[data-theme="dark"] .btn-favorite.text-danger i,
[data-theme="dark"] .favorite-button.text-danger i,
[data-theme="dark"] .like-button.text-danger svg,
[data-theme="dark"] .btn-like.text-danger svg,
[data-theme="dark"] .btn-favorite.text-danger svg,
[data-theme="dark"] .favorite-button.text-danger svg,
[data-theme="dark"] .like-button.text-danger .bi-heart,
[data-theme="dark"] .btn-like.text-danger .bi-heart,
[data-theme="dark"] .btn-favorite.text-danger .bi-heart,
[data-theme="dark"] .favorite-button.text-danger .bi-heart,
[data-theme="dark"] .like-button.text-danger .bi-heart-fill,
[data-theme="dark"] .btn-like.text-danger .bi-heart-fill,
[data-theme="dark"] .btn-favorite.text-danger .bi-heart-fill,
[data-theme="dark"] .favorite-button.text-danger .bi-heart-fill {
  color: #ff3366 !important;
  fill: #ff3366 !important;
  filter: drop-shadow(0 0 3px rgba(255, 51, 102, 0.5)) !important;
}

/* Hover effects for dark mode - maintain white for unliked */
[data-theme="dark"] .like-button:not(.text-danger):hover i,
[data-theme="dark"] .btn-like:not(.text-danger):hover i,
[data-theme="dark"] .btn-favorite:not(.text-danger):hover i,
[data-theme="dark"] .favorite-button:not(.text-danger):hover i,
[data-theme="dark"] .like-button:not(.text-danger):hover svg,
[data-theme="dark"] .btn-like:not(.text-danger):hover svg,
[data-theme="dark"] .btn-favorite:not(.text-danger):hover svg,
[data-theme="dark"] .favorite-button:not(.text-danger):hover svg,
[data-theme="dark"] .like-button:not(.text-danger):hover .bi-heart,
[data-theme="dark"] .btn-like:not(.text-danger):hover .bi-heart,
[data-theme="dark"] .btn-favorite:not(.text-danger):hover .bi-heart,
[data-theme="dark"] .favorite-button:not(.text-danger):hover .bi-heart,
[data-theme="dark"] .like-button:not(.text-danger):hover .bi-heart-fill,
[data-theme="dark"] .btn-like:not(.text-danger):hover .bi-heart-fill,
[data-theme="dark"] .btn-favorite:not(.text-danger):hover .bi-heart-fill,
[data-theme="dark"] .favorite-button:not(.text-danger):hover .bi-heart-fill {
  color: #ffffff !important;
  fill: #ffffff !important;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.6)) !important;
}

/* Hover effects for dark mode - maintain pink/red for liked */
[data-theme="dark"] .like-button.text-danger:hover i,
[data-theme="dark"] .btn-like.text-danger:hover i,
[data-theme="dark"] .btn-favorite.text-danger:hover i,
[data-theme="dark"] .favorite-button.text-danger:hover i,
[data-theme="dark"] .like-button.text-danger:hover svg,
[data-theme="dark"] .btn-like.text-danger:hover svg,
[data-theme="dark"] .btn-favorite.text-danger:hover svg,
[data-theme="dark"] .favorite-button.text-danger:hover svg,
[data-theme="dark"] .like-button.text-danger:hover .bi-heart,
[data-theme="dark"] .btn-like.text-danger:hover .bi-heart,
[data-theme="dark"] .btn-favorite.text-danger:hover .bi-heart,
[data-theme="dark"] .favorite-button.text-danger:hover .bi-heart,
[data-theme="dark"] .like-button.text-danger:hover .bi-heart-fill,
[data-theme="dark"] .btn-like.text-danger:hover .bi-heart-fill,
[data-theme="dark"] .btn-favorite.text-danger:hover .bi-heart-fill,
[data-theme="dark"] .favorite-button.text-danger:hover .bi-heart-fill {
  color: #ff3366 !important;
  fill: #ff3366 !important;
  filter: drop-shadow(0 0 5px rgba(255, 51, 102, 0.7)) !important;
}

/* Ensure all backgrounds are transparent in dark mode */
[data-theme="dark"] .like-button,
[data-theme="dark"] .btn-like,
[data-theme="dark"] .btn-favorite,
[data-theme="dark"] .favorite-button,
[data-theme="dark"] .like-button.text-danger,
[data-theme="dark"] .btn-like.text-danger,
[data-theme="dark"] .btn-favorite.text-danger,
[data-theme="dark"] .favorite-button.text-danger {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
}
