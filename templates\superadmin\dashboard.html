{% extends "superadmin/base_superadmin.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ title }} - {% trans "Superadmin" %}{% endblock %}

{% block extra_css %}
{{ block.super }}
<link href="{% static 'css/superadmin-enhanced.css' %}" rel="stylesheet">
{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item active" aria-current="page">{% trans "Dashboard" %}</li>
{% endblock %}

{% block superadmin_content %}
<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="superadmin-card dashboard-welcome-card">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="welcome-icon">
                        <i class="bi bi-speedometer2"></i>
                    </div>
                    <h4 class="mb-0">{% trans "Welcome to the Superadmin Dashboard" %}</h4>
                </div>
                <p class="mb-0 ms-5 ps-3">{% trans "This dashboard provides an overview of the platform's status and pending actions. Use the sidebar to navigate to specific management areas." %}</p>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <!-- Pending Company Approvals -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="superadmin-card dashboard-stat-card card-primary h-100">
            <div class="card-body">
                <div class="stat-card-header">
                    <div class="stat-card-icon">
                        <i class="bi bi-building"></i>
                    </div>
                    <div class="stat-card-title">
                        {% trans "PENDING COMPANY APPROVALS" %}
                    </div>
                </div>
                <div class="stat-card-value">
                    {{ pending_company_approvals }}
                </div>
                <div class="stat-card-label">
                    {% trans "companies" %}
                </div>
                <div class="stat-card-action">
                    <a href="{% url 'superadmin:company_list' %}?status=inactive&entity_type=company" class="btn btn-admin-primary btn-sm btn-icon">
                        <i class="bi bi-eye"></i> {% trans "View Inactive Companies" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Community Approvals -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="superadmin-card dashboard-stat-card card-info h-100">
            <div class="card-body">
                <div class="stat-card-header">
                    <div class="stat-card-icon">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="stat-card-title">
                        {% trans "PENDING COMMUNITY APPROVALS" %}
                    </div>
                </div>
                <div class="stat-card-value">
                    {{ pending_community_approvals }}
                </div>
                <div class="stat-card-label">
                    {% trans "communities" %}
                </div>
                <div class="stat-card-action">
                    <a href="{% url 'superadmin:company_list' %}?status=inactive&entity_type=community" class="btn btn-admin-info btn-sm btn-icon">
                        <i class="bi bi-eye"></i> {% trans "View Inactive Communities" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Company Tier Changes -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="superadmin-card dashboard-stat-card card-warning h-100">
            <div class="card-body">
                <div class="stat-card-header">
                    <div class="stat-card-icon">
                        <i class="bi bi-tag"></i>
                    </div>
                    <div class="stat-card-title">
                        {% trans "PENDING COMPANY TIER CHANGES" %}
                    </div>
                </div>
                <div class="stat-card-value">
                    {{ pending_company_tier_changes }}
                </div>
                <div class="stat-card-label">
                    {% trans "requests" %}
                </div>
                <div class="stat-card-action">
                    <a href="{% url 'superadmin:company_list' %}?tier_pending=true" class="btn btn-admin-warning btn-sm btn-icon">
                        <i class="bi bi-eye"></i> {% trans "View Pending Tiers" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Assistant Tier Changes -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="superadmin-card dashboard-stat-card card-info h-100">
            <div class="card-body">
                <div class="stat-card-header">
                    <div class="stat-card-icon">
                        <i class="bi bi-robot"></i>
                    </div>
                    <div class="stat-card-title">
                        {% trans "PENDING ASSISTANT TIER CHANGES" %}
                    </div>
                </div>
                <div class="stat-card-value">
                    {{ pending_assistant_tier_changes }}
                </div>
                <div class="stat-card-label">
                    {% trans "requests" %}
                </div>
                <div class="stat-card-action">
                    <a href="{% url 'superadmin:assistant_list' %}?tier_pending=true" class="btn btn-admin-info btn-sm btn-icon">
                        <i class="bi bi-eye"></i> {% trans "View Pending Tiers" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Community Assistants -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="superadmin-card dashboard-stat-card card-success h-100">
            <div class="card-body">
                <div class="stat-card-header">
                    <div class="stat-card-icon">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="stat-card-title">
                        {% trans "COMMUNITY ASSISTANTS" %}
                    </div>
                </div>
                <div class="stat-card-value">
                    {{ community_assistants_count }}
                </div>
                <div class="stat-card-label">
                    {% trans "assistants" %}
                </div>
                <div class="stat-card-action">
                    <a href="{% url 'superadmin:community_assistant_list' %}" class="btn btn-admin-success btn-sm btn-icon">
                        <i class="bi bi-eye"></i> {% trans "View Community Assistants" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Second Row - Featured Requests -->
<div class="row mb-4">
    <!-- Pending Company Featured Requests -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="superadmin-card dashboard-stat-card card-warning h-100">
            <div class="card-body">
                <div class="stat-card-header">
                    <div class="stat-card-icon">
                        <i class="bi bi-star"></i>
                    </div>
                    <div class="stat-card-title">
                        {% trans "PENDING COMPANY FEATURED REQUESTS" %}
                    </div>
                </div>
                <div class="stat-card-value">
                    {{ pending_company_featured_requests }}
                </div>
                <div class="stat-card-label">
                    {% trans "requests" %}
                </div>
                <div class="stat-card-action">
                    <a href="{% url 'superadmin:company_list' %}?featured_pending=true" class="btn btn-admin-warning btn-sm btn-icon">
                        <i class="bi bi-eye"></i> {% trans "View Pending Featured" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Assistant Featured Requests -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="superadmin-card dashboard-stat-card card-warning h-100">
            <div class="card-body">
                <div class="stat-card-header">
                    <div class="stat-card-icon">
                        <i class="bi bi-star"></i>
                    </div>
                    <div class="stat-card-title">
                        {% trans "PENDING ASSISTANT FEATURED REQUESTS" %}
                    </div>
                </div>
                <div class="stat-card-value">
                    {{ pending_assistant_featured_requests }}
                </div>
                <div class="stat-card-label">
                    {% trans "requests" %}
                </div>
                <div class="stat-card-action">
                    <a href="{% url 'superadmin:assistant_list' %}?featured_pending=true" class="btn btn-admin-warning btn-sm btn-icon">
                        <i class="bi bi-eye"></i> {% trans "View Pending Featured" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Pending Notifications -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="superadmin-card dashboard-stat-card card-danger h-100">
            <div class="card-body">
                <div class="stat-card-header">
                    <div class="stat-card-icon">
                        <i class="bi bi-bell"></i>
                    </div>
                    <div class="stat-card-title">
                        {% trans "TOTAL PENDING NOTIFICATIONS" %}
                    </div>
                </div>
                <div class="stat-card-value">
                    {{ total_pending_notifications }}
                </div>
                <div class="stat-card-label">
                    {% trans "items" %}
                </div>
                <div class="stat-card-action">
                    <span class="text-muted small">
                        {% trans "All pending approvals" %}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- System Health Status -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="superadmin-card dashboard-stat-card card-info h-100">
            <div class="card-body">
                <div class="stat-card-header">
                    <div class="stat-card-icon">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <div class="stat-card-title">
                        {% trans "SYSTEM STATUS" %}
                    </div>
                </div>
                <div class="stat-card-value">
                    {% if total_pending_notifications > 0 %}
                        <span class="text-warning">{% trans "ATTENTION" %}</span>
                    {% else %}
                        <span class="text-success">{% trans "HEALTHY" %}</span>
                    {% endif %}
                </div>
                <div class="stat-card-label">
                    {% if total_pending_notifications > 0 %}
                        {% trans "requires review" %}
                    {% else %}
                        {% trans "all clear" %}
                    {% endif %}
                </div>
                <div class="stat-card-action">
                    <span class="text-muted small">
                        {% trans "Platform status" %}
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Analytics Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="superadmin-card dashboard-analytics-card">
            <div class="card-header">
                <h5><i class="bi bi-graph-up me-2"></i>{% trans "Platform Analytics" %}</h5>
            </div>
            <div class="card-body">
                <!-- User Analytics -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-muted mb-3">{% trans "User Metrics" %}</h6>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-metric">
                            <div class="metric-value">{{ total_users|default:"0" }}</div>
                            <div class="metric-label">{% trans "Total Users" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-metric">
                            <div class="metric-value">{{ active_users_30_days|default:"0" }}</div>
                            <div class="metric-label">{% trans "Active Users (30 days)" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-metric">
                            <div class="metric-value">{{ new_users_30_days|default:"0" }}</div>
                            <div class="metric-label">{% trans "New Users (30 days)" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-metric">
                            <div class="metric-value">
                                {% if active_users_30_days and total_users %}
                                    {{ active_users_30_days|floatformat:0 }}/{{ total_users|floatformat:0 }}
                                {% else %}
                                    0/0
                                {% endif %}
                            </div>
                            <div class="metric-label">{% trans "Activity Ratio" %}</div>
                        </div>
                    </div>
                </div>

                <!-- Company Analytics -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-muted mb-3">{% trans "Company Metrics" %}</h6>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-metric">
                            <div class="metric-value">{{ total_companies|default:"0" }}</div>
                            <div class="metric-label">{% trans "Total Companies" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-metric">
                            <div class="metric-value">{{ active_companies|default:"0" }}</div>
                            <div class="metric-label">{% trans "Active Companies" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-metric">
                            <div class="metric-value">{{ featured_companies|default:"0" }}</div>
                            <div class="metric-label">{% trans "Featured Companies" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-metric">
                            <div class="metric-value">{{ new_companies_30_days|default:"0" }}</div>
                            <div class="metric-label">{% trans "New Companies (30 days)" %}</div>
                        </div>
                    </div>
                </div>

                <!-- Assistant Analytics -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-muted mb-3">{% trans "Assistant Metrics" %}</h6>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-metric">
                            <div class="metric-value">{{ total_assistants|default:"0" }}</div>
                            <div class="metric-label">{% trans "Total Assistants" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-metric">
                            <div class="metric-value">{{ active_assistants|default:"0" }}</div>
                            <div class="metric-label">{% trans "Active Assistants" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-metric">
                            <div class="metric-value">{{ public_assistants|default:"0" }}</div>
                            <div class="metric-label">{% trans "Public Assistants" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-metric">
                            <div class="metric-value">{{ featured_assistants|default:"0" }}</div>
                            <div class="metric-label">{% trans "Featured Assistants" %}</div>
                        </div>
                    </div>
                </div>

                <!-- Interaction Analytics -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-muted mb-3">{% trans "Interaction Metrics" %}</h6>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-metric">
                            <div class="metric-value">{{ total_interactions|default:"0" }}</div>
                            <div class="metric-label">{% trans "Total Interactions" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-metric">
                            <div class="metric-value">{{ interactions_30_days|default:"0" }}</div>
                            <div class="metric-label">{% trans "Interactions (30 days)" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-metric">
                            <div class="metric-value">
                                {% if average_rating %}
                                    {{ average_rating|floatformat:1 }}/5.0
                                {% else %}
                                    N/A
                                {% endif %}
                            </div>
                            <div class="metric-label">{% trans "Average Rating" %}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="analytics-metric">
                            <div class="metric-value">{{ total_tokens_used|default:"0" }}</div>
                            <div class="metric-label">{% trans "Total Tokens Used" %}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Top Performing Assistants -->
{% if top_assistants %}
<div class="row mb-4">
    <div class="col-md-6">
        <div class="superadmin-card">
            <div class="card-header">
                <h5><i class="bi bi-trophy me-2"></i>{% trans "Top Performing Assistants" %}</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% for assistant in top_assistants %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>{{ assistant.name }}</strong>
                            <br>
                            <small class="text-muted">{{ assistant.company.name }}</small>
                        </div>
                        <span class="badge bg-primary rounded-pill">{{ assistant.interaction_count }} interactions</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="superadmin-card">
            <div class="card-header">
                <h5><i class="bi bi-clock-history me-2"></i>{% trans "Recent Activity" %}</h5>
            </div>
            <div class="card-body">
                {% if recent_companies or recent_assistants %}
                <div class="activity-list">
                    {% for company in recent_companies %}
                    <div class="activity-item">
                        <i class="bi bi-building text-primary"></i>
                        <span>New company: <strong>{{ company.name }}</strong></span>
                        <small class="text-muted">{{ company.created_at|timesince }} ago</small>
                    </div>
                    {% endfor %}
                    {% for assistant in recent_assistants %}
                    <div class="activity-item">
                        <i class="bi bi-robot text-success"></i>
                        <span>New assistant: <strong>{{ assistant.name }}</strong></span>
                        <small class="text-muted">{{ assistant.created_at|timesince }} ago</small>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted">{% trans "No recent activity" %}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Quick Actions Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="superadmin-card dashboard-actions-card">
            <div class="card-header">
                <h5><i class="bi bi-lightning-charge me-2"></i>{% trans "Quick Actions" %}</h5>
            </div>
            <div class="card-body">
                <div class="row quick-actions">
                    <div class="col-md-2 mb-3">
                        <a href="{% url 'superadmin:company_list' %}?entity_type=company" class="dashboard-action-btn">
                            <div class="action-icon">
                                <i class="bi bi-building"></i>
                            </div>
                            <div class="action-text">
                                {% trans "Manage Companies" %}
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 mb-3">
                        <a href="{% url 'superadmin:company_list' %}?entity_type=community" class="dashboard-action-btn">
                            <div class="action-icon">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="action-text">
                                {% trans "Manage Communities" %}
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 mb-3">
                        <a href="{% url 'superadmin:assistant_list' %}" class="dashboard-action-btn">
                            <div class="action-icon">
                                <i class="bi bi-robot"></i>
                            </div>
                            <div class="action-text">
                                {% trans "Manage Assistants" %}
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 mb-3">
                        <a href="{% url 'superadmin:community_assistant_list' %}" class="dashboard-action-btn">
                            <div class="action-icon">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="action-text">
                                {% trans "Manage Community Assistants" %}
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 mb-3">
                        <a href="{% url 'admin:index' %}" class="dashboard-action-btn">
                            <div class="action-icon">
                                <i class="bi bi-gear"></i>
                            </div>
                            <div class="action-text">
                                {% trans "Django Admin" %}
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row">
    <div class="col-12">
        <div class="superadmin-card dashboard-system-card">
            <div class="card-header">
                <h5><i class="bi bi-info-circle me-2"></i>{% trans "System Information" %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="system-info-section">
                            <div class="system-info-title">{% trans "Technical Information" %}</div>
                            <div class="system-info-item">
                                <div class="info-label">{% trans "Django Version" %}</div>
                                <div class="info-value">{{ django_version|default:"Unknown" }}</div>
                            </div>
                            <div class="system-info-item">
                                <div class="info-label">{% trans "Python Version" %}</div>
                                <div class="info-value">{{ python_version|default:"Unknown" }}</div>
                            </div>
                            <div class="system-info-item">
                                <div class="info-label">{% trans "Database" %}</div>
                                <div class="info-value">{{ database_engine|default:"Unknown" }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="system-info-section">
                            <div class="system-info-title">{% trans "Platform Statistics" %}</div>
                            <div class="system-info-item">
                                <div class="info-label">{% trans "Total Companies" %}</div>
                                <div class="info-value">{{ total_companies|default:"0" }}</div>
                            </div>
                            <div class="system-info-item">
                                <div class="info-label">{% trans "Total Assistants" %}</div>
                                <div class="info-value">{{ total_assistants|default:"0" }}</div>
                            </div>
                            <div class="system-info-item">
                                <div class="info-label">{% trans "Total Users" %}</div>
                                <div class="info-value">{{ total_users|default:"0" }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Auto-refresh functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh dashboard every 2 minutes
    const AUTO_REFRESH_INTERVAL = 2 * 60 * 1000; // 2 minutes
    let isRefreshing = false;

    function updateDashboardData(data) {
        // Update stat card values
        const statCardMappings = {
            'pending_company_approvals': 'pending-company-approvals',
            'pending_community_approvals': 'pending-community-approvals',
            'pending_company_tier_changes': 'pending-company-tier-changes',
            'pending_assistant_tier_changes': 'pending-assistant-tier-changes',
            'pending_company_featured_requests': 'pending-company-featured-requests',
            'pending_assistant_featured_requests': 'pending-assistant-featured-requests',
            'total_pending_notifications': 'total-pending-notifications'
        };

        // Update analytics metrics
        const analyticsMetrics = {
            'total_users': 'Total Users',
            'active_users_30_days': 'Active Users (30 days)',
            'new_users_30_days': 'New Users (30 days)',
            'total_companies': 'Total Companies',
            'active_companies': 'Active Companies',
            'featured_companies': 'Featured Companies',
            'new_companies_30_days': 'New Companies (30 days)',
            'total_assistants': 'Total Assistants',
            'active_assistants': 'Active Assistants',
            'public_assistants': 'Public Assistants',
            'featured_assistants': 'Featured Assistants',
            'total_interactions': 'Total Interactions',
            'interactions_30_days': 'Interactions (30 days)',
            'total_tokens_used': 'Total Tokens Used'
        };

        // Update stat cards
        Object.entries(statCardMappings).forEach(([dataKey, elementId]) => {
            const element = document.querySelector(`[data-stat="${dataKey}"]`);
            if (element && data[dataKey] !== undefined) {
                element.textContent = data[dataKey];
            }
        });

        // Update analytics metrics
        Object.entries(analyticsMetrics).forEach(([dataKey, label]) => {
            const metricCards = document.querySelectorAll('.analytics-metric');
            metricCards.forEach(card => {
                const labelElement = card.querySelector('.metric-label');
                if (labelElement && labelElement.textContent.toLowerCase().includes(label.toLowerCase())) {
                    const valueElement = card.querySelector('.metric-value');
                    if (valueElement && data[dataKey] !== undefined) {
                        valueElement.textContent = data[dataKey];
                    }
                }
            });
        });

        // Update average rating display
        if (data.average_rating !== undefined) {
            const ratingElements = document.querySelectorAll('.metric-value');
            ratingElements.forEach(element => {
                const parent = element.closest('.analytics-metric');
                if (parent && parent.querySelector('.metric-label').textContent.includes('Average Rating')) {
                    element.textContent = data.average_rating ? `${data.average_rating.toFixed(1)}/5.0` : 'N/A';
                }
            });
        }

        // Update sidebar notification badges
        updateSidebarBadges(data);
    }

    function updateSidebarBadges(data) {
        // Update dashboard total notifications badge
        const dashboardBadge = document.querySelector('.nav-link[href*="dashboard"] .badge');
        if (data.total_pending_notifications > 0) {
            if (dashboardBadge) {
                dashboardBadge.textContent = data.total_pending_notifications;
                dashboardBadge.title = 'Total pending notifications';
            } else {
                // Create badge if it doesn't exist
                const dashboardLink = document.querySelector('.nav-link[href*="dashboard"]');
                if (dashboardLink) {
                    const badge = document.createElement('span');
                    badge.className = 'badge rounded-pill bg-danger ms-auto pulse-badge';
                    badge.textContent = data.total_pending_notifications;
                    badge.title = 'Total pending notifications';
                    dashboardLink.appendChild(badge);
                }
            }
        } else if (dashboardBadge) {
            dashboardBadge.remove();
        }

        // Update companies badge
        const companiesBadge = document.querySelector('.nav-link[href*="entity_type=company"] .badge');
        const totalCompanyPending = (data.pending_company_approvals || 0) +
                                   (data.pending_company_tier_changes || 0) +
                                   (data.pending_company_featured_requests || 0);

        if (totalCompanyPending > 0) {
            if (companiesBadge) {
                companiesBadge.textContent = totalCompanyPending;
                companiesBadge.title = `Pending: ${data.pending_company_approvals || 0} approvals, ${data.pending_company_tier_changes || 0} tier changes, ${data.pending_company_featured_requests || 0} featured requests`;
            } else {
                const companiesLink = document.querySelector('.nav-link[href*="entity_type=company"]');
                if (companiesLink) {
                    const badge = document.createElement('span');
                    badge.className = 'badge rounded-pill bg-danger ms-auto';
                    badge.textContent = totalCompanyPending;
                    badge.title = `Pending: ${data.pending_company_approvals || 0} approvals, ${data.pending_company_tier_changes || 0} tier changes, ${data.pending_company_featured_requests || 0} featured requests`;
                    companiesLink.appendChild(badge);
                }
            }
        } else if (companiesBadge) {
            companiesBadge.remove();
        }

        // Update communities badge
        const communitiesBadge = document.querySelector('.nav-link[href*="entity_type=community"] .badge');
        if (data.pending_community_approvals > 0) {
            if (communitiesBadge) {
                communitiesBadge.textContent = data.pending_community_approvals;
                communitiesBadge.title = `Pending: ${data.pending_community_approvals} approvals`;
            } else {
                const communitiesLink = document.querySelector('.nav-link[href*="entity_type=community"]');
                if (communitiesLink) {
                    const badge = document.createElement('span');
                    badge.className = 'badge rounded-pill bg-danger ms-auto';
                    badge.textContent = data.pending_community_approvals;
                    badge.title = `Pending: ${data.pending_community_approvals} approvals`;
                    communitiesLink.appendChild(badge);
                }
            }
        } else if (communitiesBadge) {
            communitiesBadge.remove();
        }

        // Update assistants badge
        const assistantsBadge = document.querySelector('.nav-link[href*="assistant_list"]:not([href*="community"]) .badge');
        const totalAssistantPending = (data.pending_assistant_approvals || 0) +
                                     (data.pending_assistant_tier_changes || 0) +
                                     (data.pending_assistant_featured_requests || 0);

        if (totalAssistantPending > 0) {
            if (assistantsBadge) {
                assistantsBadge.textContent = totalAssistantPending;
                assistantsBadge.title = `Pending: ${data.pending_assistant_approvals || 0} approvals, ${data.pending_assistant_tier_changes || 0} tier changes, ${data.pending_assistant_featured_requests || 0} featured requests`;
            } else {
                const assistantsLink = document.querySelector('.nav-link[href*="assistant_list"]:not([href*="community"])');
                if (assistantsLink) {
                    const badge = document.createElement('span');
                    badge.className = 'badge rounded-pill bg-warning ms-auto';
                    badge.textContent = totalAssistantPending;
                    badge.title = `Pending: ${data.pending_assistant_approvals || 0} approvals, ${data.pending_assistant_tier_changes || 0} tier changes, ${data.pending_assistant_featured_requests || 0} featured requests`;
                    assistantsLink.appendChild(badge);
                }
            }
        } else if (assistantsBadge) {
            assistantsBadge.remove();
        }

        // Update community assistants badge (info badge for count, not pending)
        const communityAssistantsBadge = document.querySelector('.nav-link[href*="community_assistant_list"] .badge');
        if (data.community_assistants_count > 0) {
            if (communityAssistantsBadge) {
                communityAssistantsBadge.textContent = data.community_assistants_count;
                communityAssistantsBadge.title = 'Total community assistants';
            } else {
                const communityAssistantsLink = document.querySelector('.nav-link[href*="community_assistant_list"]');
                if (communityAssistantsLink) {
                    const badge = document.createElement('span');
                    badge.className = 'badge rounded-pill bg-info ms-auto';
                    badge.textContent = data.community_assistants_count;
                    badge.title = 'Total community assistants';
                    communityAssistantsLink.appendChild(badge);
                }
            }
        } else if (communityAssistantsBadge) {
            communityAssistantsBadge.remove();
        }
    }

    function refreshDashboard(showIndicator = true) {
        if (isRefreshing) return;
        isRefreshing = true;

        let refreshIndicator;
        if (showIndicator) {
            // Add a subtle loading indicator
            refreshIndicator = document.createElement('div');
            refreshIndicator.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Updating...';
            refreshIndicator.className = 'position-fixed top-0 end-0 m-3 p-2 bg-primary text-white rounded';
            refreshIndicator.style.zIndex = '9999';
            document.body.appendChild(refreshIndicator);

            // Add spin animation
            const style = document.createElement('style');
            style.textContent = `
                .spin {
                    animation: spin 1s linear infinite;
                }
                @keyframes spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        // Fetch fresh data from API
        fetch('{% url "superadmin:dashboard_api" %}')
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    updateDashboardData(result.data);

                    // Update last refresh time
                    const now = new Date();
                    const timeString = now.toLocaleTimeString();

                    // Add or update last refresh indicator
                    let lastRefreshElement = document.querySelector('#last-refresh');
                    if (!lastRefreshElement) {
                        lastRefreshElement = document.createElement('small');
                        lastRefreshElement.id = 'last-refresh';
                        lastRefreshElement.className = 'text-muted position-fixed bottom-0 end-0 m-3';
                        document.body.appendChild(lastRefreshElement);
                    }
                    lastRefreshElement.textContent = `Last updated: ${timeString}`;
                }
            })
            .catch(error => {
                console.error('Error refreshing dashboard:', error);
                // Fallback to page reload on error
                if (showIndicator) {
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            })
            .finally(() => {
                isRefreshing = false;
                if (refreshIndicator) {
                    setTimeout(() => {
                        refreshIndicator.remove();
                    }, 1000);
                }
            });
    }

    // Set up auto-refresh
    setInterval(() => refreshDashboard(false), AUTO_REFRESH_INTERVAL);

    // Add manual refresh button
    const header = document.querySelector('.superadmin-header');
    if (header) {
        const refreshBtn = document.createElement('button');
        refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i>';
        refreshBtn.className = 'btn btn-outline-light btn-sm ms-2';
        refreshBtn.title = 'Refresh Dashboard';
        refreshBtn.onclick = () => refreshDashboard(true);

        const headerActions = header.querySelector('.d-flex') || header;
        headerActions.appendChild(refreshBtn);
    }

    // Highlight pending notifications if any
    const totalPending = {{ total_pending_notifications|default:"0" }};
    if (totalPending > 0) {
        // Add a subtle animation to the notification cards
        const notificationCards = document.querySelectorAll('.card-danger, .card-warning');
        notificationCards.forEach(card => {
            card.style.animation = 'pulse 2s infinite';
        });

        // Add CSS for pulse animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05); }
                50% { box-shadow: 0 8px 20px rgba(231, 74, 59, 0.3), 0 0 0 1px rgba(231, 74, 59, 0.2); }
                100% { box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05); }
            }
        `;
        document.head.appendChild(style);
    }

    // Add tooltips to metric cards
    const metricCards = document.querySelectorAll('.analytics-metric');
    metricCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const label = this.querySelector('.metric-label').textContent;
            const value = this.querySelector('.metric-value').textContent;

            // You could add more detailed tooltips here
            this.title = `${label}: ${value}`;
        });
    });
});
</script>
{% endblock superadmin_content %}
