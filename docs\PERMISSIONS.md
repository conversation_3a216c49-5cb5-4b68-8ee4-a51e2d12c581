# Permissions System Documentation

## Overview

The application uses Django's permission system along with django-guardian for object-level permissions. This allows for fine-grained control over what users can do within the application.

## Roles

There are four main roles in the system:

1. **Owner**: The user who created the company
2. **Administrator**: Users with administrative privileges for a company
3. **Member**: Regular users of a company
4. **Viewer**: Users with limited view-only access to a company

## Permission Types

The application uses two types of permissions:

1. **Model Permissions**: These are standard Django permissions that apply to all instances of a model. They are defined in the `Meta` class of each model.

2. **Object Permissions**: These are permissions that apply to specific instances of a model. They are managed using django-guardian.

## Permission Assignment

Permissions are assigned in several ways:

1. **Company Creation**: When a company is created, the owner is automatically assigned all owner permissions for that company.

2. **Assistant Creation**: When an assistant is created, permissions are assigned to the Company Administrators, Company Members, and Company Guests (Viewers) groups, as well as to the company owner.

3. **Group Membership**: Users are assigned to groups (Company Administrators, Company Members, Company Guests) which determine their role-based permissions.

4. **Management Commands**: There are several management commands to fix or update permissions:
   - `create_missing_permissions`: Creates missing permissions in the database
   - `fix_owner_assistant_permissions`: Ensures all owners have the correct permissions on their assistants
   - `add_viewer_permissions`: Adds view permissions for viewers on assistants

## Role Permissions

### Owner Permissions

The owner has the highest level of permissions and can perform all actions within their company:

- **Company Management**:
  - Can change company settings (`accounts.change_company_settings`)
  - Can manage billing (`accounts.manage_billing`)
  - Can manage directory listing (`accounts.manage_directory_listing`)
  - Can manage members (`accounts.manage_members`)
  - Can manage invitations and registration links (`accounts.manage_invites_links`)
  - Can view company activity (`accounts.view_company_activity`)
  - Can delete the company (`accounts.delete_company_object`)

- **Assistant Management**:
  - Can manage company assistants (`accounts.manage_company_assistants`)
  - Can view, change, and delete assistants (`assistants.view_assistant`, `assistants.change_assistant`, `assistants.delete_assistant`)
  - Can view assistant usage and analytics (`assistants.view_assistant_usage`, `assistants.view_assistant_analytics`)

- **Folder Management**:
  - Can add, change, view, and delete assistant folders (`accounts.add_assistantfolder`, `accounts.change_assistantfolder`, `accounts.view_assistantfolder`, `accounts.delete_assistantfolder`)
  - Can manage folder access (`accounts.manage_folder_access`)

### Administrator Permissions

Administrators have extensive permissions but cannot delete the company:

- **Company Management**:
  - Can manage company assistants (`accounts.manage_company_assistants`)
  - Can manage members (`accounts.manage_members`)
  - Cannot change company settings or delete the company

- **Assistant Management**:
  - Can view, change, and delete assistants (`assistants.view_assistant`, `assistants.change_assistant`, `assistants.delete_assistant`)
  - Can view assistant usage and analytics (`assistants.view_assistant_usage`, `assistants.view_assistant_analytics`)

### Member Permissions

Members have permissions to work with assistants but cannot manage the company:

- **Company Management**:
  - Can manage company assistants (`accounts.manage_company_assistants`)
  - Cannot change company settings or delete the company

- **Assistant Management**:
  - Can only view, change, and delete assistants they created (`assistants.view_assistant`, `assistants.change_assistant`, `assistants.delete_assistant`)
  - Can view assistant usage and analytics for assistants they created (`assistants.view_assistant_usage`, `assistants.view_assistant_analytics`)
  - Can view public assistants

### Viewer Permissions

Viewers have very limited permissions:

- **Company Management**:
  - No company management permissions

- **Assistant Management**:
  - Can view public assistants
  - Can view private assistants they have been specifically assigned to (`assistants.view_assistant`)
  - Cannot change or delete assistants

## Permission Checking

Permissions are checked in several ways:

1. **View Permissions**: In views, permissions are checked using `user.has_perm('permission_name', object)` to determine if a user can access a particular view or perform a particular action.

2. **Template Permissions**: In templates, permissions are checked using the `has_obj_perm` filter or the `check_perm` tag.

3. **Utility Functions**: There are utility functions like `can_view_assistant_usage` and `can_view_assistant_analytics` that check specific permissions.

## Public vs. Private Assistants

Assistants can be public or private:

- **Public Assistants**: These are accessible to all authenticated users, regardless of their permissions.
- **Private Assistants**: These are only accessible to users who have the appropriate permissions.

## Permission Management

To manage permissions, use the following management commands:

1. **Create Missing Permissions**:
   ```
   python manage.py create_missing_permissions
   ```

2. **Fix Owner Assistant Permissions**:
   ```
   python manage.py fix_owner_assistant_permissions
   ```

3. **Add Viewer Permissions**:
   ```
   python manage.py add_viewer_permissions
   ```

## Troubleshooting

If you encounter permission issues, check the following:

1. **User Group**: Make sure the user is in the correct group (Company Administrators, Company Members, Company Guests).

2. **Object Permissions**: Check if the user has the necessary object-level permissions using `get_perms(user, object)`.

3. **Missing Permissions**: Run the `create_missing_permissions` command to ensure all required permissions exist in the database.

4. **Owner Permissions**: Run the `fix_owner_assistant_permissions` command to ensure all owners have the correct permissions on their assistants.

5. **Viewer Permissions**: Run the `add_viewer_permissions` command to ensure all viewers have the correct permissions on assistants.

## Best Practices

1. **Always Check Permissions**: Always check permissions before allowing a user to perform an action.

2. **Use Object-Level Permissions**: Use object-level permissions to control access to specific instances of a model.

3. **Keep Permissions Simple**: Keep the permission system as simple as possible to avoid confusion.

4. **Document Permissions**: Document all permissions and their meanings to help developers understand the system.

5. **Test Permissions**: Write tests to ensure that permissions are working correctly.
