// <PERSON>ript to hide the welcome message and avatar when there are messages in the chat
// and show them again when the chat is reset
(function() {
    // Immediately hide the initial display area before anything else
    (function immediateHide() {
        // Check for existing messages in the DOM
        const existingMessages = document.querySelectorAll('.message:not(.initial-greeting)');
        const userMessages = document.querySelectorAll('.user-message');
        if (existingMessages.length > 0 || userMessages.length > 0) {
            const initialDisplayArea = document.getElementById('initial-display-area');
            if (initialDisplayArea) {
                initialDisplayArea.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; height: 0 !important; overflow: hidden !important; margin: 0 !important; padding: 0 !important;';
                console.log('Initial display area immediately hidden due to existing messages');
            }
        }
    })();

    // Function to hide the initial display area
    function hideInitialDisplayArea() {
        const initialDisplayArea = document.getElementById('initial-display-area');
        if (initialDisplayArea) {
            initialDisplayArea.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; height: 0 !important; overflow: hidden !important; margin: 0 !important; padding: 0 !important;';
            console.log('Initial display area hidden by hide-welcome-message.js');
        }
    }

    // Check if we should hide the initial display area based on chat history
    function shouldHideInitialDisplayArea() {
        // Get assistant ID for session storage key
        const assistantId = document.querySelector('meta[name="assistant-id"]')?.content || 'default';
        const sessionStorageKey = `chatHistory_${assistantId}`;

        // Check if there's existing chat history
        const storedHistory = sessionStorage.getItem(sessionStorageKey);

        if (storedHistory) {
            try {
                const history = JSON.parse(storedHistory);
                return Array.isArray(history) && history.length > 0;
            } catch (e) {
                console.error('Failed to parse chat history:', e);
                return false;
            }
        }

        return false;
    }

    // Function to check if there are messages in the chat DOM
    function checkForMessages() {
        // Get all message elements except the initial greeting
        const messages = document.querySelectorAll('.message:not(.initial-greeting)');

        // Also check specifically for user messages which should always hide the welcome
        const userMessages = document.querySelectorAll('.user-message');

        // If there are any messages or user messages, hide the initial display area
        if (messages.length > 0 || userMessages.length > 0) {
            hideInitialDisplayArea();
            console.log('Messages found in the DOM, hiding initial display area');
            return true;
        }

        // Check if there's any text content in the chat box that might indicate messages
        const chatBox = document.getElementById('chat-box');
        if (chatBox) {
            // Look for any divs that might contain messages but don't have the right class
            const possibleMessages = chatBox.querySelectorAll('div:not(#initial-display-area)');
            for (let i = 0; i < possibleMessages.length; i++) {
                const node = possibleMessages[i];
                // If it has text content and isn't part of the initial display area
                if (node.textContent.trim() &&
                    !node.closest('#initial-display-area') &&
                    !node.classList.contains('initial-greeting')) {
                    hideInitialDisplayArea();
                    console.log('Possible message content found, hiding initial display area');
                    return true;
                }
            }
        }

        return false;
    }

    // Function to show the initial display area
    function showInitialDisplayArea() {
        const initialDisplayArea = document.getElementById('initial-display-area');
        if (initialDisplayArea) {
            // Make sure it's fully visible with all necessary properties
            initialDisplayArea.style.cssText = 'display: block !important; visibility: visible !important; opacity: 1 !important; height: auto !important; overflow: visible !important; margin-bottom: 1rem !important; padding: 0 !important;';

            // Also make sure all child elements are visible with proper styling
            const children = initialDisplayArea.querySelectorAll('*');
            children.forEach(child => {
                if (child.classList.contains('assistant-profile-pic')) {
                    child.style.cssText = 'display: inline-block !important; visibility: visible !important; opacity: 1 !important; width: 190px !important; height: 190px !important; border-radius: 50% !important; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important; border: 4px solid rgba(255, 255, 255, 0.2) !important; object-fit: cover !important;';
                } else if (child.classList.contains('assistant-profile-pic-placeholder')) {
                    child.style.cssText = 'display: inline-block !important; visibility: visible !important; opacity: 1 !important; font-size: 190px !important; width: 190px !important; height: 190px !important; line-height: 190px !important; text-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;';
                } else if (child.classList.contains('initial-greeting') || child.classList.contains('welcome-text-small')) {
                    child.style.cssText = 'display: block !important; visibility: visible !important; opacity: 1 !important;';
                }
            });

            console.log('Initial display area shown by hide-welcome-message.js');
        }
    }

    // Export the show/hide functions to the global scope so they can be called from other scripts
    window.hideWelcomeMessage = hideInitialDisplayArea;
    window.showWelcomeMessage = showInitialDisplayArea;

    // Run immediately when the script loads
    function init() {
        // Check if we should hide the initial display area based on session storage
        if (shouldHideInitialDisplayArea()) {
            hideInitialDisplayArea();
        } else {
            // Check for messages in the DOM
            if (checkForMessages()) {
                hideInitialDisplayArea();
            } else {
                // Make sure the initial display area is visible if no messages or history
                showInitialDisplayArea();
            }
        }

        // Set up a mutation observer to watch for new messages
        const chatBox = document.getElementById('chat-box');
        if (chatBox) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        // Check if any of the added nodes are messages
                        for (let i = 0; i < mutation.addedNodes.length; i++) {
                            const node = mutation.addedNodes[i];
                            if (node.classList &&
                                node.classList.contains('message') &&
                                !node.classList.contains('initial-greeting')) {
                                // Only hide if it's a user message or if there are multiple messages
                                if (node.classList.contains('user-message') ||
                                    document.querySelectorAll('.message:not(.initial-greeting)').length > 1) {
                                    hideInitialDisplayArea();
                                    return;
                                }
                            }
                        }
                    }
                });
            });

            // Start observing the chat box for changes
            observer.observe(chatBox, { childList: true, subtree: true });
        }

        // Set up event listeners for the send button and form submission
        const sendButton = document.getElementById('send-button');
        if (sendButton) {
            sendButton.addEventListener('click', function() {
                const messageInput = document.getElementById('message-input');
                if (messageInput && messageInput.value.trim()) {
                    hideInitialDisplayArea();
                }
            });
        }

        const chatForm = document.getElementById('chat-form');
        if (chatForm) {
            chatForm.addEventListener('submit', function(e) {
                const messageInput = document.getElementById('message-input');
                if (messageInput && messageInput.value.trim()) {
                    hideInitialDisplayArea();
                }
            });
        }

        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey && messageInput.value.trim()) {
                    hideInitialDisplayArea();
                }
            });
        }

        // Listen for reset button clicks to show the welcome message again
        const resetButton = document.getElementById('reset-chat-btn');
        if (resetButton) {
            resetButton.addEventListener('click', function() {
                // Show the welcome message when the chat is reset
                showInitialDisplayArea();
            });
        }
    }

    // Run the initialization when the DOM is fully loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();
