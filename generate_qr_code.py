#!/usr/bin/env python
"""
QR Code Generator Script

This script generates QR codes with an "A" logo in the center for assistants and companies.
It can be run from the command line to generate QR codes for specific entities.

Usage:
    python generate_qr_code.py <assistant_id>
    python generate_qr_code.py --company <company_id>
    python generate_qr_code.py --all-assistants
    python generate_qr_code.py --all-companies
    python generate_qr_code.py --help
"""

import os
import sys
import django
import argparse
from io import BytesIO
from PIL import Image

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Import Django models and utilities after setup
from django.urls import reverse
from django.contrib.sites.models import Site
from assistants.models import Assistant
from accounts.models import Company
from utils.qr_generator import generate_qr_with_a, generate_model_qr_code


def generate_assistant_qr(assistant_id, save_to_model=True, output_path=None):
    """
    Generate a QR code for an assistant.

    Args:
        assistant_id: The ID of the assistant
        save_to_model: Whether to save the QR code to the assistant model
        output_path: Path to save the QR code image to (optional)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get the assistant
        assistant = Assistant.objects.get(id=assistant_id)
        print(f"Generating QR code for assistant: {assistant.name} (ID: {assistant.id})")

        # Generate QR code
        url_path = reverse('assistants:assistant_chat', kwargs={'slug': assistant.slug})

        if save_to_model:
            # Save to model
            success = generate_model_qr_code(assistant, url_path, field_name='qr_code')
            if success:
                assistant.save(update_fields=['qr_code'])
                print(f"QR code saved to assistant model. URL: {assistant.qr_code.url}")

                # Save to file if output_path is provided
                if output_path:
                    with open(output_path, 'wb') as f:
                        f.write(assistant.qr_code.read())
                    print(f"QR code saved to file: {output_path}")

                return True
            else:
                print("Failed to generate QR code")
                return False
        else:
            # Generate QR code without saving to model
            current_site = Site.objects.get_current()
            if not url_path.startswith('/'):
                url_path = '/' + url_path
            full_url = f"http://{current_site.domain}{url_path}"

            qr_img = generate_qr_with_a(full_url)

            if output_path:
                qr_img.save(output_path)
                print(f"QR code saved to file: {output_path}")
            else:
                # Save to a default location
                default_path = f"assistant_qr_{assistant.id}.png"
                qr_img.save(default_path)
                print(f"QR code saved to file: {default_path}")

            return True
    except Assistant.DoesNotExist:
        print(f"Error: Assistant with ID {assistant_id} not found")
        return False
    except Exception as e:
        print(f"Error generating QR code: {e}")
        return False


def generate_company_qr(company_id, save_to_model=True, output_path=None):
    """
    Generate a QR code for a company.

    Args:
        company_id: The ID of the company
        save_to_model: Whether to save the QR code to the company model
        output_path: Path to save the QR code image to (optional)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get the company
        company = Company.objects.get(id=company_id)
        print(f"Generating QR code for company: {company.name} (ID: {company.id})")

        # Generate QR code
        try:
            # Try the new URL pattern first
            url_path = reverse('accounts:company_detail', kwargs={'slug': company.slug})
        except:
            # Fall back to the old URL pattern
            url_path = f"/accounts/company/{company.id}/"
            print(f"Using fallback URL path: {url_path}")

        if save_to_model:
            # Save to model
            success = generate_model_qr_code(company, url_path, field_name='qr_code')
            if success:
                company.save(update_fields=['qr_code'])
                print(f"QR code saved to company model. URL: {company.qr_code.url}")

                # Save to file if output_path is provided
                if output_path:
                    with open(output_path, 'wb') as f:
                        f.write(company.qr_code.read())
                    print(f"QR code saved to file: {output_path}")

                return True
            else:
                print("Failed to generate QR code")
                return False
        else:
            # Generate QR code without saving to model
            current_site = Site.objects.get_current()
            if not url_path.startswith('/'):
                url_path = '/' + url_path
            full_url = f"http://{current_site.domain}{url_path}"

            qr_img = generate_qr_with_a(full_url)

            if output_path:
                qr_img.save(output_path)
                print(f"QR code saved to file: {output_path}")
            else:
                # Save to a default location
                default_path = f"company_qr_{company.id}.png"
                qr_img.save(default_path)
                print(f"QR code saved to file: {default_path}")

            return True
    except Company.DoesNotExist:
        print(f"Error: Company with ID {company_id} not found")
        return False
    except Exception as e:
        print(f"Error generating QR code: {e}")
        return False


def generate_all_assistant_qrs():
    """Generate QR codes for all assistants that don't have one."""
    assistants = Assistant.objects.filter(qr_code='')
    total = assistants.count()

    print(f"Found {total} assistants that need QR codes")

    success_count = 0
    fail_count = 0

    for i, assistant in enumerate(assistants, 1):
        print(f"Processing {i}/{total}: {assistant.name} (ID: {assistant.id})")

        success = generate_assistant_qr(assistant.id)

        if success:
            success_count += 1
        else:
            fail_count += 1

    print("\nSummary:")
    print(f"Successfully generated QR codes for {success_count} assistants")
    if fail_count > 0:
        print(f"Failed to generate QR codes for {fail_count} assistants")
    else:
        print("All QR codes were generated successfully")


def generate_all_company_qrs():
    """Generate QR codes for all companies that don't have one."""
    companies = Company.objects.filter(qr_code='')
    total = companies.count()

    print(f"Found {total} companies that need QR codes")

    success_count = 0
    fail_count = 0

    for i, company in enumerate(companies, 1):
        print(f"Processing {i}/{total}: {company.name} (ID: {company.id})")

        success = generate_company_qr(company.id)

        if success:
            success_count += 1
        else:
            fail_count += 1

    print("\nSummary:")
    print(f"Successfully generated QR codes for {success_count} companies")
    if fail_count > 0:
        print(f"Failed to generate QR codes for {fail_count} companies")
    else:
        print("All QR codes were generated successfully")


def main():
    """Main function to parse arguments and generate QR codes."""
    parser = argparse.ArgumentParser(description='Generate QR codes with "A" logo for assistants and companies.')

    # Add arguments
    parser.add_argument('assistant_id', nargs='?', type=int, help='ID of the assistant to generate a QR code for')
    parser.add_argument('--company', '-c', type=int, help='ID of the company to generate a QR code for')
    parser.add_argument('--all-assistants', '-a', action='store_true', help='Generate QR codes for all assistants')
    parser.add_argument('--all-companies', '-C', action='store_true', help='Generate QR codes for all companies')
    parser.add_argument('--output', '-o', help='Path to save the QR code image to')
    parser.add_argument('--no-save', '-n', action='store_true', help='Do not save the QR code to the model')

    # Parse arguments
    args = parser.parse_args()

    # Check if any action was specified
    if not (args.assistant_id or args.company or args.all_assistants or args.all_companies):
        parser.print_help()
        return

    # Generate QR codes
    if args.assistant_id:
        generate_assistant_qr(args.assistant_id, not args.no_save, args.output)
    elif args.company:
        generate_company_qr(args.company, not args.no_save, args.output)
    elif args.all_assistants:
        generate_all_assistant_qrs()
    elif args.all_companies:
        generate_all_company_qrs()


if __name__ == '__main__':
    main()
