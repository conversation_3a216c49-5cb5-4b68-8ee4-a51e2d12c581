{% load static %}
{% load account_tags %}
{% load guardian_tags %} {# Load guardian tags for permission checks #}
{% load permission_tags %} {# Load custom permission tags #}
{% load rating_tags %} {# Load rating tags for star ratings #}
<!DOCTYPE html>
<html lang="en" style="background-color: #121212 !important;" data-theme="dark">
<head>
    <!-- Immediate dark background to prevent white flash -->
    <style>
        html, body, main { background-color: #121212 !important; color: #ffffff !important; }
    </style>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <meta name="color-scheme" content="dark">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    {% if request.user.is_authenticated and request.real_user and request.real_user.is_authenticated and request.real_user.id != request.user.id %}
    <meta name="is-impersonating" content="true">
    <meta name="impersonated-user" content="{{ request.user.get_username }}">
    <meta name="real-user" content="{{ request.real_user.get_username }}">
    <script>
        // Add global variables for impersonation detection
        window.realUserUsername = "{{ request.real_user.get_username }}";
        window.currentUsername = "{{ request.user.get_username }}";
        window.isImpersonating = true;

        // Add debug info object
        window.debug_impersonation_info = {
            is_impersonate: true,
            real_user_username: "{{ request.real_user.get_username }}",
            impersonated_user_username: "{{ request.user.get_username }}"
        };
    </script>
    {% endif %}
    <title>{% block title %}24seven{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{% static 'img/24seven-logo.svg' %}">

    <!-- Early-loading CSS for dark background fixes -->
    <link href="{% static 'css/prevent-white-flash.css' %}" rel="stylesheet">
    <link href="{% static 'css/filter-background-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/community-dashboard-dark.css' %}" rel="stylesheet">
    <link href="{% static 'css/facebook-style-dark-mode.css' %}" rel="stylesheet">
    <link href="{% static 'css/force-dark-mode.css' %}" rel="stylesheet">

    <!-- Early-loading JavaScript for dark background fixes -->
    <script src="{% static 'js/dark-mode-performance-fix.js' %}"></script>
    <script src="{% static 'js/prevent-white-flash.js' %}"></script>
    <script src="{% static 'js/force-dark-mode.js' %}"></script>
    <script src="{% static 'js/filter-background-fix.js' %}"></script>
    <script src="{% static 'js/http-protocol-fix.js' %}"></script>
    <script src="{% static 'js/toast-notification.js' %}"></script>

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    <link href="{% static 'css/global-tinymce.css' %}" rel="stylesheet">
    <link href="{% static 'css/directory-theme.css' %}" rel="stylesheet">
    <link href="{% static 'css/featured-carousel.css' %}" rel="stylesheet">
    <link href="{% static 'css/impersonation.css' %}" rel="stylesheet">
    <link href="{% static 'css/eye-friendly-theme.css' %}" rel="stylesheet">
    <link href="{% static 'css/enhanced-dark-mode.css' %}" rel="stylesheet">
    <link href="{% static 'css/theme-transitions.css' %}" rel="stylesheet">
    <link href="{% static 'css/body-background-transition.css' %}" rel="stylesheet">
    <link href="{% static 'css/theme-toggle-button.css' %}" rel="stylesheet">
    <link href="{% static 'css/directory-dark-mode.css' %}" rel="stylesheet">
    <link href="{% static 'css/directory-cards-dark.css' %}" rel="stylesheet">
    <link href="{% static 'css/white-sections-dark.css' %}" rel="stylesheet">
    <link href="{% static 'css/homepage-dark-mode.css' %}" rel="stylesheet">
    <link href="{% static 'css/assistants-list-dark-mode.css' %}" rel="stylesheet">
    <link href="{% static 'css/vibrant-blue.css' %}" rel="stylesheet">
    <link href="{% static 'css/community-badge.css' %}" rel="stylesheet">
    <link href="{% static 'css/category-badge.css' %}" rel="stylesheet">
    <link href="{% static 'css/rating-compact.css' %}" rel="stylesheet">
    <link href="{% static 'css/community-buttons.css' %}" rel="stylesheet">
    <link href="{% static 'css/badge-visibility.css' %}" rel="stylesheet">
    <link href="{% static 'css/header-text-enhancement.css' %}" rel="stylesheet">
    <link href="{% static 'css/header-glass-buttons.css' %}" rel="stylesheet">
    <link href="{% static 'css/dark-mode.css' %}" rel="stylesheet">
    <link href="{% static 'css/chat-bubbles-dark-mode.css' %}" rel="stylesheet">
    <link href="{% static 'css/chat-transitions.css' %}" rel="stylesheet">
    <link href="{% static 'css/chat-container-glass.css' %}" rel="stylesheet">
    <link href="{% static 'css/message-content-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/chat-scroll-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/remove-purple-space.css' %}" rel="stylesheet">
    <link href="{% static 'css/desktop-mode-restore.css' %}" rel="stylesheet">
    <link href="{% static 'css/header-icons-dark-mode.css' %}" rel="stylesheet">
    <link href="{% static 'css/assistant-buttons-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/mobile-responsive.css' %}" rel="stylesheet">
    <link href="{% static 'css/mobile-navbar.css' %}" rel="stylesheet">
    <link href="{% static 'css/touch-friendly.css' %}" rel="stylesheet">
    <link href="{% static 'css/landscape-mode-optimization.css' %}" rel="stylesheet">
    <link href="{% static 'css/container-3d-glow.css' %}" rel="stylesheet">
    <link href="{% static 'css/floating-buttons-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/modal-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/folder-modal-dark.css' %}" rel="stylesheet">
    <link href="{% static 'css/like-button-enhanced.css' %}" rel="stylesheet">
    <link href="{% static 'css/heart-icon-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/dark-mode-heart-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/white-hearts-override.css' %}" rel="stylesheet">
    <link href="{% static 'css/navbar-dropdown-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/like-button-text-hide.css' %}" rel="stylesheet">
    <link href="{% static 'css/navbar-toggle-dark.css' %}" rel="stylesheet">
    <link href="{% static 'css/navbar-toggle-mobile-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/company-settings-dark-mode.css' %}" rel="stylesheet">
    <link href="{% static 'css/glass-notifications.css' %}" rel="stylesheet">
    <link href="{% static 'css/category-dropdowns.css' %}" rel="stylesheet">
    {% block extra_css %}{% endblock %}

    <!-- Direct style override for heart icons in dark mode -->
    <style>
        [data-theme="dark"] .like-button svg,
        [data-theme="dark"] .btn-like svg,
        [data-theme="dark"] .btn-favorite svg,
        [data-theme="dark"] .favorite-button svg,
        [data-theme="dark"] .like-button:not(.text-danger) svg path,
        [data-theme="dark"] .btn-like:not(.text-danger) svg path,
        [data-theme="dark"] .btn-favorite:not(.text-danger) svg path,
        [data-theme="dark"] .favorite-button:not(.text-danger) svg path {
            fill: #ffffff !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .like-button.text-danger svg,
        [data-theme="dark"] .btn-like.text-danger svg,
        [data-theme="dark"] .btn-favorite.text-danger svg,
        [data-theme="dark"] .favorite-button.text-danger svg,
        [data-theme="dark"] .like-button.text-danger svg path,
        [data-theme="dark"] .btn-like.text-danger svg path,
        [data-theme="dark"] .btn-favorite.text-danger svg path,
        [data-theme="dark"] .favorite-button.text-danger svg path {
            fill: #ff3366 !important;
            color: #ff3366 !important;
        }
    </style>

    <!-- Meta tags -->
    <meta name="description" content="{% block meta_description %}24seven - AI-powered virtual assistant platform{% endblock %}">
    <meta name="theme-color" content="#121212">

    <!-- Open Graph tags -->
    <meta property="og:title" content="{% block og_title %}{{ self.title }}{% endblock %}">
    <meta property="og:description" content="{% block og_description %}{{ self.meta_description }}{% endblock %}">
    <meta property="og:image" content="{% block og_image %}{% static 'img/og-image.svg' %}{% endblock %}">

    <!-- CSRF Token for AJAX requests -->
    <meta name="csrf-token" content="{{ csrf_token }}">

     <!-- REMOVED: TinyMCE CDN Script -->

     <!-- Form Media (for widgets like TinyMCE) -->
     {{ form.media }}
</head>
<body class="dark-mode {% block body_class %}{% endblock %}{% if request.user.is_authenticated and request.real_user and request.real_user.is_authenticated and request.real_user.id != request.user.id %} is-impersonating{% endif %}" style="background-color: #121212 !important; {% block body_style %}{% endblock %}" data-theme="dark">
    <!-- Hidden debug elements for impersonation detection -->
    {% if request.user.is_authenticated and request.real_user and request.real_user.is_authenticated and request.real_user.id != request.user.id %}
    <div id="debug-real-user" style="display:none;">{{ request.real_user.username }}</div>
    <div id="debug-current-user" style="display:none;">{{ request.user.username }}</div>
    {% endif %}
    {% block navbar %}
    <nav class="navbar navbar-expand-lg navbar-light sticky-top">
        <div class="container">
            <!-- Logo -->
            <a class="navbar-brand" href="{% url 'home' %}">
                <i class="bi bi-house me-2"></i> Home
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarContent" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation -->
            <div class="collapse navbar-collapse" id="navbarContent">
                {% if user.is_authenticated %}
                    <!-- Main Navigation -->
                    <ul class="navbar-nav me-auto">
                        {# Show Dashboard link only if user has permission for the active company and is not a superuser #}
                        {# Permission check moved to context processor #}
                        {% if can_view_active_dashboard and not request.user.is_superuser %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'accounts:dashboard' %}">
                                <span class="nav-icon-container">
                                    <i class="bi bi-speedometer2"></i>
                                </span>
                                Dashboard
                            </a>
                        </li>
                        {% endif %}
                        {# Links requiring permission (implicitly requires active company via context processor) #}
                        {# --- START SUPERUSER MODIFICATION for Manage Link --- #}
                        {% if request.user.is_superuser %}
                            <li class="nav-item">
                                {# Superuser sees Manage All Assistants as the dashboard link #}
                                <a class="nav-link" href="{% url 'superadmin:assistant_list' %}">
                                    <span class="nav-icon-container">
                                        <i class="bi bi-speedometer2"></i>
                                    </span>
                                    Dashboard
                                </a>
                            </li>
                            {% if active_company %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'assistants:list' company_id=active_company.id %}">
                                    <span class="nav-icon-container">
                                        <i class="bi bi-robot"></i>
                                    </span>
                                    Manage ({{ active_company.name }})
                                </a>
                            </li>
                            {% endif %}
                        {% elif can_manage_active_assistants %}
                            {# Original logic for non-superusers #}
                            <li class="nav-item">
                                {% if active_company %}
                                <a class="nav-link" href="{% url 'assistants:list' company_id=active_company.id %}">
                                    <span class="nav-icon-container">
                                        <i class="bi bi-robot"></i>
                                    </span>
                                    Manage
                                </a>
                                {% endif %}
                            </li>
                        {% endif %}
                        {# --- END SUPERUSER MODIFICATION for Manage Link --- #}
                        {# Removed Content Link #}
                        {# Removed Team Link #}
                        {# Directory Links for logged-in users #}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'directory:company_list' %}">
                                <span class="nav-icon-container">
                                    <i class="bi bi-building"></i>
                                </span>
                                Companies
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'directory:assistant_list' %}">
                                <span class="nav-icon-container">
                                    <i class="bi bi-robot"></i>
                                </span>
                                Assistants Directory
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'directory:my_favorites' %}">
                                <span class="nav-icon-container">
                                    <i class="bi bi-heart"></i>
                                </span>
                                My Favorites
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'assistants:community_assistants_list' %}">
                                <span class="nav-icon-container">
                                    <i class="bi bi-people"></i>
                                </span>
                                Community Assistants
                            </a>
                        </li>

                    </ul>

                    <!-- User Menu -->
                    <ul class="navbar-nav">
                        <!-- Company Switcher -->
                        {% if all_user_companies %} {# MODIFIED: Check if the user is associated with any company #}
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                                    {{ active_company.name|default:"Select Company" }}
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    {# Assume 'all_user_companies' (owned + member) is passed in context #}
                                    {% for company in all_user_companies %} {# MODIFIED: Use all_user_companies #}
                                        <li>
                                            <a class="dropdown-item {% if company == active_company %}active{% endif %}"
                                               href="{% url 'accounts:company_switch' %}?company={{ company.id }}">
                                                {{ company.name }} ({{ request.user|company_role:company|title }}) {# MODIFIED: Display role #}
                                            </a>
                                        </li>
                                    {% endfor %}
                                    {# Create Company link moved to user dropdown #}
                                </ul>
                            </li>
                        {% endif %}

                        <!-- User Dropdown -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" data-bs-toggle="dropdown"> {# Added d-flex align-items-center #}
                                {# Check for profile avatar first, then Gravatar #}
                                {% if user.profile.avatar %}
                                    <img src="{{ user.profile.avatar.url }}"
                                         class="rounded-circle me-2" width="24" height="24" {# Increased margin slightly #}
                                         alt="{{ user.username }} avatar">
                                {% elif user.email %}
                                    <img src="{{ user.email|gravatar_url:32 }}"
                                         class="rounded-circle me-2" width="24" height="24" {# Increased margin slightly #}
                                         alt="{{ user.username }} gravatar">
                                {% else %}
                                    <i class="bi bi-person-circle"></i>
                                {% endif %}
                                {{ user.get_full_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li> {# MOVED: Create Company here #}
                                    <a class="dropdown-item" href="{% url 'accounts:company_create' %}?type=company">
                                        <i class="bi bi-plus-circle"></i> Create Company
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'accounts:company_create' %}?type=community">
                                        <i class="bi bi-people"></i> Create Community
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'accounts:user_settings' %}">
                                        <i class="bi bi-gear"></i> Settings
                                    </a>
                                </li>
                                {% if active_company and user == active_company.owner %}
                                    <li>
                                        <a class="dropdown-item" href="{% url 'accounts:company_settings' active_company.id %}">
                                            <i class="bi bi-building-gear"></i> Company Settings
                                        </a>
                                    </li>
                                {% endif %}
                                {% if request.user.is_superuser %}
                                    <li>
                                        <a class="dropdown-item" href="{% url 'superadmin:dashboard' %}">
                                            <i class="bi bi-shield-lock"></i> Superadmin
                                        </a>
                                    </li>
                                {% endif %}

                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="post" action="{% url 'accounts:logout' %}">
                                        {% csrf_token %}
                                        <button type="submit" class="dropdown-item text-danger">
                                            <i class="bi bi-box-arrow-right"></i> Log Out
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </li>
                    </ul>
                {% else %}
                    <!-- Public Navigation -->
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'about' %}">About</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'pricing' %}">Pricing</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ site_config.contact_url|default:'/contact/' }}">Contact</a>
                        </li>
                        {# Directory Links for public users #}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'directory:company_list' %}">Companies</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'directory:assistant_list' %}">Assistants</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'assistants:community_assistants_list' %}">Community Assistants</a>
                        </li>

                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'accounts:login' %}">Log In</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary" href="{% url 'accounts:register' %}">Sign Up</a>
                        </li>
                    </ul>
                {% endif %}
            </div>
        </div>
    </nav>
    {% endblock %}

    {% if request.user.is_authenticated and request.real_user and request.real_user.is_authenticated and request.real_user.id != request.user.id %}
    <div class="w-100 text-center py-2" id="impersonation-alert"
         style="background-color: #dc3545; color: white; z-index: 9999; display: flex !important; justify-content: center; align-items: center; position: fixed; top: 0; left: 0; right: 0;">
        <div style="flex-grow: 1; text-align: center;">
            <i class="bi bi-person-badge"></i>
            <strong>IMPERSONATION MODE:</strong> You are currently impersonating <strong>{{ request.user.get_username }}</strong>.
            {% if request.real_user.is_superuser %}(Logged in as a superadmin){% endif %}
        </div>
        <div style="position: absolute; right: 10px;">
            <a href="{% url 'impersonate-stop' %}" class="btn btn-sm btn-outline-light">
                Stop Impersonating
            </a>
        </div>
    </div>
    <style>
        /* Adjust body and navbar when impersonation is active */
        body.is-impersonating {
            padding-top: 40px !important; /* Height of the impersonation banner */
        }
        body.is-impersonating .navbar.sticky-top {
            top: 40px; /* Position navbar below the impersonation banner */
        }
    </style>
    {% endif %}

    <!-- Impersonation alert is handled in the main script -->

    <!-- Messages -->
    {% if messages %}
        <div class="message-container">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main class="dark-mode" style="background-color: #121212 !important; color: #ffffff;">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    {% block footer %}
    <footer class="footer">
        <div class="container">
            <!-- Desktop Footer (hidden on mobile) -->
            <div class="row d-none d-md-flex">
                <div class="col-md-4">
                    <h5>24seven</h5>
                    <p class="text-muted small">
                        AI-powered virtual assistant platform designed to enhance productivity and streamline workflows.
                    </p>
                    <div class="social-icons">
                        <a href="#" aria-label="Twitter"><i class="bi bi-twitter"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="bi bi-linkedin"></i></a>
                        <a href="#" aria-label="GitHub"><i class="bi bi-github"></i></a>
                    </div>
                </div>
                <div class="col-md-2">
                    <h6>Product</h6>
                    <ul class="list-unstyled small">
                        <li><a href="{% url 'features' %}">Features</a></li>
                        <li><a href="{% url 'pricing' %}">Pricing</a></li>
                        <li><a href="{% url 'security' %}">Security</a></li>
                    </ul>
                </div>
                <div class="col-md-2">
                    <h6>Company</h6>
                    <ul class="list-unstyled small">
                        <li><a href="{% url 'about' %}">About Us</a></li>
                        <li><a href="{{ site_config.contact_url|default:'/contact/' }}">Contact</a></li>
                        <li><a href="{% url 'careers' %}">Careers</a></li>
                    </ul>
                </div>
                <div class="col-md-2">
                    <h6>Legal</h6>
                    <ul class="list-unstyled small">
                        <li><a href="{% url 'privacy' %}">Privacy</a></li>
                        <li><a href="{% url 'terms' %}">Terms</a></li>
                        <li><a href="{% url 'cookies' %}">Cookies</a></li>
                    </ul>
                </div>
                <div class="col-md-2">
                    <h6>Resources</h6>
                    <ul class="list-unstyled small">
                        <li><a href="#">Documentation</a></li>
                        <li><a href="{{ site_config.contact_url|default:'/contact/' }}">Support</a></li>
                        <li><a href="#">Blog</a></li>
                    </ul>
                </div>
            </div>

            <!-- Mobile Footer with Accordion (visible only on mobile) -->
            <div class="d-md-none footer-accordion">
                <div class="social-icons-mobile mb-3">
                    <a href="#" aria-label="Twitter"><i class="bi bi-twitter"></i></a>
                    <a href="#" aria-label="LinkedIn"><i class="bi bi-linkedin"></i></a>
                    <a href="#" aria-label="GitHub"><i class="bi bi-github"></i></a>
                </div>

                <div class="accordion" id="footerAccordion">
                    <!-- Product Section -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="productHeading">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#productCollapse" aria-expanded="false" aria-controls="productCollapse">
                                Product
                            </button>
                        </h2>
                        <div id="productCollapse" class="accordion-collapse collapse" aria-labelledby="productHeading" data-bs-parent="#footerAccordion">
                            <div class="accordion-body">
                                <ul class="list-unstyled small">
                                    <li><a href="{% url 'features' %}">Features</a></li>
                                    <li><a href="{% url 'pricing' %}">Pricing</a></li>
                                    <li><a href="{% url 'security' %}">Security</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Company Section -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="companyHeading">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#companyCollapse" aria-expanded="false" aria-controls="companyCollapse">
                                Company
                            </button>
                        </h2>
                        <div id="companyCollapse" class="accordion-collapse collapse" aria-labelledby="companyHeading" data-bs-parent="#footerAccordion">
                            <div class="accordion-body">
                                <ul class="list-unstyled small">
                                    <li><a href="{% url 'about' %}">About Us</a></li>
                                    <li><a href="{{ site_config.contact_url|default:'/contact/' }}">Contact</a></li>
                                    <li><a href="{% url 'careers' %}">Careers</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Legal Section -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="legalHeading">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#legalCollapse" aria-expanded="false" aria-controls="legalCollapse">
                                Legal
                            </button>
                        </h2>
                        <div id="legalCollapse" class="accordion-collapse collapse" aria-labelledby="legalHeading" data-bs-parent="#footerAccordion">
                            <div class="accordion-body">
                                <ul class="list-unstyled small">
                                    <li><a href="{% url 'privacy' %}">Privacy</a></li>
                                    <li><a href="{% url 'terms' %}">Terms</a></li>
                                    <li><a href="{% url 'cookies' %}">Cookies</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Resources Section -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="resourcesHeading">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#resourcesCollapse" aria-expanded="false" aria-controls="resourcesCollapse">
                                Resources
                            </button>
                        </h2>
                        <div id="resourcesCollapse" class="accordion-collapse collapse" aria-labelledby="resourcesHeading" data-bs-parent="#footerAccordion">
                            <div class="accordion-body">
                                <ul class="list-unstyled small">
                                    <li><a href="#">Documentation</a></li>
                                    <li><a href="{{ site_config.contact_url|default:'/contact/' }}">Support</a></li>
                                    <li><a href="#">Blog</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <hr class="my-4 my-md-4 my-2">
            <div class="text-center text-muted small py-1">
                © {% now "Y" %} 24seven. All rights reserved.
            </div>
        </div>
    </footer>
    {% endblock %}

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>




    <script>
        // CSRF token setup for AJAX requests
        const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

        // Helper function (can be defined once)
        function getCsrfToken() {
            const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
            if (!csrfInput) {
                const csrfMeta = document.querySelector('meta[name="csrf-token"]');
                if (csrfMeta) return csrfMeta.getAttribute('content');
            }
            return csrfInput ? csrfInput.value : null; // Use the one from meta tag if input not found
        }

        document.addEventListener('DOMContentLoaded', function() {

            // Auto-dismiss alerts (except impersonation alert)
            document.querySelectorAll('.alert:not(.alert-warning[role="alert"])').forEach(alert => {
                setTimeout(() => {
                    const closeButton = alert.querySelector('.btn-close');
                    if (closeButton) closeButton.click();
                }, 5000);
            });

            // Ensure impersonation alert is always visible
            const impersonationAlert = document.getElementById('impersonation-alert');
            if (impersonationAlert) {
                // Add class to body for proper spacing
                document.body.classList.add('is-impersonating');

                // Check visibility once after a short delay
                setTimeout(() => {
                    if (impersonationAlert.style.display === 'none' ||
                        impersonationAlert.style.visibility === 'hidden' ||
                        impersonationAlert.style.opacity === '0') {
                        impersonationAlert.style.display = 'flex';
                        impersonationAlert.style.visibility = 'visible';
                        impersonationAlert.style.opacity = '1';
                    }
                }, 1000);

                // Check visibility periodically but at a much lower frequency
                setInterval(() => {
                    if (impersonationAlert.style.display === 'none' ||
                        impersonationAlert.style.visibility === 'hidden' ||
                        impersonationAlert.style.opacity === '0') {
                        impersonationAlert.style.display = 'flex';
                        impersonationAlert.style.visibility = 'visible';
                        impersonationAlert.style.opacity = '1';
                    }
                }, 10000);
            }

            // Common AJAX error handler (optional)
            window.handleAjaxError = function(error) {
                const message = error.responseJSON?.message || 'An error occurred. Please try again.';
                alert(message);
            };

            // Rating Modal JS removed from here
        });

        // Unified Rating Modal Logic
        const ratingModalElement = document.getElementById('ratingModal');
        if (ratingModalElement) {
            const modalStarsContainer = ratingModalElement.querySelector('.modal-stars');
            const submitRatingBtn = ratingModalElement.querySelector('#submitRatingBtn');
            const modalErrorMsg = ratingModalElement.querySelector('#modalErrorMsg');
            let ratingAssistantId = null;
            let selectedRating = 0;

            // Set up modal when it's shown
            ratingModalElement.addEventListener('show.bs.modal', function (event) {
                try {
                    const button = event.relatedTarget;
                    if (!button) {
                        return;
                    }

                    ratingAssistantId = button.getAttribute('data-assistant-id');
                    const assistantName = button.getAttribute('data-assistant-name');
                    const modalTitle = ratingModalElement.querySelector('#ratingModalLabel');
                    const modalAssistantName = ratingModalElement.querySelector('#modalAssistantName');



                    if (modalTitle) modalTitle.textContent = `Rate ${assistantName}`;
                    if (modalAssistantName) modalAssistantName.textContent = assistantName;

                    // Reset stars
                    const starButtons = modalStarsContainer.querySelectorAll('.modal-star-btn');
                    starButtons.forEach(btn => {
                        const star = btn.querySelector('i');
                        if (star) star.className = 'bi bi-star'; // Reset to empty star
                        btn.classList.remove('text-warning');
                        btn.classList.add('text-secondary');
                    });

                    // Reset form state
                    selectedRating = 0;
                    if (submitRatingBtn) submitRatingBtn.disabled = true;
                    if (modalErrorMsg) modalErrorMsg.style.display = 'none';
                } catch (error) {
                    // Handle error silently
                }
            });

            // Handle star clicks
            if (modalStarsContainer) {
                modalStarsContainer.addEventListener('click', function (event) {
                    try {
                        const starButton = event.target.closest('.modal-star-btn');
                        if (!starButton) return;

                        const ratingValue = starButton.getAttribute('data-rating-value');
                        if (!ratingValue) {
                            return;
                        }

                        selectedRating = parseInt(ratingValue);


                        if (submitRatingBtn) submitRatingBtn.disabled = false;
                        if (modalErrorMsg) modalErrorMsg.style.display = 'none';

                        // Update star visuals
                        const starButtons = modalStarsContainer.querySelectorAll('.modal-star-btn');
                        starButtons.forEach(btn => {
                            const btnValue = parseInt(btn.getAttribute('data-rating-value'));
                            const star = btn.querySelector('i');
                            if (star) {
                                if (btnValue <= selectedRating) {
                                    star.className = 'bi bi-star-fill'; // Filled star
                                    btn.classList.remove('text-secondary');
                                    btn.classList.add('text-warning'); // Gold color
                                } else {
                                    star.className = 'bi bi-star'; // Empty star
                                    btn.classList.remove('text-warning');
                                    btn.classList.add('text-secondary'); // Gray color
                                }
                            }
                        });
                    } catch (error) {
                        // Handle error silently
                    }
                });
            }

            // Handle submit button click
            if (submitRatingBtn) {
                submitRatingBtn.addEventListener('click', async function () {
                    try {
                        if (!selectedRating || !ratingAssistantId) {
                            if (modalErrorMsg) {
                                modalErrorMsg.textContent = 'Please select a rating.';
                                modalErrorMsg.style.display = 'block';
                            }
                            return;
                        }

                        // Get CSRF token
                        const csrfToken = getCsrfToken();
                        if (!csrfToken) {
                            if (modalErrorMsg) {
                                modalErrorMsg.textContent = 'CSRF token not found. Please refresh the page.';
                                modalErrorMsg.style.display = 'block';
                            }
                            return;
                        }

                        // Save original button text for restoration
                        const originalButtonText = submitRatingBtn.innerHTML;
                        submitRatingBtn.disabled = true;
                        submitRatingBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Submitting...';

                        // Construct the URL with the assistant ID
                        const rateUrl = `/directory/rate-assistant/${ratingAssistantId}/`;

                        const formData = new URLSearchParams();
                        formData.append('assistant_id', ratingAssistantId);
                        formData.append('rating', selectedRating);

                        const response = await fetch(rateUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                                'X-CSRFToken': csrfToken,
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            body: formData
                        });

                        const data = await response.json();

                        if (response.ok && data.status === 'success') {
                            // Update the rating display if available
                            if (data.rendered_stars_html) {
                                const ratingDisplay = document.getElementById(`rating-display-${ratingAssistantId}`);
                                if (ratingDisplay) {
                                    ratingDisplay.innerHTML = data.rendered_stars_html;
                                }
                            }

                            // Close the modal
                            const modalInstance = bootstrap.Modal.getInstance(ratingModalElement);
                            if (modalInstance) modalInstance.hide();

                            // Show success message using toast notification
                            if (typeof showToast === 'function') {
                                showToast('Rating submitted successfully!', 'success');
                            } else {
                                console.log('Rating submitted successfully!');
                            }
                        } else {
                            // Show error message
                            if (modalErrorMsg) {
                                modalErrorMsg.textContent = data.message || 'Error submitting rating.';
                                modalErrorMsg.style.display = 'block';
                            }
                        }
                    } catch (error) {
                        if (modalErrorMsg) {
                            modalErrorMsg.textContent = 'An unexpected error occurred.';
                            modalErrorMsg.style.display = 'block';
                        }
                    } finally {
                        // Restore button state
                        submitRatingBtn.disabled = false;
                        submitRatingBtn.innerHTML = 'Submit Rating';
                    }
                });
            }

            // Handle modal hidden event to ensure proper cleanup
            ratingModalElement.addEventListener('hidden.bs.modal', function () {
                // Remove any lingering backdrop
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }

                // Ensure body classes are cleaned up
                document.body.classList.remove('modal-open');
                document.body.style.removeProperty('overflow');
                document.body.style.removeProperty('padding-right');
            });
        }

    </script>
    {# Rating Modal Structure #}
    <div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="ratingModalLabel">Rate Assistant</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <p>Select your rating for <strong id="modalAssistantName">this assistant</strong>:</p>
            <div class="modal-stars text-center mb-3" style="font-size: 2rem;"> {# Larger stars in modal #}
                {% for i_int in "12345" %}
                <button class="modal-star-btn btn btn-link text-secondary p-1" data-rating-value="{{ i_int }}" title="Rate {{ i_int }} star{{ i_int|pluralize }}">
                    <i class="bi bi-star"></i>
                </button>
                {% endfor %}
            </div>
            <div id="modalErrorMsg" class="text-danger small mt-2" style="display: none;"></div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            <button type="button" class="btn btn-primary" id="submitRatingBtn" disabled>Submit Rating</button>
          </div>
        </div>
      </div>
    </div>

    {% block extra_js %}{% endblock %}

    <!-- HTMX Script -->
    <script src="https://unpkg.com/htmx.org@1.9.10" integrity="sha384-D1Kt99CQMDuVetoL1lrYwg5t+9QdHe7NLX/SoJYkXDFfX37iInKRy5xLSi8nO7UC" crossorigin="anonymous"></script>

    <!-- TinyMCE Script -->
    <script src="{% static 'tinymce/tinymce.min.js' %}"></script>
    <script src="{% static 'js/global-tinymce.js' %}"></script>
    <script src="{% static 'js/tinymce-dark-init.js' %}"></script>
    <script src="{% static 'js/simple-tinymce.js' %}"></script>
    <script src="{% static 'js/tinymce-responsive-handler.js' %}"></script>
    <link rel="stylesheet" href="{% static 'css/tinymce-dark-theme.css' %}">
    <link rel="stylesheet" href="{% static 'css/dark-form-elements.css' %}">
    <link rel="stylesheet" href="{% static 'css/tinymce-content-display.css' %}">
    <link rel="stylesheet" href="{% static 'css/message-content-fix.css' %}">
    <link rel="stylesheet" href="{% static 'css/remove-purple-space.css' %}">
    <link rel="stylesheet" href="{% static 'css/desktop-mode-restore.css' %}">

    <!-- Mobile Enhancements -->
    <script src="{% static 'js/mobile-enhancements.js' %}"></script>
    <script src="{% static 'js/touch-interactions.js' %}"></script>
    <script src="{% static 'js/navbar-dropdown-fix.js' %}"></script>
    <script src="{% static 'js/like-button-text-hide.js' %}"></script>

    <!-- Theme Switcher Script -->
    <script src="{% static 'js/enhanced-theme-switcher.js' %}"></script>
    <script src="{% static 'js/theme-toggle-button.js' %}"></script>
    <script src="{% static 'js/directory-dark-mode.js' %}"></script>
    <script src="{% static 'js/company-directory-dark-mode.js' %}"></script>
    <script src="{% static 'js/navbar-dark-mode.js' %}"></script>
    <script src="{% static 'js/homepage-dark-mode.js' %}"></script>
    <script src="{% static 'js/assistants-list-dark-mode.js' %}"></script>
    <script src="{% static 'js/chat-theme-transition.js' %}"></script>
    <script src="{% static 'js/facebook-style-dark-mode.js' %}"></script>
    <script src="{% static 'js/company-settings-dark-mode.js' %}"></script>

    <!-- Button Fix Scripts -->
    <script src="{% static 'js/button-click-fix.js' %}"></script>
    <script src="{% static 'js/assistant-buttons-fix.js' %}"></script>
    <script src="{% static 'js/menu-buttons-fix.js' %}"></script>
    <script src="{% static 'js/navbar-toggle-fix.js' %}"></script>
    <script src="{% static 'js/performance-fix.js' %}"></script>
    <script src="{% static 'js/chat-scroll-fix.js' %}"></script>

    <!-- Favorites Functionality -->
    <script src="{% static 'js/favorites-functionality.js' %}"></script>
    <script src="{% static 'js/like-button-enhanced.js' %}"></script>
    <script src="{% static 'js/force-white-hearts.js' %}"></script>

    <!-- Modal Fix -->
    <script src="{% static 'js/modal-fix.js' %}"></script>

    <!-- QR Code Regeneration -->
    <script src="{% static 'js/qr-code-regeneration.js' %}"></script>

    <!-- Category Dropdowns -->
    <script src="{% static 'js/category-dropdowns.js' %}"></script>


</body>
</html>
