{% comment %}
This template tag displays a consolidated warning alert for all entities that are pending approval.
Usage:
1. {% include 'accounts/tags/consolidated_pending_approvals.html' with companies=pending_companies communities=pending_communities assistants=pending_assistants %}
OR
2. {% include 'accounts/tags/consolidated_pending_approvals.html' %} - Will automatically fetch pending items for the current user
Parameters:
- companies: List of companies pending approval (optional)
- communities: List of communities pending approval (optional)
- assistants: List of assistants pending approval (optional)
{% endcomment %}

{% load account_tags %}

{% if not companies %}
    {% get_pending_companies user as companies %}
{% endif %}

{% if not communities %}
    {% get_pending_communities user as communities %}
{% endif %}

{% if not assistants %}
    {% get_pending_assistants user as assistants %}
{% endif %}

{% if companies or communities or assistants %}
{% with unique_id=companies|length|add:communities|length|add:assistants|length|stringformat:"s" %}
{% with collapse_id="pendingApprovalsCollapse_"|add:unique_id %}
<div class="card border-0 shadow-sm mb-4 pending-approvals-card">
    <div class="card-header bg-warning bg-opacity-10 d-flex align-items-center">
        <i class="bi bi-exclamation-triangle-fill text-warning fs-4 me-3"></i>
        <h5 class="mb-0">Pending Approvals</h5>
        <span class="badge bg-warning text-dark ms-2">{{ companies|length|add:communities|length|add:assistants|length }}</span>
        <button class="btn btn-sm ms-auto" type="button" data-bs-toggle="collapse" data-bs-target="#{{ collapse_id }}" aria-expanded="true" aria-controls="{{ collapse_id }}">
            <i class="bi bi-chevron-down"></i>
        </button>
    </div>
    <style>
        .pending-approvals-card {
            border-left: 4px solid #ffc107 !important;
        }
        .pending-approvals-card .list-group-item {
            transition: background-color 0.2s ease;
        }
        .pending-approvals-card .list-group-item:hover {
            background-color: rgba(255, 193, 7, 0.05);
        }
        .pending-approvals-card .card-header {
            border-bottom: 1px solid rgba(255, 193, 7, 0.2);
        }
    </style>
    <div class="collapse show" id="{{ collapse_id }}">
        <div class="card-body p-0">
            <ul class="list-group list-group-flush">
                {% for company in companies %}
                <li class="list-group-item d-flex align-items-center py-3 px-4 border-bottom">
                    <div class="d-flex align-items-center">
                        {% if company.info.logo %}
                        <img src="{{ company.info.logo.url }}" alt="{{ company.name }}" class="rounded-circle me-3" style="width: 32px; height: 32px; object-fit: cover;">
                        {% else %}
                        <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 32px; height: 32px;">
                            <i class="bi bi-building text-secondary"></i>
                        </div>
                        {% endif %}
                        <div>
                            <h6 class="mb-0">{{ company.name }}</h6>
                            <p class="text-muted small mb-0">Company • Pending administrator approval</p>
                        </div>
                    </div>
                    <a href="{% url 'accounts:company_settings' company.id %}" class="btn btn-sm btn-outline-secondary ms-auto">
                        <i class="bi bi-gear"></i> Settings
                    </a>
                </li>
                {% endfor %}

                {% for community in communities %}
                <li class="list-group-item d-flex align-items-center py-3 px-4 border-bottom">
                    <div class="d-flex align-items-center">
                        {% if community.info.logo %}
                        <img src="{{ community.info.logo.url }}" alt="{{ community.name }}" class="rounded-circle me-3" style="width: 32px; height: 32px; object-fit: cover;">
                        {% else %}
                        <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 32px; height: 32px;">
                            <i class="bi bi-people text-secondary"></i>
                        </div>
                        {% endif %}
                        <div>
                            <h6 class="mb-0">{{ community.name }}</h6>
                            <p class="text-muted small mb-0">Community • Pending administrator approval</p>
                        </div>
                    </div>
                    <a href="{% url 'accounts:company_settings' community.id %}" class="btn btn-sm btn-outline-secondary ms-auto">
                        <i class="bi bi-gear"></i> Settings
                    </a>
                </li>
                {% endfor %}

                {% for assistant in assistants %}
                <li class="list-group-item d-flex align-items-center py-3 px-4 border-bottom">
                    <div class="d-flex align-items-center">
                        {% if assistant.logo %}
                        <img src="{{ assistant.logo.url }}" alt="{{ assistant.name }}" class="rounded-circle me-3" style="width: 32px; height: 32px; object-fit: cover;">
                        {% else %}
                        <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 32px; height: 32px;">
                            <i class="bi bi-robot text-secondary"></i>
                        </div>
                        {% endif %}
                        <div>
                            <h6 class="mb-0">{{ assistant.name }}</h6>
                            <p class="text-muted small mb-0">Assistant • Pending administrator approval</p>
                        </div>
                    </div>
                    <a href="{% url 'assistants:detail' assistant.company.id assistant.id %}" class="btn btn-sm btn-outline-secondary ms-auto">
                        <i class="bi bi-eye"></i> View
                    </a>
                </li>
                {% endfor %}
            </ul>
        </div>
        <div class="card-footer bg-light py-2 px-4">
            <p class="text-muted small mb-0">
                <i class="bi bi-info-circle me-1"></i>
                Items pending approval will be available for full use once approved by an administrator.
            </p>
        </div>
    </div>
</div>
{% endwith %}
{% endwith %}
{% endif %}
