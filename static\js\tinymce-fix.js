/**
 * TinyMCE Fix for Community Assistants
 * This script forces TinyMCE to initialize properly on community assistant pages
 */

// Global flag to track if TinyMCE has been initialized for the contributions textarea
window.tinyMCEInitialized = window.tinyMCEInitialized || false;

// Function to initialize TinyMCE
function forceTinyMCEInit() {
    console.log("Forcing TinyMCE initialization...");

    // Check if TinyMCE is already initialized for the contributions textarea
    if (window.tinyMCEInitialized && tinymce.get('id_text_content')) {
        console.log("TinyMCE already initialized for id_text_content, skipping initialization");
        // Just force visibility of existing elements
        forceVisibility();
        return;
    }

    // Check if TinyMCE is loaded
    if (typeof tinymce === 'undefined') {
        console.log("TinyMCE not loaded, loading script...");
        // Load TinyMCE if not already loaded
        var script = document.createElement('script');
        script.src = '/staticfiles/tinymce/tinymce.min.js';
        script.onload = initEditor;
        document.head.appendChild(script);
    } else {
        console.log("TinyMCE already loaded, initializing editor...");
        initEditor();
    }

    // Force visibility of any existing TinyMCE elements
    forceVisibility();
}

// Function to force visibility of TinyMCE elements
function forceVisibility() {
    console.log("Forcing visibility of TinyMCE elements...");

    // Force visibility of all TinyMCE elements
    document.querySelectorAll('.tox-tinymce, .tox-toolbar__primary, .tox-toolbar-overlord, .tox-edit-area, .tox-edit-area__iframe').forEach(function(el) {
        el.style.visibility = 'visible';
        el.style.display = el.tagName === 'DIV' ? 'block' : 'flex';
        el.style.opacity = '1';
        el.style.height = 'auto';
        el.style.minHeight = el.classList.contains('tox-edit-area__iframe') ? '250px' : 'auto';
    });

    // Also try to force visibility of the textarea
    var textarea = document.getElementById('id_text_content');
    if (textarea) {
        textarea.style.visibility = 'visible';
        textarea.style.display = 'block';
        textarea.style.opacity = '1';
        textarea.style.height = 'auto';
        textarea.style.minHeight = '200px';
    }
}

// Function to initialize the editor
function initEditor() {
    console.log("Initializing TinyMCE editor...");

    // Check if TinyMCE is already initialized for the contributions textarea
    if (window.tinyMCEInitialized && tinymce.get('id_text_content')) {
        console.log("TinyMCE already initialized for id_text_content, skipping initialization");
        return;
    }

    // Remove any existing editor instances
    if (tinymce.get('id_text_content')) {
        tinymce.get('id_text_content').remove();
    }

    // Count how many TinyMCE editors are already on the page
    const existingEditors = document.querySelectorAll('.tox-tinymce').length;
    console.log(`Found ${existingEditors} existing TinyMCE editors on the page`);

    // Initialize TinyMCE with specific settings
    tinymce.init({
        selector: '#id_text_content',
        height: 300,
        menubar: false,
        plugins: [
            'advlist autolink lists link image charmap print preview anchor',
            'searchreplace visualblocks code fullscreen',
            'insertdatetime media table paste code help wordcount'
        ],
        toolbar: 'undo redo | formatselect | bold italic backcolor | ' +
                'alignleft aligncenter alignright alignjustify | ' +
                'bullist numlist outdent indent | removeformat | help',
        content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
        setup: function (editor) {
            editor.on('change', function () {
                editor.save();
            });
        },
        init_instance_callback: function(editor) {
            console.log("TinyMCE initialized, forcing visibility...");

            // Set the global flag to indicate TinyMCE has been initialized
            window.tinyMCEInitialized = true;

            // Force visibility of the editor container
            var editorContainer = editor.getContainer();
            if (editorContainer) {
                editorContainer.style.visibility = 'visible';
                editorContainer.style.display = 'block';
                editorContainer.style.opacity = '1';
                editorContainer.style.height = 'auto';
                editorContainer.style.minHeight = '300px';
            }

            // Force visibility of all TinyMCE elements
            document.querySelectorAll('.tox-tinymce, .tox-toolbar__primary, .tox-toolbar-overlord, .tox-edit-area, .tox-edit-area__iframe').forEach(function(el) {
                el.style.visibility = 'visible';
                el.style.display = el.tagName === 'DIV' ? 'block' : 'flex';
                el.style.opacity = '1';
            });

            // Force visibility of the iframe
            var iframe = document.querySelector('.tox-edit-area__iframe');
            if (iframe) {
                iframe.style.visibility = 'visible';
                iframe.style.display = 'block';
                iframe.style.opacity = '1';
                iframe.style.height = '250px';
            }

            console.log("TinyMCE visibility forced.");

            // Call our forceVisibility function for good measure
            setTimeout(forceVisibility, 100);
            setTimeout(forceVisibility, 500);
            setTimeout(forceVisibility, 1000);
        }
    });
}

// Function to check if we're on a community assistant page
function isCommunityAssistantPage() {
    // Check URL patterns
    var url = window.location.href;
    if (url.includes('/assistant/assistant/') && url.includes('/chat/')) {
        return true;
    }
    if (url.includes('/assistant/company/') && url.includes('/assistants/') && url.includes('/chat/')) {
        return true;
    }

    // Check for community assistant elements
    var contributionsTab = document.getElementById('contributions-tab');
    var communityTabs = document.getElementById('communityTabs');

    return contributionsTab !== null || communityTabs !== null;
}

// Main initialization function
function initTinyMCEFix() {
    console.log("TinyMCE fix script loaded");

    if (!isCommunityAssistantPage()) {
        console.log("Not a community assistant page, exiting");
        return;
    }

    console.log("Community assistant page detected");

    // Initialize immediately
    forceTinyMCEInit();

    // Also initialize after a delay to ensure DOM is fully loaded
    // Only try once more after a delay
    setTimeout(forceTinyMCEInit, 1000);

    // Add click handler to contributions tab
    var contributionsTab = document.getElementById('contributions-tab');
    if (contributionsTab) {
        contributionsTab.addEventListener('click', function() {
            console.log("Contributions tab clicked");
            // Only try once when tab is clicked
            setTimeout(forceTinyMCEInit, 100);
        });
    }

    // Add MutationObserver to detect when the contributions pane becomes visible
    var contributionsPane = document.getElementById('contributions-pane');
    if (contributionsPane) {
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'class' &&
                    (contributionsPane.classList.contains('active') ||
                    contributionsPane.classList.contains('show'))) {
                    console.log("Contributions pane became visible");
                    // Only try once when pane becomes visible
                    setTimeout(forceTinyMCEInit, 100);
                }
            });
        });

        observer.observe(contributionsPane, { attributes: true });
    }

    // Add event listener for hash changes to handle direct links to tabs
    window.addEventListener('hashchange', function() {
        if (window.location.hash === '#contributions-pane') {
            console.log("Hash changed to contributions pane");
            // Only try once when hash changes
            setTimeout(forceTinyMCEInit, 100);
        }
    });

    // Check if URL has a hash for the contributions tab
    if (window.location.hash === '#contributions-pane') {
        console.log("URL has contributions pane hash");
        setTimeout(function() {
            var contributionsTab = document.getElementById('contributions-tab');
            if (contributionsTab) {
                contributionsTab.click();
            }
            // Only try once when hash is present on load
            setTimeout(forceTinyMCEInit, 100);
        }, 100);
    }
}

// Run the initialization when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', initTinyMCEFix);

// Also run it now in case the DOM is already loaded
if (document.readyState === 'complete' || document.readyState === 'interactive') {
    initTinyMCEFix();
}
