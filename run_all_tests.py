"""
Comprehensive test script to run all tests for the application.
"""

import os
import sys
import time
import importlib
import traceback

def run_test_module(module_name):
    """Run a test module and return True if all tests pass."""
    print(f"\n{'=' * 80}")
    print(f"Running {module_name}...")
    print(f"{'=' * 80}")

    try:
        # Import the module
        module = importlib.import_module(module_name)

        # Check for a run_all_tests function first
        if hasattr(module, 'run_all_backend_tests') and callable(getattr(module, 'run_all_backend_tests')):
            print("\nRunning run_all_backend_tests()...")
            try:
                result = module.run_all_backend_tests()
                return result is True
            except Exception as e:
                print(f"Error in run_all_backend_tests: {e}")
                traceback.print_exc()
                return False

        # Get all test functions
        test_functions = [f for f in dir(module) if f.startswith('test_') and callable(getattr(module, f))]

        if not test_functions:
            print(f"No test functions found in {module_name}")
            return False

        # Run each test function
        all_passed = True
        for func_name in test_functions:
            print(f"\nRunning {func_name}...")
            try:
                func = getattr(module, func_name)
                result = func()
                if result is not True:
                    print(f"Test {func_name} did not return True")
                    all_passed = False
            except Exception as e:
                print(f"Error in {func_name}: {e}")
                traceback.print_exc()
                all_passed = False

        return all_passed

    except Exception as e:
        print(f"Error importing or running {module_name}: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    start_time = time.time()

    # List of test modules to run
    test_modules = [
        # Basic tests
        'test_basic',
        'test_frontend',
        'test_backend',

        # User and company management tests
        'test_user_management',
        'test_company_management',

        # Assistant tests
        'test_assistant',

        # Directory tests
        'test_directory',

        # Rich text editor tests
        'test_rich_text_editor',

        # Dark mode tests
        'test_dark_mode'
    ]

    # Run each test module
    results = {}
    for module in test_modules:
        results[module] = run_test_module(module)

    # Print summary
    print("\n\n")
    print("=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)

    all_passed = True
    for module, passed in results.items():
        status = "PASSED" if passed else "FAILED"
        print(f"{module}: {status}")
        if not passed:
            all_passed = False

    end_time = time.time()
    duration = end_time - start_time

    print(f"\nTotal test duration: {duration:.2f} seconds")
    print(f"Overall result: {'PASSED' if all_passed else 'FAILED'}")

    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
