/**
 * Company Detail Page Mobile Improvements
 * Enhances the mobile experience for company detail pages
 * Focuses only on centering the company logo in mobile view
 */

/* Mobile optimizations (up to 768px) */
@media (max-width: 768px) {
  /* Base styles for company logo containers - only for mobile mode */
  .company-logo-container, .company-logo-container-sidebar {
    height: 80px;
    width: 80px;
    min-width: 80px;
    min-height: 80px;
    max-width: 80px;
    max-height: 80px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .company-header-logo, .company-sidebar-logo {
    height: 80px;
    width: 80px;
    max-width: 80px;
    max-height: 80px;
    object-fit: contain;
  }

  /* Keep card header in row layout but adjust for mobile */
  .card-header.bg-light.d-flex.align-items-center {
    flex-direction: row !important;
    justify-content: flex-start !important;
    align-items: center !important;
    padding: 1rem !important;
  }

  /* Position logo on the left side in mobile mode */
  .company-logo-container {
    margin-right: 1rem !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }

  /* Ensure company logo is fully visible */
  .company-header-logo {
    object-fit: contain !important;
  }

  /* Adjust company name and rating for mobile */
  .card-header .me-auto {
    margin-right: 0 !important;
    flex: 1 !important;
  }

  /* Adjust rating display for mobile */
  #rating-display-company {
    display: flex !important;
  }

  /* Move action buttons below in mobile view */
  .card-header .action-buttons {
    width: 100% !important;
    margin-top: 0.75rem !important;
    justify-content: center !important;
    order: 3 !important;
  }

  /* Adjust card header layout to wrap elements */
  .card-header.bg-light.d-flex.align-items-center {
    flex-wrap: wrap !important;
  }

  /* Style for sidebar logo container */
  .company-logo-container-sidebar {
    height: 80px !important;
    width: 80px !important;
    min-width: 80px !important;
    min-height: 80px !important;
    max-width: 80px !important;
    max-height: 80px !important;
    margin: 0 auto 1rem auto !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }

  /* Style for sidebar logo */
  .company-sidebar-logo {
    height: 80px !important;
    width: 80px !important;
    max-width: 80px !important;
    max-height: 80px !important;
    object-fit: contain !important;
  }
}


