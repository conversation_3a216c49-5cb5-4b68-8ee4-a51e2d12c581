/**
 * Directory Dark Mode CSS
 * Specific dark mode styling for directory pages and cards
 */

/* Directory container */
[data-theme="dark"] .directory-container,
[data-theme="dark"] .container,
[data-theme="dark"] .container-fluid {
  background-color: #121212 !important;
  color: #ffffff !important;
}

/* Directory cards */
[data-theme="dark"] .directory-card,
[data-theme="dark"] .card {
  background: linear-gradient(145deg, #1e1e1e, #252525) !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  color: #ffffff !important;
}

[data-theme="dark"] .directory-card:hover,
[data-theme="dark"] .card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
}

[data-theme="dark"] .directory-card .card-title,
[data-theme="dark"] .card .card-title {
  color: #ffffff !important;
  font-weight: 600 !important;
}

[data-theme="dark"] .directory-card .card-text,
[data-theme="dark"] .card .card-text {
  color: #cccccc !important;
}

/* Directory card image container */
[data-theme="dark"] .directory-card .card-img-top,
[data-theme="dark"] .card .card-img-top {
  background-color: #1a1a1a !important;
  border-bottom: 1px solid #333333 !important;
}

/* Directory card footer */
[data-theme="dark"] .directory-card .card-footer,
[data-theme="dark"] .card .card-footer {
  background-color: rgba(0, 0, 0, 0.2) !important;
  border-top: 1px solid #333333 !important;
}

/* Directory badges */
[data-theme="dark"] .badge {
  background-color: #333333 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .badge.bg-primary {
  background-color: #0077ff !important;
  color: #ffffff !important;
}

[data-theme="dark"] .badge.bg-secondary {
  background-color: #555555 !important;
  color: #ffffff !important;
}

/* Directory buttons */
[data-theme="dark"] .btn-primary {
  background: linear-gradient(to bottom, #0077ff, #0055cc) !important;
  border: none !important;
  color: white !important;
  box-shadow: 0 4px 10px rgba(0, 102, 255, 0.3) !important;
}

[data-theme="dark"] .btn-primary:hover {
  background: linear-gradient(to bottom, #0088ff, #0066dd) !important;
  box-shadow: 0 6px 15px rgba(0, 102, 255, 0.4) !important;
  transform: translateY(-2px) !important;
}

[data-theme="dark"] .btn-outline-primary {
  color: #0077ff !important;
  border: 1px solid #0077ff !important;
  background-color: transparent !important;
}

[data-theme="dark"] .btn-outline-primary:hover {
  background-color: rgba(0, 119, 255, 0.1) !important;
  color: #0088ff !important;
}

/* Directory sections */
[data-theme="dark"] section {
  background-color: #121212 !important;
  color: #ffffff !important;
}

/* Directory headings */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
  color: #ffffff !important;
}

/* Directory paragraphs */
[data-theme="dark"] p {
  color: #cccccc !important;
}

/* Directory links */
[data-theme="dark"] a:not(.btn) {
  color: #0088ff !important;
}

[data-theme="dark"] a:not(.btn):hover {
  color: #00aaff !important;
}

/* Directory icons */
[data-theme="dark"] .bi {
  color: inherit !important;
}

/* Directory company logos */
[data-theme="dark"] .company-logo {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  padding: 10px !important;
}

/* Directory search box */
[data-theme="dark"] .search-box,
[data-theme="dark"] .form-control {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .search-box:focus,
[data-theme="dark"] .form-control:focus {
  border-color: #0077ff !important;
  box-shadow: 0 0 0 3px rgba(0, 119, 255, 0.25) !important;
}

/* Directory filters */
[data-theme="dark"] .filter-section {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  padding: 15px !important;
}

/* Directory pagination */
[data-theme="dark"] .pagination .page-link {
  background-color: #1a1a1a !important;
  border-color: #333333 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .pagination .page-link:hover {
  background-color: #252525 !important;
  border-color: #444444 !important;
}

[data-theme="dark"] .pagination .page-item.active .page-link {
  background-color: #0077ff !important;
  border-color: #0077ff !important;
  color: #ffffff !important;
}

/* Directory empty state */
[data-theme="dark"] .empty-state {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  color: #cccccc !important;
}

/* Directory footer */
[data-theme="dark"] footer {
  background-color: #1a1a1a !important;
  border-top: 1px solid #333333 !important;
  color: #cccccc !important;
}

/* Directory call-to-action section */
[data-theme="dark"] .cta-section {
  background: linear-gradient(145deg, #1a1a1a, #121212) !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4) !important;
}

/* Directory white and light sections */
[data-theme="dark"] .bg-white,
[data-theme="dark"] .bg-light,
[data-theme="dark"] [style*="background-color: white"],
[data-theme="dark"] [style*="background-color: #fff"],
[data-theme="dark"] [style*="background-color: rgb(255, 255, 255)"],
[data-theme="dark"] [style*="background-color: rgba(255, 255, 255"],
[data-theme="dark"] [style*="background-color: #f8f9fa"],
[data-theme="dark"] [style*="background-color: rgb(248, 249, 250)"],
[data-theme="dark"] [style*="background-color: #e9ecef"],
[data-theme="dark"] [style*="background-color: rgb(233, 236, 239)"],
[data-theme="dark"] [style*="background-color: rgba(233, 236, 239"],
[data-theme="dark"] [style*="background-color: #dee2e6"],
[data-theme="dark"] [style*="background-color: rgb(222, 226, 230)"],
[data-theme="dark"] [style*="background-color: rgba(222, 226, 230"] {
  background-color: #121212 !important;
  background: #121212 !important;
  color: #ffffff !important;
}

/* Directory body with bg-light class */
[data-theme="dark"] body.bg-light {
  background-color: #121212 !important;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(30, 30, 30, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(20, 20, 20, 0.2) 0%, transparent 50%),
    linear-gradient(to bottom, #121212, #0a0a0a) !important;
  background-attachment: fixed !important;
  color: #ffffff !important;
}

/* Company Directory specific styles */
[data-theme="dark"] .company-directory-container,
[data-theme="dark"] body.bg-light .container,
[data-theme="dark"] body.bg-light .container-fluid {
  background-color: #121212 !important;
  color: #ffffff !important;
}

/* Gray background fix for company directory */
[data-theme="dark"] body.bg-light {
  background-color: #121212 !important;
}

[data-theme="dark"] .bg-light {
  background-color: #121212 !important;
}

[data-theme="dark"] .featured-companies {
  background-color: #1a1a1a !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  padding: 20px !important;
}

[data-theme="dark"] .featured-companies h2 {
  color: #ffffff !important;
  border-bottom: 1px solid #333333 !important;
  padding-bottom: 10px !important;
}

[data-theme="dark"] .company-card {
  background-color: #252525 !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
}

[data-theme="dark"] .company-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .company-card .company-logo-container {
  background-color: #1a1a1a !important;
  border-bottom: 1px solid #333333 !important;
  padding: 15px !important;
}

[data-theme="dark"] .company-card .company-name {
  color: #ffffff !important;
  font-weight: 600 !important;
}

[data-theme="dark"] .company-card .company-description {
  color: #cccccc !important;
}

[data-theme="dark"] .company-search-container {
  background-color: #1a1a1a !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  padding: 20px !important;
  margin-bottom: 30px !important;
}

[data-theme="dark"] .search-input {
  background-color: #252525 !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .search-input:focus {
  border-color: #0077ff !important;
  box-shadow: 0 0 0 3px rgba(0, 119, 255, 0.25) !important;
}

[data-theme="dark"] .search-button {
  background-color: #0077ff !important;
  border: none !important;
  color: #ffffff !important;
}

[data-theme="dark"] .search-button:hover {
  background-color: #0066dd !important;
}
