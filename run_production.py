import os
import sys
import subprocess

print("Starting Django server in production mode with --insecure flag...")
try:
    result = subprocess.run(
        [sys.executable, "manage.py", "runserver", "--insecure"],
        check=True,
        text=True,
        capture_output=True
    )
    print("Output:", result.stdout)
    print("Errors:", result.stderr)
except subprocess.CalledProcessError as e:
    print(f"Error running server: {e}")
    print("Output:", e.stdout)
    print("Errors:", e.stderr)
except Exception as e:
    print(f"Unexpected error: {e}")
