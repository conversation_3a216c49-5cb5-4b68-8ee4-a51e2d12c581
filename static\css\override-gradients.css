/**
 * Override Gradients CSS
 * This file contains !important rules to override any gradient styles
 */

/* Override any inline styles with !important */
.user-message .message-content,
.user-message .message-content[style],
.message.user-message .message-content,
.message.user-message .message-content[style] {
    background: #3b7dd8 !important;
    background-color: #3b7dd8 !important;
    background-image: none !important;
    color: #ffffff !important;
    border: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.assistant-message .message-content,
.assistant-message .message-content[style],
.message.assistant-message .message-content,
.message.assistant-message .message-content[style] {
    background: #ffffff !important;
    background-color: #ffffff !important;
    background-image: none !important;
    color: #333333 !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

.nav-content-bubble .message-content,
.nav-content-bubble .message-content[style] {
    background: #ffffff !important;
    background-color: #ffffff !important;
    background-image: none !important;
    color: #333333 !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03) !important;
    border-left: 3px solid #0d6efd !important;
}

/* Remove any pseudo-elements that might add gradients */
.message-content::before,
.message-content::after {
    display: none !important;
    content: none !important;
    background: none !important;
    background-image: none !important;
}

/* Ensure the message container itself has no background */
.message,
.message[style] {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
}

/* Override any gradient backgrounds that might be applied */
[style*="linear-gradient"],
[style*="radial-gradient"],
[style*="gradient"] {
    background: inherit !important;
    background-color: inherit !important;
    background-image: none !important;
}
