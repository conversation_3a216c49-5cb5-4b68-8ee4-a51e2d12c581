/**
 * Responsive Chat Bubbles CSS
 * Improves chat bubble sizing based on content and device
 */

/* Base message styling for all devices */
.message {
  display: flex;
  width: 100%;
  margin: 0.75rem 0;
  padding: 0;
  transition: all 0.2s ease;
}

/* User message alignment */
.user-message {
  justify-content: flex-end !important;
}

/* Assistant message alignment */
.assistant-message {
  justify-content: flex-start !important;
}

/* Base message content styling */
.message-content {
  max-width: fit-content;
  width: auto;
  border-radius: 1.25rem;
  padding: 0.8rem 1.2rem !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  hyphens: auto !important;
  line-height: 1.5 !important;
  font-size: 1rem !important;
  display: block !important; /* Change from inline-block to block */
}

/* Ensure content inside bubbles is properly contained */
.message-content * {
  max-width: 100% !important;
  width: 100% !important;
  overflow-wrap: break-word !important;
  word-wrap: break-word !important;
  box-sizing: border-box !important;
}

/* Make images responsive inside chat bubbles */
.message-content img {
  max-width: 100% !important;
  height: auto !important;
  border-radius: 0.5rem;
  margin: 0.5rem 0;
}

/* Make tables responsive inside chat bubbles */
.message-content table {
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: auto !important;
  display: block !important;
  margin: 0.5rem 0 !important;
  border-collapse: collapse !important;
}

.message-content table td,
.message-content table th {
  padding: 0.5rem !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  word-break: break-word !important;
}

/* Ensure code blocks don't overflow */
.message-content pre,
.message-content code {
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  max-width: 100% !important;
  background-color: rgba(0, 0, 0, 0.05) !important;
  padding: 0.5rem !important;
  border-radius: 0.25rem !important;
  font-size: 0.9rem !important;
}

/* Ensure lists are properly contained */
.message-content ul,
.message-content ol {
  padding-left: 1.5rem !important;
  margin: 0.5rem 0 !important;
}

/* Ensure blockquotes are properly styled */
.message-content blockquote {
  border-left: 3px solid rgba(0, 0, 0, 0.2) !important;
  padding-left: 1rem !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  font-style: italic !important;
}

/* Responsive adjustments for different screen sizes */
/* Tablet devices */
@media (max-width: 992px) {
  .message-content {
    max-width: 100% !important;
    width: 100% !important;
    font-size: 0.95rem !important;
  }
}

/* Mobile devices */
@media (max-width: 768px) {
  .message-content {
    max-width: 100% !important;
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.95rem !important;
    border-radius: 1rem !important;
  }

  /* Adjust spacing for mobile */
  .message {
    margin: 0.5rem 0 !important;
    width: 100% !important;
  }

  /* Ensure tables are scrollable on mobile */
  .message-content table {
    font-size: 0.85rem !important;
    width: 100% !important;
  }
}

/* Small mobile devices */
@media (max-width: 576px) {
  .message-content {
    max-width: 100% !important;
    width: 100% !important;
    padding: 0.7rem 0.9rem !important;
    font-size: 0.9rem !important;
  }

  /* Reduce padding in lists for small screens */
  .message-content ul,
  .message-content ol {
    padding-left: 1.2rem !important;
  }
}

/* TinyMCE content specific styling */
.tox-tinymce-aux {
  z-index: 9999 !important; /* Ensure TinyMCE popups appear above other elements */
}

/* Ensure TinyMCE editor is responsive */
.tox-tinymce {
  border-radius: 0.5rem !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  max-width: 100% !important;
}

/* Ensure TinyMCE content area is responsive */
.tox-edit-area {
  max-width: 100% !important;
}

.tox-edit-area__iframe {
  max-width: 100% !important;
}

/* Fix for TinyMCE content in dark mode */
[data-theme="dark"] .tox-edit-area__iframe {
  background-color: #2d2d2d !important;
}

/* Ensure TinyMCE toolbar is responsive */
.tox-toolbar__primary {
  flex-wrap: wrap !important;
}

/* Ensure TinyMCE buttons are visible in dark mode */
[data-theme="dark"] .tox-tbtn {
  color: #f0f0f0 !important;
}
