import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Connect to PostgreSQL server
conn = psycopg2.connect(
    dbname='postgres',
    user='postgres',
    password='M@kerere1',
    host='localhost',
    port='5432'
)

conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
cursor = conn.cursor()

# Create the database
try:
    cursor.execute("CREATE DATABASE virtualo")
    print("Database 'virtualo' created successfully!")
except psycopg2.errors.DuplicateDatabase:
    print("Database 'virtualo' already exists.")
except Exception as e:
    print(f"Error creating database: {e}")

# Close the connection
cursor.close()
conn.close()
