# cPanel Deployment Guide

This guide provides step-by-step instructions for deploying your Django application to a cPanel hosting environment.

## Prerequisites

- A cPanel hosting account with Python support
- SSH access to your cPanel server (recommended)
- MySQL database access
- Domain name configured to point to your cPanel account

## Step 1: Prepare Your Local Project

1. **Create a production-ready package**:
   ```bash
   # Remove unnecessary files
   rm -rf __pycache__/ .git/ .pytest_cache/ .coverage
   
   # Create a deployment package
   zip -r deployment.zip . -x "*.git*" "*.pyc" "__pycache__/*" "*.sqlite3" "venv/*" "*.env" ".vscode/*" ".idea/*"
   ```

## Step 2: Upload Files to cPanel

1. **Upload your deployment package**:
   - Log in to cPanel
   - Navigate to File Manager
   - Go to the directory where you want to deploy your application (e.g., `public_html` or a subdirectory)
   - Upload the `deployment.zip` file
   - Extract the zip file

2. **Alternative: Use SFTP or SCP**:
   ```bash
   # Using SCP
   scp deployment.zip <EMAIL>:~/public_html/
   
   # Using SFTP client like FileZilla
   # Connect to your server and upload the file
   ```

## Step 3: Set Up Python Environment

1. **Create a Python virtual environment**:
   ```bash
   # SSH into your cPanel server
   ssh <EMAIL>
   
   # Navigate to your application directory
   cd ~/public_html/your_app_directory
   
   # Create a virtual environment
   python3 -m venv venv
   
   # Activate the virtual environment
   source venv/bin/activate
   
   # Install production dependencies
   pip install -r requirements.production.txt
   ```

## Step 4: Configure Environment Variables

1. **Create a .env file**:
   ```bash
   # Copy the production environment template
   cp .env.production .env
   
   # Edit the .env file with your actual values
   nano .env
   ```

2. **Update the following values in your .env file**:
   - `SECRET_KEY`: Generate a secure random key
   - `ALLOWED_HOSTS`: Your domain name(s)
   - `DB_NAME`, `DB_USER`, `DB_PASSWORD`: Your MySQL database credentials
   - `EMAIL_*`: Your email server settings
   - API keys for OpenAI, Gemini, etc.

## Step 5: Set Up the Database

1. **Create a MySQL database in cPanel**:
   - Log in to cPanel
   - Navigate to "MySQL Databases"
   - Create a new database
   - Create a new user
   - Add the user to the database with all privileges

2. **Run migrations**:
   ```bash
   # Activate the virtual environment if not already activated
   source venv/bin/activate
   
   # Run migrations
   python manage.py migrate --settings=company_assistant.production_settings
   
   # Create a superuser
   python manage.py createsuperuser --settings=company_assistant.production_settings
   ```

## Step 6: Collect Static Files

1. **Collect static files**:
   ```bash
   python manage.py collectstatic --settings=company_assistant.production_settings --no-input
   ```

## Step 7: Configure cPanel Python Application

1. **Set up Python application in cPanel**:
   - Log in to cPanel
   - Navigate to "Setup Python App"
   - Click "Create Application"
   - Fill in the form:
     - Application root: The directory where your application is located
     - Application URL: The URL path where your application will be accessible
     - Application startup file: `passenger_wsgi.py`
     - Application entry point: `application`
     - Python version: Select the appropriate version (3.8+)
   - Click "Create"

2. **Configure the application to use your virtual environment**:
   - Edit the `passenger_wsgi.py` file to uncomment the virtual environment lines
   - Update the path to match your virtual environment location

## Step 8: Configure .htaccess

1. **Ensure your .htaccess file is properly configured**:
   ```apache
   # Serve static and media files directly
   <IfModule mod_rewrite.c>
       RewriteEngine On
       
       # Serve static files directly
       RewriteCond %{REQUEST_URI} ^/static/
       RewriteRule ^static/(.*)$ static/$1 [L]
       
       # Serve media files directly
       RewriteCond %{REQUEST_URI} ^/media/
       RewriteRule ^media/(.*)$ media/$1 [L]
       
       # Pass all other requests to Passenger
       RewriteCond %{REQUEST_FILENAME} !-f
       RewriteRule ^(.*)$ passenger_wsgi.py/$1 [QSA,L]
   </IfModule>
   
   # Compress text files
   <IfModule mod_deflate.c>
       AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
   </IfModule>
   ```

## Step 9: Set File Permissions

1. **Set proper file permissions**:
   ```bash
   # Make sure the web server can write to necessary directories
   chmod 755 ~/public_html/your_app_directory
   chmod -R 755 ~/public_html/your_app_directory/static
   chmod -R 755 ~/public_html/your_app_directory/media
   
   # Make sure log files and upload directories are writable
   mkdir -p ~/public_html/your_app_directory/logs
   chmod -R 755 ~/public_html/your_app_directory/logs
   ```

## Step 10: Restart the Application

1. **Restart your Python application**:
   - Log in to cPanel
   - Navigate to "Setup Python App"
   - Find your application
   - Click "Restart"

## Troubleshooting

### Application Not Loading

1. **Check the error logs**:
   - Look in the `logs` directory of your application
   - Check the cPanel error logs

2. **Verify the virtual environment**:
   - Make sure the paths in `passenger_wsgi.py` are correct
   - Ensure all dependencies are installed

3. **Check file permissions**:
   - Make sure the web server has read access to your application files
   - Make sure the web server has write access to necessary directories

### Database Connection Issues

1. **Verify database credentials**:
   - Double-check the database name, username, and password in your `.env` file
   - Make sure the database user has the necessary privileges

2. **Check MySQL connection**:
   - Try connecting to the database using the MySQL command line client
   ```bash
   mysql -u your_username -p your_database_name
   ```

### Static Files Not Loading

1. **Check static file configuration**:
   - Make sure `STATIC_ROOT` and `STATIC_URL` are correctly set
   - Verify that you've run `collectstatic`
   - Check that the `.htaccess` file is properly configured

## Maintenance

### Updating Your Application

1. **Prepare an update package**:
   ```bash
   zip -r update.zip . -x "*.git*" "*.pyc" "__pycache__/*" "*.sqlite3" "venv/*" "*.env" ".vscode/*" ".idea/*"
   ```

2. **Upload and extract the update**:
   - Upload the update package to your server
   - Extract it, overwriting existing files

3. **Update dependencies and run migrations**:
   ```bash
   source venv/bin/activate
   pip install -r requirements.production.txt
   python manage.py migrate --settings=company_assistant.production_settings
   python manage.py collectstatic --settings=company_assistant.production_settings --no-input
   ```

4. **Restart the application**:
   - Restart your Python application in cPanel

### Backing Up Your Application

1. **Back up your database**:
   - Use cPanel's backup tools to create a database backup
   - Or use mysqldump:
   ```bash
   mysqldump -u your_username -p your_database_name > backup.sql
   ```

2. **Back up your application files**:
   - Use cPanel's backup tools to create a full backup
   - Or create a zip archive:
   ```bash
   zip -r application_backup.zip ~/public_html/your_app_directory
   ```
