"""
Script to fake the remaining accounts migrations.
"""
import os
import sys
import django
from django.db import connection

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

def fake_migration_record(app, name):
    """Manually insert a record in the django_migrations table."""
    print(f"Faking migration record for {app}.{name}...")
    try:
        with connection.cursor() as cursor:
            # Check if the migration is already recorded
            cursor.execute(
                "SELECT EXISTS (SELECT 1 FROM django_migrations WHERE app = '%s' AND name = '%s');" % (app, name)
            )
            migration_exists = cursor.fetchone()[0]

            if migration_exists:
                print(f"Migration {app}.{name} already recorded.")
                return True

            # Insert the migration record
            cursor.execute(
                "INSERT INTO django_migrations (app, name, applied) VALUES ('%s', '%s', datetime('now'));" % (app, name)
            )

            print(f"Migration record for {app}.{name} added successfully.")
            return True
    except Exception as e:
        print(f"Error faking migration record: {e}")
        return False

def main():
    """Main function to fake the remaining accounts migrations."""
    print("Starting migration fix...")

    # Fake the migration records
    migrations_to_fake = [
        ('accounts', '0020_remove_membership_accessible_folders_and_more'),
        ('accounts', '0021_registrationlink_accessible_folders')
    ]

    for app, name in migrations_to_fake:
        if not fake_migration_record(app, name):
            print(f"Failed to fake migration record for {app}.{name}. Continuing...")

    print("\nNext steps:")
    print("1. Run 'python manage.py runserver' to start the server")
    print("2. If you still encounter issues, try 'python manage.py migrate --fake-initial'")

if __name__ == "__main__":
    main()
