from django.db import models
from django.conf import settings
from django.utils.text import slugify
from assistants.models import Assistant

class Content(models.Model):
    """Model for storing company content."""
    
    # Content Types
    TYPE_DOCUMENT = 'document'
    TYPE_WIKI = 'wiki'
    TYPE_FAQ = 'faq'
    TYPE_POLICY = 'policy'
    
    CONTENT_TYPES = [
        (TYPE_DOCUMENT, 'Document'),
        (TYPE_WIKI, 'Wiki Page'),
        (TYPE_FAQ, 'FAQ'),
        (TYPE_POLICY, 'Policy')
    ]
    
    title = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200)
    content_type = models.CharField(
        max_length=20,
        choices=CONTENT_TYPES,
        default=TYPE_DOCUMENT
    )
    body = models.TextField()
    summary = models.TextField(blank=True)
    company = models.ForeignKey(
        'accounts.Company',
        on_delete=models.CASCADE,
        related_name='content'
    )
    author = models.Foreign<PERSON>ey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='authored_content'
    )
    assistant = models.Foreign<PERSON><PERSON>(
        Assistant,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='content'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_public = models.BooleanField(default=False)
    is_archived = models.BooleanField(default=False)
    
    class Meta:
        ordering = ['-updated_at']
        unique_together = ['company', 'slug']
        indexes = [
            models.Index(fields=['company', 'content_type']),
            models.Index(fields=['company', 'is_public']),
            models.Index(fields=['company', 'is_archived'])
        ]

    def __str__(self):
        return self.title
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

class ContentVersion(models.Model):
    """Model for tracking content versions."""
    
    content = models.ForeignKey(
        Content,
        on_delete=models.CASCADE,
        related_name='versions'
    )
    body = models.TextField()
    edited_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='content_edits'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    change_summary = models.CharField(max_length=200, blank=True)
    
    class Meta:
        ordering = ['-created_at']
        get_latest_by = 'created_at'

    def __str__(self):
        return f"Version {self.id} of {self.content.title}"

class ContentImage(models.Model):
    """Model for storing images associated with content."""
    
    content = models.ForeignKey(
        Content,
        on_delete=models.CASCADE,
        related_name='images'
    )
    image = models.ImageField(upload_to='content/images/')
    alt_text = models.CharField(max_length=200, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        
    def __str__(self):
        return f"Image for {self.content.title}"
