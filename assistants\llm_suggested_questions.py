import os
import traceback
from .llm_utils import openai_client, groq_client, anthropic_client, gemini_client # Import clients

# Define default model here or get dynamically if needed
DEFAULT_SUGGESTION_MODEL = "gpt-3.5-turbo" # Or perhaps assistant.model?

def generate_llm_suggested_questions(conversation, nav_context):
    """
    Calls the appropriate LLM API to generate 5 next questions based on conversation and navigation context.
    Returns a list of 5 questions.

    Args:
        conversation: The conversation history (can be empty for initial suggestions)
        nav_context: The navigation context containing the content from the database

    Returns:
        A list of 5 questions.
    """
    # Use a default model for suggestions
    model_name = DEFAULT_SUGGESTION_MODEL

    # --- Limit Conversation History for Suggestions ---
    # Split the conversation string into lines and take the last N lines (e.g., 6 for last user/assistant pair + previous)
    conversation_lines = conversation.strip().split('\n')
    recent_conversation = "\n".join(conversation_lines[-6:]) # Keep last 6 lines
    # --- End Limit ---

    prompt = f"""
You are an expert assistant helping a user. Your task is to suggest 5 relevant questions the user might ask, based STRICTLY and ONLY on the provided 'Navigation/context' content.

**CRITICAL INSTRUCTIONS (Follow Strictly):**

1. **EXTRACT SPECIFIC FACTS:** First, identify 5-10 specific facts, details, or pieces of information from the 'Navigation/context' section. For example: business hours, phone numbers, services offered, company descriptions, etc.

2. **GENERATE QUESTIONS FROM FACTS:** For each fact you identified, create a question that would elicit that specific information. For example, if you see "Office hours: 9am-5pm", create the question "What are your office hours?"

3. **SPECIFICITY IS REQUIRED:** Questions MUST be about SPECIFIC information in the context. Generic questions like "What services do you offer?" are only allowed if services are specifically mentioned and described.

4. **USE EXACT TERMINOLOGY:** Use the exact terminology, names, and phrases from the context in your questions.

5. **DIVERSE COVERAGE:** Generate questions covering different sections/topics in the context. Don't focus all questions on a single aspect.

6. **ABSOLUTELY NO INVENTION:** Do NOT create questions about information that isn't explicitly present in the context. This is critical.

**EXAMPLES OF GOOD QUESTIONS (if the information is present):**
- "What are your office hours?"
- "How can I contact your sales team?"
- "What specific training solutions do you offer?"
- "Where is your office located in Uganda?"
- "What is your approach to sales training?"

**EXAMPLES OF BAD QUESTIONS (too generic or not based on specific content):**
- "Can you tell me more about your company?" (too vague)
- "What services do you offer?" (unless specific services are listed)
- "How can you help me?" (too generic)
- "What makes your company unique?" (unless uniqueness factors are specified)

**Output Format:**
- Return ONLY the 5 suggested questions.
- Each question MUST be on a new line.
- NO introduction, preamble, numbering, bullet points, or any other text.

Recent Conversation Exchange (latest last):
{recent_conversation}

Navigation/context:
{nav_context}
"""
    messages = [{"role": "system", "content": prompt}]
    response_content = ""

    try:
        if model_name.startswith('gpt') or model_name.startswith('llama'):
            # Select the appropriate client based on the model
            if model_name.startswith('llama'):
                # Use Groq client for Llama models
                if not groq_client: raise ConnectionError("Groq client not initialized.")
                print(f"[LLM DEBUG] Using Groq client for suggested questions with model: {model_name}")
                # Make sure we're using the exact format from the documentation
                client = groq_client
            else:
                # Use OpenAI client for GPT models
                if not openai_client: raise ConnectionError("OpenAI client not initialized.")
                print(f"[LLM DEBUG] Using OpenAI client for suggested questions with model: {model_name}")
                client = openai_client

            response = client.chat.completions.create(
                model=model_name,
                messages=messages,
                max_tokens=256,
                temperature=0.7,
                n=1
            )
            response_content = response.choices[0].message.content
        elif model_name.startswith('claude'):
            if not anthropic_client: raise ConnectionError("Anthropic client not initialized.")
            # Anthropic needs system prompt separated
            system_prompt = prompt # Use the whole prompt as system for this task
            user_messages = [] # No user/assistant messages needed for this specific prompt structure
            response = anthropic_client.messages.create(
                model=model_name,
                system=system_prompt,
                messages=user_messages,
                max_tokens=256,
                temperature=0.7,
            )
            response_content = response.content[0].text if response.content else ""
        elif model_name.startswith('gemini'):
            if not gemini_client: raise ConnectionError("Gemini client not initialized.")
            # Use the appropriate Gemini client based on which implementation is being used
            if hasattr(gemini_client, 'chat'):
                # Using OpenAI-compatible endpoint
                gemini_messages = [{"role": "user", "content": prompt}]
                response = gemini_client.chat.completions.create(
                    model=model_name,
                    messages=gemini_messages, # Use modified messages list
                    max_tokens=256,
                    temperature=0.7,
                    n=1
                )
                response_content = response.choices[0].message.content
            elif hasattr(gemini_client, 'GenerativeModel'):
                # Using google-generativeai package
                gemini_model = gemini_client.GenerativeModel(model_name=model_name)
                gemini_messages = [{"role": "user", "parts": [{"text": prompt}]}]
                response = gemini_model.generate_content(
                    gemini_messages,
                    generation_config={
                        "temperature": 0.7,
                        "max_output_tokens": 256
                    }
                )
                response_content = response.text
            else:
                raise ValueError("Gemini client is initialized but doesn't have expected methods")
        else:
            print(f"Unsupported model for suggestions: {model_name}. Falling back to default.")
            # Fallback to OpenAI default model
            if not openai_client: raise ConnectionError("OpenAI client not initialized for fallback.")
            print(f"[LLM DEBUG] Using OpenAI client for fallback with model: {DEFAULT_SUGGESTION_MODEL}")
            response = openai_client.chat.completions.create(
                model=DEFAULT_SUGGESTION_MODEL, messages=messages, max_tokens=256, temperature=0.7, n=1
            )
            response_content = response.choices[0].message.content

    except Exception as e:
        print(f"Error generating suggested questions with model {model_name}: {e}")
        traceback.print_exc()
        return [] # Return empty list on error

    # Parse LLM output
    content = response_content.strip()
    print(f"DEBUG: Raw LLM response: {content}")

    # Extract questions from the response
    questions = [line.lstrip('-0123456789. ').strip() for line in content.split('\n') if line.strip() and '?' in line]
    print(f"DEBUG: Extracted questions: {questions}")

    # If no questions were found, use a fallback approach
    if not questions:
        print("DEBUG: No questions found in LLM response, using fallback approach")
        # Try to extract any lines that look like questions
        questions = [line.strip() for line in content.split('\n') if line.strip() and len(line.strip()) > 10]
        print(f"DEBUG: Fallback questions: {questions}")

    # Ensure we return at most 5 questions
    result = questions[:5]
    print(f"DEBUG: Final questions: {result}")
    return result
