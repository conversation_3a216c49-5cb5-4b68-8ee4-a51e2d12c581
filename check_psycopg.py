import sys
print(f"Python version: {sys.version}")
print(f"Python executable: {sys.executable}")

try:
    import psycopg2
    print(f"Successfully imported psycopg2 version: {psycopg2.__version__}")
    print(f"psycopg2 location: {psycopg2.__file__}")
except ImportError as e:
    print(f"Failed to import psycopg2: {e}")
except Exception as e:
    print(f"Error with psycopg2: {e}")

try:
    import psycopg
    print(f"Successfully imported psycopg version: {psycopg.__version__}")
    print(f"psycopg location: {psycopg.__file__}")
except ImportError as e:
    print(f"Failed to import psycopg: {e}")
except Exception as e:
    print(f"Error with psycopg: {e}")
